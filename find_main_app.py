#!/usr/bin/env python3
"""
Find the main application code specifically
"""
import struct
import os
import sys
import marshal
import dis
import types

def is_main_app_code(filename, function_name):
    """Check if this looks like main application code"""
    if not filename:
        return False
        
    filename_lower = filename.lower()
    
    # Look for main application indicators
    main_indicators = [
        'main.py',
        '__main__.py',
        'imgproc.py',  # Based on the exe name
        'app.py',
        'run.py',
        'start.py'
    ]
    
    # Check for main-like filenames
    for indicator in main_indicators:
        if indicator in filename_lower:
            return True
            
    # Check for files that don't look like standard library
    if ('site-packages' in filename_lower or 
        'lib' in filename_lower or
        filename_lower.endswith('.py')):
        
        # Exclude common library patterns
        exclude_patterns = [
            'python3', 'lib', 'site-packages', 'dist-packages',
            'numpy', 'cv2', 'opencv', 'scipy', 'matplotlib',
            'pandas', 'sklearn', 'torch', 'tensorflow'
        ]
        
        for pattern in exclude_patterns:
            if pattern in filename_lower:
                return False
                
        # If it's a .py file and doesn't match exclusions, it might be app code
        return True
    
    # Check for specific function names that indicate main code
    if function_name in ['main', '__main__', 'run', 'start', 'execute']:
        return True
        
    return False

def find_main_application_code(data):
    """Find main application code objects"""
    marshal_candidates = []
    
    # Look for marshal data pattern
    pattern = b'\xe3'  # TYPE_CODE
    
    offset = 0
    total_found = 0
    
    print("Searching for main application code...")
    
    while total_found < 2000:  # Search more thoroughly
        pos = data.find(pattern, offset)
        if pos == -1:
            break
            
        # Try to unmarshal from this position
        try:
            remaining_data = data[pos:]
            if len(remaining_data) > 100:  # Need reasonable amount of data
                obj = marshal.loads(remaining_data)
                if isinstance(obj, types.CodeType):
                    total_found += 1
                    
                    # Check if this is main application code
                    filename = getattr(obj, 'co_filename', '')
                    function_name = getattr(obj, 'co_name', '')
                    
                    if is_main_app_code(filename, function_name):
                        marshal_candidates.append({
                            'position': pos,
                            'code_object': obj,
                            'pattern': pattern
                        })
                        print(f"Found main app code at position {pos}: {filename} ({function_name})")
                    
                    if total_found % 100 == 0:  # Progress indicator
                        print(f"Scanned {total_found} code objects, found {len(marshal_candidates)} main app objects...")
        except:
            pass
            
        offset = pos + 1
        
    return marshal_candidates

def search_for_specific_strings(data):
    """Search for application-specific strings"""
    print("\nSearching for application-specific strings...")
    
    # Look for strings that might indicate the main application
    app_strings = [
        b'imgproc',
        b'ImgProc',
        b'IMGPROC',
        b'align_image',
        b'create_object_normal_map',
        b'create_tangent_normal_map',
        b'create_specular_map',
        b'--function',
        b'--image_dir',
        b'--output',
        b'argparse',
        b'ArgumentParser',
        b'if __name__ == "__main__"',
        b'def main(',
        b'def run(',
    ]
    
    findings = []
    for search_str in app_strings:
        pos = data.find(search_str)
        if pos != -1:
            findings.append((search_str, pos))
            print(f"Found '{search_str.decode('utf-8', errors='ignore')}' at position {pos}")
            
    return findings

def extract_main_application(exe_path, output_dir):
    """Extract main application code"""
    print(f"Searching for main application code in {exe_path}")
    
    with open(exe_path, 'rb') as f:
        data = f.read()
        
    print(f"File size: {len(data)} bytes")
    
    # Create output directory
    os.makedirs(output_dir, exist_ok=True)
    
    # Search for application-specific strings first
    string_findings = search_for_specific_strings(data)
    
    # Find main application code
    marshal_candidates = find_main_application_code(data)
    
    print(f"\nFound {len(marshal_candidates)} main application code objects")
    
    if not marshal_candidates:
        print("No main application code found. The executable might use a different structure.")
        return
    
    extracted_count = 0
    for i, candidate in enumerate(marshal_candidates):
        try:
            code_obj = candidate['code_object']
            filename = f"main_app_{i+1:03d}"
            
            # Get original filename if available
            if hasattr(code_obj, 'co_filename') and code_obj.co_filename:
                orig_name = os.path.basename(code_obj.co_filename)
                if orig_name and orig_name not in ['<string>', '<frozen>', '<built-in>']:
                    filename = orig_name.replace('.py', '').replace('.', '_').replace('\\', '_').replace('/', '_')
                    
            print(f"\nProcessing {filename}")
            print(f"  Original: {getattr(code_obj, 'co_filename', 'unknown')}")
            print(f"  Function: {getattr(code_obj, 'co_name', 'unknown')}")
            
            # Create detailed disassembly
            output_path = os.path.join(output_dir, filename + '_main_disasm.txt')
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write(f"# MAIN APPLICATION CODE OBJECT\n")
                f.write(f"# Position: {candidate['position']}\n")
                f.write(f"# Filename: {getattr(code_obj, 'co_filename', 'unknown')}\n")
                f.write(f"# Function: {getattr(code_obj, 'co_name', 'unknown')}\n")
                f.write(f"# Args: {getattr(code_obj, 'co_argcount', 0)}\n")
                f.write(f"# Locals: {getattr(code_obj, 'co_nlocals', 0)}\n")
                f.write(f"# Stack size: {getattr(code_obj, 'co_stacksize', 0)}\n")
                f.write(f"# Flags: {getattr(code_obj, 'co_flags', 0)}\n\n")
                
                # Disassemble
                f.write("# BYTECODE DISASSEMBLY:\n")
                f.write("=" * 50 + "\n")
                dis.dis(code_obj, file=f)
                
                # Extract constants with more detail
                if hasattr(code_obj, 'co_consts'):
                    f.write("\n\n# CONSTANTS:\n")
                    f.write("=" * 50 + "\n")
                    for j, const in enumerate(code_obj.co_consts):
                        if isinstance(const, str):
                            if len(const) < 200:
                                f.write(f"# {j}: {repr(const)}\n")
                            else:
                                f.write(f"# {j}: <string length {len(const)}> '{const[:100]}...'\n")
                        elif isinstance(const, (int, float, bool, type(None))):
                            f.write(f"# {j}: {repr(const)}\n")
                        elif isinstance(const, types.CodeType):
                            f.write(f"# {j}: <code object {const.co_name} from {const.co_filename}>\n")
                        else:
                            f.write(f"# {j}: {type(const).__name__} - {repr(const)[:100]}\n")
                            
                # Extract names
                if hasattr(code_obj, 'co_names'):
                    f.write("\n\n# NAMES (global/attribute references):\n")
                    f.write("=" * 50 + "\n")
                    for j, name in enumerate(code_obj.co_names):
                        f.write(f"# {j}: {repr(name)}\n")
                        
                # Extract variable names
                if hasattr(code_obj, 'co_varnames'):
                    f.write("\n\n# VARIABLE NAMES (local variables):\n")
                    f.write("=" * 50 + "\n")
                    for j, name in enumerate(code_obj.co_varnames):
                        f.write(f"# {j}: {repr(name)}\n")
                        
                # Extract free variables
                if hasattr(code_obj, 'co_freevars'):
                    f.write("\n\n# FREE VARIABLES:\n")
                    f.write("=" * 50 + "\n")
                    for j, name in enumerate(code_obj.co_freevars):
                        f.write(f"# {j}: {repr(name)}\n")
                        
            print(f"  ✓ Detailed analysis saved to {output_path}")
            extracted_count += 1
                
        except Exception as e:
            print(f"  ✗ Failed to process code object {i+1}: {e}")
    
    # Save string findings
    if string_findings:
        strings_path = os.path.join(output_dir, 'app_strings.txt')
        with open(strings_path, 'w', encoding='utf-8') as f:
            f.write("Application-specific strings found:\n")
            f.write("=" * 50 + "\n\n")
            for string, pos in string_findings:
                f.write(f"Position {pos}: {string.decode('utf-8', errors='ignore')}\n")
        print(f"\nApplication strings saved to {strings_path}")
    
    print(f"\nExtracted {extracted_count} main application code objects to {output_dir}")

if __name__ == "__main__":
    if len(sys.argv) != 3:
        print("Usage: python find_main_app.py <exe_file> <output_dir>")
        sys.exit(1)
        
    exe_path = sys.argv[1]
    output_dir = sys.argv[2]
    
    extract_main_application(exe_path, output_dir)
