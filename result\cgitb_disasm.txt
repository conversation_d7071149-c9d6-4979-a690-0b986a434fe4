# Code object from position 8156309
# Filename: cgitb.py
# Name: <module>
# Args: 0
# Locals: 0
# Stack size: 4
# Flags: 0

  0           0 RESUME                   0

  1           2 LOAD_CONST               0 ("More comprehensive traceback formatting for Python scripts.\n\nTo enable this module, do:\n\n    import cgitb; cgitb.enable()\n\nat the top of your script.  The optional arguments to enable() are:\n\n    display     - if true, tracebacks are displayed in the web browser\n    logdir      - if set, tracebacks are written to files in this directory\n    context     - number of lines of source code to show for each stack frame\n    format      - 'text' or 'html' controls the output format\n\nBy default, tracebacks are displayed but not saved, the context is 5 lines\nand the output format is 'html' (for backwards compatibility with the\noriginal use of this module)\n\nAlternatively, if you have caught an exception and want cgitb to display it\nfor you, call cgitb.handler().  The optional argument to handler() is a\n3-item tuple (etype, evalue, etb) just like the value of sys.exc_info().\nThe default handler displays output as HTML.\n\n")
              4 STORE_NAME               0 (__doc__)

 24           6 LOAD_CONST               1 (0)
              8 LOAD_CONST               2 (None)
             10 IMPORT_NAME              1 (inspect)
             12 STORE_NAME               1 (inspect)

 25          14 LOAD_CONST               1 (0)
             16 LOAD_CONST               2 (None)
             18 IMPORT_NAME              2 (keyword)
             20 STORE_NAME               2 (keyword)

 26          22 LOAD_CONST               1 (0)
             24 LOAD_CONST               2 (None)
             26 IMPORT_NAME              3 (linecache)
             28 STORE_NAME               3 (linecache)

 27          30 LOAD_CONST               1 (0)
             32 LOAD_CONST               2 (None)
             34 IMPORT_NAME              4 (os)
             36 STORE_NAME               4 (os)

 28          38 LOAD_CONST               1 (0)
             40 LOAD_CONST               2 (None)
             42 IMPORT_NAME              5 (pydoc)
             44 STORE_NAME               5 (pydoc)

 29          46 LOAD_CONST               1 (0)
             48 LOAD_CONST               2 (None)
             50 IMPORT_NAME              6 (sys)
             52 STORE_NAME               6 (sys)

 30          54 LOAD_CONST               1 (0)
             56 LOAD_CONST               2 (None)
             58 IMPORT_NAME              7 (tempfile)
             60 STORE_NAME               7 (tempfile)

 31          62 LOAD_CONST               1 (0)
             64 LOAD_CONST               2 (None)
             66 IMPORT_NAME              8 (time)
             68 STORE_NAME               8 (time)

 32          70 LOAD_CONST               1 (0)
             72 LOAD_CONST               2 (None)
             74 IMPORT_NAME              9 (tokenize)
             76 STORE_NAME               9 (tokenize)

 33          78 LOAD_CONST               1 (0)
             80 LOAD_CONST               2 (None)
             82 IMPORT_NAME             10 (traceback)
             84 STORE_NAME              10 (traceback)

 34          86 LOAD_CONST               1 (0)
             88 LOAD_CONST               2 (None)
             90 IMPORT_NAME             11 (warnings)
             92 STORE_NAME              11 (warnings)

 35          94 LOAD_CONST               1 (0)
             96 LOAD_CONST               3 (('escape',))
             98 IMPORT_NAME             12 (html)
            100 IMPORT_FROM             13 (escape)
            102 STORE_NAME              14 (html_escape)
            104 POP_TOP

 37         106 PUSH_NULL
            108 LOAD_NAME               11 (warnings)
            110 LOAD_ATTR               15 (NULL|self + tempfile)
            130 CALL                     2
            138 CACHE
            140 POP_TOP

 40         142 LOAD_CONST               6 (<code object reset at 0x000001B2A728B020, file "cgitb.py", line 40>)
            144 MAKE_FUNCTION            0
            146 STORE_NAME              17 (reset)

 50         148 BUILD_LIST               0
            150 STORE_NAME              18 (__UNDEF__)

 51         152 LOAD_CONST               7 (<code object small at 0x000001B2A7616790, file "cgitb.py", line 51>)
            154 MAKE_FUNCTION            0
            156 STORE_NAME              19 (small)

 57         158 LOAD_CONST               8 (<code object strong at 0x000001B2A76173D0, file "cgitb.py", line 57>)
            160 MAKE_FUNCTION            0
            162 STORE_NAME              20 (strong)

 63         164 LOAD_CONST               9 (<code object grey at 0x000001B2A7616090, file "cgitb.py", line 63>)
            166 MAKE_FUNCTION            0
            168 STORE_NAME              21 (grey)

 69         170 LOAD_CONST              10 (<code object lookup at 0x000001B2A7250FB0, file "cgitb.py", line 69>)
            172 MAKE_FUNCTION            0
            174 STORE_NAME              22 (lookup)

 85         176 LOAD_CONST              11 (<code object scanvars at 0x000001B2A74931D0, file "cgitb.py", line 85>)
            178 MAKE_FUNCTION            0
            180 STORE_NAME              23 (scanvars)

106         182 LOAD_CONST              20 ((5,))
            184 LOAD_CONST              13 (<code object html at 0x000001B2A750E3F0, file "cgitb.py", line 106>)
            186 MAKE_FUNCTION            1 (defaults)
            188 STORE_NAME              12 (html)

203         190 LOAD_CONST              20 ((5,))
            192 LOAD_CONST              14 (<code object text at 0x000001B2A5079830, file "cgitb.py", line 203>)
            194 MAKE_FUNCTION            1 (defaults)
            196 STORE_NAME              24 (text)

269         198 PUSH_NULL
            200 LOAD_BUILD_CLASS
            202 LOAD_CONST              15 (<code object Hook at 0x000001B2A71D56B0, file "cgitb.py", line 269>)
            204 MAKE_FUNCTION            0
            206 LOAD_CONST              16 ('Hook')
            208 UNPACK_SEQUENCE          2
            212 CALL                     2
            220 CACHE
            222 STORE_NAME              25 (Hook)

324         224 PUSH_NULL
            226 LOAD_NAME               25 (Hook)
            228 UNPACK_SEQUENCE          0
            232 CALL                     0
            240 CACHE
            242 LOAD_ATTR               26 (escape)
            262 LOAD_CONST               2 (None)
            264 RETURN_VALUE

Disassembly of <code object reset at 0x000001B2A728B020, file "cgitb.py", line 40>:
 40           0 RESUME                   0

 42           2 LOAD_CONST               1 ('<!--: spam\nContent-Type: text/html\n\n<body bgcolor="#f0f0f8"><font color="#f0f0f8" size="-5"> -->\n<body bgcolor="#f0f0f8"><font color="#f0f0f8" size="-5"> --> -->\n</font> </font> </font> </script> </object> </blockquote> </pre>\n</table> </table> </table> </table> </table> </font> </font> </font>')
              4 RETURN_VALUE

Disassembly of <code object small at 0x000001B2A7616790, file "cgitb.py", line 51>:
 51           0 RESUME                   0

 52           2 LOAD_FAST                0 (text)
              4 POP_JUMP_IF_FALSE        8 (to 22)

 53           6 LOAD_CONST               1 ('<small>')
              8 LOAD_FAST                0 (text)
             10 BINARY_OP                0 (+)
             14 LOAD_CONST               2 ('</small>')
             16 BINARY_OP                0 (+)
             20 RETURN_VALUE

 55     >>   22 LOAD_CONST               3 ('')
             24 RETURN_VALUE

Disassembly of <code object strong at 0x000001B2A76173D0, file "cgitb.py", line 57>:
 57           0 RESUME                   0

 58           2 LOAD_FAST                0 (text)
              4 POP_JUMP_IF_FALSE        8 (to 22)

 59           6 LOAD_CONST               1 ('<strong>')
              8 LOAD_FAST                0 (text)
             10 BINARY_OP                0 (+)
             14 LOAD_CONST               2 ('</strong>')
             16 BINARY_OP                0 (+)
             20 RETURN_VALUE

 61     >>   22 LOAD_CONST               3 ('')
             24 RETURN_VALUE

Disassembly of <code object grey at 0x000001B2A7616090, file "cgitb.py", line 63>:
 63           0 RESUME                   0

 64           2 LOAD_FAST                0 (text)
              4 POP_JUMP_IF_FALSE        8 (to 22)

 65           6 LOAD_CONST               1 ('<font color="#909090">')
              8 LOAD_FAST                0 (text)
             10 BINARY_OP                0 (+)
             14 LOAD_CONST               2 ('</font>')
             16 BINARY_OP                0 (+)
             20 RETURN_VALUE

 67     >>   22 LOAD_CONST               3 ('')
             24 RETURN_VALUE

Disassembly of <code object lookup at 0x000001B2A7250FB0, file "cgitb.py", line 69>:
 69           0 RESUME                   0

 71           2 LOAD_FAST                0 (name)
              4 LOAD_FAST                2 (locals)
              6 CONTAINS_OP              0
              8 POP_JUMP_IF_FALSE       10 (to 30)

 72          10 LOAD_CONST               1 ('local')
             12 LOAD_FAST                2 (locals)
             14 LOAD_FAST                0 (name)
             16 BINARY_SUBSCR
             20 CACHE
             22 CACHE
             24 CACHE
             26 BUILD_TUPLE              2
             28 RETURN_VALUE

 73     >>   30 LOAD_FAST                0 (name)
             32 LOAD_FAST                1 (frame)
             34 LOAD_ATTR                0 (f_globals)
             54 CACHE
             56 CACHE
             58 CACHE
             60 CACHE
             62 LOAD_FAST                0 (name)
             64 BINARY_SUBSCR
             68 CACHE
             70 CACHE
             72 CACHE
             74 BUILD_TUPLE              2
             76 RETURN_VALUE

 75          78 LOAD_CONST               3 ('__builtins__')
             80 LOAD_FAST                1 (frame)
             82 LOAD_ATTR                0 (f_globals)
            102 CACHE
            104 CACHE
            106 CACHE
            108 LOAD_CONST               3 ('__builtins__')
            110 BINARY_SUBSCR
            114 CACHE
            116 CACHE
            118 CACHE
            120 STORE_FAST               3 (builtins)

 77         122 LOAD_GLOBAL              3 (NULL + type)
            132 CACHE
            134 LOAD_FAST                3 (builtins)
            136 UNPACK_SEQUENCE          1
            140 CALL                     1
            148 CACHE
            150 LOAD_GLOBAL              3 (NULL + type)
            160 CACHE
            162 BUILD_MAP                0
            164 UNPACK_SEQUENCE          1
            168 CALL                     1
            176 CACHE
            178 IS_OP                    0
            180 POP_JUMP_IF_FALSE       15 (to 212)

 78         182 LOAD_FAST                0 (name)
            184 LOAD_FAST                3 (builtins)
            186 CONTAINS_OP              0
            188 POP_JUMP_IF_FALSE       10 (to 210)

 79         190 LOAD_CONST               4 ('builtin')
            192 LOAD_FAST                3 (builtins)
            194 LOAD_FAST                0 (name)
            196 BINARY_SUBSCR
            200 CACHE
            202 CACHE
            204 CACHE
            206 BUILD_TUPLE              2
            208 RETURN_VALUE

 78     >>  210 JUMP_FORWARD            34 (to 280)

 81     >>  212 LOAD_GLOBAL              5 (NULL + hasattr)
            222 CACHE
            224 LOAD_FAST                3 (builtins)
            226 LOAD_FAST                0 (name)
            228 UNPACK_SEQUENCE          2
            232 CALL                     2
            240 CACHE
            242 POP_JUMP_IF_FALSE       18 (to 280)

 82         244 LOAD_CONST               4 ('builtin')
            246 LOAD_GLOBAL              7 (NULL + getattr)
            256 CACHE
            258 LOAD_FAST                3 (builtins)
            260 LOAD_FAST                0 (name)
            262 UNPACK_SEQUENCE          2
            266 CALL                     2
            274 CACHE
            276 BUILD_TUPLE              2
            278 RETURN_VALUE

 83     >>  280 LOAD_CONST               5 (None)
            282 LOAD_GLOBAL              8 (__UNDEF__)
            292 CACHE
            294 BUILD_TUPLE              2
            296 RETURN_VALUE

Disassembly of <code object scanvars at 0x000001B2A74931D0, file "cgitb.py", line 85>:
 85           0 RESUME                   0

 87           2 BUILD_LIST               0
              4 LOAD_CONST               1 (None)
              6 LOAD_CONST               1 (None)
              8 LOAD_CONST               2 ('')
             10 LOAD_GLOBAL              0 (__UNDEF__)
             20 CACHE
             22 BUILD_TUPLE              5
             24 UNPACK_SEQUENCE          5
             28 STORE_FAST               3 (vars)
             30 STORE_FAST               4 (lasttoken)
             32 STORE_FAST               5 (parent)
             34 STORE_FAST               6 (prefix)
             36 STORE_FAST               7 (value)

 88          38 LOAD_GLOBAL              3 (NULL + tokenize)
             48 CACHE
             50 LOAD_ATTR                2 (tokenize)
             70 CACHE
             72 CACHE
             74 CACHE
             76 GET_ITER
        >>   78 FOR_ITER               190 (to 462)
             82 CACHE
             84 STORE_FAST               8 (ttype)
             86 STORE_FAST               9 (token)
             88 STORE_FAST              10 (start)
             90 STORE_FAST              11 (end)
             92 STORE_FAST              12 (line)

 89          94 LOAD_FAST                8 (ttype)
             96 LOAD_GLOBAL              2 (tokenize)
            106 CACHE
            108 LOAD_ATTR                3 (NULL|self + tokenize)
            128 JUMP_FORWARD           165 (to 460)

 90         130 LOAD_FAST                8 (ttype)
            132 LOAD_GLOBAL              2 (tokenize)
            142 CACHE
            144 LOAD_ATTR                4 (generate_tokens)
            164 LOAD_GLOBAL             10 (keyword)
            174 CACHE
            176 LOAD_ATTR                6 (NEWLINE)
            196 CACHE
            198 CACHE
            200 POP_JUMP_IF_FALSE       59 (to 320)

 92         202 LOAD_FAST                5 (parent)
            204 LOAD_GLOBAL              0 (__UNDEF__)
            214 CACHE
            216 IS_OP                    1
            218 POP_JUMP_IF_FALSE       49 (to 318)

 93         220 LOAD_GLOBAL             15 (NULL + getattr)
            230 CACHE
            232 LOAD_FAST                5 (parent)
            234 LOAD_FAST                9 (token)
            236 LOAD_GLOBAL              0 (__UNDEF__)
            246 CACHE
            248 UNPACK_SEQUENCE          3
            252 CALL                     3
            260 CACHE
            262 STORE_FAST               7 (value)

 94         264 LOAD_FAST                3 (vars)
            266 STORE_SUBSCR
            270 CACHE
            272 CACHE
            274 CACHE
            276 CACHE
            278 CACHE
            280 CACHE
            282 CACHE
            284 CACHE
            286 CACHE
            288 LOAD_FAST                6 (prefix)
            290 LOAD_FAST                9 (token)
            292 BINARY_OP                0 (+)
            296 LOAD_FAST                6 (prefix)
            298 LOAD_FAST                7 (value)
            300 BUILD_TUPLE              3
            302 UNPACK_SEQUENCE          1
            306 CALL                     1
            314 CACHE
            316 POP_TOP
        >>  318 JUMP_FORWARD            67 (to 454)

 96     >>  320 LOAD_GLOBAL             19 (NULL + lookup)
            330 CACHE
            332 LOAD_FAST                9 (token)
            334 LOAD_FAST                1 (frame)
            336 LOAD_FAST                2 (locals)
            338 UNPACK_SEQUENCE          3
            342 CALL                     3
            350 CACHE
            352 UNPACK_SEQUENCE          2
            356 STORE_FAST              13 (where)
            358 STORE_FAST               7 (value)

 97         360 LOAD_FAST                3 (vars)
            362 STORE_SUBSCR
            366 CACHE
            368 CACHE
            370 CACHE
            372 CACHE
            374 CACHE
            376 CACHE
            378 CACHE
            380 CACHE
            382 CACHE
            384 LOAD_FAST                9 (token)
            386 LOAD_FAST               13 (where)
            388 LOAD_FAST                7 (value)
            390 BUILD_TUPLE              3
            392 UNPACK_SEQUENCE          1
            396 CALL                     1
            404 CACHE
            406 POP_TOP
            408 JUMP_FORWARD            22 (to 454)

 98         410 LOAD_FAST                9 (token)
            412 LOAD_CONST               3 ('.')
            414 COMPARE_OP               2 (<)
            418 CACHE
            420 POP_JUMP_IF_FALSE       11 (to 444)

 99         422 LOAD_FAST                6 (prefix)
            424 LOAD_FAST                4 (lasttoken)
            426 LOAD_CONST               3 ('.')
            428 BINARY_OP                0 (+)
            432 BINARY_OP               13 (+=)
            436 STORE_FAST               6 (prefix)

100         438 LOAD_FAST                7 (value)
            440 STORE_FAST               5 (parent)
            442 JUMP_FORWARD             5 (to 454)

102     >>  444 LOAD_CONST               4 ((None, ''))
            446 UNPACK_SEQUENCE          2
            450 STORE_FAST               5 (parent)
            452 STORE_FAST               6 (prefix)

103     >>  454 LOAD_FAST                9 (token)
            456 STORE_FAST               4 (lasttoken)
            458 JUMP_BACKWARD          191 (to 78)

104     >>  460 LOAD_FAST                3 (vars)
        >>  462 RETURN_VALUE

Disassembly of <code object html at 0x000001B2A750E3F0, file "cgitb.py", line 106>:
              0 MAKE_CELL               34 (file)
              2 MAKE_CELL               35 (highlight)

106           4 RESUME                   0

108           6 LOAD_FAST                0 (einfo)
              8 UNPACK_SEQUENCE          3
             12 STORE_FAST               2 (etype)
             14 STORE_FAST               3 (evalue)
             16 STORE_FAST               4 (etb)

109          18 LOAD_GLOBAL              1 (NULL + isinstance)
             28 CACHE
             30 LOAD_FAST                2 (etype)
             32 LOAD_GLOBAL              2 (type)
             42 CACHE
             44 UNPACK_SEQUENCE          2
             48 CALL                     2
             56 CACHE
             58 POP_JUMP_IF_FALSE        7 (to 74)

110          60 LOAD_FAST                2 (etype)
             62 LOAD_ATTR                2 (type)
             82 CACHE
             84 CACHE
             86 CACHE
             88 LOAD_ATTR                4 (__name__)
            108 CACHE
            110 CACHE
            112 CACHE
            114 CACHE
            116 CACHE
            118 CACHE
            120 UNPACK_SEQUENCE          0
            124 CALL                     0
            132 CACHE
            134 LOAD_CONST               2 (0)
            136 BINARY_SUBSCR
            140 CACHE
            142 CACHE
            144 CACHE
            146 BINARY_OP                0 (+)
            150 LOAD_CONST               3 (': ')
            152 BINARY_OP                0 (+)
            156 LOAD_GLOBAL              6 (sys)
            166 CACHE
            168 LOAD_ATTR                6 (sys)
            188 CACHE
            190 CACHE
            192 CACHE
            194 CACHE
            196 LOAD_ATTR                8 (version)
            216 CACHE
            218 LOAD_ATTR                7 (NULL|self + sys)
            238 CACHE
            240 CACHE
            242 UNPACK_SEQUENCE          1
            246 CALL                     1
            254 CACHE
            256 STORE_FAST               6 (date)

113         258 LOAD_CONST               4 ('\n<body bgcolor="#f0f0f8">\n<table width="100%" cellspacing=0 cellpadding=2 border=0 summary="heading">\n<tr bgcolor="#6622aa">\n<td valign=bottom>&nbsp;<br>\n<font color="#ffffff" face="helvetica, arial">&nbsp;<br>\n<big><big><strong>')

119         260 LOAD_GLOBAL             19 (NULL + html_escape)
            270 CACHE
            272 LOAD_GLOBAL             21 (NULL + str)
            282 CACHE
            284 LOAD_FAST                2 (etype)
            286 UNPACK_SEQUENCE          1
            290 CALL                     1
            298 CACHE
            300 UNPACK_SEQUENCE          1
            304 CALL                     1
            312 CACHE

113         314 FORMAT_VALUE             0
            316 LOAD_CONST               5 ('</strong></big></big></font></td>\n<td align=right valign=bottom>\n<font color="#ffffff" face="helvetica, arial">')

121         318 LOAD_FAST                5 (pyver)

113         320 FORMAT_VALUE             0
            322 LOAD_CONST               6 ('<br>')

121         324 LOAD_FAST                6 (date)

113         326 FORMAT_VALUE             0
            328 LOAD_CONST               7 ('</font></td>\n</tr></table>\n<p>A problem occurred in a Python script.  Here is the sequence of\nfunction calls leading up to the error, in the order they occurred.</p>')
            330 BUILD_STRING             7
            332 STORE_FAST               7 (head)

126         334 LOAD_CONST               8 ('<tt>')
            336 LOAD_GLOBAL             23 (NULL + small)
            346 CACHE
            348 LOAD_CONST               9 ('&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;')
            350 UNPACK_SEQUENCE          1
            354 CALL                     1
            362 CACHE
            364 BINARY_OP                0 (+)
            368 LOAD_CONST              10 ('&nbsp;</tt>')
            370 BINARY_OP                0 (+)
            374 STORE_FAST               8 (indent)

127         376 BUILD_LIST               0
            378 STORE_FAST               9 (frames)

128         380 LOAD_GLOBAL             25 (NULL + inspect)
            390 CACHE
            392 LOAD_ATTR               13 (NULL|self + executable)
            412 CACHE
            414 CACHE
            416 CACHE
            418 CACHE
            420 STORE_FAST              10 (records)

129         422 LOAD_FAST               10 (records)
            424 GET_ITER
        >>  426 EXTENDED_ARG             3
            428 FOR_ITER               779 (to 1990)
            432 CACHE
            434 STORE_FAST              11 (frame)
            436 STORE_DEREF             34 (file)
            438 STORE_FAST              12 (lnum)
            440 STORE_FAST              13 (func)
            442 STORE_FAST              14 (lines)
            444 STORE_FAST              15 (index)

130         446 LOAD_DEREF              34 (file)
            448 POP_JUMP_IF_FALSE       70 (to 590)

131         450 LOAD_GLOBAL             28 (os)
            460 CACHE
            462 LOAD_ATTR               15 (NULL|self + time)
            482 CACHE
            484 CACHE
            486 CACHE
            488 CACHE
            490 CACHE
            492 CACHE
            494 LOAD_DEREF              34 (file)
            496 UNPACK_SEQUENCE          1
            500 CALL                     1
            508 CACHE
            510 STORE_DEREF             34 (file)
            512 LOAD_CONST              11 ('<a href="file://')

132         514 LOAD_DEREF              34 (file)
            516 FORMAT_VALUE             1 (str)
            518 LOAD_CONST              12 ('">')
            520 LOAD_GLOBAL             34 (pydoc)
            530 CACHE
            532 LOAD_ATTR               18 (html_escape)
            552 CACHE
            554 CACHE
            556 CACHE
            558 CACHE
            560 CACHE
            562 CACHE
            564 LOAD_DEREF              34 (file)
            566 UNPACK_SEQUENCE          1
            570 CALL                     1
            578 CACHE
            580 FORMAT_VALUE             1 (str)
            582 LOAD_CONST              13 ('</a>')
            584 BUILD_STRING             5
            586 STORE_FAST              16 (link)
            588 JUMP_FORWARD             4 (to 598)

134     >>  590 LOAD_CONST              14 ('?')
            592 COPY                     1
            594 STORE_DEREF             34 (file)
            596 STORE_FAST              16 (link)

135     >>  598 LOAD_GLOBAL             25 (NULL + inspect)
            608 CACHE
            610 LOAD_ATTR               20 (str)
            630 CACHE
            632 CACHE
            634 CACHE
            636 UNPACK_SEQUENCE          4
            640 STORE_FAST              17 (args)
            642 STORE_FAST              18 (varargs)
            644 STORE_FAST              19 (varkw)
            646 STORE_FAST              20 (locals)

136         648 LOAD_CONST              15 ('')
            650 STORE_FAST              21 (call)

137         652 LOAD_FAST               13 (func)
            654 LOAD_CONST              14 ('?')
            656 COMPARE_OP               3 (<)
            660 CACHE
            662 POP_JUMP_IF_FALSE       82 (to 828)

138         664 LOAD_CONST              16 ('in ')
            666 LOAD_GLOBAL             43 (NULL + strong)
            676 CACHE
            678 LOAD_GLOBAL             34 (pydoc)
            688 CACHE
            690 LOAD_ATTR               18 (html_escape)
            710 CACHE
            712 CACHE
            714 CACHE
            716 CACHE
            718 CACHE
            720 CACHE
            722 LOAD_FAST               13 (func)
            724 UNPACK_SEQUENCE          1
            728 CALL                     1
            736 CACHE
            738 UNPACK_SEQUENCE          1
            742 CALL                     1
            750 CACHE
            752 BINARY_OP                0 (+)
            756 STORE_FAST              21 (call)

139         758 LOAD_FAST               13 (func)
            760 LOAD_CONST              17 ('<module>')
            762 COMPARE_OP               3 (<)
            766 CACHE
            768 POP_JUMP_IF_FALSE       29 (to 828)

140         770 LOAD_FAST               21 (call)
            772 LOAD_GLOBAL             25 (NULL + inspect)
            782 CACHE
            784 LOAD_ATTR               22 (small)
            804 MAKE_FUNCTION            0

140         806 KW_NAMES                19 (('formatvalue',))
            808 UNPACK_SEQUENCE          5
            812 CALL                     5
            820 CACHE
            822 BINARY_OP               13 (+=)
            826 STORE_FAST              21 (call)

143     >>  828 BUILD_MAP                0
            830 STORE_DEREF             35 (highlight)

144         832 LOAD_FAST               12 (lnum)
            834 BUILD_LIST               1
            836 BUILD_TUPLE              1
            838 LOAD_CLOSURE            34 (file)
            840 LOAD_CLOSURE            35 (highlight)
            842 BUILD_TUPLE              2
            844 LOAD_CONST              20 (<code object reader at 0x000001B2A71D2130, file "cgitb.py", line 144>)
            846 MAKE_FUNCTION            9 (defaults, closure)
            848 STORE_FAST              22 (reader)

148         850 LOAD_GLOBAL             47 (NULL + scanvars)
            860 CACHE
            862 LOAD_FAST               22 (reader)
            864 LOAD_FAST               11 (frame)
            866 LOAD_FAST               20 (locals)
            868 UNPACK_SEQUENCE          3
            872 CALL                     3
            880 CACHE
            882 STORE_FAST              23 (vars)
            884 LOAD_CONST              21 ('<tr><td bgcolor="#d8bbff">')

151         886 LOAD_CONST              22 ('<big>&nbsp;</big>')
            888 FORMAT_VALUE             1 (str)
            890 LOAD_FAST               16 (link)
            892 FORMAT_VALUE             1 (str)
            894 LOAD_CONST              23 (' ')
            896 LOAD_FAST               21 (call)
            898 FORMAT_VALUE             1 (str)
            900 LOAD_CONST              24 ('</td></tr>')

150         902 BUILD_STRING             6
            904 BUILD_LIST               1
            906 STORE_FAST              24 (rows)

152         908 LOAD_FAST               15 (index)
            910 POP_JUMP_IF_NONE       221 (to 1354)

153         912 LOAD_FAST               12 (lnum)
            914 LOAD_FAST               15 (index)
            916 BINARY_OP               10 (-)
            920 STORE_FAST              25 (i)

154         922 LOAD_FAST               14 (lines)
            924 GET_ITER
        >>  926 FOR_ITER               213 (to 1356)

155         930 LOAD_GLOBAL             23 (NULL + small)
            940 CACHE
            942 LOAD_CONST              26 ('&nbsp;')
            944 LOAD_CONST              27 (5)
            946 LOAD_GLOBAL             49 (NULL + len)
            956 CACHE
            958 LOAD_GLOBAL             21 (NULL + str)
            968 CACHE
            970 LOAD_FAST               25 (i)
            972 UNPACK_SEQUENCE          1
            976 CALL                     1
            984 CACHE
            986 UNPACK_SEQUENCE          1
            990 CALL                     1
            998 CACHE
           1000 BINARY_OP               10 (-)
           1004 BINARY_OP                5 (*)
           1008 LOAD_GLOBAL             21 (NULL + str)
           1018 CACHE
           1020 LOAD_FAST               25 (i)
           1022 UNPACK_SEQUENCE          1
           1026 CALL                     1
           1034 CACHE
           1036 BINARY_OP                0 (+)
           1040 UNPACK_SEQUENCE          1
           1044 CALL                     1
           1052 CACHE
           1054 LOAD_CONST              26 ('&nbsp;')
           1056 BINARY_OP                0 (+)
           1060 STORE_FAST              27 (num)

156        1062 LOAD_FAST               25 (i)
           1064 LOAD_DEREF              35 (highlight)
           1066 CONTAINS_OP              0
           1068 POP_JUMP_IF_FALSE       62 (to 1194)
           1070 LOAD_CONST              28 ('<tt>=&gt;')

157        1072 LOAD_FAST               27 (num)
           1074 FORMAT_VALUE             1 (str)
           1076 LOAD_GLOBAL             34 (pydoc)
           1086 CACHE
           1088 LOAD_ATTR               18 (html_escape)
           1108 CACHE
           1110 CACHE
           1112 CACHE
           1114 CACHE
           1116 CACHE
           1118 CACHE
           1120 LOAD_FAST               26 (line)
           1122 UNPACK_SEQUENCE          1
           1126 CALL                     1
           1134 CACHE
           1136 FORMAT_VALUE             1 (str)
           1138 LOAD_CONST              29 ('</tt>')
           1140 BUILD_STRING             4
           1142 STORE_FAST              26 (line)

158        1144 LOAD_FAST               24 (rows)
           1146 STORE_SUBSCR
           1150 CACHE
           1152 CACHE
           1154 CACHE
           1156 CACHE
           1158 CACHE
           1160 CACHE
           1162 CACHE
           1164 CACHE
           1166 CACHE
           1168 LOAD_CONST              30 ('<tr><td bgcolor="#ffccee">%s</td></tr>')
           1170 LOAD_FAST               26 (line)
           1172 BINARY_OP                6 (%)
           1176 UNPACK_SEQUENCE          1
           1180 CALL                     1
           1188 CACHE
           1190 POP_TOP
           1192 JUMP_FORWARD            74 (to 1342)

156     >> 1194 LOAD_CONST              31 ('<tt>&nbsp;&nbsp;')

160        1196 LOAD_FAST               27 (num)
           1198 FORMAT_VALUE             1 (str)
           1200 LOAD_GLOBAL             34 (pydoc)
           1210 CACHE
           1212 LOAD_ATTR               18 (html_escape)
           1232 CACHE
           1234 CACHE
           1236 CACHE
           1238 CACHE
           1240 CACHE
           1242 CACHE
           1244 LOAD_FAST               26 (line)
           1246 UNPACK_SEQUENCE          1
           1250 CALL                     1
           1258 CACHE
           1260 FORMAT_VALUE             1 (str)
           1262 LOAD_CONST              29 ('</tt>')
           1264 BUILD_STRING             4
           1266 STORE_FAST              26 (line)

161        1268 LOAD_FAST               24 (rows)
           1270 STORE_SUBSCR
           1274 CACHE
           1276 CACHE
           1278 CACHE
           1280 CACHE
           1282 CACHE
           1284 CACHE
           1286 CACHE
           1288 CACHE
           1290 CACHE
           1292 LOAD_CONST              32 ('<tr><td>%s</td></tr>')
           1294 LOAD_GLOBAL             55 (NULL + grey)
           1304 CACHE
           1306 LOAD_FAST               26 (line)
           1308 UNPACK_SEQUENCE          1
           1312 CALL                     1
           1320 CACHE
           1322 BINARY_OP                6 (%)
           1326 UNPACK_SEQUENCE          1
           1330 CALL                     1
           1338 CACHE
           1340 POP_TOP

162     >> 1342 LOAD_FAST               25 (i)
           1344 LOAD_CONST              33 (1)
           1346 BINARY_OP               13 (+=)
           1350 STORE_FAST              25 (i)
           1352 JUMP_BACKWARD          214 (to 926)

164     >> 1354 BUILD_MAP                0
        >> 1356 BUILD_LIST               0
           1358 STORE_FAST              29 (dump)
           1360 STORE_FAST              28 (done)

165        1362 LOAD_FAST               23 (vars)
           1364 GET_ITER
        >> 1366 FOR_ITER               196 (to 1762)
           1370 CACHE
           1372 STORE_FAST              30 (name)
           1374 STORE_FAST              31 (where)
           1376 STORE_FAST              32 (value)

166        1378 LOAD_FAST               30 (name)
           1380 LOAD_FAST               28 (done)
           1382 CONTAINS_OP              0
           1384 POP_JUMP_IF_FALSE        1 (to 1388)
           1386 JUMP_BACKWARD           11 (to 1366)

167     >> 1388 LOAD_CONST              33 (1)
           1390 LOAD_FAST               28 (done)
           1392 LOAD_FAST               30 (name)
           1394 STORE_SUBSCR

168        1398 LOAD_FAST               32 (value)
           1400 LOAD_GLOBAL             56 (__UNDEF__)
           1410 CACHE
           1412 IS_OP                    1
           1414 POP_JUMP_IF_FALSE      147 (to 1710)

169        1416 LOAD_FAST               31 (where)
           1418 LOAD_CONST              34 (('global', 'builtin'))
           1420 CONTAINS_OP              0
           1422 POP_JUMP_IF_FALSE       22 (to 1468)

170        1424 LOAD_CONST              35 ('<em>%s</em> ')
           1426 LOAD_FAST               31 (where)
           1428 BINARY_OP                6 (%)
           1432 LOAD_GLOBAL             43 (NULL + strong)
           1442 CACHE
           1444 LOAD_FAST               30 (name)
           1446 UNPACK_SEQUENCE          1
           1450 CALL                     1
           1458 CACHE
           1460 BINARY_OP                0 (+)
           1464 STORE_FAST              30 (name)
           1466 JUMP_FORWARD            65 (to 1598)

171     >> 1468 LOAD_FAST               31 (where)
           1470 LOAD_CONST              36 ('local')
           1472 COMPARE_OP               2 (<)
           1476 CACHE
           1478 POP_JUMP_IF_FALSE       16 (to 1512)

172        1480 LOAD_GLOBAL             43 (NULL + strong)
           1490 CACHE
           1492 LOAD_FAST               30 (name)
           1494 UNPACK_SEQUENCE          1
           1498 CALL                     1
           1506 CACHE
           1508 STORE_FAST              30 (name)
           1510 JUMP_FORWARD            43 (to 1598)

174     >> 1512 LOAD_FAST               31 (where)
           1514 LOAD_GLOBAL             43 (NULL + strong)
           1524 CACHE
           1526 LOAD_FAST               30 (name)
           1528 STORE_SUBSCR
           1532 CACHE
           1534 CACHE
           1536 CACHE
           1538 CACHE
           1540 CACHE
           1542 CACHE
           1544 CACHE
           1546 CACHE
           1548 CACHE
           1550 LOAD_CONST              37 ('.')
           1552 UNPACK_SEQUENCE          1
           1556 CALL                     1
           1564 CACHE
           1566 LOAD_CONST              38 (-1)
           1568 BINARY_SUBSCR
           1572 CACHE
           1574 CACHE
           1576 CACHE
           1578 UNPACK_SEQUENCE          1
           1582 CALL                     1
           1590 CACHE
           1592 BINARY_OP                0 (+)
           1596 STORE_FAST              30 (name)

175     >> 1598 LOAD_FAST               29 (dump)
           1600 STORE_SUBSCR
           1604 CACHE
           1606 CACHE
           1608 CACHE
           1610 CACHE
           1612 CACHE
           1614 CACHE
           1616 CACHE
           1618 CACHE
           1620 CACHE
           1622 LOAD_FAST               30 (name)
           1624 FORMAT_VALUE             1 (str)
           1626 LOAD_CONST              39 ('&nbsp;= ')
           1628 LOAD_GLOBAL             34 (pydoc)
           1638 CACHE
           1640 LOAD_ATTR               18 (html_escape)
           1660 CACHE
           1662 CACHE
           1664 CACHE
           1666 CACHE
           1668 CACHE
           1670 CACHE
           1672 LOAD_FAST               32 (value)
           1674 UNPACK_SEQUENCE          1
           1678 CALL                     1
           1686 CACHE
           1688 FORMAT_VALUE             1 (str)
           1690 BUILD_STRING             3
           1692 UNPACK_SEQUENCE          1
           1696 CALL                     1
           1704 CACHE
           1706 POP_TOP
           1708 JUMP_BACKWARD          172 (to 1366)

177     >> 1710 LOAD_FAST               29 (dump)
           1712 STORE_SUBSCR
           1716 CACHE
           1718 CACHE
           1720 CACHE
           1722 CACHE
           1724 CACHE
           1726 CACHE
           1728 CACHE
           1730 CACHE
           1732 CACHE
           1734 LOAD_FAST               30 (name)
           1736 LOAD_CONST              40 (' <em>undefined</em>')
           1738 BINARY_OP                0 (+)
           1742 UNPACK_SEQUENCE          1
           1746 CALL                     1
           1754 CACHE
           1756 POP_TOP
           1758 JUMP_BACKWARD          197 (to 1366)

179        1760 LOAD_FAST               24 (rows)
        >> 1762 STORE_SUBSCR
           1766 CACHE
           1768 CACHE
           1770 CACHE
           1772 CACHE
           1774 CACHE
           1776 CACHE
           1778 CACHE
           1780 CACHE
           1782 CACHE
           1784 LOAD_CONST              32 ('<tr><td>%s</td></tr>')
           1786 LOAD_GLOBAL             23 (NULL + small)
           1796 CACHE
           1798 LOAD_GLOBAL             55 (NULL + grey)
           1808 CACHE
           1810 LOAD_CONST              41 (', ')
           1812 STORE_SUBSCR
           1816 CACHE
           1818 CACHE
           1820 CACHE
           1822 CACHE
           1824 CACHE
           1826 CACHE
           1828 CACHE
           1830 CACHE
           1832 CACHE
           1834 LOAD_FAST               29 (dump)
           1836 UNPACK_SEQUENCE          1
           1840 CALL                     1
           1848 CACHE
           1850 UNPACK_SEQUENCE          1
           1854 CALL                     1
           1862 CACHE
           1864 UNPACK_SEQUENCE          1
           1868 CALL                     1
           1876 CACHE
           1878 BINARY_OP                6 (%)
           1882 UNPACK_SEQUENCE          1
           1886 CALL                     1
           1894 CACHE
           1896 POP_TOP

180        1898 LOAD_FAST                9 (frames)
           1900 STORE_SUBSCR
           1904 CACHE
           1906 CACHE
           1908 CACHE
           1910 CACHE
           1912 CACHE
           1914 CACHE
           1916 CACHE
           1918 CACHE
           1920 CACHE
           1922 LOAD_CONST              42 ('\n<table width="100%%" cellspacing=0 cellpadding=0 border=0>\n%s</table>')

182        1924 LOAD_CONST              43 ('\n')
           1926 STORE_SUBSCR
           1930 CACHE
           1932 CACHE
           1934 CACHE
           1936 CACHE
           1938 CACHE
           1940 CACHE
           1942 CACHE
           1944 CACHE
           1946 CACHE
           1948 LOAD_FAST               24 (rows)
           1950 UNPACK_SEQUENCE          1
           1954 CALL                     1
           1962 CACHE

180        1964 BINARY_OP                6 (%)
           1968 UNPACK_SEQUENCE          1
           1972 CALL                     1
           1980 CACHE
           1982 POP_TOP
           1984 EXTENDED_ARG             3
           1986 JUMP_BACKWARD          781 (to 426)

129        1988 LOAD_CONST              44 ('<p>')

184     >> 1990 LOAD_GLOBAL             43 (NULL + strong)
           2000 CACHE
           2002 LOAD_GLOBAL             34 (pydoc)
           2012 CACHE
           2014 LOAD_ATTR               18 (html_escape)
           2034 CACHE
           2036 CACHE
           2038 CACHE
           2040 CACHE
           2042 CACHE
           2044 CACHE
           2046 LOAD_GLOBAL             21 (NULL + str)
           2056 CACHE
           2058 LOAD_FAST                2 (etype)
           2060 UNPACK_SEQUENCE          1
           2064 CALL                     1
           2072 CACHE
           2074 UNPACK_SEQUENCE          1
           2078 CALL                     1
           2086 CACHE
           2088 UNPACK_SEQUENCE          1
           2092 CALL                     1
           2100 CACHE
           2102 FORMAT_VALUE             1 (str)
           2104 LOAD_CONST               3 (': ')

185        2106 LOAD_GLOBAL             34 (pydoc)
           2116 CACHE
           2118 LOAD_ATTR               18 (html_escape)
           2138 CACHE
           2140 CACHE
           2142 CACHE
           2144 CACHE
           2146 CACHE
           2148 CACHE
           2150 LOAD_GLOBAL             21 (NULL + str)
           2160 CACHE
           2162 LOAD_FAST                3 (evalue)
           2164 UNPACK_SEQUENCE          1
           2168 CALL                     1
           2176 CACHE
           2178 UNPACK_SEQUENCE          1
           2182 CALL                     1
           2190 CACHE
           2192 FORMAT_VALUE             1 (str)

184        2194 BUILD_STRING             4
           2196 BUILD_LIST               1
           2198 STORE_FAST              33 (exception)

186        2200 LOAD_GLOBAL             63 (NULL + dir)
           2210 CACHE
           2212 LOAD_FAST                3 (evalue)
           2214 UNPACK_SEQUENCE          1
           2218 CALL                     1
           2226 CACHE
           2228 GET_ITER
        >> 2230 FOR_ITER                91 (to 2416)

187        2234 LOAD_FAST               30 (name)
           2236 LOAD_CONST              25 (None)
           2238 LOAD_CONST              33 (1)
           2240 BUILD_SLICE              2
           2242 BINARY_SUBSCR
           2246 CACHE
           2248 CACHE
           2250 CACHE
           2252 LOAD_CONST              45 ('_')
           2254 COMPARE_OP               2 (<)
           2258 CACHE
           2260 POP_JUMP_IF_FALSE        1 (to 2264)
           2262 JUMP_BACKWARD           17 (to 2230)

188     >> 2264 LOAD_GLOBAL             34 (pydoc)
           2274 CACHE
           2276 LOAD_ATTR               18 (html_escape)
           2296 CACHE
           2298 CACHE
           2300 CACHE
           2302 CACHE
           2304 CACHE
           2306 CACHE
           2308 LOAD_GLOBAL             65 (NULL + getattr)
           2318 CACHE
           2320 LOAD_FAST                3 (evalue)
           2322 LOAD_FAST               30 (name)
           2324 UNPACK_SEQUENCE          2
           2328 CALL                     2
           2336 CACHE
           2338 UNPACK_SEQUENCE          1
           2342 CALL                     1
           2350 CACHE
           2352 STORE_FAST              32 (value)

189        2354 LOAD_FAST               33 (exception)
           2356 STORE_SUBSCR
           2360 CACHE
           2362 CACHE
           2364 CACHE
           2366 CACHE
           2368 CACHE
           2370 CACHE
           2372 CACHE
           2374 CACHE
           2376 CACHE
           2378 LOAD_CONST              46 ('\n<br>')
           2380 LOAD_FAST                8 (indent)
           2382 FORMAT_VALUE             1 (str)
           2384 LOAD_FAST               30 (name)
           2386 FORMAT_VALUE             1 (str)
           2388 LOAD_CONST              47 ('&nbsp;=\n')
           2390 LOAD_FAST               32 (value)
           2392 FORMAT_VALUE             1 (str)
           2394 BUILD_STRING             5
           2396 UNPACK_SEQUENCE          1
           2400 CALL                     1
           2408 CACHE
           2410 POP_TOP
           2412 JUMP_BACKWARD           92 (to 2230)

191        2414 LOAD_FAST                7 (head)
        >> 2416 LOAD_CONST              15 ('')
           2418 STORE_SUBSCR
           2422 CACHE
           2424 CACHE
           2426 CACHE
           2428 CACHE
           2430 CACHE
           2432 CACHE
           2434 CACHE
           2436 CACHE
           2438 CACHE
           2440 LOAD_FAST                9 (frames)
           2442 UNPACK_SEQUENCE          1
           2446 CALL                     1
           2454 CACHE
           2456 BINARY_OP                0 (+)
           2460 LOAD_CONST              15 ('')
           2462 STORE_SUBSCR
           2466 CACHE
           2468 CACHE
           2470 CACHE
           2472 CACHE
           2474 CACHE
           2476 CACHE
           2478 CACHE
           2480 CACHE
           2482 CACHE
           2484 LOAD_FAST               33 (exception)
           2486 UNPACK_SEQUENCE          1
           2490 CALL                     1
           2498 CACHE
           2500 BINARY_OP                0 (+)
           2504 LOAD_CONST              48 ("\n\n\n<!-- The above is a description of an error in a Python program, formatted\n     for a web browser because the 'cgitb' module was enabled.  In case you\n     are not reading this in a web browser, here is the original traceback:\n\n%s\n-->\n")

200        2506 LOAD_GLOBAL             34 (pydoc)
           2516 CACHE
           2518 LOAD_ATTR               18 (html_escape)
           2538 CACHE
           2540 CACHE
           2542 CACHE
           2544 CACHE
           2546 CACHE
           2548 CACHE

201        2550 LOAD_CONST              15 ('')
           2552 STORE_SUBSCR
           2556 CACHE
           2558 CACHE
           2560 CACHE
           2562 CACHE
           2564 CACHE
           2566 CACHE
           2568 CACHE
           2570 CACHE
           2572 CACHE
           2574 LOAD_GLOBAL             67 (NULL + traceback)
           2584 CACHE
           2586 LOAD_ATTR               34 (pydoc)
           2606 CALL                     3
           2614 CACHE
           2616 UNPACK_SEQUENCE          1
           2620 CALL                     1
           2628 CACHE

200        2630 UNPACK_SEQUENCE          1
           2634 CALL                     1
           2642 CACHE

191        2644 BINARY_OP                6 (%)
           2648 BINARY_OP                0 (+)
           2652 RETURN_VALUE

Disassembly of <code object <lambda> at 0x000001B2A7202890, file "cgitb.py", line 141>:
141           0 RESUME                   0
              2 LOAD_CONST               1 ('=')
              4 LOAD_GLOBAL              0 (pydoc)
             14 CACHE
             16 LOAD_ATTR                1 (NULL|self + pydoc)
             36 CACHE
             38 CACHE
             40 CACHE
             42 CACHE
             44 CACHE
             46 CACHE
             48 LOAD_FAST                0 (value)
             50 UNPACK_SEQUENCE          1
             54 CALL                     1
             62 CACHE
             64 BINARY_OP                0 (+)
             68 RETURN_VALUE

Disassembly of <code object reader at 0x000001B2A71D2130, file "cgitb.py", line 144>:
              0 COPY_FREE_VARS           2

144           2 RESUME                   0

145           4 LOAD_CONST               1 (1)
              6 LOAD_DEREF               2 (highlight)
              8 LOAD_FAST                0 (lnum)
             10 LOAD_CONST               2 (0)
             12 BINARY_SUBSCR
             16 CACHE
             18 CACHE
             20 CACHE
             22 STORE_SUBSCR

146          26 LOAD_GLOBAL              1 (NULL + linecache)
             36 CACHE
             38 LOAD_ATTR                1 (NULL|self + linecache)
             58 CACHE
             60 CACHE
             62 CACHE
             64 UNPACK_SEQUENCE          2
             68 CALL                     2
             76 CACHE

147          78 LOAD_FAST                0 (lnum)
             80 LOAD_CONST               2 (0)
             82 COPY                     2
             84 COPY                     2
             86 BINARY_SUBSCR
             90 CACHE
             92 CACHE
             94 CACHE
             96 LOAD_CONST               1 (1)
             98 BINARY_OP               13 (+=)
            102 SWAP                     3
            104 SWAP                     2
            106 STORE_SUBSCR
            110 RETURN_VALUE
        >>  112 PUSH_EXC_INFO
            114 LOAD_FAST                0 (lnum)
            116 LOAD_CONST               2 (0)
            118 COPY                     2
            120 COPY                     2
            122 BINARY_SUBSCR
            126 CACHE
            128 CACHE
            130 CACHE
            132 LOAD_CONST               1 (1)
            134 BINARY_OP               13 (+=)
            138 SWAP                     3
            140 SWAP                     2
            142 STORE_SUBSCR
            146 RERAISE                  0
        >>  148 COPY                     3
            150 POP_EXCEPT
            152 RERAISE                  1
ExceptionTable:
  26 to 76 -> 112 [0]
  112 to 146 -> 148 [1] lasti

Disassembly of <code object text at 0x000001B2A5079830, file "cgitb.py", line 203>:
              0 MAKE_CELL               32 (file)
              2 MAKE_CELL               33 (highlight)

203           4 RESUME                   0

205           6 LOAD_FAST                0 (einfo)
              8 UNPACK_SEQUENCE          3
             12 STORE_FAST               2 (etype)
             14 STORE_FAST               3 (evalue)
             16 STORE_FAST               4 (etb)

206          18 LOAD_GLOBAL              1 (NULL + isinstance)
             28 CACHE
             30 LOAD_FAST                2 (etype)
             32 LOAD_GLOBAL              2 (type)
             42 CACHE
             44 UNPACK_SEQUENCE          2
             48 CALL                     2
             56 CACHE
             58 POP_JUMP_IF_FALSE        7 (to 74)

207          60 LOAD_FAST                2 (etype)
             62 LOAD_ATTR                2 (type)
             82 CACHE
             84 CACHE
             86 CACHE
             88 LOAD_ATTR                4 (__name__)
            108 CACHE
            110 CACHE
            112 CACHE
            114 CACHE
            116 CACHE
            118 CACHE
            120 UNPACK_SEQUENCE          0
            124 CALL                     0
            132 CACHE
            134 LOAD_CONST               2 (0)
            136 BINARY_SUBSCR
            140 CACHE
            142 CACHE
            144 CACHE
            146 BINARY_OP                0 (+)
            150 LOAD_CONST               3 (': ')
            152 BINARY_OP                0 (+)
            156 LOAD_GLOBAL              6 (sys)
            166 CACHE
            168 LOAD_ATTR                6 (sys)
            188 CACHE
            190 CACHE
            192 CACHE
            194 CACHE
            196 LOAD_ATTR                8 (version)
            216 CACHE
            218 LOAD_ATTR                7 (NULL|self + sys)
            238 CACHE
            240 CACHE
            242 UNPACK_SEQUENCE          1
            246 CALL                     1
            254 CACHE
            256 STORE_FAST               6 (date)

210         258 LOAD_GLOBAL             19 (NULL + str)
            268 CACHE
            270 LOAD_FAST                2 (etype)
            272 UNPACK_SEQUENCE          1
            276 CALL                     1
            284 CACHE
            286 FORMAT_VALUE             1 (str)
            288 LOAD_CONST               4 ('\n')
            290 LOAD_FAST                5 (pyver)
            292 FORMAT_VALUE             1 (str)
            294 LOAD_CONST               4 ('\n')
            296 LOAD_FAST                6 (date)
            298 FORMAT_VALUE             1 (str)
            300 LOAD_CONST               4 ('\n')
            302 BUILD_STRING             6
            304 LOAD_CONST               5 ('\nA problem occurred in a Python script.  Here is the sequence of\nfunction calls leading up to the error, in the order they occurred.\n')
            306 BINARY_OP                0 (+)
            310 STORE_FAST               7 (head)

215         312 BUILD_LIST               0
            314 STORE_FAST               8 (frames)

216         316 LOAD_GLOBAL             21 (NULL + inspect)
            326 CACHE
            328 LOAD_ATTR               11 (NULL|self + split)
            348 CACHE
            350 CACHE
            352 CACHE
            354 CACHE
            356 STORE_FAST               9 (records)

217         358 LOAD_FAST                9 (records)
            360 GET_ITER
        >>  362 EXTENDED_ARG             1
            364 FOR_ITER               462 (to 1292)
            368 CACHE
            370 STORE_FAST              10 (frame)
            372 STORE_DEREF             32 (file)
            374 STORE_FAST              11 (lnum)
            376 STORE_FAST              12 (func)
            378 STORE_FAST              13 (lines)
            380 STORE_FAST              14 (index)

218         382 LOAD_DEREF              32 (file)
            384 POP_JUMP_IF_FALSE       31 (to 448)
            386 LOAD_GLOBAL             24 (os)
            396 CACHE
            398 LOAD_ATTR               13 (NULL|self + executable)
            418 CACHE
            420 CACHE
            422 CACHE
            424 CACHE
            426 CACHE
            428 CACHE
            430 LOAD_DEREF              32 (file)
            432 UNPACK_SEQUENCE          1
            436 CALL                     1
            444 CACHE
            446 LOAD_GLOBAL              1 (NULL + isinstance)
            456 CACHE
            458 CACHE
            460 CACHE
            462 CACHE
            464 LOAD_ATTR               15 (NULL|self + time)
            484 CACHE
            486 CACHE
            488 CACHE
            490 UNPACK_SEQUENCE          4
            494 STORE_FAST              15 (args)
            496 STORE_FAST              16 (varargs)
            498 STORE_FAST              17 (varkw)
            500 STORE_FAST              18 (locals)

220         502 LOAD_CONST               7 ('')
            504 STORE_FAST              19 (call)

221         506 LOAD_FAST               12 (func)
            508 LOAD_CONST               6 ('?')
            510 COMPARE_OP               3 (<)
            514 CACHE
            516 POP_JUMP_IF_FALSE       40 (to 598)

222         518 LOAD_CONST               8 ('in ')
            520 LOAD_FAST               12 (func)
            522 BINARY_OP                0 (+)
            526 STORE_FAST              19 (call)

223         528 LOAD_FAST               12 (func)
            530 LOAD_CONST               9 ('<module>')
            532 COMPARE_OP               3 (<)
            536 CACHE
            538 POP_JUMP_IF_FALSE       29 (to 598)

224         540 LOAD_FAST               19 (call)
            542 LOAD_GLOBAL             21 (NULL + inspect)
            552 CACHE
            554 LOAD_ATTR               16 (ctime)
            574 MAKE_FUNCTION            0

224         576 KW_NAMES                11 (('formatvalue',))
            578 UNPACK_SEQUENCE          5
            582 CALL                     5
            590 CACHE
            592 BINARY_OP               13 (+=)
            596 STORE_FAST              19 (call)

227     >>  598 BUILD_MAP                0
            600 STORE_DEREF             33 (highlight)

228         602 LOAD_FAST               11 (lnum)
            604 BUILD_LIST               1
            606 BUILD_TUPLE              1
            608 LOAD_CLOSURE            32 (file)
            610 LOAD_CLOSURE            33 (highlight)
            612 BUILD_TUPLE              2
            614 LOAD_CONST              12 (<code object reader at 0x000001B2A71D2D90, file "cgitb.py", line 228>)
            616 MAKE_FUNCTION            9 (defaults, closure)
            618 STORE_FAST              20 (reader)

232         620 LOAD_GLOBAL             35 (NULL + scanvars)
            630 CACHE
            632 LOAD_FAST               20 (reader)
            634 LOAD_FAST               10 (frame)
            636 LOAD_FAST               18 (locals)
            638 UNPACK_SEQUENCE          3
            642 CALL                     3
            650 CACHE
            652 STORE_FAST              21 (vars)
            654 LOAD_CONST              13 (' ')

234         656 LOAD_DEREF              32 (file)
            658 FORMAT_VALUE             1 (str)
            660 LOAD_CONST              13 (' ')
            662 LOAD_FAST               19 (call)
            664 FORMAT_VALUE             1 (str)
            666 BUILD_STRING             4
            668 BUILD_LIST               1
            670 STORE_FAST              22 (rows)

235         672 LOAD_FAST               14 (index)
            674 POP_JUMP_IF_NONE        62 (to 800)

236         676 LOAD_FAST               11 (lnum)
            678 LOAD_FAST               14 (index)
            680 BINARY_OP               10 (-)
            684 STORE_FAST              23 (i)

237         686 LOAD_FAST               13 (lines)
            688 GET_ITER
        >>  690 FOR_ITER                54 (to 802)

238         694 LOAD_CONST              15 ('%5d ')
            696 LOAD_FAST               23 (i)
            698 BINARY_OP                6 (%)
            702 STORE_FAST              25 (num)

239         704 LOAD_FAST               22 (rows)
            706 STORE_SUBSCR
            710 CACHE
            712 CACHE
            714 CACHE
            716 CACHE
            718 CACHE
            720 CACHE
            722 CACHE
            724 CACHE
            726 CACHE
            728 LOAD_FAST               25 (num)
            730 LOAD_FAST               24 (line)
            732 STORE_SUBSCR
            736 CACHE
            738 CACHE
            740 CACHE
            742 CACHE
            744 CACHE
            746 CACHE
            748 CACHE
            750 CACHE
            752 CACHE
            754 UNPACK_SEQUENCE          0
            758 CALL                     0
            766 CACHE
            768 BINARY_OP                0 (+)
            772 UNPACK_SEQUENCE          1
            776 CALL                     1
            784 CACHE
            786 POP_TOP

240         788 LOAD_FAST               23 (i)
            790 LOAD_CONST              16 (1)
            792 BINARY_OP               13 (+=)
            796 STORE_FAST              23 (i)
            798 JUMP_BACKWARD           55 (to 690)

242     >>  800 BUILD_MAP                0
        >>  802 BUILD_LIST               0
            804 STORE_FAST              27 (dump)
            806 STORE_FAST              26 (done)

243         808 LOAD_FAST               21 (vars)
            810 GET_ITER
        >>  812 FOR_ITER               153 (to 1122)
            816 CACHE
            818 STORE_FAST              28 (name)
            820 STORE_FAST              29 (where)
            822 STORE_FAST              30 (value)

244         824 LOAD_FAST               28 (name)
            826 LOAD_FAST               26 (done)
            828 CONTAINS_OP              0
            830 POP_JUMP_IF_FALSE        1 (to 834)
            832 JUMP_BACKWARD           11 (to 812)

245     >>  834 LOAD_CONST              16 (1)
            836 LOAD_FAST               26 (done)
            838 LOAD_FAST               28 (name)
            840 STORE_SUBSCR

246         844 LOAD_FAST               30 (value)
            846 LOAD_GLOBAL             40 (__UNDEF__)
            856 CACHE
            858 IS_OP                    1
            860 POP_JUMP_IF_FALSE      104 (to 1070)

247         862 LOAD_FAST               29 (where)
            864 LOAD_CONST              17 ('global')
            866 COMPARE_OP               2 (<)
            870 CACHE
            872 POP_JUMP_IF_FALSE        6 (to 886)
            874 LOAD_CONST              18 ('global ')
            876 LOAD_FAST               28 (name)
            878 BINARY_OP                0 (+)
            882 STORE_FAST              28 (name)
            884 JUMP_FORWARD            36 (to 958)

248     >>  886 LOAD_FAST               29 (where)
            888 LOAD_CONST              19 ('local')
            890 COMPARE_OP               3 (<)
            894 CACHE
            896 POP_JUMP_IF_FALSE       30 (to 958)
            898 LOAD_FAST               29 (where)
            900 LOAD_FAST               28 (name)
            902 STORE_SUBSCR
            906 CACHE
            908 CACHE
            910 CACHE
            912 CACHE
            914 CACHE
            916 CACHE
            918 CACHE
            920 CACHE
            922 CACHE
            924 LOAD_CONST              20 ('.')
            926 UNPACK_SEQUENCE          1
            930 CALL                     1
            938 CACHE
            940 LOAD_CONST              21 (-1)
            942 BINARY_SUBSCR
            946 CACHE
            948 CACHE
            950 CACHE
            952 BINARY_OP                0 (+)
            956 STORE_FAST              28 (name)

249     >>  958 LOAD_FAST               27 (dump)
            960 STORE_SUBSCR
            964 CACHE
            966 CACHE
            968 CACHE
            970 CACHE
            972 CACHE
            974 CACHE
            976 CACHE
            978 CACHE
            980 CACHE
            982 LOAD_FAST               28 (name)
            984 FORMAT_VALUE             1 (str)
            986 LOAD_CONST              22 (' = ')
            988 LOAD_GLOBAL             42 (pydoc)
            998 CACHE
           1000 LOAD_ATTR               22 (getinnerframes)
           1020 CACHE
           1022 CACHE
           1024 CACHE
           1026 CACHE
           1028 CACHE
           1030 CACHE
           1032 LOAD_FAST               30 (value)
           1034 UNPACK_SEQUENCE          1
           1038 CALL                     1
           1046 CACHE
           1048 FORMAT_VALUE             1 (str)
           1050 BUILD_STRING             3
           1052 UNPACK_SEQUENCE          1
           1056 CALL                     1
           1064 CACHE
           1066 POP_TOP
           1068 JUMP_BACKWARD          129 (to 812)

251     >> 1070 LOAD_FAST               27 (dump)
           1072 STORE_SUBSCR
           1076 CACHE
           1078 CACHE
           1080 CACHE
           1082 CACHE
           1084 CACHE
           1086 CACHE
           1088 CACHE
           1090 CACHE
           1092 CACHE
           1094 LOAD_FAST               28 (name)
           1096 LOAD_CONST              23 (' undefined')
           1098 BINARY_OP                0 (+)
           1102 UNPACK_SEQUENCE          1
           1106 CALL                     1
           1114 CACHE
           1116 POP_TOP
           1118 JUMP_BACKWARD          154 (to 812)

253        1120 LOAD_FAST               22 (rows)
        >> 1122 STORE_SUBSCR
           1126 CACHE
           1128 CACHE
           1130 CACHE
           1132 CACHE
           1134 CACHE
           1136 CACHE
           1138 CACHE
           1140 CACHE
           1142 CACHE
           1144 LOAD_CONST               4 ('\n')
           1146 STORE_SUBSCR
           1150 CACHE
           1152 CACHE
           1154 CACHE
           1156 CACHE
           1158 CACHE
           1160 CACHE
           1162 CACHE
           1164 CACHE
           1166 CACHE
           1168 LOAD_FAST               27 (dump)
           1170 UNPACK_SEQUENCE          1
           1174 CALL                     1
           1182 CACHE
           1184 UNPACK_SEQUENCE          1
           1188 CALL                     1
           1196 CACHE
           1198 POP_TOP

254        1200 LOAD_FAST                8 (frames)
           1202 STORE_SUBSCR
           1206 CACHE
           1208 CACHE
           1210 CACHE
           1212 CACHE
           1214 CACHE
           1216 CACHE
           1218 CACHE
           1220 CACHE
           1222 CACHE
           1224 LOAD_CONST              24 ('\n%s\n')
           1226 LOAD_CONST               4 ('\n')
           1228 STORE_SUBSCR
           1232 CACHE
           1234 CACHE
           1236 CACHE
           1238 CACHE
           1240 CACHE
           1242 CACHE
           1244 CACHE
           1246 CACHE
           1248 CACHE
           1250 LOAD_FAST               22 (rows)
           1252 UNPACK_SEQUENCE          1
           1256 CALL                     1
           1264 CACHE
           1266 BINARY_OP                6 (%)
           1270 UNPACK_SEQUENCE          1
           1274 CALL                     1
           1282 CACHE
           1284 POP_TOP
           1286 EXTENDED_ARG             1
           1288 JUMP_BACKWARD          464 (to 362)

256        1290 LOAD_GLOBAL             19 (NULL + str)
           1300 CACHE
           1302 LOAD_FAST                2 (etype)
           1304 UNPACK_SEQUENCE          1
           1308 CALL                     1
           1316 CACHE
           1318 FORMAT_VALUE             1 (str)
           1320 LOAD_CONST               3 (': ')
           1322 LOAD_GLOBAL             19 (NULL + str)
           1332 CACHE
           1334 LOAD_FAST                3 (evalue)
           1336 UNPACK_SEQUENCE          1
           1340 CALL                     1
           1348 CACHE
           1350 FORMAT_VALUE             1 (str)
           1352 BUILD_STRING             3
           1354 BUILD_LIST               1
           1356 STORE_FAST              31 (exception)

257        1358 LOAD_GLOBAL             51 (NULL + dir)
           1368 CACHE
           1370 LOAD_FAST                3 (evalue)
           1372 UNPACK_SEQUENCE          1
           1376 CALL                     1
           1384 CACHE
           1386 GET_ITER
        >> 1388 FOR_ITER                76 (to 1544)

258        1392 LOAD_GLOBAL             42 (pydoc)
           1402 CACHE
           1404 LOAD_ATTR               22 (getinnerframes)
           1424 CACHE
           1426 CACHE
           1428 CACHE
           1430 CACHE
           1432 CACHE
           1434 CACHE
           1436 LOAD_GLOBAL             53 (NULL + getattr)
           1446 CACHE
           1448 LOAD_FAST                3 (evalue)
           1450 LOAD_FAST               28 (name)
           1452 UNPACK_SEQUENCE          2
           1456 CALL                     2
           1464 CACHE
           1466 UNPACK_SEQUENCE          1
           1470 CALL                     1
           1478 CACHE
           1480 STORE_FAST              30 (value)

259        1482 LOAD_FAST               31 (exception)
           1484 STORE_SUBSCR
           1488 CACHE
           1490 CACHE
           1492 CACHE
           1494 CACHE
           1496 CACHE
           1498 CACHE
           1500 CACHE
           1502 CACHE
           1504 CACHE
           1506 LOAD_CONST               4 ('\n')
           1508 LOAD_CONST              25 ('    ')
           1510 FORMAT_VALUE             1 (str)
           1512 LOAD_FAST               28 (name)
           1514 FORMAT_VALUE             1 (str)
           1516 LOAD_CONST              22 (' = ')
           1518 LOAD_FAST               30 (value)
           1520 FORMAT_VALUE             1 (str)
           1522 BUILD_STRING             5
           1524 UNPACK_SEQUENCE          1
           1528 CALL                     1
           1536 CACHE
           1538 POP_TOP
           1540 JUMP_BACKWARD           77 (to 1388)

261        1542 LOAD_FAST                7 (head)
        >> 1544 LOAD_CONST               7 ('')
           1546 STORE_SUBSCR
           1550 CACHE
           1552 CACHE
           1554 CACHE
           1556 CACHE
           1558 CACHE
           1560 CACHE
           1562 CACHE
           1564 CACHE
           1566 CACHE
           1568 LOAD_FAST                8 (frames)
           1570 UNPACK_SEQUENCE          1
           1574 CALL                     1
           1582 CACHE
           1584 BINARY_OP                0 (+)
           1588 LOAD_CONST               7 ('')
           1590 STORE_SUBSCR
           1594 CACHE
           1596 CACHE
           1598 CACHE
           1600 CACHE
           1602 CACHE
           1604 CACHE
           1606 CACHE
           1608 CACHE
           1610 CACHE
           1612 LOAD_FAST               31 (exception)
           1614 UNPACK_SEQUENCE          1
           1618 CALL                     1
           1626 CACHE
           1628 BINARY_OP                0 (+)
           1632 LOAD_CONST              26 ('\n\nThe above is a description of an error in a Python program.  Here is\nthe original traceback:\n\n%s\n')

267        1634 LOAD_CONST               7 ('')
           1636 STORE_SUBSCR
           1640 CACHE
           1642 CACHE
           1644 CACHE
           1646 CACHE
           1648 CACHE
           1650 CACHE
           1652 CACHE
           1654 CACHE
           1656 CACHE
           1658 LOAD_GLOBAL             55 (NULL + traceback)
           1668 CACHE
           1670 LOAD_ATTR               28 (abspath)
           1690 CALL                     3
           1698 CACHE
           1700 UNPACK_SEQUENCE          1
           1704 CALL                     1
           1712 CACHE

261        1714 BINARY_OP                6 (%)
           1718 BINARY_OP                0 (+)
           1722 RETURN_VALUE

Disassembly of <code object <lambda> at 0x000001B2A7202780, file "cgitb.py", line 225>:
225           0 RESUME                   0
              2 LOAD_CONST               1 ('=')
              4 LOAD_GLOBAL              0 (pydoc)
             14 CACHE
             16 LOAD_ATTR                1 (NULL|self + pydoc)
             36 CACHE
             38 CACHE
             40 CACHE
             42 CACHE
             44 CACHE
             46 CACHE
             48 LOAD_FAST                0 (value)
             50 UNPACK_SEQUENCE          1
             54 CALL                     1
             62 CACHE
             64 BINARY_OP                0 (+)
             68 RETURN_VALUE

Disassembly of <code object reader at 0x000001B2A71D2D90, file "cgitb.py", line 228>:
              0 COPY_FREE_VARS           2

228           2 RESUME                   0

229           4 LOAD_CONST               1 (1)
              6 LOAD_DEREF               2 (highlight)
              8 LOAD_FAST                0 (lnum)
             10 LOAD_CONST               2 (0)
             12 BINARY_SUBSCR
             16 CACHE
             18 CACHE
             20 CACHE
             22 STORE_SUBSCR

230          26 LOAD_GLOBAL              1 (NULL + linecache)
             36 CACHE
             38 LOAD_ATTR                1 (NULL|self + linecache)
             58 CACHE
             60 CACHE
             62 CACHE
             64 UNPACK_SEQUENCE          2
             68 CALL                     2
             76 CACHE

231          78 LOAD_FAST                0 (lnum)
             80 LOAD_CONST               2 (0)
             82 COPY                     2
             84 COPY                     2
             86 BINARY_SUBSCR
             90 CACHE
             92 CACHE
             94 CACHE
             96 LOAD_CONST               1 (1)
             98 BINARY_OP               13 (+=)
            102 SWAP                     3
            104 SWAP                     2
            106 STORE_SUBSCR
            110 RETURN_VALUE
        >>  112 PUSH_EXC_INFO
            114 LOAD_FAST                0 (lnum)
            116 LOAD_CONST               2 (0)
            118 COPY                     2
            120 COPY                     2
            122 BINARY_SUBSCR
            126 CACHE
            128 CACHE
            130 CACHE
            132 LOAD_CONST               1 (1)
            134 BINARY_OP               13 (+=)
            138 SWAP                     3
            140 SWAP                     2
            142 STORE_SUBSCR
            146 RERAISE                  0
        >>  148 COPY                     3
            150 POP_EXCEPT
            152 RERAISE                  1
ExceptionTable:
  26 to 76 -> 112 [0]
  112 to 146 -> 148 [1] lasti

Disassembly of <code object Hook at 0x000001B2A71D56B0, file "cgitb.py", line 269>:
269           0 RESUME                   0
              2 LOAD_NAME                0 (__name__)
              4 STORE_NAME               1 (__module__)
              6 LOAD_CONST               0 ('Hook')
              8 STORE_NAME               2 (__qualname__)

270          10 LOAD_CONST               1 ('A hook to replace sys.excepthook that shows tracebacks in HTML.')
             12 STORE_NAME               3 (__doc__)

272          14 NOP

273          16 NOP

272          18 LOAD_CONST               9 ((1, None, 5, None, 'html'))
             20 LOAD_CONST               6 (<code object __init__ at 0x000001B2A725EE80, file "cgitb.py", line 272>)
             22 MAKE_FUNCTION            1 (defaults)
             24 STORE_NAME               4 (__init__)

280          26 LOAD_CONST               7 (<code object __call__ at 0x000001B2A71C2630, file "cgitb.py", line 280>)
             28 MAKE_FUNCTION            0
             30 STORE_NAME               5 (__call__)

283          32 LOAD_CONST              10 ((None,))
             34 LOAD_CONST               8 (<code object handle at 0x000001B2A75CD340, file "cgitb.py", line 283>)
             36 MAKE_FUNCTION            1 (defaults)
             38 STORE_NAME               6 (handle)
             40 LOAD_CONST               3 (None)
             42 RETURN_VALUE

Disassembly of <code object __init__ at 0x000001B2A725EE80, file "cgitb.py", line 272>:
272           0 RESUME                   0

274           2 LOAD_FAST                1 (display)
              4 LOAD_FAST                0 (self)
              6 STORE_ATTR               0 (display)

275          16 LOAD_FAST                2 (logdir)
             18 LOAD_FAST                0 (self)
             20 STORE_ATTR               1 (logdir)

276          30 LOAD_FAST                3 (context)
             32 LOAD_FAST                0 (self)
             34 STORE_ATTR               2 (context)

277          44 LOAD_FAST                4 (file)
             46 LOAD_GLOBAL             11 (NULL + file)
             56 CACHE
             58 CACHE
             60 LOAD_ATTR                4 (context)
             80 CACHE

278          82 LOAD_FAST                5 (format)
             84 LOAD_FAST                0 (self)
             86 STORE_ATTR               6 (format)
             96 LOAD_CONST               0 (None)
             98 RETURN_VALUE

Disassembly of <code object __call__ at 0x000001B2A71C2630, file "cgitb.py", line 280>:
280           0 RESUME                   0

281           2 LOAD_FAST                0 (self)
              4 STORE_SUBSCR
              8 CACHE
             10 CACHE
             12 CACHE
             14 CACHE
             16 CACHE
             18 CACHE
             20 CACHE
             22 CACHE
             24 CACHE
             26 LOAD_FAST                1 (etype)
             28 LOAD_FAST                2 (evalue)
             30 LOAD_FAST                3 (etb)
             32 BUILD_TUPLE              3
             34 UNPACK_SEQUENCE          1
             38 CALL                     1
             46 CACHE
             48 POP_TOP
             50 LOAD_CONST               0 (None)
             52 RETURN_VALUE

Disassembly of <code object handle at 0x000001B2A75CD340, file "cgitb.py", line 283>:
283           0 RESUME                   0

284           2 LOAD_FAST                1 (info)
              4 LOAD_GLOBAL             18 (join)
             14 CACHE
             16 CACHE
             18 LOAD_ATTR                1 (NULL|self + sys)
             38 CACHE
             40 CACHE
             42 STORE_FAST               1 (info)

285          44 LOAD_FAST                0 (self)
             46 LOAD_ATTR                2 (exc_info)

286          66 LOAD_FAST                0 (self)
             68 LOAD_ATTR                3 (NULL|self + exc_info)
             88 CACHE
             90 CACHE
             92 CACHE
             94 CACHE
             96 CACHE
             98 CACHE
            100 LOAD_GLOBAL             11 (NULL + reset)
            110 CACHE
            112 UNPACK_SEQUENCE          0
            116 CALL                     0
            124 CACHE
            126 UNPACK_SEQUENCE          1
            130 CALL                     1
            138 CACHE
            140 POP_TOP

288         142 LOAD_FAST                0 (self)
            144 LOAD_ATTR                2 (exc_info)
            164 LOAD_GLOBAL             12 (html)
            174 CACHE
            176 LOAD_GLOBAL              6 (file)
            186 CACHE
            188 CACHE
            190 STORE_FAST               2 (formatter)

289         192 LOAD_CONST               2 (False)
            194 STORE_FAST               3 (plain)

290         196 NOP

291         198 PUSH_NULL
            200 LOAD_FAST                2 (formatter)
            202 LOAD_FAST                1 (info)
            204 LOAD_FAST                0 (self)
            206 LOAD_ATTR                8 (write)
            226 CACHE
            228 CACHE
            230 STORE_FAST               4 (doc)
            232 JUMP_FORWARD            42 (to 318)
        >>  234 PUSH_EXC_INFO

292         236 POP_TOP

293         238 LOAD_CONST               3 ('')
            240 STORE_SUBSCR
            244 CACHE
            246 CACHE
            248 CACHE
            250 CACHE
            252 CACHE
            254 CACHE
            256 CACHE
            258 CACHE
            260 CACHE
            262 LOAD_GLOBAL             21 (NULL + traceback)
            272 CACHE
            274 LOAD_ATTR               11 (NULL|self + reset)
            294 CACHE
            296 CACHE
            298 CACHE
            300 CACHE
            302 STORE_FAST               4 (doc)

294         304 LOAD_CONST               4 (True)
            306 STORE_FAST               3 (plain)
            308 POP_EXCEPT
            310 JUMP_FORWARD             3 (to 318)
        >>  312 COPY                     3
            314 POP_EXCEPT
            316 RERAISE                  1

296     >>  318 LOAD_FAST                0 (self)
            320 LOAD_ATTR               12 (html)
            340 CACHE
            342 CACHE
            344 CACHE
            346 CACHE
            348 LOAD_ATTR                6 (file)
            368 CACHE
            370 CACHE
            372 CACHE
            374 CACHE
            376 CACHE
            378 CACHE
            380 LOAD_FAST                4 (doc)
            382 UNPACK_SEQUENCE          1
            386 CALL                     1
            394 CACHE
            396 STORE_FAST               4 (doc)

299         398 LOAD_FAST                0 (self)
            400 LOAD_ATTR                3 (NULL|self + exc_info)
            420 CACHE
            422 CACHE
            424 CACHE
            426 CACHE
            428 CACHE
            430 CACHE
            432 LOAD_CONST               5 ('<pre>')
            434 LOAD_FAST                4 (doc)
            436 BINARY_OP                0 (+)
            440 LOAD_CONST               6 ('</pre>\n')
            442 BINARY_OP                0 (+)
            446 UNPACK_SEQUENCE          1
            450 CALL                     1
            458 CACHE
            460 POP_TOP
            462 JUMP_FORWARD            56 (to 576)

301         464 LOAD_FAST                0 (self)
            466 LOAD_ATTR                3 (NULL|self + exc_info)
            486 CACHE
            488 CACHE
            490 CACHE
            492 CACHE
            494 CACHE
            496 CACHE
            498 LOAD_FAST                4 (doc)
            500 LOAD_CONST               7 ('\n')
            502 BINARY_OP                0 (+)
            506 UNPACK_SEQUENCE          1
            510 CALL                     1
            518 CACHE
            520 POP_TOP
            522 JUMP_FORWARD            26 (to 576)

303         524 LOAD_FAST                0 (self)
            526 LOAD_ATTR                3 (NULL|self + exc_info)
            546 CACHE
            548 CACHE
            550 CACHE
            552 CACHE
            554 CACHE
            556 CACHE
            558 LOAD_CONST               8 ('<p>A problem occurred in a Python script.\n')
            560 UNPACK_SEQUENCE          1
            564 CALL                     1
            572 CACHE
            574 POP_TOP

305     >>  576 LOAD_FAST                0 (self)
            578 LOAD_ATTR               15 (NULL|self + text)
            598 LOAD_ATTR                2 (exc_info)
            618 CACHE
            620 CACHE
            622 CACHE
            624 CACHE
            626 STORE_FAST               5 (suffix)

307         628 LOAD_GLOBAL             33 (NULL + tempfile)
            638 CACHE
            640 LOAD_ATTR               17 (NULL|self + context)
            660 CACHE
            662 CACHE
            664 KW_NAMES                11 (('suffix', 'dir'))
            666 UNPACK_SEQUENCE          2
            670 CALL                     2
            678 CACHE
            680 UNPACK_SEQUENCE          2
            684 STORE_FAST               6 (fd)
            686 STORE_FAST               7 (path)

309         688 NOP

310         690 LOAD_GLOBAL             37 (NULL + os)
            700 CACHE
            702 LOAD_ATTR               19 (NULL|self + join)
            722 CACHE
            724 CACHE
            726 CACHE
            728 CACHE
            730 BEFORE_WITH
            732 STORE_FAST               8 (file)

311         734 LOAD_FAST                8 (file)
            736 STORE_SUBSCR
            740 CACHE
            742 CACHE
            744 CACHE
            746 CACHE
            748 CACHE
            750 CACHE
            752 CACHE
            754 CACHE
            756 CACHE
            758 LOAD_FAST                4 (doc)
            760 UNPACK_SEQUENCE          1
            764 CALL                     1
            772 CACHE
            774 POP_TOP

310         776 LOAD_CONST               0 (None)
            778 LOAD_CONST               0 (None)
            780 LOAD_CONST               0 (None)
            782 UNPACK_SEQUENCE          2
            786 CALL                     2
            794 CACHE
            796 POP_TOP
            798 JUMP_FORWARD            11 (to 822)
        >>  800 PUSH_EXC_INFO
            802 WITH_EXCEPT_START
            804 POP_JUMP_IF_TRUE         4 (to 814)
            806 RERAISE                  2
        >>  808 COPY                     3
            810 POP_EXCEPT
            812 RERAISE                  1
        >>  814 POP_TOP
            816 POP_EXCEPT
            818 POP_TOP
            820 POP_TOP

312     >>  822 LOAD_CONST              13 ('%s contains the description of this error.')
            824 LOAD_FAST                7 (path)
            826 BINARY_OP                6 (%)
            830 STORE_FAST               9 (msg)
            832 JUMP_FORWARD            12 (to 858)
        >>  834 PUSH_EXC_INFO

313         836 POP_TOP

314         838 LOAD_CONST              14 ('Tried to save traceback to %s, but failed.')
            840 LOAD_FAST                7 (path)
            842 BINARY_OP                6 (%)
            846 STORE_FAST               9 (msg)
            848 POP_EXCEPT
            850 JUMP_FORWARD             3 (to 858)
        >>  852 COPY                     3
            854 POP_EXCEPT
            856 RERAISE                  1

316     >>  858 LOAD_FAST                0 (self)
            860 LOAD_ATTR                2 (exc_info)

317         880 LOAD_FAST                0 (self)
            882 LOAD_ATTR                3 (NULL|self + exc_info)
            902 CACHE
            904 CACHE
            906 CACHE
            908 CACHE
            910 CACHE
            912 CACHE
            914 LOAD_CONST              15 ('<p>%s</p>\n')
            916 LOAD_FAST                9 (msg)
            918 BINARY_OP                6 (%)
            922 UNPACK_SEQUENCE          1
            926 CALL                     1
            934 CACHE
            936 POP_TOP
            938 JUMP_FORWARD            29 (to 998)

319         940 LOAD_FAST                0 (self)
            942 LOAD_ATTR                3 (NULL|self + exc_info)
            962 CACHE
            964 CACHE
            966 CACHE
            968 CACHE
            970 CACHE
            972 CACHE
            974 LOAD_FAST                9 (msg)
            976 LOAD_CONST               7 ('\n')
            978 BINARY_OP                0 (+)
            982 UNPACK_SEQUENCE          1
            986 CALL                     1
            994 CACHE
            996 POP_TOP

320     >>  998 NOP

321        1000 LOAD_FAST                0 (self)
           1002 LOAD_ATTR                3 (NULL|self + exc_info)
           1022 CACHE
           1024 CACHE
           1026 CACHE
           1028 CACHE
           1030 CACHE
           1032 CACHE
           1034 UNPACK_SEQUENCE          0
           1038 CALL                     0
           1046 CACHE
           1048 POP_TOP
           1050 LOAD_CONST               0 (None)
           1052 RETURN_VALUE
        >> 1054 PUSH_EXC_INFO

322        1056 POP_TOP
           1058 POP_EXCEPT
           1060 LOAD_CONST               0 (None)
           1062 RETURN_VALUE
        >> 1064 COPY                     3
           1066 POP_EXCEPT
           1068 RERAISE                  1
ExceptionTable:
  198 to 230 -> 234 [0]
  234 to 306 -> 312 [1] lasti
  690 to 730 -> 834 [0]
  732 to 774 -> 800 [1] lasti
  776 to 798 -> 834 [0]
  800 to 806 -> 808 [3] lasti
  808 to 812 -> 834 [0]
  814 to 814 -> 808 [3] lasti
  816 to 830 -> 834 [0]
  834 to 846 -> 852 [1] lasti
  1000 to 1048 -> 1054 [0]
  1054 to 1056 -> 1064 [1] lasti

Disassembly of <code object enable at 0x000001B2A7672630, file "cgitb.py", line 325>:
325           0 RESUME                   0

331           2 LOAD_GLOBAL              1 (NULL + Hook)
             12 CACHE
             14 LOAD_FAST                0 (display)
             16 LOAD_FAST                1 (logdir)

332          18 LOAD_FAST                2 (context)
             20 LOAD_FAST                3 (format)

331          22 KW_NAMES                 1 (('display', 'logdir', 'context', 'format'))
             24 UNPACK_SEQUENCE          4
             28 CALL                     4
             36 CACHE
             38 LOAD_GLOBAL              2 (sys)
             48 CACHE
             50 STORE_ATTR               2 (excepthook)
             60 LOAD_CONST               2 (None)
             62 RETURN_VALUE


# Constants:
# 0: <string length 922>
# 1: 0
# 2: None
# 3: tuple
# 4: tuple
# 5: tuple
# 6: <code object reset>
# 7: <code object small>
# 8: <code object strong>
# 9: <code object grey>
# 10: <code object lookup>
# 11: <code object scanvars>
# 12: 5
# 13: <code object html>
# 14: <code object text>
# 15: <code object Hook>
# 16: 'Hook'
# 17: 1
# 18: 'html'
# 19: <code object enable>
# 20: tuple
# 21: tuple


# Names:
# 0: '__doc__'
# 1: 'inspect'
# 2: 'keyword'
# 3: 'linecache'
# 4: 'os'
# 5: 'pydoc'
# 6: 'sys'
# 7: 'tempfile'
# 8: 'time'
# 9: 'tokenize'
# 10: 'traceback'
# 11: 'warnings'
# 12: 'html'
# 13: 'escape'
# 14: 'html_escape'
# 15: '_deprecated'
# 16: '__name__'
# 17: 'reset'
# 18: '__UNDEF__'
# 19: 'small'
# 20: 'strong'
# 21: 'grey'
# 22: 'lookup'
# 23: 'scanvars'
# 24: 'text'
# 25: 'Hook'
# 26: 'handle'
# 27: 'handler'
# 28: 'enable'


# Variable names:
