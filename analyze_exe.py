#!/usr/bin/env python3
"""
Advanced PyInstaller executable analyzer
"""
import struct
import os
import sys

def analyze_exe(filename):
    """Analyze a potential PyInstaller executable"""
    print(f"Analyzing {filename}")
    
    with open(filename, 'rb') as f:
        # Get file size
        f.seek(0, 2)
        file_size = f.tell()
        print(f"File size: {file_size} bytes")
        
        # Read PE header to understand the structure
        f.seek(0)
        dos_header = f.read(64)
        if dos_header[:2] != b'MZ':
            print("Not a valid PE file")
            return
            
        # Get PE header offset
        pe_offset = struct.unpack('<I', dos_header[60:64])[0]
        f.seek(pe_offset)
        pe_signature = f.read(4)
        if pe_signature != b'PE\x00\x00':
            print("Invalid PE signature")
            return
            
        print("Valid PE file detected")
        
        # Search for PyInstaller patterns throughout the file
        patterns_to_search = [
            b'MEI\x0c\x0b\x08\x0b\x0e',  # PyInstaller magic
            b'PYZ-',
            b'pyiboot',
            b'pyi-',
            b'python',
            b'.pyc',
            b'__pycache__',
            b'PYTHONPATH',
            b'sys.path',
            b'importlib',
        ]
        
        # Search in chunks to handle large files
        chunk_size = 1024 * 1024  # 1MB chunks
        overlap = 1024  # Overlap to catch patterns across chunk boundaries
        
        found_patterns = {}
        
        f.seek(0)
        position = 0
        
        while position < file_size:
            # Read chunk with overlap
            f.seek(position)
            chunk = f.read(chunk_size + overlap)
            if not chunk:
                break
                
            # Search for patterns in this chunk
            for pattern in patterns_to_search:
                offset = 0
                while True:
                    pos = chunk.find(pattern, offset)
                    if pos == -1:
                        break
                    
                    absolute_pos = position + pos
                    if pattern not in found_patterns:
                        found_patterns[pattern] = []
                    found_patterns[pattern].append(absolute_pos)
                    offset = pos + 1
            
            position += chunk_size
            
        # Report findings
        print("\nFound patterns:")
        for pattern, positions in found_patterns.items():
            print(f"  {pattern}: {len(positions)} occurrences")
            for pos in positions[:5]:  # Show first 5 occurrences
                print(f"    Position: {pos} (0x{pos:x})")
                
        # Try to find the overlay (data appended after PE)
        print("\nSearching for overlay data...")
        
        # Parse PE sections to find where the PE data ends
        f.seek(pe_offset + 4)  # Skip PE signature
        coff_header = f.read(20)
        num_sections = struct.unpack('<H', coff_header[2:4])[0]
        optional_header_size = struct.unpack('<H', coff_header[16:18])[0]
        
        # Skip optional header
        f.seek(pe_offset + 24 + optional_header_size)
        
        # Read section headers
        max_section_end = 0
        for i in range(num_sections):
            section_header = f.read(40)
            if len(section_header) < 40:
                break
                
            raw_size = struct.unpack('<I', section_header[16:20])[0]
            raw_addr = struct.unpack('<I', section_header[20:24])[0]
            section_end = raw_addr + raw_size
            max_section_end = max(max_section_end, section_end)
            
        print(f"PE sections end at: {max_section_end} (0x{max_section_end:x})")
        
        if max_section_end < file_size:
            overlay_size = file_size - max_section_end
            print(f"Overlay detected: {overlay_size} bytes starting at {max_section_end}")
            
            # Analyze overlay
            f.seek(max_section_end)
            overlay_start = f.read(1024)  # Read first 1KB of overlay
            
            print("Overlay starts with:")
            print(f"  Hex: {overlay_start[:64].hex()}")
            print(f"  ASCII: {''.join(chr(b) if 32 <= b <= 126 else '.' for b in overlay_start[:64])}")
            
            # Look for PyInstaller cookie at the end
            cookie_search_size = min(1024, overlay_size)
            f.seek(file_size - cookie_search_size)
            end_data = f.read()
            
            print(f"\nLast {len(end_data)} bytes:")
            print(f"  Hex: {end_data[-64:].hex()}")
            print(f"  ASCII: {''.join(chr(b) if 32 <= b <= 126 else '.' for b in end_data[-64:])}")
            
            # Try to find PyInstaller cookie pattern
            # PyInstaller typically stores metadata at the end
            for i in range(len(end_data) - 8, -1, -1):
                if end_data[i:i+8] == b'MEI\x0c\x0b\x08\x0b\x0e':
                    print(f"Found PyInstaller cookie at position {file_size - len(end_data) + i}")
                    break
        else:
            print("No overlay detected")

if __name__ == "__main__":
    if len(sys.argv) != 2:
        print("Usage: python analyze_exe.py <exe_file>")
        sys.exit(1)
        
    analyze_exe(sys.argv[1])
