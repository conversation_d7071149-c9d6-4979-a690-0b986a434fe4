# Code object from position 7248865
# Filename: __phello__.py
# Name: <module>
# Args: 0
# Locals: 0

  0           0 RESUME                   0

  1           2 LOAD_CONST               0 (True)
              4 STORE_NAME               0 (initialized)

  3           6 LOAD_CONST               1 (<code object main at 0x000001A2D05C8300, file "__phello__.py", line 3>)
              8 MAKE_FUNCTION            0
             10 STORE_NAME               1 (main)

  6          12 LOAD_NAME                2 (__name__)
             14 LOAD_CONST               2 ('__main__')
             16 COMPARE_OP               2 (<)
             20 CACHE
             22 POP_JUMP_IF_FALSE       12 (to 48)

  7          24 PUSH_NULL
             26 LOAD_NAME                1 (main)
             28 UNPACK_SEQUENCE          0
             32 CALL                     0
             40 CACHE
             42 POP_TOP
             44 LOAD_CONST               3 (None)
             46 RETURN_VALUE

  6     >>   48 LOAD_CONST               3 (None)
             50 RETURN_VALUE

Disassembly of <code object main at 0x000001A2D05C8300, file "__phello__.py", line 3>:
  3           0 RESUME                   0

  4           2 LOAD_GLOBAL              1 (NULL + print)
             12 CACHE
             14 LOAD_CONST               1 ('Hello world!')
             16 UNPACK_SEQUENCE          1
             20 CALL                     1
             28 CACHE
             30 POP_TOP
             32 LOAD_CONST               0 (None)
             34 RETURN_VALUE


# Constants:
# 0: True
# 1: code
# 2: '__main__'
# 3: None


# Names:
# 0: 'initialized'
# 1: 'main'
# 2: '__name__'


# Variable names:
