Extraction Summary
==================

Source file: Windows/AMD64/imgproc.exe
Total code objects found: 20
Successfully extracted: 10

Code objects:
  1. Position:  7242941, File: __future__.py, Name: <module>
  2. Position:  7247821, File: __hello__.py, Name: <module>
  3. Position:  7248865, File: __phello__.py, Name: <module>
  4. Position:  7249254, File: __phello__\ham.py, Name: <module>
  5. Position:  7249369, File: __phello__\ham\eggs.py, Name: <module>
  6. Position:  7249489, File: __phello__\spam.py, Name: <module>
  7. Position:  7249883, File: _aix_support.py, Name: <module>
  8. Position:  7254216, File: _bootsubprocess.py, Name: <module>
  9. Position:  7258642, File: _collections_abc.py, Name: <module>
 10. Position:  7309824, File: _compat_pickle.py, Name: <module>
 11. Position:  7317306, File: _compression.py, Name: <module>
 12. Position:  7325322, File: _markupbase.py, Name: <module>
 13. Position:  7339370, File: _osx_support.py, Name: <module>
 14. Position:  7359262, File: _py_abc.py, Name: <module>
 15. Position:  7367106, File: _pydecimal.py, Name: <module>
 16. Position:  7611333, File: _pyio.py, Name: <module>
 17. Position:  7731438, File: _sitebuiltins.py, Name: <module>
 18. Position:  7736828, File: _strptime.py, Name: <module>
 19. Position:  7764702, File: _threading_local.py, Name: <module>
 20. Position:  7773873, File: _weakrefset.py, Name: <module>
