# Code object from position 10343284
# Filename: hmac.py
# Name: <module>
# Args: 0
# Locals: 0
# Stack size: 6
# Flags: 0

  0           0 RESUME                   0

  1           2 LOAD_CONST               0 ('HMAC (Keyed-Hashing for Message Authentication) module.\n\nImplements the HMAC algorithm as described by RFC 2104.\n')
              4 STORE_NAME               0 (__doc__)

  6           6 LOAD_CONST               1 (0)
              8 LOAD_CONST               2 (None)
             10 IMPORT_NAME              1 (warnings)
             12 STORE_NAME               2 (_warnings)

  7          14 NOP

  8          16 LOAD_CONST               1 (0)
             18 LOAD_CONST               2 (None)
             20 IMPORT_NAME              3 (_hashlib)
             22 STORE_NAME               4 (_hashopenssl)

 14          24 LOAD_NAME                4 (_hashopenssl)
             26 LOAD_ATTR                5 (NULL|self + _warnings)
             46 CACHE
             48 CACHE
             50 CACHE
             52 CACHE
             54 UNPACK_SEQUENCE          1
             58 CALL                     1
             66 CACHE
             68 STORE_NAME               8 (_functype)
             70 JUMP_FORWARD            21 (to 114)
        >>   72 PUSH_EXC_INFO

  9          74 LOAD_NAME                9 (ImportError)
             76 CHECK_EXC_MATCH
             78 POP_JUMP_IF_FALSE       13 (to 106)
             80 POP_TOP

 10          82 LOAD_CONST               2 (None)
             84 STORE_NAME               4 (_hashopenssl)

 11          86 LOAD_CONST               2 (None)
             88 STORE_NAME               8 (_functype)

 12          90 LOAD_CONST               1 (0)
             92 LOAD_CONST               3 (('_compare_digest',))
             94 IMPORT_NAME             10 (_operator)
             96 IMPORT_FROM             11 (_compare_digest)
             98 STORE_NAME               5 (compare_digest)
            100 POP_TOP
            102 POP_EXCEPT
            104 JUMP_FORWARD             4 (to 114)

  9     >>  106 RERAISE                  0
        >>  108 COPY                     3
            110 POP_EXCEPT
            112 RERAISE                  1

 17     >>  114 LOAD_CONST               1 (0)
            116 LOAD_CONST               2 (None)
            118 IMPORT_NAME             12 (hashlib)
            120 STORE_NAME               3 (_hashlib)

 19         122 PUSH_NULL
            124 LOAD_NAME               13 (bytes)
            126 LOAD_CONST               4 (<code object <genexpr> at 0x000001B2A768BC90, file "hmac.py", line 19>)
            128 MAKE_FUNCTION            0
            130 PUSH_NULL
            132 LOAD_NAME               14 (range)
            134 LOAD_CONST               5 (256)
            136 UNPACK_SEQUENCE          1
            140 CALL                     1
            148 CACHE
            150 GET_ITER
            152 UNPACK_SEQUENCE          0
            156 CALL                     0
            164 CACHE
            166 UNPACK_SEQUENCE          1
            170 CALL                     1
            178 CACHE
            180 STORE_NAME              15 (trans_5C)

 20         182 PUSH_NULL
            184 LOAD_NAME               13 (bytes)
            186 LOAD_CONST               6 (<code object <genexpr> at 0x000001B2A768BD70, file "hmac.py", line 20>)
            188 MAKE_FUNCTION            0
            190 PUSH_NULL
            192 LOAD_NAME               14 (range)
            194 LOAD_CONST               5 (256)
            196 UNPACK_SEQUENCE          1
            200 CALL                     1
            208 CACHE
            210 GET_ITER
            212 UNPACK_SEQUENCE          0
            216 CALL                     0
            224 CACHE
            226 UNPACK_SEQUENCE          1
            230 CALL                     1
            238 CACHE
            240 STORE_NAME              16 (trans_36)

 24         242 LOAD_CONST               2 (None)
            244 STORE_NAME              17 (digest_size)

 27         246 PUSH_NULL
            248 LOAD_BUILD_CLASS
            250 LOAD_CONST               7 (<code object HMAC at 0x000001B2A76B6170, file "hmac.py", line 27>)
            252 MAKE_FUNCTION            0
            254 LOAD_CONST               8 ('HMAC')
            256 UNPACK_SEQUENCE          2
            260 CALL                     2
            268 CACHE
            270 STORE_NAME              18 (HMAC)

167         272 LOAD_CONST              12 ((None, ''))
            274 LOAD_CONST              10 (<code object new at 0x000001B2A767F5A0, file "hmac.py", line 167>)
            276 MAKE_FUNCTION            1 (defaults)
            278 STORE_NAME              19 (new)

187         280 LOAD_CONST              11 (<code object digest at 0x000001B2A75C6C40, file "hmac.py", line 187>)
            282 MAKE_FUNCTION            0
            284 STORE_NAME              20 (digest)
            286 LOAD_CONST               2 (None)
            288 RETURN_VALUE
ExceptionTable:
  16 to 22 -> 72 [0]
  72 to 100 -> 108 [1] lasti
  106 to 106 -> 108 [1] lasti

Disassembly of <code object <genexpr> at 0x000001B2A768BC90, file "hmac.py", line 19>:
 19           0 RETURN_GENERATOR
              2 POP_TOP
              4 RESUME                   0
              6 LOAD_FAST                0 (.0)
        >>    8 FOR_ITER                 9 (to 30)
             12 LOAD_FAST                1 (x)
             14 LOAD_CONST               0 (92)
             16 BINARY_OP               12 (^)
             20 LOAD_FAST                0 (.0)
             22 RESUME                   1
             24 POP_TOP
             26 JUMP_BACKWARD           10 (to 8)
             28 LOAD_CONST               1 (None)
        >>   30 RETURN_VALUE

Disassembly of <code object <genexpr> at 0x000001B2A768BD70, file "hmac.py", line 20>:
 20           0 RETURN_GENERATOR
              2 POP_TOP
              4 RESUME                   0
              6 LOAD_FAST                0 (.0)
        >>    8 FOR_ITER                 9 (to 30)
             12 LOAD_FAST                1 (x)
             14 LOAD_CONST               0 (54)
             16 BINARY_OP               12 (^)
             20 LOAD_FAST                0 (.0)
             22 RESUME                   1
             24 POP_TOP
             26 JUMP_BACKWARD           10 (to 8)
             28 LOAD_CONST               1 (None)
        >>   30 RETURN_VALUE

Disassembly of <code object HMAC at 0x000001B2A76B6170, file "hmac.py", line 27>:
 27           0 RESUME                   0
              2 LOAD_NAME                0 (__name__)
              4 STORE_NAME               1 (__module__)
              6 LOAD_CONST               0 ('HMAC')
              8 STORE_NAME               2 (__qualname__)

 28          10 LOAD_CONST               1 ('RFC 2104 HMAC class.  Also complies with RFC 4231.\n\n    This supports the API for Cryptographic Hash Functions (PEP 247).\n    ')
             12 STORE_NAME               3 (__doc__)

 32          14 LOAD_CONST               2 (64)
             16 STORE_NAME               4 (blocksize)

 34          18 LOAD_CONST               3 (('_hmac', '_inner', '_outer', 'block_size', 'digest_size'))
             20 STORE_NAME               5 (__slots__)

 38          22 LOAD_CONST              15 ((None, ''))
             24 LOAD_CONST               6 (<code object __init__ at 0x000001B2A74A4FC0, file "hmac.py", line 38>)
             26 MAKE_FUNCTION            1 (defaults)
             28 STORE_NAME               6 (__init__)

 66          30 LOAD_CONST               7 (<code object _init_hmac at 0x000001B2A71C6A30, file "hmac.py", line 66>)
             32 MAKE_FUNCTION            0
             34 STORE_NAME               7 (_init_hmac)

 71          36 LOAD_CONST               8 (<code object _init_old at 0x000001B2A75B49C0, file "hmac.py", line 71>)
             38 MAKE_FUNCTION            0
             40 STORE_NAME               8 (_init_old)

110          42 LOAD_NAME                9 (property)

111          44 LOAD_CONST               9 (<code object name at 0x000001B2A76AF770, file "hmac.py", line 110>)
             46 MAKE_FUNCTION            0

110          48 UNPACK_SEQUENCE          0
             52 CALL                     0
             60 CACHE

111          62 STORE_NAME              10 (name)

117          64 LOAD_CONST              10 (<code object update at 0x000001B2A76AE780, file "hmac.py", line 117>)
             66 MAKE_FUNCTION            0
             68 STORE_NAME              11 (update)

122          70 LOAD_CONST              11 (<code object copy at 0x000001B2A75DE0C0, file "hmac.py", line 122>)
             72 MAKE_FUNCTION            0
             74 STORE_NAME              12 (copy)

139          76 LOAD_CONST              12 (<code object _current at 0x000001B2A728F280, file "hmac.py", line 139>)
             78 MAKE_FUNCTION            0
             80 STORE_NAME              13 (_current)

151          82 LOAD_CONST              13 (<code object digest at 0x000001B2A76DBE10, file "hmac.py", line 151>)
             84 MAKE_FUNCTION            0
             86 STORE_NAME              14 (digest)

161          88 LOAD_CONST              14 (<code object hexdigest at 0x000001B2A8A4C4B0, file "hmac.py", line 161>)
             90 MAKE_FUNCTION            0
             92 STORE_NAME              15 (hexdigest)
             94 LOAD_CONST               4 (None)
             96 RETURN_VALUE

Disassembly of <code object __init__ at 0x000001B2A74A4FC0, file "hmac.py", line 38>:
 38           0 RESUME                   0

 52           2 LOAD_GLOBAL              1 (NULL + isinstance)
             12 CACHE
             14 LOAD_FAST                1 (key)
             16 LOAD_GLOBAL              2 (bytes)
             26 CACHE
             28 LOAD_GLOBAL              4 (bytearray)
             38 CACHE
             40 BUILD_TUPLE              2
             42 UNPACK_SEQUENCE          2
             46 CALL                     2
             54 CACHE
             56 POP_JUMP_IF_TRUE        36 (to 130)

 53          58 LOAD_GLOBAL              7 (NULL + TypeError)
             68 CACHE
             70 LOAD_CONST               1 ('key: expected bytes or bytearray, but got %r')
             72 LOAD_GLOBAL              9 (NULL + type)
             82 CACHE
             84 LOAD_FAST                1 (key)
             86 UNPACK_SEQUENCE          1
             90 CALL                     1
             98 CACHE
            100 LOAD_ATTR                5 (NULL|self + bytearray)
            120 CACHE
            122 CACHE
            124 CACHE
            126 CACHE
            128 RAISE_VARARGS            1

 55     >>  130 LOAD_FAST                3 (digestmod)
            132 POP_JUMP_IF_TRUE        15 (to 164)

 56         134 LOAD_GLOBAL              7 (NULL + TypeError)
            144 CACHE
            146 LOAD_CONST               2 ("Missing required argument 'digestmod'.")
            148 UNPACK_SEQUENCE          1
            152 CALL                     1
            160 CACHE
            162 RAISE_VARARGS            1

 58     >>  164 LOAD_GLOBAL             12 (_hashopenssl)
            174 CACHE
            176 POP_JUMP_IF_FALSE       99 (to 376)
            178 LOAD_GLOBAL              1 (NULL + isinstance)
            188 CACHE
            190 LOAD_FAST                3 (digestmod)
            192 LOAD_GLOBAL             14 (str)
            202 CACHE
            204 LOAD_GLOBAL             16 (_functype)
            214 CACHE
            216 BUILD_TUPLE              2
            218 UNPACK_SEQUENCE          2
            222 CALL                     2
            230 CACHE
            232 POP_JUMP_IF_FALSE       71 (to 376)

 59         234 NOP

 60         236 LOAD_FAST                0 (self)
            238 STORE_SUBSCR
            242 CACHE
            244 CACHE
            246 CACHE
            248 CACHE
            250 CACHE
            252 CACHE
            254 CACHE
            256 CACHE
            258 CACHE
            260 LOAD_FAST                1 (key)
            262 LOAD_FAST                2 (msg)
            264 LOAD_FAST                3 (digestmod)
            266 UNPACK_SEQUENCE          3
            270 CALL                     3
            278 CACHE
            280 POP_TOP
            282 LOAD_CONST               3 (None)
            284 RETURN_VALUE
        >>  286 PUSH_EXC_INFO

 61         288 LOAD_GLOBAL             12 (_hashopenssl)
            298 CACHE
            300 LOAD_ATTR               10 (__name__)
            320 CACHE
            322 CACHE
            324 CACHE
            326 CACHE
            328 CACHE
            330 CACHE
            332 CACHE
            334 CACHE
            336 CACHE
            338 CACHE
            340 LOAD_FAST                1 (key)
            342 LOAD_FAST                2 (msg)
            344 LOAD_FAST                3 (digestmod)
            346 UNPACK_SEQUENCE          3
            350 CALL                     3
            358 CACHE
            360 POP_TOP
            362 POP_EXCEPT
            364 LOAD_CONST               3 (None)
            366 RETURN_VALUE

 61         368 RERAISE                  0
        >>  370 COPY                     3
            372 POP_EXCEPT
            374 RERAISE                  1

 64     >>  376 LOAD_FAST                0 (self)
            378 STORE_SUBSCR
            382 CACHE
            384 CACHE
            386 CACHE
            388 CACHE
            390 CACHE
            392 CACHE
            394 CACHE
            396 CACHE
            398 CACHE
            400 LOAD_FAST                1 (key)
            402 LOAD_FAST                2 (msg)
            404 LOAD_FAST                3 (digestmod)
            406 UNPACK_SEQUENCE          3
            410 CALL                     3
            418 CACHE
            420 POP_TOP
            422 LOAD_CONST               3 (None)
            424 RETURN_VALUE
ExceptionTable:
  236 to 280 -> 286 [0]
  286 to 360 -> 370 [1] lasti
  368 to 368 -> 370 [1] lasti

Disassembly of <code object _init_hmac at 0x000001B2A71C6A30, file "hmac.py", line 66>:
 66           0 RESUME                   0

 67           2 LOAD_GLOBAL              1 (NULL + _hashopenssl)
             12 CACHE
             14 LOAD_ATTR                1 (NULL|self + _hashopenssl)
             34 CACHE
             36 CALL                     3
             44 CACHE
             46 LOAD_FAST                0 (self)
             48 STORE_ATTR               2 (_hmac)

 68          58 LOAD_FAST                0 (self)
             60 LOAD_ATTR                2 (hmac_new)
             80 LOAD_FAST                0 (self)
             82 STORE_ATTR               3 (digest_size)

 69          92 LOAD_FAST                0 (self)
             94 LOAD_ATTR                2 (hmac_new)
            114 LOAD_FAST                0 (self)
            116 STORE_ATTR               4 (block_size)
            126 LOAD_CONST               0 (None)
            128 RETURN_VALUE

Disassembly of <code object _init_old at 0x000001B2A75B49C0, file "hmac.py", line 71>:
              0 MAKE_CELL                3 (digestmod)

 71           2 RESUME                   0

 72           4 LOAD_GLOBAL              1 (NULL + callable)
             14 CACHE
             16 LOAD_DEREF               3 (digestmod)
             18 UNPACK_SEQUENCE          1
             22 CALL                     1
             30 CACHE
             32 POP_JUMP_IF_FALSE        3 (to 40)

 73          34 LOAD_DEREF               3 (digestmod)
             36 STORE_FAST               4 (digest_cons)
             38 JUMP_FORWARD            34 (to 108)

 74     >>   40 LOAD_GLOBAL              3 (NULL + isinstance)
             50 CACHE
             52 LOAD_DEREF               3 (digestmod)
             54 LOAD_GLOBAL              4 (str)
             64 CACHE
             66 UNPACK_SEQUENCE          2
             70 CALL                     2
             78 CACHE
             80 POP_JUMP_IF_FALSE        7 (to 96)

 75          82 LOAD_CONST              10 ((b'',))
             84 LOAD_CLOSURE             3 (digestmod)
             86 BUILD_TUPLE              1
             88 LOAD_CONST               2 (<code object <lambda> at 0x000001B2A767DA70, file "hmac.py", line 75>)
             90 MAKE_FUNCTION            9 (defaults, closure)
             92 STORE_FAST               4 (digest_cons)
             94 JUMP_FORWARD             6 (to 108)

 77     >>   96 LOAD_CONST              10 ((b'',))
             98 LOAD_CLOSURE             3 (digestmod)
            100 BUILD_TUPLE              1
            102 LOAD_CONST               3 (<code object <lambda> at 0x000001B2A767D6B0, file "hmac.py", line 77>)
            104 MAKE_FUNCTION            9 (defaults, closure)
            106 STORE_FAST               4 (digest_cons)

 79     >>  108 LOAD_CONST               0 (None)
            110 LOAD_FAST                0 (self)
            112 STORE_ATTR               3 (_hmac)

 80         122 PUSH_NULL
            124 LOAD_FAST                4 (digest_cons)
            126 UNPACK_SEQUENCE          0
            130 CALL                     0
            138 CACHE
            140 LOAD_FAST                0 (self)
            142 STORE_ATTR               4 (_outer)

 81         152 PUSH_NULL
            154 LOAD_FAST                4 (digest_cons)
            156 UNPACK_SEQUENCE          0
            160 CALL                     0
            168 CACHE
            170 LOAD_FAST                0 (self)
            172 STORE_ATTR               5 (_inner)

 82         182 LOAD_FAST                0 (self)
            184 LOAD_ATTR                5 (NULL|self + str)
            204 LOAD_FAST                0 (self)
            206 STORE_ATTR               6 (digest_size)

 84         216 LOAD_GLOBAL             15 (NULL + hasattr)
            226 CACHE
            228 LOAD_FAST                0 (self)
            230 LOAD_ATTR                5 (NULL|self + str)
            250 CACHE
            252 CACHE
            254 CACHE
            256 POP_JUMP_IF_FALSE       63 (to 384)

 85         258 LOAD_FAST                0 (self)
            260 LOAD_ATTR                5 (NULL|self + str)
            280 STORE_FAST               5 (blocksize)

 86         282 LOAD_FAST                5 (blocksize)
            284 LOAD_CONST               5 (16)
            286 COMPARE_OP               0 (<)
            290 CACHE
            292 POP_JUMP_IF_FALSE       44 (to 382)

 87         294 LOAD_GLOBAL             19 (NULL + _warnings)
            304 CACHE
            306 LOAD_ATTR               10 (_inner)
            326 CACHE
            328 CACHE
            330 CACHE
            332 BUILD_TUPLE              2

 87         334 BINARY_OP                6 (%)

 89         338 LOAD_GLOBAL             24 (RuntimeWarning)
            348 CACHE
            350 LOAD_CONST               7 (2)

 87         352 UNPACK_SEQUENCE          3
            356 CALL                     3
            364 CACHE
            366 POP_TOP

 90         368 LOAD_FAST                0 (self)
            370 LOAD_ATTR               11 (NULL|self + _inner)
            390 CACHE
            392 CACHE
            394 CACHE
            396 LOAD_ATTR               10 (_inner)
            416 CACHE
            418 CACHE

 92         420 BINARY_OP                6 (%)

 94         424 LOAD_GLOBAL             24 (RuntimeWarning)
            434 CACHE
            436 LOAD_CONST               7 (2)

 92         438 UNPACK_SEQUENCE          3
            442 CALL                     3
            450 CACHE
            452 POP_TOP

 95         454 LOAD_FAST                0 (self)
            456 LOAD_ATTR               11 (NULL|self + _inner)
            476 CACHE
            478 CACHE
            480 LOAD_FAST                1 (key)
            482 UNPACK_SEQUENCE          1
            486 CALL                     1
            494 CACHE
            496 LOAD_FAST                5 (blocksize)
            498 COMPARE_OP               4 (<)
            502 CACHE
            504 POP_JUMP_IF_FALSE       29 (to 564)

 98         506 PUSH_NULL
            508 LOAD_FAST                4 (digest_cons)
            510 LOAD_FAST                1 (key)
            512 UNPACK_SEQUENCE          1
            516 CALL                     1
            524 CACHE
            526 STORE_SUBSCR
            530 CACHE
            532 CACHE
            534 CACHE
            536 CACHE
            538 CACHE
            540 CACHE
            542 CACHE
            544 CACHE
            546 CACHE
            548 UNPACK_SEQUENCE          0
            552 CALL                     0
            560 CACHE
            562 STORE_FAST               1 (key)

102     >>  564 LOAD_FAST                5 (blocksize)
            566 LOAD_FAST                0 (self)
            568 STORE_ATTR               8 (block_size)

104         578 LOAD_FAST                1 (key)
            580 STORE_SUBSCR
            584 CACHE
            586 CACHE
            588 CACHE
            590 CACHE
            592 CACHE
            594 CACHE
            596 CACHE
            598 CACHE
            600 CACHE
            602 LOAD_FAST                5 (blocksize)
            604 LOAD_CONST               9 (b'\x00')
            606 UNPACK_SEQUENCE          2
            610 CALL                     2
            618 CACHE
            620 STORE_FAST               1 (key)

105         622 LOAD_FAST                0 (self)
            624 LOAD_ATTR                4 (str)
            644 CACHE
            646 CACHE
            648 CACHE
            650 CACHE
            652 CACHE
            654 CACHE
            656 LOAD_FAST                1 (key)
            658 STORE_SUBSCR
            662 CACHE
            664 CACHE
            666 CACHE
            668 CACHE
            670 CACHE
            672 CACHE
            674 CACHE
            676 CACHE
            678 CACHE
            680 LOAD_GLOBAL             36 (trans_5C)
            690 CACHE
            692 UNPACK_SEQUENCE          1
            696 CALL                     1
            704 CACHE
            706 UNPACK_SEQUENCE          1
            710 CALL                     1
            718 CACHE
            720 POP_TOP

106         722 LOAD_FAST                0 (self)
            724 LOAD_ATTR                5 (NULL|self + str)
            744 CACHE
            746 CACHE
            748 CACHE
            750 CACHE
            752 CACHE
            754 CACHE
            756 LOAD_FAST                1 (key)
            758 STORE_SUBSCR
            762 CACHE
            764 CACHE
            766 CACHE
            768 CACHE
            770 CACHE
            772 CACHE
            774 CACHE
            776 CACHE
            778 CACHE
            780 LOAD_GLOBAL             38 (trans_36)
            790 CACHE
            792 UNPACK_SEQUENCE          1
            796 CALL                     1
            804 CACHE
            806 UNPACK_SEQUENCE          1
            810 CALL                     1
            818 CACHE
            820 POP_TOP

107         822 LOAD_FAST                2 (msg)
            824 POP_JUMP_IF_NONE        23 (to 872)

108         826 LOAD_FAST                0 (self)
            828 STORE_SUBSCR
            832 CACHE
            834 CACHE
            836 CACHE
            838 CACHE
            840 CACHE
            842 CACHE
            844 CACHE
            846 CACHE
            848 CACHE
            850 LOAD_FAST                2 (msg)
            852 UNPACK_SEQUENCE          1
            856 CALL                     1
            864 CACHE
            866 POP_TOP
            868 LOAD_CONST               0 (None)
            870 RETURN_VALUE

107     >>  872 LOAD_CONST               0 (None)
            874 RETURN_VALUE

Disassembly of <code object <lambda> at 0x000001B2A767DA70, file "hmac.py", line 75>:
              0 COPY_FREE_VARS           1

 75           2 RESUME                   0
              4 LOAD_GLOBAL              1 (NULL + _hashlib)
             14 CACHE
             16 LOAD_ATTR                1 (NULL|self + _hashlib)
             36 CACHE
             38 CACHE
             40 CACHE
             42 CACHE
             44 RETURN_VALUE

Disassembly of <code object <lambda> at 0x000001B2A767D6B0, file "hmac.py", line 77>:
              0 COPY_FREE_VARS           1

 77           2 RESUME                   0
              4 LOAD_DEREF               1 (digestmod)
              6 STORE_SUBSCR
             10 CACHE
             12 CACHE
             14 CACHE
             16 CACHE
             18 CACHE
             20 CACHE
             22 CACHE
             24 CACHE
             26 CACHE
             28 LOAD_FAST                0 (d)
             30 UNPACK_SEQUENCE          1
             34 CALL                     1
             42 CACHE
             44 RETURN_VALUE

Disassembly of <code object name at 0x000001B2A76AF770, file "hmac.py", line 110>:
110           0 RESUME                   0

112           2 LOAD_FAST                0 (self)
              4 LOAD_ATTR                0 (_hmac)
             24 CACHE
             26 CACHE
             28 LOAD_ATTR                1 (NULL|self + _hmac)
             48 CACHE
             50 CACHE
             52 CACHE
             54 LOAD_ATTR                1 (NULL|self + _hmac)

Disassembly of <code object update at 0x000001B2A76AE780, file "hmac.py", line 117>:
117           0 RESUME                   0

119           2 LOAD_FAST                0 (self)
              4 LOAD_ATTR                0 (_hmac)
             24 CACHE
             26 CACHE
             28 STORE_FAST               2 (inst)

120          30 LOAD_FAST                2 (inst)
             32 STORE_SUBSCR
             36 CACHE
             38 CACHE
             40 CACHE
             42 CACHE
             44 CACHE
             46 CACHE
             48 CACHE
             50 CACHE
             52 CACHE
             54 LOAD_FAST                1 (msg)
             56 UNPACK_SEQUENCE          1
             60 CALL                     1
             68 CACHE
             70 POP_TOP
             72 LOAD_CONST               1 (None)
             74 RETURN_VALUE

Disassembly of <code object copy at 0x000001B2A75DE0C0, file "hmac.py", line 122>:
122           0 RESUME                   0

128           2 LOAD_FAST                0 (self)
              4 LOAD_ATTR                0 (__class__)
             24 CACHE
             26 CACHE
             28 CACHE
             30 CACHE
             32 CACHE
             34 CACHE
             36 LOAD_FAST                0 (self)
             38 LOAD_ATTR                0 (__class__)
             58 CACHE
             60 CACHE
             62 STORE_FAST               1 (other)

129          64 LOAD_FAST                0 (self)
             66 LOAD_ATTR                2 (__new__)
             86 CACHE

130          88 LOAD_FAST                0 (self)
             90 LOAD_ATTR                3 (NULL|self + __new__)
            110 CACHE
            112 CACHE
            114 STORE_SUBSCR
            118 CACHE
            120 CACHE
            122 CACHE
            124 CACHE
            126 CACHE
            128 CACHE
            130 CACHE
            132 CACHE
            134 CACHE
            136 UNPACK_SEQUENCE          0
            140 CALL                     0
            148 CACHE
            150 LOAD_FAST                1 (other)
            152 STORE_ATTR               3 (_hmac)

132         162 LOAD_CONST               1 (None)
            164 COPY                     1
            166 LOAD_FAST                1 (other)
            168 STORE_ATTR               5 (_inner)
            178 LOAD_FAST                1 (other)
            180 STORE_ATTR               6 (_outer)
            190 JUMP_FORWARD            67 (to 326)

134         192 LOAD_CONST               1 (None)
            194 LOAD_FAST                1 (other)
            196 STORE_ATTR               3 (_hmac)

135         206 LOAD_FAST                0 (self)
            208 LOAD_ATTR                5 (NULL|self + digest_size)
            228 CACHE
            230 CACHE
            232 CACHE
            234 CACHE
            236 CACHE
            238 CACHE
            240 UNPACK_SEQUENCE          0
            244 CALL                     0
            252 CACHE
            254 LOAD_FAST                1 (other)
            256 STORE_ATTR               5 (_inner)

136         266 LOAD_FAST                0 (self)
            268 LOAD_ATTR                6 (_hmac)
            288 CACHE
            290 CACHE
            292 CACHE
            294 CACHE
            296 CACHE
            298 CACHE
            300 UNPACK_SEQUENCE          0
            304 CALL                     0
            312 CACHE
            314 LOAD_FAST                1 (other)
            316 STORE_ATTR               6 (_outer)

137     >>  326 LOAD_FAST                1 (other)
            328 RETURN_VALUE

Disassembly of <code object _current at 0x000001B2A728F280, file "hmac.py", line 139>:
139           0 RESUME                   0

144           2 LOAD_FAST                0 (self)
              4 LOAD_ATTR                0 (_hmac)
             24 CACHE
             26 CACHE
             28 RETURN_VALUE

147          30 LOAD_FAST                0 (self)
             32 LOAD_ATTR                1 (NULL|self + _hmac)
             52 CACHE
             54 CACHE
             56 CACHE
             58 CACHE
             60 CACHE
             62 CACHE
             64 UNPACK_SEQUENCE          0
             68 CALL                     0
             76 CACHE
             78 STORE_FAST               1 (h)

148          80 LOAD_FAST                1 (h)
             82 STORE_SUBSCR
             86 CACHE
             88 CACHE
             90 CACHE
             92 CACHE
             94 CACHE
             96 CACHE
             98 CACHE
            100 CACHE
            102 CACHE
            104 LOAD_FAST                0 (self)
            106 LOAD_ATTR                4 (copy)
            126 CACHE
            128 CACHE
            130 CACHE
            132 CACHE
            134 CACHE
            136 CACHE
            138 UNPACK_SEQUENCE          0
            142 CALL                     0
            150 CACHE
            152 UNPACK_SEQUENCE          1
            156 CALL                     1
            164 CACHE
            166 POP_TOP

149         168 LOAD_FAST                1 (h)
            170 RETURN_VALUE

Disassembly of <code object digest at 0x000001B2A76DBE10, file "hmac.py", line 151>:
151           0 RESUME                   0

158           2 LOAD_FAST                0 (self)
              4 STORE_SUBSCR
              8 CACHE
             10 CACHE
             12 CACHE
             14 CACHE
             16 CACHE
             18 CACHE
             20 CACHE
             22 CACHE
             24 CACHE
             26 UNPACK_SEQUENCE          0
             30 CALL                     0
             38 CACHE
             40 STORE_FAST               1 (h)

159          42 LOAD_FAST                1 (h)
             44 STORE_SUBSCR
             48 CACHE
             50 CACHE
             52 CACHE
             54 CACHE
             56 CACHE
             58 CACHE
             60 CACHE
             62 CACHE
             64 CACHE
             66 UNPACK_SEQUENCE          0
             70 CALL                     0
             78 CACHE
             80 RETURN_VALUE

Disassembly of <code object hexdigest at 0x000001B2A8A4C4B0, file "hmac.py", line 161>:
161           0 RESUME                   0

164           2 LOAD_FAST                0 (self)
              4 STORE_SUBSCR
              8 CACHE
             10 CACHE
             12 CACHE
             14 CACHE
             16 CACHE
             18 CACHE
             20 CACHE
             22 CACHE
             24 CACHE
             26 UNPACK_SEQUENCE          0
             30 CALL                     0
             38 CACHE
             40 STORE_FAST               1 (h)

165          42 LOAD_FAST                1 (h)
             44 STORE_SUBSCR
             48 CACHE
             50 CACHE
             52 CACHE
             54 CACHE
             56 CACHE
             58 CACHE
             60 CACHE
             62 CACHE
             64 CACHE
             66 UNPACK_SEQUENCE          0
             70 CALL                     0
             78 CACHE
             80 RETURN_VALUE

Disassembly of <code object new at 0x000001B2A767F5A0, file "hmac.py", line 167>:
167           0 RESUME                   0

184           2 LOAD_GLOBAL              1 (NULL + HMAC)
             12 CACHE
             14 LOAD_FAST                0 (key)
             16 LOAD_FAST                1 (msg)
             18 LOAD_FAST                2 (digestmod)
             20 UNPACK_SEQUENCE          3
             24 CALL                     3
             32 CACHE
             34 RETURN_VALUE

Disassembly of <code object digest at 0x000001B2A75C6C40, file "hmac.py", line 187>:
              0 MAKE_CELL                2 (digest)

187           2 RESUME                   0

196           4 LOAD_GLOBAL              0 (_hashopenssl)
             14 CACHE
             16 POP_JUMP_IF_NONE        72 (to 162)
             18 LOAD_GLOBAL              3 (NULL + isinstance)
             28 CACHE
             30 LOAD_DEREF               2 (digest)
             32 LOAD_GLOBAL              4 (str)
             42 CACHE
             44 LOAD_GLOBAL              6 (_functype)
             54 CACHE
             56 BUILD_TUPLE              2
             58 UNPACK_SEQUENCE          2
             62 CALL                     2
             70 CACHE
             72 POP_JUMP_IF_FALSE       44 (to 162)

197          74 NOP

198          76 LOAD_GLOBAL              1 (NULL + _hashopenssl)
             86 CACHE
             88 LOAD_ATTR                4 (str)
            108 CALL                     3
            116 CACHE
            118 RETURN_VALUE
        >>  120 PUSH_EXC_INFO

199         122 LOAD_GLOBAL              0 (_hashopenssl)
            132 CACHE
            134 LOAD_ATTR                5 (NULL|self + str)

199         154 RERAISE                  0
        >>  156 COPY                     3
            158 POP_EXCEPT
            160 RERAISE                  1

202     >>  162 LOAD_GLOBAL             13 (NULL + callable)
            172 CACHE
            174 LOAD_DEREF               2 (digest)
            176 UNPACK_SEQUENCE          1
            180 CALL                     1
            188 CACHE
            190 POP_JUMP_IF_FALSE        3 (to 198)

203         192 LOAD_DEREF               2 (digest)
            194 STORE_FAST               3 (digest_cons)
            196 JUMP_FORWARD            34 (to 266)

204     >>  198 LOAD_GLOBAL              3 (NULL + isinstance)
            208 CACHE
            210 LOAD_DEREF               2 (digest)
            212 LOAD_GLOBAL              4 (str)
            222 CACHE
            224 UNPACK_SEQUENCE          2
            228 CALL                     2
            236 CACHE
            238 POP_JUMP_IF_FALSE        7 (to 254)

205         240 LOAD_CONST               8 ((b'',))
            242 LOAD_CLOSURE             2 (digest)
            244 BUILD_TUPLE              1
            246 LOAD_CONST               3 (<code object <lambda> at 0x000001B2A767CC60, file "hmac.py", line 205>)
            248 MAKE_FUNCTION            9 (defaults, closure)
            250 STORE_FAST               3 (digest_cons)
            252 JUMP_FORWARD             6 (to 266)

207     >>  254 LOAD_CONST               8 ((b'',))
            256 LOAD_CLOSURE             2 (digest)
            258 BUILD_TUPLE              1
            260 LOAD_CONST               4 (<code object <lambda> at 0x000001B2A8A48030, file "hmac.py", line 207>)
            262 MAKE_FUNCTION            9 (defaults, closure)
            264 STORE_FAST               3 (digest_cons)

209     >>  266 PUSH_NULL
            268 LOAD_FAST                3 (digest_cons)
            270 UNPACK_SEQUENCE          0
            274 CALL                     0
            282 CACHE
            284 STORE_FAST               4 (inner)

210         286 PUSH_NULL
            288 LOAD_FAST                3 (digest_cons)
            290 UNPACK_SEQUENCE          0
            294 CALL                     0
            302 CACHE
            304 STORE_FAST               5 (outer)

211         306 LOAD_GLOBAL             15 (NULL + getattr)
            316 CACHE
            318 LOAD_FAST                4 (inner)
            320 LOAD_CONST               5 ('block_size')
            322 LOAD_CONST               6 (64)
            324 UNPACK_SEQUENCE          3
            328 CALL                     3
            336 CACHE
            338 STORE_FAST               6 (blocksize)

212         340 LOAD_GLOBAL             17 (NULL + len)
            350 CACHE
            352 LOAD_FAST                0 (key)
            354 UNPACK_SEQUENCE          1
            358 CALL                     1
            366 CACHE
            368 LOAD_FAST                6 (blocksize)
            370 COMPARE_OP               4 (<)
            374 CACHE
            376 POP_JUMP_IF_FALSE       29 (to 436)

213         378 PUSH_NULL
            380 LOAD_FAST                3 (digest_cons)
            382 LOAD_FAST                0 (key)
            384 UNPACK_SEQUENCE          1
            388 CALL                     1
            396 CACHE
            398 STORE_SUBSCR
            402 CACHE
            404 CACHE
            406 CACHE
            408 CACHE
            410 CACHE
            412 CACHE
            414 CACHE
            416 CACHE
            418 CACHE
            420 UNPACK_SEQUENCE          0
            424 CALL                     0
            432 CACHE
            434 STORE_FAST               0 (key)

214     >>  436 LOAD_FAST                0 (key)
            438 LOAD_CONST               7 (b'\x00')
            440 LOAD_FAST                6 (blocksize)
            442 LOAD_GLOBAL             17 (NULL + len)
            452 CACHE
            454 LOAD_FAST                0 (key)
            456 UNPACK_SEQUENCE          1
            460 CALL                     1
            468 CACHE
            470 BINARY_OP               10 (-)
            474 BINARY_OP                5 (*)
            478 BINARY_OP                0 (+)
            482 STORE_FAST               0 (key)

215         484 LOAD_FAST                4 (inner)
            486 STORE_SUBSCR
            490 CACHE
            492 CACHE
            494 CACHE
            496 CACHE
            498 CACHE
            500 CACHE
            502 CACHE
            504 CACHE
            506 CACHE
            508 LOAD_FAST                0 (key)
            510 STORE_SUBSCR
            514 CACHE
            516 CACHE
            518 CACHE
            520 CACHE
            522 CACHE
            524 CACHE
            526 CACHE
            528 CACHE
            530 CACHE
            532 LOAD_GLOBAL             24 (trans_36)
            542 CACHE
            544 UNPACK_SEQUENCE          1
            548 CALL                     1
            556 CACHE
            558 UNPACK_SEQUENCE          1
            562 CALL                     1
            570 CACHE
            572 POP_TOP

216         574 LOAD_FAST                5 (outer)
            576 STORE_SUBSCR
            580 CACHE
            582 CACHE
            584 CACHE
            586 CACHE
            588 CACHE
            590 CACHE
            592 CACHE
            594 CACHE
            596 CACHE
            598 LOAD_FAST                0 (key)
            600 STORE_SUBSCR
            604 CACHE
            606 CACHE
            608 CACHE
            610 CACHE
            612 CACHE
            614 CACHE
            616 CACHE
            618 CACHE
            620 CACHE
            622 LOAD_GLOBAL             26 (trans_5C)
            632 CACHE
            634 UNPACK_SEQUENCE          1
            638 CALL                     1
            646 CACHE
            648 UNPACK_SEQUENCE          1
            652 CALL                     1
            660 CACHE
            662 POP_TOP

217         664 LOAD_FAST                4 (inner)
            666 STORE_SUBSCR
            670 CACHE
            672 CACHE
            674 CACHE
            676 CACHE
            678 CACHE
            680 CACHE
            682 CACHE
            684 CACHE
            686 CACHE
            688 LOAD_FAST                1 (msg)
            690 UNPACK_SEQUENCE          1
            694 CALL                     1
            702 CACHE
            704 POP_TOP

218         706 LOAD_FAST                5 (outer)
            708 STORE_SUBSCR
            712 CACHE
            714 CACHE
            716 CACHE
            718 CACHE
            720 CACHE
            722 CACHE
            724 CACHE
            726 CACHE
            728 CACHE
            730 LOAD_FAST                4 (inner)
            732 STORE_SUBSCR
            736 CACHE
            738 CACHE
            740 CACHE
            742 CACHE
            744 CACHE
            746 CACHE
            748 CACHE
            750 CACHE
            752 CACHE
            754 UNPACK_SEQUENCE          0
            758 CALL                     0
            766 CACHE
            768 UNPACK_SEQUENCE          1
            772 CALL                     1
            780 CACHE
            782 POP_TOP

219         784 LOAD_FAST                5 (outer)
            786 STORE_SUBSCR
            790 CACHE
            792 CACHE
            794 CACHE
            796 CACHE
            798 CACHE
            800 CACHE
            802 CACHE
            804 CACHE
            806 CACHE
            808 UNPACK_SEQUENCE          0
            812 CALL                     0
            820 CACHE
            822 RETURN_VALUE
ExceptionTable:
  76 to 116 -> 120 [0]
  120 to 148 -> 156 [1] lasti
  154 to 154 -> 156 [1] lasti

Disassembly of <code object <lambda> at 0x000001B2A767CC60, file "hmac.py", line 205>:
              0 COPY_FREE_VARS           1

205           2 RESUME                   0
              4 LOAD_GLOBAL              1 (NULL + _hashlib)
             14 CACHE
             16 LOAD_ATTR                1 (NULL|self + _hashlib)
             36 CACHE
             38 CACHE
             40 CACHE
             42 CACHE
             44 RETURN_VALUE

Disassembly of <code object <lambda> at 0x000001B2A8A48030, file "hmac.py", line 207>:
              0 COPY_FREE_VARS           1

207           2 RESUME                   0
              4 LOAD_DEREF               1 (digest)
              6 STORE_SUBSCR
             10 CACHE
             12 CACHE
             14 CACHE
             16 CACHE
             18 CACHE
             20 CACHE
             22 CACHE
             24 CACHE
             26 CACHE
             28 LOAD_FAST                0 (d)
             30 UNPACK_SEQUENCE          1
             34 CALL                     1
             42 CACHE
             44 RETURN_VALUE


# Constants:
# 0: <string length 113>
# 1: 0
# 2: None
# 3: tuple
# 4: <code object <genexpr>>
# 5: 256
# 6: <code object <genexpr>>
# 7: <code object HMAC>
# 8: 'HMAC'
# 9: ''
# 10: <code object new>
# 11: <code object digest>
# 12: tuple


# Names:
# 0: '__doc__'
# 1: 'warnings'
# 2: '_warnings'
# 3: '_hashlib'
# 4: '_hashopenssl'
# 5: 'compare_digest'
# 6: 'type'
# 7: 'openssl_sha256'
# 8: '_functype'
# 9: 'ImportError'
# 10: '_operator'
# 11: '_compare_digest'
# 12: 'hashlib'
# 13: 'bytes'
# 14: 'range'
# 15: 'trans_5C'
# 16: 'trans_36'
# 17: 'digest_size'
# 18: 'HMAC'
# 19: 'new'
# 20: 'digest'


# Variable names:
