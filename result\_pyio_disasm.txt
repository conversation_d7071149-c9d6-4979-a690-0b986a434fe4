# Code object from position 7611333
# Filename: _pyio.py
# Name: <module>
# Args: 0
# Locals: 0

   0           0 RESUME                   0

   1           2 LOAD_CONST               0 ('\nPython implementation of the io module.\n')
               4 STORE_NAME               0 (__doc__)

   5           6 LOAD_CONST               1 (0)
               8 LOAD_CONST               2 (None)
              10 IMPORT_NAME              1 (os)
              12 STORE_NAME               1 (os)

   6          14 LOAD_CONST               1 (0)
              16 LOAD_CONST               2 (None)
              18 IMPORT_NAME              2 (abc)
              20 STORE_NAME               2 (abc)

   7          22 LOAD_CONST               1 (0)
              24 LOAD_CONST               2 (None)
              26 IMPORT_NAME              3 (codecs)
              28 STORE_NAME               3 (codecs)

   8          30 LOAD_CONST               1 (0)
              32 LOAD_CONST               2 (None)
              34 IMPORT_NAME              4 (errno)
              36 STORE_NAME               4 (errno)

   9          38 LOAD_CONST               1 (0)
              40 LOAD_CONST               2 (None)
              42 IMPORT_NAME              5 (stat)
              44 STORE_NAME               5 (stat)

  10          46 LOAD_CONST               1 (0)
              48 LOAD_CONST               2 (None)
              50 IMPORT_NAME              6 (sys)
              52 STORE_NAME               6 (sys)

  12          54 LOAD_CONST               1 (0)
              56 LOAD_CONST               3 (('allocate_lock',))
              58 IMPORT_NAME              7 (_thread)
              60 IMPORT_FROM              8 (allocate_lock)
              62 STORE_NAME               9 (Lock)
              64 POP_TOP

  13          66 LOAD_NAME                6 (sys)
              68 LOAD_ATTR               10 (stat)
              88 IMPORT_NAME             11 (msvcrt)
              90 IMPORT_FROM             12 (setmode)
              92 STORE_NAME              13 (_setmode)
              94 POP_TOP
              96 JUMP_FORWARD             2 (to 102)

  16          98 LOAD_CONST               2 (None)
             100 STORE_NAME              13 (_setmode)

  18     >>  102 LOAD_CONST               1 (0)
             104 LOAD_CONST               2 (None)
             106 IMPORT_NAME             14 (io)
             108 STORE_NAME              14 (io)

  19         110 LOAD_CONST               1 (0)
             112 LOAD_CONST               6 (('__all__', 'SEEK_SET', 'SEEK_CUR', 'SEEK_END'))
             114 IMPORT_NAME             14 (io)
             116 IMPORT_FROM             15 (__all__)
             118 STORE_NAME              15 (__all__)
             120 IMPORT_FROM             16 (SEEK_SET)
             122 STORE_NAME              16 (SEEK_SET)
             124 IMPORT_FROM             17 (SEEK_CUR)
             126 STORE_NAME              17 (SEEK_CUR)
             128 IMPORT_FROM             18 (SEEK_END)
             130 STORE_NAME              18 (SEEK_END)
             132 POP_TOP

  21         134 BUILD_SET                0
             136 LOAD_CONST               7 (frozenset({0, 1, 2}))
             138 SET_UPDATE               1
             140 STORE_NAME              19 (valid_seek_flags)

  22         142 PUSH_NULL
             144 LOAD_NAME               20 (hasattr)
             146 LOAD_NAME                1 (os)
             148 LOAD_CONST               8 ('SEEK_HOLE')
             150 UNPACK_SEQUENCE          2
             154 CALL                     2
             162 CACHE
             164 POP_JUMP_IF_FALSE       52 (to 270)

  23         166 LOAD_NAME               19 (valid_seek_flags)
             168 STORE_SUBSCR
             172 CACHE
             174 CACHE
             176 CACHE
             178 CACHE
             180 CACHE
             182 CACHE
             184 CACHE
             186 CACHE
             188 CACHE
             190 LOAD_NAME                1 (os)
             192 LOAD_ATTR               22 (msvcrt)
             212 CACHE
             214 CACHE
             216 POP_TOP

  24         218 LOAD_NAME               19 (valid_seek_flags)
             220 STORE_SUBSCR
             224 CACHE
             226 CACHE
             228 CACHE
             230 CACHE
             232 CACHE
             234 CACHE
             236 CACHE
             238 CACHE
             240 CACHE
             242 LOAD_NAME                1 (os)
             244 LOAD_ATTR               23 (NULL|self + msvcrt)
             264 CACHE
             266 CACHE
             268 POP_TOP

  27     >>  270 LOAD_CONST               9 (8192)
             272 STORE_NAME              24 (DEFAULT_BUFFER_SIZE)

  34         274 LOAD_NAME               25 (BlockingIOError)
             276 STORE_NAME              25 (BlockingIOError)

  38         278 PUSH_NULL
             280 LOAD_NAME               20 (hasattr)
             282 LOAD_NAME                6 (sys)
             284 LOAD_CONST              10 ('gettotalrefcount')
             286 UNPACK_SEQUENCE          2
             290 CALL                     2
             298 CACHE
             300 LOAD_GLOBAL             11 (NULL + stat)
             310 CACHE
             312 CACHE
             314 LOAD_ATTR               27 (NULL|self + _setmode)
             334 MAKE_FUNCTION            1 (defaults)
             336 STORE_NAME              30 (text_encoding)

  76         338 LOAD_NAME               31 (staticmethod)

  77         340 NOP

  78         342 NOP

  77         344 LOAD_CONST              52 (('r', -1, None, None, None, True, None))
             346 LOAD_CONST              16 (<code object open at 0x000001A2D034F970, file "_pyio.py", line 76>)
             348 MAKE_FUNCTION            1 (defaults)

  76         350 UNPACK_SEQUENCE          0
             354 CALL                     0
             362 CACHE

  77         364 STORE_NAME              32 (open)

 284         366 LOAD_CONST              17 (<code object _open_code_with_warning at 0x000001A2D019F800, file "_pyio.py", line 284>)
             368 MAKE_FUNCTION            0
             370 STORE_NAME              33 (_open_code_with_warning)

 300         372 NOP

 301         374 LOAD_NAME               14 (io)
             376 LOAD_ATTR               34 (SEEK_CUR)
             396 POP_JUMP_IF_FALSE        5 (to 408)
             398 POP_TOP

 303         400 LOAD_NAME               33 (_open_code_with_warning)
             402 STORE_NAME              34 (open_code)
             404 POP_EXCEPT
             406 JUMP_FORWARD             4 (to 416)

 302     >>  408 RERAISE                  0
         >>  410 COPY                     3
             412 POP_EXCEPT
             414 RERAISE                  1

 306     >>  416 LOAD_CONST              18 (<code object __getattr__ at 0x000001A2D0112EF0, file "_pyio.py", line 306>)
             418 MAKE_FUNCTION            0
             420 STORE_NAME              36 (__getattr__)

 324         422 NOP

 325         424 LOAD_NAME               14 (io)
             426 LOAD_ATTR               37 (NULL|self + SEEK_END)
             446 POP_JUMP_IF_FALSE       18 (to 484)
             448 POP_TOP

 327         450 PUSH_NULL
             452 LOAD_BUILD_CLASS
             454 LOAD_CONST              19 (<code object UnsupportedOperation at 0x000001A2D05C36A0, file "_pyio.py", line 327>)
             456 MAKE_FUNCTION            0
             458 LOAD_CONST              20 ('UnsupportedOperation')
             460 LOAD_NAME               38 (OSError)
             462 LOAD_NAME               39 (ValueError)
             464 UNPACK_SEQUENCE          4
             468 CALL                     4
             476 CACHE
             478 STORE_NAME              37 (UnsupportedOperation)
             480 POP_EXCEPT
             482 JUMP_FORWARD             4 (to 492)

 326     >>  484 RERAISE                  0
         >>  486 COPY                     3
             488 POP_EXCEPT
             490 RERAISE                  1

 331     >>  492 PUSH_NULL
             494 LOAD_BUILD_CLASS
             496 LOAD_CONST              21 (<code object IOBase at 0x000001A2D056F0A0, file "_pyio.py", line 331>)
             498 MAKE_FUNCTION            0
             500 LOAD_CONST              22 ('IOBase')
             502 LOAD_NAME                2 (abc)
             504 LOAD_ATTR               40 (hasattr)
             524 CACHE
             526 CACHE
             528 CACHE
             530 STORE_NAME              41 (IOBase)

 620         532 LOAD_NAME               14 (io)
             534 LOAD_ATTR               41 (NULL|self + hasattr)
             554 CACHE
             556 CACHE
             558 CACHE
             560 CACHE
             562 CACHE
             564 CACHE
             566 LOAD_NAME               41 (IOBase)
             568 UNPACK_SEQUENCE          1
             572 CALL                     1
             580 CACHE
             582 POP_TOP

 623         584 PUSH_NULL
             586 LOAD_BUILD_CLASS
             588 LOAD_CONST              24 (<code object RawIOBase at 0x000001A2D0631020, file "_pyio.py", line 623>)
             590 MAKE_FUNCTION            0
             592 LOAD_CONST              25 ('RawIOBase')
             594 LOAD_NAME               41 (IOBase)
             596 UNPACK_SEQUENCE          3
             600 CALL                     3
             608 CACHE
             610 STORE_NAME              43 (RawIOBase)

 684         612 LOAD_NAME               14 (io)
             614 LOAD_ATTR               43 (NULL|self + add)
             634 CACHE
             636 CACHE
             638 CACHE
             640 CACHE
             642 CACHE
             644 CACHE
             646 LOAD_NAME               43 (RawIOBase)
             648 UNPACK_SEQUENCE          1
             652 CALL                     1
             660 CACHE
             662 POP_TOP

 685         664 LOAD_CONST               1 (0)
             666 LOAD_CONST              26 (('FileIO',))
             668 IMPORT_NAME             44 (_io)
             670 IMPORT_FROM             45 (FileIO)
             672 STORE_NAME              45 (FileIO)
             674 POP_TOP

 686         676 LOAD_NAME               43 (RawIOBase)
             678 STORE_SUBSCR
             682 CACHE
             684 CACHE
             686 CACHE
             688 CACHE
             690 CACHE
             692 CACHE
             694 CACHE
             696 CACHE
             698 CACHE
             700 LOAD_NAME               45 (FileIO)
             702 UNPACK_SEQUENCE          1
             706 CALL                     1
             714 CACHE
             716 POP_TOP

 689         718 PUSH_NULL
             720 LOAD_BUILD_CLASS
             722 LOAD_CONST              27 (<code object BufferedIOBase at 0x000001A2D061D930, file "_pyio.py", line 689>)
             724 MAKE_FUNCTION            0
             726 LOAD_CONST              28 ('BufferedIOBase')
             728 LOAD_NAME               41 (IOBase)
             730 UNPACK_SEQUENCE          3
             734 CALL                     3
             742 CACHE
             744 STORE_NAME              46 (BufferedIOBase)

 792         746 LOAD_NAME               14 (io)
             748 LOAD_ATTR               46 (SEEK_DATA)
             768 CACHE
             770 CACHE
             772 CACHE
             774 CACHE
             776 CACHE
             778 CACHE
             780 LOAD_NAME               46 (BufferedIOBase)
             782 UNPACK_SEQUENCE          1
             786 CALL                     1
             794 CACHE
             796 POP_TOP

 795         798 PUSH_NULL
             800 LOAD_BUILD_CLASS
             802 LOAD_CONST              29 (<code object _BufferedIOMixin at 0x000001A2D01525B0, file "_pyio.py", line 795>)
             804 MAKE_FUNCTION            0
             806 LOAD_CONST              30 ('_BufferedIOMixin')
             808 LOAD_NAME               46 (BufferedIOBase)
             810 UNPACK_SEQUENCE          3
             814 CALL                     3
             822 CACHE
             824 STORE_NAME              47 (_BufferedIOMixin)

 902         826 PUSH_NULL
             828 LOAD_BUILD_CLASS
             830 LOAD_CONST              31 (<code object BytesIO at 0x000001A2D0634B70, file "_pyio.py", line 902>)
             832 MAKE_FUNCTION            0
             834 LOAD_CONST              32 ('BytesIO')
             836 LOAD_NAME               46 (BufferedIOBase)
             838 UNPACK_SEQUENCE          3
             842 CALL                     3
             850 CACHE
             852 STORE_NAME              48 (BytesIO)

1045         854 PUSH_NULL
             856 LOAD_BUILD_CLASS
             858 LOAD_CONST              33 (<code object BufferedReader at 0x000001A2D0629330, file "_pyio.py", line 1045>)
             860 MAKE_FUNCTION            0
             862 LOAD_CONST              34 ('BufferedReader')
             864 LOAD_NAME               47 (_BufferedIOMixin)
             866 UNPACK_SEQUENCE          3
             870 CALL                     3
             878 CACHE
             880 STORE_NAME              49 (BufferedReader)

1240         882 PUSH_NULL
             884 LOAD_BUILD_CLASS
             886 LOAD_CONST              35 (<code object BufferedWriter at 0x000001A2D0641460, file "_pyio.py", line 1240>)
             888 MAKE_FUNCTION            0
             890 LOAD_CONST              36 ('BufferedWriter')
             892 LOAD_NAME               47 (_BufferedIOMixin)
             894 UNPACK_SEQUENCE          3
             898 CALL                     3
             906 CACHE
             908 STORE_NAME              50 (BufferedWriter)

1345         910 PUSH_NULL
             912 LOAD_BUILD_CLASS
             914 LOAD_CONST              37 (<code object BufferedRWPair at 0x000001A2D0634DF0, file "_pyio.py", line 1345>)
             916 MAKE_FUNCTION            0
             918 LOAD_CONST              38 ('BufferedRWPair')
             920 LOAD_NAME               46 (BufferedIOBase)
             922 UNPACK_SEQUENCE          3
             926 CALL                     3
             934 CACHE
             936 STORE_NAME              51 (BufferedRWPair)

1418         938 PUSH_NULL
             940 LOAD_BUILD_CLASS
             942 LOAD_CONST              39 (<code object BufferedRandom at 0x000001A2D06A5110, file "_pyio.py", line 1418>)
             944 MAKE_FUNCTION            0
             946 LOAD_CONST              40 ('BufferedRandom')
             948 LOAD_NAME               50 (BufferedWriter)
             950 LOAD_NAME               49 (BufferedReader)
             952 UNPACK_SEQUENCE          4
             956 CALL                     4
             964 CACHE
             966 STORE_NAME              52 (BufferedRandom)

1492         968 PUSH_NULL
             970 LOAD_BUILD_CLASS
             972 LOAD_CONST              41 (<code object FileIO at 0x000001A2D010F0F0, file "_pyio.py", line 1492>)
             974 MAKE_FUNCTION            0
             976 LOAD_CONST              42 ('FileIO')
             978 LOAD_NAME               43 (RawIOBase)
             980 UNPACK_SEQUENCE          3
             984 CALL                     3
             992 CACHE
             994 STORE_NAME              45 (FileIO)

1835         996 PUSH_NULL
             998 LOAD_BUILD_CLASS
            1000 LOAD_CONST              43 (<code object TextIOBase at 0x000001A2D0635430, file "_pyio.py", line 1835>)
            1002 MAKE_FUNCTION            0
            1004 LOAD_CONST              44 ('TextIOBase')
            1006 LOAD_NAME               41 (IOBase)
            1008 UNPACK_SEQUENCE          3
            1012 CALL                     3
            1020 CACHE
            1022 STORE_NAME              53 (TextIOBase)

1899        1024 LOAD_NAME               14 (io)
            1026 LOAD_ATTR               53 (NULL|self + flags)
            1046 CACHE
            1048 CACHE
            1050 CACHE
            1052 CACHE
            1054 CACHE
            1056 CACHE
            1058 LOAD_NAME               53 (TextIOBase)
            1060 UNPACK_SEQUENCE          1
            1064 CALL                     1
            1072 CACHE
            1074 POP_TOP

1902        1076 PUSH_NULL
            1078 LOAD_BUILD_CLASS
            1080 LOAD_CONST              45 (<code object IncrementalNewlineDecoder at 0x000001A2D06A5590, file "_pyio.py", line 1902>)
            1082 MAKE_FUNCTION            0
            1084 LOAD_CONST              46 ('IncrementalNewlineDecoder')
            1086 LOAD_NAME                3 (codecs)
            1088 LOAD_ATTR               54 (dev_mode)
            1108 CACHE
            1110 CACHE
            1112 STORE_NAME              55 (IncrementalNewlineDecoder)

1987        1114 PUSH_NULL
            1116 LOAD_BUILD_CLASS
            1118 LOAD_CONST              47 (<code object TextIOWrapper at 0x000001A2D03B1190, file "_pyio.py", line 1987>)
            1120 MAKE_FUNCTION            0
            1122 LOAD_CONST              48 ('TextIOWrapper')
            1124 LOAD_NAME               53 (TextIOBase)
            1126 UNPACK_SEQUENCE          3
            1130 CALL                     3
            1138 CACHE
            1140 STORE_NAME              56 (TextIOWrapper)

2664        1142 PUSH_NULL
            1144 LOAD_BUILD_CLASS
            1146 LOAD_CONST              49 (<code object StringIO at 0x000001A2D062A2A0, file "_pyio.py", line 2664>)
            1148 MAKE_FUNCTION            0
            1150 LOAD_CONST              50 ('StringIO')
            1152 LOAD_NAME               56 (TextIOWrapper)
            1154 UNPACK_SEQUENCE          3
            1158 CALL                     3
            1166 CACHE
            1168 STORE_NAME              57 (StringIO)
            1170 LOAD_CONST               2 (None)
            1172 RETURN_VALUE
ExceptionTable:
  374 to 386 -> 390 [0]
  390 to 402 -> 410 [1] lasti
  408 to 408 -> 410 [1] lasti
  424 to 436 -> 440 [0]
  440 to 478 -> 486 [1] lasti
  484 to 484 -> 486 [1] lasti

Disassembly of <code object text_encoding at 0x000001A2D0112810, file "_pyio.py", line 43>:
 43           0 RESUME                   0

 58           2 LOAD_FAST                0 (encoding)
              4 POP_JUMP_IF_NOT_NONE    74 (to 154)

 59           6 LOAD_GLOBAL              0 (sys)
             16 CACHE
             18 LOAD_ATTR                1 (NULL|self + sys)
             38 POP_JUMP_IF_FALSE        3 (to 46)

 60          40 LOAD_CONST               2 ('utf-8')
             42 STORE_FAST               0 (encoding)
             44 JUMP_FORWARD             2 (to 50)

 62     >>   46 LOAD_CONST               3 ('locale')
             48 STORE_FAST               0 (encoding)

 63     >>   50 LOAD_GLOBAL              0 (sys)
             60 CACHE
             62 LOAD_ATTR                1 (NULL|self + sys)
             82 POP_JUMP_IF_FALSE       35 (to 154)

 64          84 LOAD_CONST               4 (0)
             86 LOAD_CONST               1 (None)
             88 IMPORT_NAME              4 (warnings)
             90 STORE_FAST               2 (warnings)

 65          92 LOAD_FAST                2 (warnings)
             94 STORE_SUBSCR
             98 CACHE
            100 CACHE
            102 CACHE
            104 CACHE
            106 CACHE
            108 CACHE
            110 CACHE
            112 CACHE
            114 CACHE
            116 LOAD_CONST               5 ("'encoding' argument not specified.")

 66         118 LOAD_GLOBAL             12 (EncodingWarning)
            128 CACHE
            130 LOAD_FAST                1 (stacklevel)
            132 LOAD_CONST               6 (1)
            134 BINARY_OP                0 (+)

 65         138 UNPACK_SEQUENCE          3
            142 CALL                     3
            150 CACHE
            152 POP_TOP

 67     >>  154 LOAD_FAST                0 (encoding)
            156 RETURN_VALUE

Disassembly of <code object open at 0x000001A2D034F970, file "_pyio.py", line 76>:
 76           0 RESUME                   0

195           2 LOAD_GLOBAL              1 (NULL + isinstance)
             12 CACHE
             14 LOAD_FAST                0 (file)
             16 LOAD_GLOBAL              2 (int)
             26 CACHE
             28 UNPACK_SEQUENCE          2
             32 CALL                     2
             40 CACHE
             42 POP_JUMP_IF_TRUE        20 (to 84)

196          44 LOAD_GLOBAL              5 (NULL + os)
             54 CACHE
             56 LOAD_ATTR                3 (NULL|self + int)
             76 CACHE
             78 CACHE
             80 CACHE
             82 STORE_FAST               0 (file)

197     >>   84 LOAD_GLOBAL              1 (NULL + isinstance)
             94 CACHE
             96 LOAD_FAST                0 (file)
             98 LOAD_GLOBAL              8 (str)
            108 CACHE
            110 LOAD_GLOBAL             10 (bytes)
            120 CACHE
            122 LOAD_GLOBAL              2 (int)
            132 CACHE
            134 BUILD_TUPLE              3
            136 UNPACK_SEQUENCE          2
            140 CALL                     2
            148 CACHE
            150 POP_JUMP_IF_TRUE        18 (to 188)

198         152 LOAD_GLOBAL             13 (NULL + TypeError)
            162 CACHE
            164 LOAD_CONST               1 ('invalid file: %r')
            166 LOAD_FAST                0 (file)
            168 BINARY_OP                6 (%)
            172 UNPACK_SEQUENCE          1
            176 CALL                     1
            184 CACHE
            186 RAISE_VARARGS            1

199     >>  188 LOAD_GLOBAL              1 (NULL + isinstance)
            198 CACHE
            200 LOAD_FAST                1 (mode)
            202 LOAD_GLOBAL              8 (str)
            212 CACHE
            214 UNPACK_SEQUENCE          2
            218 CALL                     2
            226 CACHE
            228 POP_JUMP_IF_TRUE        18 (to 266)

200         230 LOAD_GLOBAL             13 (NULL + TypeError)
            240 CACHE
            242 LOAD_CONST               2 ('invalid mode: %r')
            244 LOAD_FAST                1 (mode)
            246 BINARY_OP                6 (%)
            250 UNPACK_SEQUENCE          1
            254 CALL                     1
            262 CACHE
            264 RAISE_VARARGS            1

201     >>  266 LOAD_GLOBAL              1 (NULL + isinstance)
            276 CACHE
            278 LOAD_FAST                2 (buffering)
            280 LOAD_GLOBAL              2 (int)
            290 CACHE
            292 UNPACK_SEQUENCE          2
            296 CALL                     2
            304 CACHE
            306 POP_JUMP_IF_TRUE        18 (to 344)

202         308 LOAD_GLOBAL             13 (NULL + TypeError)
            318 CACHE
            320 LOAD_CONST               3 ('invalid buffering: %r')
            322 LOAD_FAST                2 (buffering)
            324 BINARY_OP                6 (%)
            328 UNPACK_SEQUENCE          1
            332 CALL                     1
            340 CACHE
            342 RAISE_VARARGS            1

203     >>  344 LOAD_FAST                3 (encoding)
            346 POP_JUMP_IF_NONE        39 (to 426)
            348 LOAD_GLOBAL              1 (NULL + isinstance)
            358 CACHE
            360 LOAD_FAST                3 (encoding)
            362 LOAD_GLOBAL              8 (str)
            372 CACHE
            374 UNPACK_SEQUENCE          2
            378 CALL                     2
            386 CACHE
            388 POP_JUMP_IF_TRUE        18 (to 426)

204         390 LOAD_GLOBAL             13 (NULL + TypeError)
            400 CACHE
            402 LOAD_CONST               5 ('invalid encoding: %r')
            404 LOAD_FAST                3 (encoding)
            406 BINARY_OP                6 (%)
            410 UNPACK_SEQUENCE          1
            414 CALL                     1
            422 CACHE
            424 RAISE_VARARGS            1

205     >>  426 LOAD_FAST                4 (errors)
            428 POP_JUMP_IF_NONE        39 (to 508)
            430 LOAD_GLOBAL              1 (NULL + isinstance)
            440 CACHE
            442 LOAD_FAST                4 (errors)
            444 LOAD_GLOBAL              8 (str)
            454 CACHE
            456 UNPACK_SEQUENCE          2
            460 CALL                     2
            468 CACHE
            470 POP_JUMP_IF_TRUE        18 (to 508)

206         472 LOAD_GLOBAL             13 (NULL + TypeError)
            482 CACHE
            484 LOAD_CONST               6 ('invalid errors: %r')
            486 LOAD_FAST                4 (errors)
            488 BINARY_OP                6 (%)
            492 UNPACK_SEQUENCE          1
            496 CALL                     1
            504 CACHE
            506 RAISE_VARARGS            1

207     >>  508 LOAD_GLOBAL             15 (NULL + set)
            518 CACHE
            520 LOAD_FAST                1 (mode)
            522 UNPACK_SEQUENCE          1
            526 CALL                     1
            534 CACHE
            536 STORE_FAST               8 (modes)

208         538 LOAD_FAST                8 (modes)
            540 LOAD_GLOBAL             15 (NULL + set)
            550 CACHE
            552 LOAD_CONST               7 ('axrwb+t')
            554 UNPACK_SEQUENCE          1
            558 CALL                     1
            566 CACHE
            568 BINARY_OP               10 (-)
            572 POP_JUMP_IF_TRUE        32 (to 638)
            574 LOAD_GLOBAL             17 (NULL + len)
            584 CACHE
            586 LOAD_FAST                1 (mode)
            588 UNPACK_SEQUENCE          1
            592 CALL                     1
            600 CACHE
            602 LOAD_GLOBAL             17 (NULL + len)
            612 CACHE
            614 LOAD_FAST                8 (modes)
            616 UNPACK_SEQUENCE          1
            620 CALL                     1
            628 CACHE
            630 COMPARE_OP               4 (<)
            634 CACHE
            636 POP_JUMP_IF_FALSE       18 (to 674)

209     >>  638 LOAD_GLOBAL             19 (NULL + ValueError)
            648 CACHE
            650 LOAD_CONST               2 ('invalid mode: %r')
            652 LOAD_FAST                1 (mode)
            654 BINARY_OP                6 (%)
            658 UNPACK_SEQUENCE          1
            662 CALL                     1
            670 CACHE
            672 RAISE_VARARGS            1

210     >>  674 LOAD_CONST               8 ('x')
            676 LOAD_FAST                8 (modes)
            678 CONTAINS_OP              0
            680 STORE_FAST               9 (creating)

211         682 LOAD_CONST               9 ('r')
            684 LOAD_FAST                8 (modes)
            686 CONTAINS_OP              0
            688 STORE_FAST              10 (reading)

212         690 LOAD_CONST              10 ('w')
            692 LOAD_FAST                8 (modes)
            694 CONTAINS_OP              0
            696 STORE_FAST              11 (writing)

213         698 LOAD_CONST              11 ('a')
            700 LOAD_FAST                8 (modes)
            702 CONTAINS_OP              0
            704 STORE_FAST              12 (appending)

214         706 LOAD_CONST              12 ('+')
            708 LOAD_FAST                8 (modes)
            710 CONTAINS_OP              0
            712 STORE_FAST              13 (updating)

215         714 LOAD_CONST              13 ('t')
            716 LOAD_FAST                8 (modes)
            718 CONTAINS_OP              0
            720 STORE_FAST              14 (text)

216         722 LOAD_CONST              14 ('b')
            724 LOAD_FAST                8 (modes)
            726 CONTAINS_OP              0
            728 STORE_FAST              15 (binary)

217         730 LOAD_FAST               14 (text)
            732 POP_JUMP_IF_FALSE       17 (to 768)
            734 LOAD_FAST               15 (binary)
            736 POP_JUMP_IF_FALSE       15 (to 768)

218         738 LOAD_GLOBAL             19 (NULL + ValueError)
            748 CACHE
            750 LOAD_CONST              15 ("can't have text and binary mode at once")
            752 UNPACK_SEQUENCE          1
            756 CALL                     1
            764 CACHE
            766 RAISE_VARARGS            1

219     >>  768 LOAD_FAST                9 (creating)
            770 LOAD_FAST               10 (reading)
            772 BINARY_OP                0 (+)
            776 LOAD_FAST               11 (writing)
            778 BINARY_OP                0 (+)
            782 LOAD_FAST               12 (appending)
            784 BINARY_OP                0 (+)
            788 LOAD_CONST              16 (1)
            790 COMPARE_OP               4 (<)
            794 CACHE
            796 POP_JUMP_IF_FALSE       15 (to 828)

220         798 LOAD_GLOBAL             19 (NULL + ValueError)
            808 CACHE
            810 LOAD_CONST              17 ("can't have read/write/append mode at once")
            812 UNPACK_SEQUENCE          1
            816 CALL                     1
            824 CACHE
            826 RAISE_VARARGS            1

221     >>  828 LOAD_FAST                9 (creating)
            830 POP_JUMP_IF_TRUE        21 (to 874)
            832 LOAD_FAST               10 (reading)
            834 POP_JUMP_IF_TRUE        19 (to 874)
            836 LOAD_FAST               11 (writing)
            838 POP_JUMP_IF_TRUE        17 (to 874)
            840 LOAD_FAST               12 (appending)
            842 POP_JUMP_IF_TRUE        15 (to 874)

222         844 LOAD_GLOBAL             19 (NULL + ValueError)
            854 CACHE
            856 LOAD_CONST              18 ('must have exactly one of read/write/append mode')
            858 UNPACK_SEQUENCE          1
            862 CALL                     1
            870 CACHE
            872 RAISE_VARARGS            1

223     >>  874 LOAD_FAST               15 (binary)
            876 POP_JUMP_IF_FALSE       17 (to 912)
            878 LOAD_FAST                3 (encoding)
            880 POP_JUMP_IF_NONE        15 (to 912)

224         882 LOAD_GLOBAL             19 (NULL + ValueError)
            892 CACHE
            894 LOAD_CONST              19 ("binary mode doesn't take an encoding argument")
            896 UNPACK_SEQUENCE          1
            900 CALL                     1
            908 CACHE
            910 RAISE_VARARGS            1

225     >>  912 LOAD_FAST               15 (binary)
            914 POP_JUMP_IF_FALSE       17 (to 950)
            916 LOAD_FAST                4 (errors)
            918 POP_JUMP_IF_NONE        15 (to 950)

226         920 LOAD_GLOBAL             19 (NULL + ValueError)
            930 CACHE
            932 LOAD_CONST              20 ("binary mode doesn't take an errors argument")
            934 UNPACK_SEQUENCE          1
            938 CALL                     1
            946 CACHE
            948 RAISE_VARARGS            1

227     >>  950 LOAD_FAST               15 (binary)
            952 POP_JUMP_IF_FALSE       17 (to 988)
            954 LOAD_FAST                5 (newline)
            956 POP_JUMP_IF_NONE        15 (to 988)

228         958 LOAD_GLOBAL             19 (NULL + ValueError)
            968 CACHE
            970 LOAD_CONST              21 ("binary mode doesn't take a newline argument")
            972 UNPACK_SEQUENCE          1
            976 CALL                     1
            984 CACHE
            986 RAISE_VARARGS            1

229     >>  988 LOAD_FAST               15 (binary)
            990 POP_JUMP_IF_FALSE       38 (to 1068)
            992 LOAD_FAST                2 (buffering)
            994 LOAD_CONST              16 (1)
            996 COMPARE_OP               2 (<)
           1000 CACHE
           1002 POP_JUMP_IF_FALSE       32 (to 1068)

230        1004 LOAD_CONST              22 (0)
           1006 LOAD_CONST               4 (None)
           1008 IMPORT_NAME             10 (warnings)
           1010 STORE_FAST              16 (warnings)

231        1012 LOAD_FAST               16 (warnings)
           1014 STORE_SUBSCR
           1018 CACHE
           1020 CACHE
           1022 CACHE
           1024 CACHE
           1026 CACHE
           1028 CACHE
           1030 CACHE
           1032 CACHE
           1034 CACHE
           1036 LOAD_CONST              23 ("line buffering (buffering=1) isn't supported in binary mode, the default buffer size will be used")

233        1038 LOAD_GLOBAL             24 (RuntimeWarning)
           1048 CACHE
           1050 LOAD_CONST              24 (2)

231        1052 UNPACK_SEQUENCE          3
           1056 CALL                     3
           1064 CACHE
           1066 POP_TOP

234     >> 1068 LOAD_GLOBAL             27 (NULL + FileIO)
           1078 CACHE
           1080 LOAD_FAST                0 (file)

235        1082 LOAD_FAST                9 (creating)
           1084 POP_JUMP_IF_FALSE        2 (to 1090)
           1086 LOAD_CONST               8 ('x')
           1088 LOAD_GLOBAL              1 (NULL + isinstance)
           1098 LOAD_GLOBAL              1 (NULL + isinstance)
           1108 POP_JUMP_IF_FALSE        2 (to 1114)
           1110 LOAD_CONST              10 ('w')
           1112 LOAD_GLOBAL              1 (NULL + isinstance)
           1122 POP_JUMP_IF_FALSE        2 (to 1128)
           1124 LOAD_CONST              11 ('a')
           1126 LOAD_GLOBAL              1 (NULL + isinstance)
           1136 POP_JUMP_IF_FALSE        2 (to 1142)
           1138 LOAD_CONST              12 ('+')
           1140 LOAD_GLOBAL              1 (NULL + isinstance)
           1150 LOAD_FAST                7 (opener)

234        1152 KW_NAMES                26 (('opener',))
           1154 UNPACK_SEQUENCE          4
           1158 CALL                     4
           1166 CACHE
           1168 STORE_FAST              17 (raw)

241        1170 LOAD_FAST               17 (raw)
           1172 STORE_FAST              18 (result)

242        1174 NOP

243        1176 LOAD_CONST              27 (False)
           1178 STORE_FAST              19 (line_buffering)

244        1180 LOAD_FAST                2 (buffering)
           1182 LOAD_CONST              16 (1)
           1184 COMPARE_OP               2 (<)
           1188 CACHE
           1190 POP_JUMP_IF_TRUE        26 (to 1244)
           1192 LOAD_FAST                2 (buffering)
           1194 LOAD_CONST              22 (0)
           1196 COMPARE_OP               0 (<)
           1200 CACHE
           1202 POP_JUMP_IF_FALSE       24 (to 1252)
           1204 LOAD_FAST               17 (raw)
           1206 STORE_SUBSCR
           1210 CACHE
           1212 CACHE
           1214 CACHE
           1216 CACHE
           1218 CACHE
           1220 CACHE
           1222 CACHE
           1224 CACHE
           1226 CACHE
           1228 UNPACK_SEQUENCE          0
           1232 CALL                     0
           1240 CACHE
           1242 POP_JUMP_IF_FALSE        4 (to 1252)

245     >> 1244 LOAD_CONST              28 (-1)
           1246 STORE_FAST               2 (buffering)

246        1248 LOAD_CONST              29 (True)
           1250 STORE_FAST              19 (line_buffering)

247     >> 1252 LOAD_FAST                2 (buffering)
           1254 LOAD_CONST              22 (0)
           1256 COMPARE_OP               0 (<)
           1260 CACHE
           1262 POP_JUMP_IF_FALSE       83 (to 1430)

248        1264 LOAD_GLOBAL             30 (DEFAULT_BUFFER_SIZE)
           1274 CACHE
           1276 STORE_FAST               2 (buffering)

249        1278 NOP

250        1280 LOAD_GLOBAL              5 (NULL + os)
           1290 CACHE
           1292 LOAD_ATTR               16 (len)
           1312 CACHE
           1314 CACHE
           1316 CACHE
           1318 CACHE
           1320 CACHE
           1322 CACHE
           1324 CACHE
           1326 UNPACK_SEQUENCE          0
           1330 CALL                     0
           1338 CACHE
           1340 UNPACK_SEQUENCE          1
           1344 CALL                     1
           1352 CACHE
           1354 LOAD_ATTR               18 (ValueError)
           1374 CACHE
           1376 POP_JUMP_IF_FALSE        2 (to 1382)

255        1378 LOAD_FAST               20 (bs)
           1380 STORE_FAST               2 (buffering)
        >> 1382 JUMP_FORWARD            23 (to 1430)
        >> 1384 PUSH_EXC_INFO

251        1386 LOAD_GLOBAL             38 (OSError)
           1396 CACHE
           1398 LOAD_GLOBAL             40 (AttributeError)
           1408 CACHE
           1410 BUILD_TUPLE              2
           1412 CHECK_EXC_MATCH
           1414 POP_JUMP_IF_FALSE        3 (to 1422)
           1416 POP_TOP

252        1418 POP_EXCEPT
           1420 JUMP_FORWARD             4 (to 1430)

251     >> 1422 RERAISE                  0
        >> 1424 COPY                     3
           1426 POP_EXCEPT
           1428 RERAISE                  1

256     >> 1430 LOAD_FAST                2 (buffering)
           1432 LOAD_CONST              22 (0)
           1434 COMPARE_OP               0 (<)
           1438 CACHE
           1440 POP_JUMP_IF_FALSE       15 (to 1472)

257        1442 LOAD_GLOBAL             19 (NULL + ValueError)
           1452 CACHE
           1454 LOAD_CONST              30 ('invalid buffering size')
           1456 UNPACK_SEQUENCE          1
           1460 CALL                     1
           1468 CACHE
           1470 RAISE_VARARGS            1

258     >> 1472 LOAD_FAST                2 (buffering)
           1474 LOAD_CONST              22 (0)
           1476 COMPARE_OP               2 (<)
           1480 CACHE
           1482 POP_JUMP_IF_FALSE       19 (to 1522)

259        1484 LOAD_FAST               15 (binary)
           1486 POP_JUMP_IF_FALSE        2 (to 1492)

260        1488 LOAD_FAST               18 (result)
           1490 RETURN_VALUE

261     >> 1492 LOAD_GLOBAL             19 (NULL + ValueError)
           1502 CACHE
           1504 LOAD_CONST              31 ("can't have unbuffered text I/O")
           1506 UNPACK_SEQUENCE          1
           1510 CALL                     1
           1518 CACHE
           1520 RAISE_VARARGS            1

262     >> 1522 LOAD_FAST               13 (updating)
           1524 POP_JUMP_IF_FALSE       17 (to 1560)

263        1526 LOAD_GLOBAL             43 (NULL + BufferedRandom)
           1536 CACHE
           1538 LOAD_FAST               17 (raw)
           1540 LOAD_FAST                2 (buffering)
           1542 UNPACK_SEQUENCE          2
           1546 CALL                     2
           1554 CACHE
           1556 STORE_FAST              21 (buffer)
           1558 JUMP_FORWARD            60 (to 1680)

264     >> 1560 LOAD_FAST                9 (creating)
           1562 POP_JUMP_IF_TRUE         4 (to 1572)
           1564 LOAD_FAST               11 (writing)
           1566 POP_JUMP_IF_TRUE         2 (to 1572)
           1568 LOAD_FAST               12 (appending)
           1570 POP_JUMP_IF_FALSE       17 (to 1606)

265     >> 1572 LOAD_GLOBAL             45 (NULL + BufferedWriter)
           1582 CACHE
           1584 LOAD_FAST               17 (raw)
           1586 LOAD_FAST                2 (buffering)
           1588 UNPACK_SEQUENCE          2
           1592 CALL                     2
           1600 CACHE
           1602 STORE_FAST              21 (buffer)
           1604 JUMP_FORWARD            37 (to 1680)

266     >> 1606 LOAD_FAST               10 (reading)
           1608 POP_JUMP_IF_FALSE       17 (to 1644)

267        1610 LOAD_GLOBAL             47 (NULL + BufferedReader)
           1620 CACHE
           1622 LOAD_FAST               17 (raw)
           1624 LOAD_FAST                2 (buffering)
           1626 UNPACK_SEQUENCE          2
           1630 CALL                     2
           1638 CACHE
           1640 STORE_FAST              21 (buffer)
           1642 JUMP_FORWARD            18 (to 1680)

269     >> 1644 LOAD_GLOBAL             19 (NULL + ValueError)
           1654 CACHE
           1656 LOAD_CONST              32 ('unknown mode: %r')
           1658 LOAD_FAST                1 (mode)
           1660 BINARY_OP                6 (%)
           1664 UNPACK_SEQUENCE          1
           1668 CALL                     1
           1676 CACHE
           1678 RAISE_VARARGS            1

270     >> 1680 LOAD_FAST               21 (buffer)
           1682 STORE_FAST              18 (result)

271        1684 LOAD_FAST               15 (binary)
           1686 POP_JUMP_IF_FALSE        2 (to 1692)

272        1688 LOAD_FAST               18 (result)
           1690 RETURN_VALUE

273     >> 1692 LOAD_GLOBAL             49 (NULL + text_encoding)
           1702 CACHE
           1704 LOAD_FAST                3 (encoding)
           1706 UNPACK_SEQUENCE          1
           1710 CALL                     1
           1718 CACHE
           1720 STORE_FAST               3 (encoding)

274        1722 LOAD_GLOBAL             51 (NULL + TextIOWrapper)
           1732 CACHE
           1734 LOAD_FAST               21 (buffer)
           1736 LOAD_FAST                3 (encoding)
           1738 LOAD_FAST                4 (errors)
           1740 LOAD_FAST                5 (newline)
           1742 LOAD_FAST               19 (line_buffering)
           1744 UNPACK_SEQUENCE          5
           1748 CALL                     5
           1756 CACHE
           1758 STORE_FAST              14 (text)

275        1760 LOAD_FAST               14 (text)
           1762 STORE_FAST              18 (result)

276        1764 LOAD_FAST                1 (mode)
           1766 LOAD_FAST               14 (text)
           1768 STORE_ATTR              26 (mode)

277        1778 LOAD_FAST               18 (result)
           1780 RETURN_VALUE
        >> 1782 PUSH_EXC_INFO

278        1784 POP_TOP

279        1786 LOAD_FAST               18 (result)
           1788 STORE_SUBSCR
           1792 CACHE
           1794 CACHE
           1796 CACHE
           1798 CACHE
           1800 CACHE
           1802 CACHE
           1804 CACHE
           1806 CACHE
           1808 CACHE
           1810 UNPACK_SEQUENCE          0
           1814 CALL                     0
           1822 CACHE
           1824 POP_TOP

280        1826 RAISE_VARARGS            0
        >> 1828 COPY                     3
           1830 POP_EXCEPT
           1832 RERAISE                  1
ExceptionTable:
  1176 to 1276 -> 1782 [0]
  1280 to 1364 -> 1384 [0]
  1366 to 1382 -> 1782 [0]
  1384 to 1416 -> 1424 [1] lasti
  1418 to 1420 -> 1782 [0]
  1422 to 1422 -> 1424 [1] lasti
  1424 to 1488 -> 1782 [0]
  1492 to 1688 -> 1782 [0]
  1692 to 1778 -> 1782 [0]
  1782 to 1826 -> 1828 [1] lasti

Disassembly of <code object _open_code_with_warning at 0x000001A2D019F800, file "_pyio.py", line 284>:
284           0 RESUME                   0

295           2 LOAD_CONST               1 (0)
              4 LOAD_CONST               2 (None)
              6 IMPORT_NAME              0 (warnings)
              8 STORE_FAST               1 (warnings)

296          10 LOAD_FAST                1 (warnings)
             12 STORE_SUBSCR
             16 CACHE
             18 CACHE
             20 CACHE
             22 CACHE
             24 CACHE
             26 CACHE
             28 CACHE
             30 CACHE
             32 CACHE
             34 LOAD_CONST               3 ('_pyio.open_code() may not be using hooks')

297          36 LOAD_GLOBAL              4 (RuntimeWarning)
             46 CACHE
             48 LOAD_CONST               4 (2)

296          50 UNPACK_SEQUENCE          3
             54 CALL                     3
             62 CACHE
             64 POP_TOP

298          66 LOAD_GLOBAL              7 (NULL + open)
             76 CACHE
             78 LOAD_FAST                0 (path)
             80 LOAD_CONST               5 ('rb')
             82 UNPACK_SEQUENCE          2
             86 CALL                     2
             94 CACHE
             96 RETURN_VALUE

Disassembly of <code object __getattr__ at 0x000001A2D0112EF0, file "_pyio.py", line 306>:
306           0 RESUME                   0

307           2 LOAD_FAST                0 (name)
              4 LOAD_CONST               1 ('OpenWrapper')
              6 COMPARE_OP               2 (<)
             10 CACHE
             12 POP_JUMP_IF_FALSE       47 (to 108)

313          14 LOAD_CONST               2 (0)
             16 LOAD_CONST               0 (None)
             18 IMPORT_NAME              0 (warnings)
             20 STORE_FAST               1 (warnings)

314          22 LOAD_FAST                1 (warnings)
             24 STORE_SUBSCR
             28 CACHE
             30 CACHE
             32 CACHE
             34 CACHE
             36 CACHE
             38 CACHE
             40 CACHE
             42 CACHE
             44 CACHE
             46 LOAD_CONST               3 ('OpenWrapper is deprecated, use open instead')

315          48 LOAD_GLOBAL              4 (DeprecationWarning)
             58 CACHE
             60 LOAD_CONST               4 (2)

314          62 KW_NAMES                 5 (('stacklevel',))
             64 UNPACK_SEQUENCE          3
             68 CALL                     3
             76 CACHE
             78 POP_TOP

317          80 LOAD_GLOBAL              6 (open)
             90 CACHE
             92 STORE_GLOBAL             4 (OpenWrapper)

318          94 LOAD_GLOBAL              8 (OpenWrapper)
            104 CACHE
            106 RETURN_VALUE

319     >>  108 LOAD_GLOBAL             11 (NULL + AttributeError)
            118 CACHE
            120 LOAD_CONST               6 ('module ')
            122 LOAD_GLOBAL             12 (__name__)
            132 CACHE
            134 FORMAT_VALUE             2 (repr)
            136 LOAD_CONST               7 (' has no attribute ')
            138 LOAD_FAST                0 (name)
            140 FORMAT_VALUE             2 (repr)
            142 BUILD_STRING             4
            144 UNPACK_SEQUENCE          1
            148 CALL                     1
            156 CACHE
            158 RAISE_VARARGS            1

Disassembly of <code object UnsupportedOperation at 0x000001A2D05C36A0, file "_pyio.py", line 327>:
327           0 RESUME                   0
              2 LOAD_NAME                0 (__name__)
              4 STORE_NAME               1 (__module__)
              6 LOAD_CONST               0 ('UnsupportedOperation')
              8 STORE_NAME               2 (__qualname__)

328          10 LOAD_CONST               1 (None)
             12 RETURN_VALUE

Disassembly of <code object IOBase at 0x000001A2D056F0A0, file "_pyio.py", line 331>:
331           0 RESUME                   0
              2 LOAD_NAME                0 (__name__)
              4 STORE_NAME               1 (__module__)
              6 LOAD_CONST               0 ('IOBase')
              8 STORE_NAME               2 (__qualname__)

333          10 LOAD_CONST               1 ("The abstract base class for all I/O classes.\n\n    This class provides dummy implementations for many methods that\n    derived classes can override selectively; the default implementations\n    represent a file that cannot be read, written or seeked.\n\n    Even though IOBase does not declare read or write because\n    their signatures will vary, implementations and clients should\n    consider those methods part of the interface. Also, implementations\n    may raise UnsupportedOperation when operations they do not support are\n    called.\n\n    The basic type used for binary data read from or written to a file is\n    bytes. Other bytes-like objects are accepted as method arguments too.\n    Text I/O classes work with str data.\n\n    Note that calling any method (even inquiries) on a closed stream is\n    undefined. Implementations may raise OSError in this case.\n\n    IOBase (and its subclasses) support the iterator protocol, meaning\n    that an IOBase object can be iterated over yielding the lines in a\n    stream.\n\n    IOBase also supports the :keyword:`with` statement. In this example,\n    fp is closed after the suite of the with statement is complete:\n\n    with open('spam.txt', 'r') as fp:\n        fp.write('Spam and eggs!')\n    ")
             12 STORE_NAME               3 (__doc__)

365          14 LOAD_CONST               2 (<code object _unsupported at 0x000001A2D059BC30, file "_pyio.py", line 365>)
             16 MAKE_FUNCTION            0
             18 STORE_NAME               4 (_unsupported)

372          20 LOAD_CONST              30 ((0,))
             22 LOAD_CONST               4 (<code object seek at 0x000001A2D05CAD30, file "_pyio.py", line 372>)
             24 MAKE_FUNCTION            1 (defaults)
             26 STORE_NAME               5 (seek)

388          28 LOAD_CONST               5 (<code object tell at 0x000001A2D05CAE20, file "_pyio.py", line 388>)
             30 MAKE_FUNCTION            0
             32 STORE_NAME               6 (tell)

392          34 LOAD_CONST              31 ((None,))
             36 LOAD_CONST               7 (<code object truncate at 0x000001A2D06305D0, file "_pyio.py", line 392>)
             38 MAKE_FUNCTION            1 (defaults)
             40 STORE_NAME               7 (truncate)

402          42 LOAD_CONST               8 (<code object flush at 0x000001A2D06306C0, file "_pyio.py", line 402>)
             44 MAKE_FUNCTION            0
             46 STORE_NAME               8 (flush)

410          48 LOAD_CONST               9 (False)
             50 STORE_NAME               9 (_IOBase__closed)

412          52 LOAD_CONST              10 (<code object close at 0x000001A2D0628620, file "_pyio.py", line 412>)
             54 MAKE_FUNCTION            0
             56 STORE_NAME              10 (close)

423          58 LOAD_CONST              11 (<code object __del__ at 0x000001A2D01513B0, file "_pyio.py", line 423>)
             60 MAKE_FUNCTION            0
             62 STORE_NAME              11 (__del__)

450          64 LOAD_CONST              12 (<code object seekable at 0x000001A2D05C3770, file "_pyio.py", line 450>)
             66 MAKE_FUNCTION            0
             68 STORE_NAME              12 (seekable)

458          70 LOAD_CONST              31 ((None,))
             72 LOAD_CONST              13 (<code object _checkSeekable at 0x000001A2D05F1C50, file "_pyio.py", line 458>)
             74 MAKE_FUNCTION            1 (defaults)
             76 STORE_NAME              13 (_checkSeekable)

465          78 LOAD_CONST              14 (<code object readable at 0x000001A2D05C3840, file "_pyio.py", line 465>)
             80 MAKE_FUNCTION            0
             82 STORE_NAME              14 (readable)

472          84 LOAD_CONST              31 ((None,))
             86 LOAD_CONST              15 (<code object _checkReadable at 0x000001A2D05F1FB0, file "_pyio.py", line 472>)
             88 MAKE_FUNCTION            1 (defaults)
             90 STORE_NAME              15 (_checkReadable)

479          92 LOAD_CONST              16 (<code object writable at 0x000001A2D05C3910, file "_pyio.py", line 479>)
             94 MAKE_FUNCTION            0
             96 STORE_NAME              16 (writable)

486          98 LOAD_CONST              31 ((None,))
            100 LOAD_CONST              17 (<code object _checkWritable at 0x000001A2D05F2430, file "_pyio.py", line 486>)
            102 MAKE_FUNCTION            1 (defaults)
            104 STORE_NAME              17 (_checkWritable)

493         106 LOAD_NAME               18 (property)

494         108 LOAD_CONST              18 (<code object closed at 0x000001A2D05C39E0, file "_pyio.py", line 493>)
            110 MAKE_FUNCTION            0

493         112 UNPACK_SEQUENCE          0
            116 CALL                     0
            124 CACHE

494         126 STORE_NAME              19 (closed)

501         128 LOAD_CONST              31 ((None,))
            130 LOAD_CONST              19 (<code object _checkClosed at 0x000001A2D061C030, file "_pyio.py", line 501>)
            132 MAKE_FUNCTION            1 (defaults)
            134 STORE_NAME              20 (_checkClosed)

510         136 LOAD_CONST              20 (<code object __enter__ at 0x000001A2D06308A0, file "_pyio.py", line 510>)
            138 MAKE_FUNCTION            0
            140 STORE_NAME              21 (__enter__)

515         142 LOAD_CONST              21 (<code object __exit__ at 0x000001A2D0630990, file "_pyio.py", line 515>)
            144 MAKE_FUNCTION            0
            146 STORE_NAME              22 (__exit__)

523         148 LOAD_CONST              22 (<code object fileno at 0x000001A2D0630A80, file "_pyio.py", line 523>)
            150 MAKE_FUNCTION            0
            152 STORE_NAME              23 (fileno)

530         154 LOAD_CONST              23 (<code object isatty at 0x000001A2D0630B70, file "_pyio.py", line 530>)
            156 MAKE_FUNCTION            0
            158 STORE_NAME              24 (isatty)

540         160 LOAD_CONST              32 ((-1,))
            162 LOAD_CONST              25 (<code object readline at 0x000001A2CDEA5A40, file "_pyio.py", line 540>)
            164 MAKE_FUNCTION            1 (defaults)
            166 STORE_NAME              25 (readline)

582         168 LOAD_CONST              26 (<code object __iter__ at 0x000001A2D0630C60, file "_pyio.py", line 582>)
            170 MAKE_FUNCTION            0
            172 STORE_NAME              26 (__iter__)

586         174 LOAD_CONST              27 (<code object __next__ at 0x000001A2D061C230, file "_pyio.py", line 586>)
            176 MAKE_FUNCTION            0
            178 STORE_NAME              27 (__next__)

592         180 LOAD_CONST              31 ((None,))
            182 LOAD_CONST              28 (<code object readlines at 0x000001A2D01CE420, file "_pyio.py", line 592>)
            184 MAKE_FUNCTION            1 (defaults)
            186 STORE_NAME              28 (readlines)

610         188 LOAD_CONST              29 (<code object writelines at 0x000001A2D0628880, file "_pyio.py", line 610>)
            190 MAKE_FUNCTION            0
            192 STORE_NAME              29 (writelines)
            194 LOAD_CONST               6 (None)
            196 RETURN_VALUE

Disassembly of <code object _unsupported at 0x000001A2D059BC30, file "_pyio.py", line 365>:
365           0 RESUME                   0

367           2 LOAD_GLOBAL              1 (NULL + UnsupportedOperation)
             12 CACHE

368          14 LOAD_FAST                0 (self)
             16 LOAD_ATTR                1 (NULL|self + UnsupportedOperation)
             36 FORMAT_VALUE             1 (str)
             38 LOAD_CONST               1 ('.')
             40 LOAD_FAST                1 (name)
             42 FORMAT_VALUE             1 (str)
             44 LOAD_CONST               2 ('() not supported')

367          46 BUILD_STRING             4
             48 UNPACK_SEQUENCE          1
             52 CALL                     1
             60 CACHE
             62 RAISE_VARARGS            1

Disassembly of <code object seek at 0x000001A2D05CAD30, file "_pyio.py", line 372>:
372           0 RESUME                   0

386           2 LOAD_FAST                0 (self)
              4 STORE_SUBSCR
              8 CACHE
             10 CACHE
             12 CACHE
             14 CACHE
             16 CACHE
             18 CACHE
             20 CACHE
             22 CACHE
             24 CACHE
             26 LOAD_CONST               1 ('seek')
             28 UNPACK_SEQUENCE          1
             32 CALL                     1
             40 CACHE
             42 POP_TOP
             44 LOAD_CONST               2 (None)
             46 RETURN_VALUE

Disassembly of <code object tell at 0x000001A2D05CAE20, file "_pyio.py", line 388>:
388           0 RESUME                   0

390           2 LOAD_FAST                0 (self)
              4 STORE_SUBSCR
              8 CACHE
             10 CACHE
             12 CACHE
             14 CACHE
             16 CACHE
             18 CACHE
             20 CACHE
             22 CACHE
             24 CACHE
             26 LOAD_CONST               1 (0)
             28 LOAD_CONST               2 (1)
             30 UNPACK_SEQUENCE          2
             34 CALL                     2
             42 CACHE
             44 RETURN_VALUE

Disassembly of <code object truncate at 0x000001A2D06305D0, file "_pyio.py", line 392>:
392           0 RESUME                   0

398           2 LOAD_FAST                0 (self)
              4 STORE_SUBSCR
              8 CACHE
             10 CACHE
             12 CACHE
             14 CACHE
             16 CACHE
             18 CACHE
             20 CACHE
             22 CACHE
             24 CACHE
             26 LOAD_CONST               1 ('truncate')
             28 UNPACK_SEQUENCE          1
             32 CALL                     1
             40 CACHE
             42 POP_TOP
             44 LOAD_CONST               2 (None)
             46 RETURN_VALUE

Disassembly of <code object flush at 0x000001A2D06306C0, file "_pyio.py", line 402>:
402           0 RESUME                   0

407           2 LOAD_FAST                0 (self)
              4 STORE_SUBSCR
              8 CACHE
             10 CACHE
             12 CACHE
             14 CACHE
             16 CACHE
             18 CACHE
             20 CACHE
             22 CACHE
             24 CACHE
             26 UNPACK_SEQUENCE          0
             30 CALL                     0
             38 CACHE
             40 POP_TOP
             42 LOAD_CONST               1 (None)
             44 RETURN_VALUE

Disassembly of <code object close at 0x000001A2D0628620, file "_pyio.py", line 412>:
412           0 RESUME                   0

417           2 LOAD_FAST                0 (self)
              4 LOAD_ATTR                0 (_IOBase__closed)
             24 CACHE
             26 CACHE
             28 CACHE
             30 CACHE
             32 CACHE
             34 CACHE
             36 CACHE
             38 CACHE
             40 CACHE
             42 UNPACK_SEQUENCE          0
             46 CALL                     0
             54 CACHE
             56 POP_TOP

421          58 LOAD_CONST               1 (True)
             60 LOAD_FAST                0 (self)
             62 STORE_ATTR               0 (_IOBase__closed)
             72 LOAD_CONST               2 (None)
             74 RETURN_VALUE
        >>   76 PUSH_EXC_INFO
             78 LOAD_CONST               1 (True)
             80 LOAD_FAST                0 (self)
             82 STORE_ATTR               0 (_IOBase__closed)
             92 RERAISE                  0
        >>   94 COPY                     3
             96 POP_EXCEPT
             98 RERAISE                  1

417         100 LOAD_CONST               2 (None)
            102 RETURN_VALUE
ExceptionTable:
  18 to 56 -> 76 [0]
  76 to 92 -> 94 [1] lasti

Disassembly of <code object __del__ at 0x000001A2D01513B0, file "_pyio.py", line 423>:
423           0 RESUME                   0

425           2 NOP

426           4 LOAD_FAST                0 (self)
              6 LOAD_ATTR                0 (closed)
             26 CACHE
             28 CACHE
             30 CACHE
             32 CACHE
             34 CHECK_EXC_MATCH
             36 POP_JUMP_IF_FALSE        4 (to 46)
             38 POP_TOP

430          40 POP_EXCEPT
             42 LOAD_CONST               1 (None)
             44 RETURN_VALUE

427     >>   46 RERAISE                  0
        >>   48 COPY                     3
             50 POP_EXCEPT
             52 RERAISE                  1

432          54 LOAD_FAST                1 (closed)
             56 POP_JUMP_IF_FALSE        2 (to 62)

433          58 LOAD_CONST               1 (None)
             60 RETURN_VALUE

435     >>   62 LOAD_GLOBAL              4 (_IOBASE_EMITS_UNRAISABLE)
             72 CACHE
             74 POP_JUMP_IF_FALSE       22 (to 120)

436          76 LOAD_FAST                0 (self)
             78 STORE_SUBSCR
             82 CACHE
             84 CACHE
             86 CACHE
             88 CACHE
             90 CACHE
             92 CACHE
             94 CACHE
             96 CACHE
             98 CACHE
            100 UNPACK_SEQUENCE          0
            104 CALL                     0
            112 CACHE
            114 POP_TOP
            116 LOAD_CONST               1 (None)
            118 RETURN_VALUE

443     >>  120 NOP

444         122 LOAD_FAST                0 (self)
            124 STORE_SUBSCR
            128 CACHE
            130 CACHE
            132 CACHE
            134 CACHE
            136 CACHE
            138 CACHE
            140 CACHE
            142 CACHE
            144 CACHE
            146 UNPACK_SEQUENCE          0
            150 CALL                     0
            158 CACHE
            160 POP_TOP
            162 LOAD_CONST               1 (None)
            164 RETURN_VALUE
        >>  166 PUSH_EXC_INFO

445         168 POP_TOP

446         170 POP_EXCEPT
            172 LOAD_CONST               1 (None)
            174 RETURN_VALUE
        >>  176 COPY                     3
            178 POP_EXCEPT
            180 RERAISE                  1
ExceptionTable:
  4 to 16 -> 20 [0]
  20 to 38 -> 48 [1] lasti
  46 to 46 -> 48 [1] lasti
  122 to 160 -> 166 [0]
  166 to 168 -> 176 [1] lasti

Disassembly of <code object seekable at 0x000001A2D05C3770, file "_pyio.py", line 450>:
450           0 RESUME                   0

456           2 LOAD_CONST               1 (False)
              4 RETURN_VALUE

Disassembly of <code object _checkSeekable at 0x000001A2D05F1C50, file "_pyio.py", line 458>:
458           0 RESUME                   0

461           2 LOAD_FAST                0 (self)
              4 STORE_SUBSCR
              8 CACHE
             10 CACHE
             12 CACHE
             14 CACHE
             16 CACHE
             18 CACHE
             20 CACHE
             22 CACHE
             24 CACHE
             26 UNPACK_SEQUENCE          0
             30 CALL                     0
             38 CACHE
             40 POP_JUMP_IF_TRUE        19 (to 80)

462          42 LOAD_GLOBAL              3 (NULL + UnsupportedOperation)
             52 CACHE

463          54 LOAD_FAST                1 (msg)
             56 POP_JUMP_IF_NOT_NONE     2 (to 62)

462          58 LOAD_CONST               2 ('File or stream is not seekable.')
             60 JUMP_FORWARD             1 (to 64)

463     >>   62 LOAD_FAST                1 (msg)

462     >>   64 UNPACK_SEQUENCE          1
             68 CALL                     1
             76 CACHE
             78 RAISE_VARARGS            1

461     >>   80 LOAD_CONST               1 (None)
             82 RETURN_VALUE

Disassembly of <code object readable at 0x000001A2D05C3840, file "_pyio.py", line 465>:
465           0 RESUME                   0

470           2 LOAD_CONST               1 (False)
              4 RETURN_VALUE

Disassembly of <code object _checkReadable at 0x000001A2D05F1FB0, file "_pyio.py", line 472>:
472           0 RESUME                   0

475           2 LOAD_FAST                0 (self)
              4 STORE_SUBSCR
              8 CACHE
             10 CACHE
             12 CACHE
             14 CACHE
             16 CACHE
             18 CACHE
             20 CACHE
             22 CACHE
             24 CACHE
             26 UNPACK_SEQUENCE          0
             30 CALL                     0
             38 CACHE
             40 POP_JUMP_IF_TRUE        19 (to 80)

476          42 LOAD_GLOBAL              3 (NULL + UnsupportedOperation)
             52 CACHE

477          54 LOAD_FAST                1 (msg)
             56 POP_JUMP_IF_NOT_NONE     2 (to 62)

476          58 LOAD_CONST               2 ('File or stream is not readable.')
             60 JUMP_FORWARD             1 (to 64)

477     >>   62 LOAD_FAST                1 (msg)

476     >>   64 UNPACK_SEQUENCE          1
             68 CALL                     1
             76 CACHE
             78 RAISE_VARARGS            1

475     >>   80 LOAD_CONST               1 (None)
             82 RETURN_VALUE

Disassembly of <code object writable at 0x000001A2D05C3910, file "_pyio.py", line 479>:
479           0 RESUME                   0

484           2 LOAD_CONST               1 (False)
              4 RETURN_VALUE

Disassembly of <code object _checkWritable at 0x000001A2D05F2430, file "_pyio.py", line 486>:
486           0 RESUME                   0

489           2 LOAD_FAST                0 (self)
              4 STORE_SUBSCR
              8 CACHE
             10 CACHE
             12 CACHE
             14 CACHE
             16 CACHE
             18 CACHE
             20 CACHE
             22 CACHE
             24 CACHE
             26 UNPACK_SEQUENCE          0
             30 CALL                     0
             38 CACHE
             40 POP_JUMP_IF_TRUE        19 (to 80)

490          42 LOAD_GLOBAL              3 (NULL + UnsupportedOperation)
             52 CACHE

491          54 LOAD_FAST                1 (msg)
             56 POP_JUMP_IF_NOT_NONE     2 (to 62)

490          58 LOAD_CONST               2 ('File or stream is not writable.')
             60 JUMP_FORWARD             1 (to 64)

491     >>   62 LOAD_FAST                1 (msg)

490     >>   64 UNPACK_SEQUENCE          1
             68 CALL                     1
             76 CACHE
             78 RAISE_VARARGS            1

489     >>   80 LOAD_CONST               1 (None)
             82 RETURN_VALUE

Disassembly of <code object closed at 0x000001A2D05C39E0, file "_pyio.py", line 493>:
493           0 RESUME                   0

499           2 LOAD_FAST                0 (self)
              4 LOAD_ATTR                0 (_IOBase__closed)

Disassembly of <code object _checkClosed at 0x000001A2D061C030, file "_pyio.py", line 501>:
501           0 RESUME                   0

504           2 LOAD_FAST                0 (self)
              4 LOAD_ATTR                0 (closed)
             24 CACHE
             26 CACHE

506          28 LOAD_FAST                1 (msg)
             30 POP_JUMP_IF_NOT_NONE     2 (to 36)

505          32 LOAD_CONST               2 ('I/O operation on closed file.')
             34 JUMP_FORWARD             1 (to 38)

506     >>   36 LOAD_FAST                1 (msg)

505     >>   38 UNPACK_SEQUENCE          1
             42 CALL                     1
             50 CACHE
             52 RAISE_VARARGS            1

504          54 LOAD_CONST               1 (None)
             56 RETURN_VALUE

Disassembly of <code object __enter__ at 0x000001A2D06308A0, file "_pyio.py", line 510>:
510           0 RESUME                   0

512           2 LOAD_FAST                0 (self)
              4 STORE_SUBSCR
              8 CACHE
             10 CACHE
             12 CACHE
             14 CACHE
             16 CACHE
             18 CACHE
             20 CACHE
             22 CACHE
             24 CACHE
             26 UNPACK_SEQUENCE          0
             30 CALL                     0
             38 CACHE
             40 POP_TOP

513          42 LOAD_FAST                0 (self)
             44 RETURN_VALUE

Disassembly of <code object __exit__ at 0x000001A2D0630990, file "_pyio.py", line 515>:
515           0 RESUME                   0

517           2 LOAD_FAST                0 (self)
              4 STORE_SUBSCR
              8 CACHE
             10 CACHE
             12 CACHE
             14 CACHE
             16 CACHE
             18 CACHE
             20 CACHE
             22 CACHE
             24 CACHE
             26 UNPACK_SEQUENCE          0
             30 CALL                     0
             38 CACHE
             40 POP_TOP
             42 LOAD_CONST               1 (None)
             44 RETURN_VALUE

Disassembly of <code object fileno at 0x000001A2D0630A80, file "_pyio.py", line 523>:
523           0 RESUME                   0

528           2 LOAD_FAST                0 (self)
              4 STORE_SUBSCR
              8 CACHE
             10 CACHE
             12 CACHE
             14 CACHE
             16 CACHE
             18 CACHE
             20 CACHE
             22 CACHE
             24 CACHE
             26 LOAD_CONST               1 ('fileno')
             28 UNPACK_SEQUENCE          1
             32 CALL                     1
             40 CACHE
             42 POP_TOP
             44 LOAD_CONST               2 (None)
             46 RETURN_VALUE

Disassembly of <code object isatty at 0x000001A2D0630B70, file "_pyio.py", line 530>:
530           0 RESUME                   0

535           2 LOAD_FAST                0 (self)
              4 STORE_SUBSCR
              8 CACHE
             10 CACHE
             12 CACHE
             14 CACHE
             16 CACHE
             18 CACHE
             20 CACHE
             22 CACHE
             24 CACHE
             26 UNPACK_SEQUENCE          0
             30 CALL                     0
             38 CACHE
             40 POP_TOP

536          42 LOAD_CONST               1 (False)
             44 RETURN_VALUE

Disassembly of <code object readline at 0x000001A2CDEA5A40, file "_pyio.py", line 540>:
              0 MAKE_CELL                0 (self)
              2 MAKE_CELL                1 (size)

540           4 RESUME                   0

551           6 LOAD_GLOBAL              1 (NULL + hasattr)
             16 CACHE
             18 LOAD_DEREF               0 (self)
             20 LOAD_CONST               1 ('peek')
             22 UNPACK_SEQUENCE          2
             26 CALL                     2
             34 CACHE
             36 POP_JUMP_IF_FALSE        7 (to 52)

552          38 LOAD_CLOSURE             0 (self)
             40 LOAD_CLOSURE             1 (size)
             42 BUILD_TUPLE              2
             44 LOAD_CONST               2 (<code object nreadahead at 0x000001A2D0151B30, file "_pyio.py", line 552>)
             46 MAKE_FUNCTION            8 (closure)
             48 STORE_FAST               2 (nreadahead)
             50 JUMP_FORWARD             3 (to 58)

561     >>   52 LOAD_CONST               3 (<code object nreadahead at 0x000001A2D06206B0, file "_pyio.py", line 561>)
             54 MAKE_FUNCTION            0
             56 STORE_FAST               2 (nreadahead)

563     >>   58 LOAD_DEREF               1 (size)
             60 POP_JUMP_IF_NOT_NONE     3 (to 68)

564          62 LOAD_CONST               5 (-1)
             64 STORE_DEREF              1 (size)
             66 JUMP_FORWARD            51 (to 170)

566     >>   68 NOP

567          70 LOAD_DEREF               1 (size)
             72 LOAD_ATTR                1 (NULL|self + hasattr)
             92 CALL                     0
            100 CACHE
            102 STORE_DEREF              1 (size)
            104 JUMP_FORWARD            32 (to 170)
        >>  106 PUSH_EXC_INFO

568         108 LOAD_GLOBAL              4 (AttributeError)
            118 CACHE
            120 CHECK_EXC_MATCH
            122 POP_JUMP_IF_FALSE       19 (to 162)
            124 POP_TOP

569         126 LOAD_GLOBAL              7 (NULL + TypeError)
            136 CACHE
            138 LOAD_DEREF               1 (size)
            140 FORMAT_VALUE             2 (repr)
            142 LOAD_CONST               6 (' is not an integer')
            144 BUILD_STRING             2
            146 UNPACK_SEQUENCE          1
            150 CALL                     1
            158 CACHE
            160 RAISE_VARARGS            1

568     >>  162 RERAISE                  0
        >>  164 COPY                     3
            166 POP_EXCEPT
            168 RERAISE                  1

572     >>  170 LOAD_GLOBAL              9 (NULL + bytearray)
            180 CACHE
            182 UNPACK_SEQUENCE          0
            186 CALL                     0
            194 CACHE
            196 STORE_FAST               4 (res)

573         198 LOAD_DEREF               1 (size)
            200 LOAD_CONST               7 (0)
            202 COMPARE_OP               0 (<)
            206 CACHE
            208 POP_JUMP_IF_TRUE        19 (to 248)
            210 LOAD_GLOBAL             11 (NULL + len)
            220 CACHE
            222 LOAD_FAST                4 (res)
            224 UNPACK_SEQUENCE          1
            228 CALL                     1
            236 CACHE
            238 LOAD_DEREF               1 (size)
            240 COMPARE_OP               0 (<)
            244 CACHE
            246 POP_JUMP_IF_FALSE       84 (to 416)

574     >>  248 LOAD_DEREF               0 (self)
            250 STORE_SUBSCR
            254 CACHE
            256 CACHE
            258 CACHE
            260 CACHE
            262 CACHE
            264 CACHE
            266 CACHE
            268 CACHE
            270 CACHE
            272 PUSH_NULL
            274 LOAD_FAST                2 (nreadahead)
            276 UNPACK_SEQUENCE          0
            280 CALL                     0
            288 CACHE
            290 UNPACK_SEQUENCE          1
            294 CALL                     1
            302 CACHE
            304 STORE_FAST               5 (b)

575         306 LOAD_FAST                5 (b)
            308 POP_JUMP_IF_TRUE         1 (to 312)

576         310 JUMP_FORWARD            52 (to 416)

577     >>  312 LOAD_FAST                4 (res)
            314 LOAD_FAST                5 (b)
            316 BINARY_OP               13 (+=)
            320 STORE_FAST               4 (res)

578         322 LOAD_FAST                4 (res)
            324 STORE_SUBSCR
            328 CACHE
            330 CACHE
            332 CACHE
            334 CACHE
            336 CACHE
            338 CACHE
            340 CACHE
            342 CACHE
            344 CACHE
            346 LOAD_CONST               8 (b'\n')
            348 UNPACK_SEQUENCE          1
            352 CALL                     1
            360 CACHE
            362 POP_JUMP_IF_FALSE        1 (to 366)

579         364 JUMP_FORWARD            25 (to 416)

573     >>  366 LOAD_DEREF               1 (size)
            368 LOAD_CONST               7 (0)
            370 COMPARE_OP               0 (<)
            374 CACHE
