# MAIN APPLICATION CODE OBJECT
# Position: 8216982
# Filename: codecs.py
# Function: <module>
# Args: 0
# Locals: 0
# Stack size: 5
# Flags: 0

# BYTECODE DISASSEMBLY:
==================================================
   0           0 RESUME                   0

   1           2 LOAD_CONST               0 (' codecs -- Python Codec Registry, API and helpers.\n\n\nWritten by <PERSON><PERSON><PERSON> (<EMAIL>).\n\n(c) Copyright CNRI, All Rights Reserved. NO WARRANTY.\n\n')
               4 STORE_NAME               0 (__doc__)

  10           6 LOAD_CONST               1 (0)
               8 LOAD_CONST               2 (None)
              10 IMPORT_NAME              1 (builtins)
              12 STORE_NAME               1 (builtins)

  11          14 LOAD_CONST               1 (0)
              16 LOAD_CONST               2 (None)
              18 IMPORT_NAME              2 (sys)
              20 STORE_NAME               2 (sys)

  15          22 NOP

  16          24 LOAD_CONST               1 (0)
              26 LOAD_CONST               3 (('*',))
              28 IMPORT_NAME              3 (_codecs)
              30 LOAD_CONST               0 (' codecs -- Python Codec Registry, API and helpers.\n\n\nWritten by Marc-Andre Lemburg (<EMAIL>).\n\n(c) Copyright CNRI, All Rights Reserved. NO WARRANTY.\n\n')
              32 JUMP_FORWARD            27 (to 88)
         >>   34 PUSH_EXC_INFO

  17          36 LOAD_NAME                4 (ImportError)
              38 CHECK_EXC_MATCH
              40 POP_JUMP_IF_FALSE       19 (to 80)
              42 STORE_NAME               5 (why)

  18          44 PUSH_NULL
              46 LOAD_NAME                6 (SystemError)
              48 LOAD_CONST               4 ('Failed to load the builtin codecs: %s')
              50 LOAD_NAME                5 (why)
              52 BINARY_OP                6 (%)
              56 UNPACK_SEQUENCE          1
              60 CALL                     1
              68 CACHE
              70 RAISE_VARARGS            1
         >>   72 LOAD_CONST               2 (None)
              74 STORE_NAME               5 (why)
              76 DELETE_NAME              5 (why)
              78 RERAISE                  1

  17     >>   80 RERAISE                  0
         >>   82 COPY                     3
              84 POP_EXCEPT
              86 RERAISE                  1

  20     >>   88 BUILD_LIST               0
              90 LOAD_CONST               5 (('register', 'lookup', 'open', 'EncodedFile', 'BOM', 'BOM_BE', 'BOM_LE', 'BOM32_BE', 'BOM32_LE', 'BOM64_BE', 'BOM64_LE', 'BOM_UTF8', 'BOM_UTF16', 'BOM_UTF16_LE', 'BOM_UTF16_BE', 'BOM_UTF32', 'BOM_UTF32_LE', 'BOM_UTF32_BE', 'CodecInfo', 'Codec', 'IncrementalEncoder', 'IncrementalDecoder', 'StreamReader', 'StreamWriter', 'StreamReaderWriter', 'StreamRecoder', 'getencoder', 'getdecoder', 'getincrementalencoder', 'getincrementaldecoder', 'getreader', 'getwriter', 'encode', 'decode', 'iterencode', 'iterdecode', 'strict_errors', 'ignore_errors', 'replace_errors', 'xmlcharrefreplace_errors', 'backslashreplace_errors', 'namereplace_errors', 'register_error', 'lookup_error'))
              92 LIST_EXTEND              1
              94 STORE_NAME               7 (__all__)

  44          96 LOAD_CONST               6 (b'\xef\xbb\xbf')
              98 STORE_NAME               8 (BOM_UTF8)

  47         100 LOAD_CONST               7 (b'\xff\xfe')
             102 COPY                     1
             104 STORE_NAME               9 (BOM_LE)
             106 STORE_NAME              10 (BOM_UTF16_LE)

  50         108 LOAD_CONST               8 (b'\xfe\xff')
             110 COPY                     1
             112 STORE_NAME              11 (BOM_BE)
             114 STORE_NAME              12 (BOM_UTF16_BE)

  53         116 LOAD_CONST               9 (b'\xff\xfe\x00\x00')
             118 STORE_NAME              13 (BOM_UTF32_LE)

  56         120 LOAD_CONST              10 (b'\x00\x00\xfe\xff')
             122 STORE_NAME              14 (BOM_UTF32_BE)

  58         124 LOAD_NAME                2 (sys)
             126 LOAD_ATTR               15 (NULL|self + __all__)

  61         146 LOAD_NAME               10 (BOM_UTF16_LE)
             148 COPY                     1
             150 STORE_NAME              16 (BOM)
             152 STORE_NAME              17 (BOM_UTF16)

  64         154 LOAD_NAME               13 (BOM_UTF32_LE)
             156 STORE_NAME              18 (BOM_UTF32)
             158 JUMP_FORWARD             6 (to 172)

  69         160 LOAD_NAME               12 (BOM_UTF16_BE)
             162 COPY                     1
             164 STORE_NAME              16 (BOM)
             166 STORE_NAME              17 (BOM_UTF16)

  72         168 LOAD_NAME               14 (BOM_UTF32_BE)
             170 STORE_NAME              18 (BOM_UTF32)

  75     >>  172 LOAD_NAME               10 (BOM_UTF16_LE)
             174 STORE_NAME              19 (BOM32_LE)

  76         176 LOAD_NAME               12 (BOM_UTF16_BE)
             178 STORE_NAME              20 (BOM32_BE)

  77         180 LOAD_NAME               13 (BOM_UTF32_LE)
             182 STORE_NAME              21 (BOM64_LE)

  78         184 LOAD_NAME               14 (BOM_UTF32_BE)
             186 STORE_NAME              22 (BOM64_BE)

  83         188 PUSH_NULL
             190 LOAD_BUILD_CLASS
             192 LOAD_CONST              12 (<code object CodecInfo at 0x000001E77ECF5F20, file "codecs.py", line 83>)
             194 MAKE_FUNCTION            0
             196 LOAD_CONST              13 ('CodecInfo')
             198 LOAD_NAME               23 (tuple)
             200 UNPACK_SEQUENCE          3
             204 CALL                     3
             212 CACHE
             214 STORE_NAME              24 (CodecInfo)

 114         216 PUSH_NULL
             218 LOAD_BUILD_CLASS
             220 LOAD_CONST              14 (<code object Codec at 0x000001E77ECF6010, file "codecs.py", line 114>)
             222 MAKE_FUNCTION            0
             224 LOAD_CONST              15 ('Codec')
             226 UNPACK_SEQUENCE          2
             230 CALL                     2
             238 CACHE
             240 STORE_NAME              25 (Codec)

 180         242 PUSH_NULL
             244 LOAD_BUILD_CLASS
             246 LOAD_CONST              16 (<code object IncrementalEncoder at 0x000001E77ECDA630, file "codecs.py", line 180>)
             248 MAKE_FUNCTION            0
             250 LOAD_CONST              17 ('IncrementalEncoder')
             252 LOAD_NAME               26 (object)
             254 UNPACK_SEQUENCE          3
             258 CALL                     3
             266 CACHE
             268 STORE_NAME              27 (IncrementalEncoder)

 220         270 PUSH_NULL
             272 LOAD_BUILD_CLASS
             274 LOAD_CONST              18 (<code object BufferedIncrementalEncoder at 0x000001E77ECDA930, file "codecs.py", line 220>)
             276 MAKE_FUNCTION            0
             278 LOAD_CONST              19 ('BufferedIncrementalEncoder')
             280 LOAD_NAME               27 (IncrementalEncoder)
             282 UNPACK_SEQUENCE          3
             286 CALL                     3
             294 CACHE
             296 STORE_NAME              28 (BufferedIncrementalEncoder)

 254         298 PUSH_NULL
             300 LOAD_BUILD_CLASS
             302 LOAD_CONST              20 (<code object IncrementalDecoder at 0x000001E77ECDAB30, file "codecs.py", line 254>)
             304 MAKE_FUNCTION            0
             306 LOAD_CONST              21 ('IncrementalDecoder')
             308 LOAD_NAME               26 (object)
             310 UNPACK_SEQUENCE          3
             314 CALL                     3
             322 CACHE
             324 STORE_NAME              29 (IncrementalDecoder)

 303         326 PUSH_NULL
             328 LOAD_BUILD_CLASS
             330 LOAD_CONST              22 (<code object BufferedIncrementalDecoder at 0x000001E77ECDAC30, file "codecs.py", line 303>)
             332 MAKE_FUNCTION            0
             334 LOAD_CONST              23 ('BufferedIncrementalDecoder')
             336 LOAD_NAME               29 (IncrementalDecoder)
             338 UNPACK_SEQUENCE          3
             342 CALL                     3
             350 CACHE
             352 STORE_NAME              30 (BufferedIncrementalDecoder)

 346         354 PUSH_NULL
             356 LOAD_BUILD_CLASS
             358 LOAD_CONST              24 (<code object StreamWriter at 0x000001E77ECF3110, file "codecs.py", line 346>)
             360 MAKE_FUNCTION            0
             362 LOAD_CONST              25 ('StreamWriter')
             364 LOAD_NAME               25 (Codec)
             366 UNPACK_SEQUENCE          3
             370 CALL                     3
             378 CACHE
             380 STORE_NAME              31 (StreamWriter)

 422         382 PUSH_NULL
             384 LOAD_BUILD_CLASS
             386 LOAD_CONST              26 (<code object StreamReader at 0x000001E77ECBF0E0, file "codecs.py", line 422>)
             388 MAKE_FUNCTION            0
             390 LOAD_CONST              27 ('StreamReader')
             392 LOAD_NAME               25 (Codec)
             394 UNPACK_SEQUENCE          3
             398 CALL                     3
             406 CACHE
             408 STORE_NAME              32 (StreamReader)

 674         410 PUSH_NULL
             412 LOAD_BUILD_CLASS
             414 LOAD_CONST              28 (<code object StreamReaderWriter at 0x000001E77ECEE1F0, file "codecs.py", line 674>)
             416 MAKE_FUNCTION            0
             418 LOAD_CONST              29 ('StreamReaderWriter')
             420 UNPACK_SEQUENCE          2
             424 CALL                     2
             432 CACHE
             434 STORE_NAME              33 (StreamReaderWriter)

 764         436 PUSH_NULL
             438 LOAD_BUILD_CLASS
             440 LOAD_CONST              30 (<code object StreamRecoder at 0x000001E77ECEE6F0, file "codecs.py", line 764>)
             442 MAKE_FUNCTION            0
             444 LOAD_CONST              31 ('StreamRecoder')
             446 UNPACK_SEQUENCE          2
             450 CALL                     2
             458 CACHE
             460 STORE_NAME              34 (StreamRecoder)

 883         462 LOAD_CONST              55 (('r', None, 'strict', -1))
             464 LOAD_CONST              35 (<code object open at 0x000001E77ECFCF60, file "codecs.py", line 883>)
             466 MAKE_FUNCTION            1 (defaults)
             468 STORE_NAME              35 (open)

 932         470 LOAD_CONST              56 ((None, 'strict'))
             472 LOAD_CONST              36 (<code object EncodedFile at 0x000001E77ECB99B0, file "codecs.py", line 932>)
             474 MAKE_FUNCTION            1 (defaults)
             476 STORE_NAME              36 (EncodedFile)

 970         478 LOAD_CONST              37 (<code object getencoder at 0x000001E77ECF6C40, file "codecs.py", line 970>)
             480 MAKE_FUNCTION            0
             482 STORE_NAME              37 (getencoder)

 980         484 LOAD_CONST              38 (<code object getdecoder at 0x000001E77ECF6D30, file "codecs.py", line 980>)
             486 MAKE_FUNCTION            0
             488 STORE_NAME              38 (getdecoder)

 990         490 LOAD_CONST              39 (<code object getincrementalencoder at 0x000001E77ECF3330, file "codecs.py", line 990>)
             492 MAKE_FUNCTION            0
             494 STORE_NAME              39 (getincrementalencoder)

1004         496 LOAD_CONST              40 (<code object getincrementaldecoder at 0x000001E77ECF3440, file "codecs.py", line 1004>)
             498 MAKE_FUNCTION            0
             500 STORE_NAME              40 (getincrementaldecoder)

1018         502 LOAD_CONST              41 (<code object getreader at 0x000001E77ECF6E20, file "codecs.py", line 1018>)
             504 MAKE_FUNCTION            0
             506 STORE_NAME              41 (getreader)

1028         508 LOAD_CONST              42 (<code object getwriter at 0x000001E77ECF6F10, file "codecs.py", line 1028>)
             510 MAKE_FUNCTION            0
             512 STORE_NAME              42 (getwriter)

1038         514 LOAD_CONST              57 (('strict',))
             516 LOAD_CONST              43 (<code object iterencode at 0x000001E77ECB9B30, file "codecs.py", line 1038>)
             518 MAKE_FUNCTION            1 (defaults)
             520 STORE_NAME              43 (iterencode)

1056         522 LOAD_CONST              57 (('strict',))
             524 LOAD_CONST              44 (<code object iterdecode at 0x000001E77ECB9CB0, file "codecs.py", line 1056>)
             526 MAKE_FUNCTION            1 (defaults)
             528 STORE_NAME              44 (iterdecode)

1076         530 LOAD_CONST              45 (<code object make_identity_dict at 0x000001E77ECD32F0, file "codecs.py", line 1076>)
             532 MAKE_FUNCTION            0
             534 STORE_NAME              45 (make_identity_dict)

1086         536 LOAD_CONST              46 (<code object make_encoding_map at 0x000001E77ECCF510, file "codecs.py", line 1086>)
             538 MAKE_FUNCTION            0
             540 STORE_NAME              46 (make_encoding_map)

1109         542 NOP

1110         544 PUSH_NULL
             546 LOAD_NAME               47 (lookup_error)
             548 LOAD_CONST              33 ('strict')
             550 UNPACK_SEQUENCE          1
             554 CALL                     1
             562 CACHE
             564 STORE_NAME              48 (strict_errors)

1111         566 PUSH_NULL
             568 LOAD_NAME               47 (lookup_error)
             570 LOAD_CONST              47 ('ignore')
             572 UNPACK_SEQUENCE          1
             576 CALL                     1
             584 CACHE
             586 STORE_NAME              49 (ignore_errors)

1112         588 PUSH_NULL
             590 LOAD_NAME               47 (lookup_error)
             592 LOAD_CONST              48 ('replace')
             594 UNPACK_SEQUENCE          1
             598 CALL                     1
             606 CACHE
             608 STORE_NAME              50 (replace_errors)

1113         610 PUSH_NULL
             612 LOAD_NAME               47 (lookup_error)
             614 LOAD_CONST              49 ('xmlcharrefreplace')
             616 UNPACK_SEQUENCE          1
             620 CALL                     1
             628 CACHE
             630 STORE_NAME              51 (xmlcharrefreplace_errors)

1114         632 PUSH_NULL
             634 LOAD_NAME               47 (lookup_error)
             636 LOAD_CONST              50 ('backslashreplace')
             638 UNPACK_SEQUENCE          1
             642 CALL                     1
             650 CACHE
             652 STORE_NAME              52 (backslashreplace_errors)

1115         654 PUSH_NULL
             656 LOAD_NAME               47 (lookup_error)
             658 LOAD_CONST              51 ('namereplace')
             660 UNPACK_SEQUENCE          1
             664 CALL                     1
             672 CACHE
             674 STORE_NAME              53 (namereplace_errors)
             676 JUMP_FORWARD            23 (to 724)
         >>  678 PUSH_EXC_INFO

1116         680 LOAD_NAME               54 (LookupError)
             682 CHECK_EXC_MATCH
             684 POP_JUMP_IF_FALSE       15 (to 716)
             686 POP_TOP

1118         688 LOAD_CONST               2 (None)
             690 STORE_NAME              48 (strict_errors)

1119         692 LOAD_CONST               2 (None)
             694 STORE_NAME              49 (ignore_errors)

1120         696 LOAD_CONST               2 (None)
             698 STORE_NAME              50 (replace_errors)

1121         700 LOAD_CONST               2 (None)
             702 STORE_NAME              51 (xmlcharrefreplace_errors)

1122         704 LOAD_CONST               2 (None)
             706 STORE_NAME              52 (backslashreplace_errors)

1123         708 LOAD_CONST               2 (None)
             710 STORE_NAME              53 (namereplace_errors)
             712 POP_EXCEPT
             714 JUMP_FORWARD             4 (to 724)

1116     >>  716 RERAISE                  0
         >>  718 COPY                     3
             720 POP_EXCEPT
             722 RERAISE                  1

1127     >>  724 LOAD_CONST               1 (0)
             726 STORE_NAME              55 (_false)

1128         728 LOAD_NAME               55 (_false)
             730 POP_JUMP_IF_FALSE        4 (to 740)

1129         732 LOAD_CONST               1 (0)
             734 LOAD_CONST               2 (None)
             736 IMPORT_NAME             56 (encodings)
             738 STORE_NAME              56 (encodings)

1133     >>  740 LOAD_NAME               57 (__name__)
             742 LOAD_CONST              52 ('__main__')
             744 COMPARE_OP               2 (<)
             748 CACHE
             750 POP_JUMP_IF_FALSE       48 (to 848)

1136         752 PUSH_NULL
             754 LOAD_NAME               36 (EncodedFile)
             756 LOAD_NAME                2 (sys)
             758 LOAD_ATTR               58 (IncrementalDecoder)
             778 CACHE
             780 CACHE
             782 CACHE
             784 CACHE
             786 LOAD_NAME                2 (sys)
             788 STORE_ATTR              58 (stdout)

1139         798 PUSH_NULL
             800 LOAD_NAME               36 (EncodedFile)
             802 LOAD_NAME                2 (sys)
             804 LOAD_ATTR               59 (NULL|self + IncrementalDecoder)
             824 CACHE
             826 CACHE
             828 CACHE
             830 CACHE
             832 LOAD_NAME                2 (sys)
             834 STORE_ATTR              59 (stdin)
             844 LOAD_CONST               2 (None)
             846 RETURN_VALUE

1133     >>  848 LOAD_CONST               2 (None)
             850 RETURN_VALUE
ExceptionTable:
  24 to 30 -> 34 [0]
  34 to 42 -> 82 [1] lasti
  44 to 70 -> 72 [1] lasti
  72 to 80 -> 82 [1] lasti
  544 to 674 -> 678 [0]
  678 to 710 -> 718 [1] lasti
  716 to 716 -> 718 [1] lasti

Disassembly of <code object CodecInfo at 0x000001E77ECF5F20, file "codecs.py", line 83>:
 83           0 RESUME                   0
              2 LOAD_NAME                0 (__name__)
              4 STORE_NAME               1 (__module__)
              6 LOAD_CONST               0 ('CodecInfo')
              8 STORE_NAME               2 (__qualname__)

 84          10 LOAD_CONST               1 ('Codec details when looking up the codec registry')
             12 STORE_NAME               3 (__doc__)

 92          14 LOAD_CONST               2 (True)
             16 STORE_NAME               4 (_is_text_encoding)

 94          18 NOP

 95          20 NOP

 94          22 LOAD_CONST               7 ((None, None, None, None, None))

 96          24 LOAD_CONST               3 (None)

 94          26 LOAD_CONST               4 (('_is_text_encoding',))
             28 BUILD_CONST_KEY_MAP      1
             30 LOAD_CONST               5 (<code object __new__ at 0x000001E77ECB93B0, file "codecs.py", line 94>)
             32 MAKE_FUNCTION            3 (defaults, kwdefaults)
             34 STORE_NAME               5 (__new__)

109          36 LOAD_CONST               6 (<code object __repr__ at 0x000001E77ECCE9D0, file "codecs.py", line 109>)
             38 MAKE_FUNCTION            0
             40 STORE_NAME               6 (__repr__)
             42 LOAD_CONST               3 (None)
             44 RETURN_VALUE

Disassembly of <code object __new__ at 0x000001E77ECB93B0, file "codecs.py", line 94>:
 94           0 RESUME                   0

 97           2 LOAD_GLOBAL              0 (tuple)
             12 CACHE
             14 STORE_SUBSCR
             18 CACHE
             20 CACHE
             22 CACHE
             24 CACHE
             26 CACHE
             28 CACHE
             30 CACHE
             32 CACHE
             34 CACHE
             36 LOAD_FAST                0 (cls)
             38 LOAD_FAST                1 (encode)
             40 LOAD_FAST                2 (decode)
             42 LOAD_FAST                3 (streamreader)
             44 LOAD_FAST                4 (streamwriter)
             46 BUILD_TUPLE              4
             48 UNPACK_SEQUENCE          2
             52 CALL                     2
             60 CACHE
             62 STORE_FAST               9 (self)

 98          64 LOAD_FAST                7 (name)
             66 LOAD_FAST                9 (self)
             68 STORE_ATTR               2 (name)

 99          78 LOAD_FAST                1 (encode)
             80 LOAD_FAST                9 (self)
             82 STORE_ATTR               3 (encode)

100          92 LOAD_FAST                2 (decode)
             94 LOAD_FAST                9 (self)
             96 STORE_ATTR               4 (decode)

101         106 LOAD_FAST                5 (incrementalencoder)
            108 LOAD_FAST                9 (self)
            110 STORE_ATTR               5 (incrementalencoder)

102         120 LOAD_FAST                6 (incrementaldecoder)
            122 LOAD_FAST                9 (self)
            124 STORE_ATTR               6 (incrementaldecoder)

103         134 LOAD_FAST                4 (streamwriter)
            136 LOAD_FAST                9 (self)
            138 STORE_ATTR               7 (streamwriter)

104         148 LOAD_FAST                3 (streamreader)
            150 LOAD_FAST                9 (self)
            152 STORE_ATTR               8 (streamreader)

105         162 LOAD_FAST                8 (_is_text_encoding)
            164 POP_JUMP_IF_NONE         7 (to 180)

106         166 LOAD_FAST                8 (_is_text_encoding)
            168 LOAD_FAST                9 (self)
            170 STORE_ATTR               9 (_is_text_encoding)

107     >>  180 LOAD_FAST                9 (self)
            182 RETURN_VALUE

Disassembly of <code object __repr__ at 0x000001E77ECCE9D0, file "codecs.py", line 109>:
109           0 RESUME                   0

110           2 LOAD_CONST               1 ('<%s.%s object for encoding %s at %#x>')

111           4 LOAD_FAST                0 (self)
              6 LOAD_ATTR                0 (__class__)
             26 LOAD_FAST                0 (self)
             28 LOAD_ATTR                0 (__class__)

112          48 LOAD_FAST                0 (self)
             50 LOAD_ATTR                3 (NULL|self + __module__)
             70 CACHE
             72 LOAD_FAST                0 (self)
             74 UNPACK_SEQUENCE          1
             78 CALL                     1
             86 CACHE

111          88 BUILD_TUPLE              4

110          90 BINARY_OP                6 (%)
             94 RETURN_VALUE

Disassembly of <code object Codec at 0x000001E77ECF6010, file "codecs.py", line 114>:
114           0 RESUME                   0
              2 LOAD_NAME                0 (__name__)
              4 STORE_NAME               1 (__module__)
              6 LOAD_CONST               0 ('Codec')
              8 STORE_NAME               2 (__qualname__)

116          10 LOAD_CONST               1 (" Defines the interface for stateless encoders/decoders.\n\n        The .encode()/.decode() methods may use different error\n        handling schemes by providing the errors argument. These\n        string values are predefined:\n\n         'strict' - raise a ValueError error (or a subclass)\n         'ignore' - ignore the character and continue with the next\n         'replace' - replace with a suitable replacement character;\n                    Python will use the official U+FFFD REPLACEMENT\n                    CHARACTER for the builtin Unicode codecs on\n                    decoding and '?' on encoding.\n         'surrogateescape' - replace with private code points U+DCnn.\n         'xmlcharrefreplace' - Replace with the appropriate XML\n                               character reference (only for encoding).\n         'backslashreplace'  - Replace with backslashed escape sequences.\n         'namereplace'       - Replace with \\N{...} escape sequences\n                               (only for encoding).\n\n        The set of allowed values can be extended via register_error.\n\n    ")
             12 STORE_NAME               3 (__doc__)

138          14 LOAD_CONST               6 (('strict',))
             16 LOAD_CONST               3 (<code object encode at 0x000001E77ED11210, file "codecs.py", line 138>)
             18 MAKE_FUNCTION            1 (defaults)
             20 STORE_NAME               4 (encode)

157          22 LOAD_CONST               6 (('strict',))
             24 LOAD_CONST               4 (<code object decode at 0x000001E77ED112E0, file "codecs.py", line 157>)
             26 MAKE_FUNCTION            1 (defaults)
             28 STORE_NAME               5 (decode)
             30 LOAD_CONST               5 (None)
             32 RETURN_VALUE

Disassembly of <code object encode at 0x000001E77ED11210, file "codecs.py", line 138>:
138           0 RESUME                   0

155           2 LOAD_GLOBAL              0 (NotImplementedError)
             12 CACHE
             14 RAISE_VARARGS            1

Disassembly of <code object decode at 0x000001E77ED112E0, file "codecs.py", line 157>:
157           0 RESUME                   0

178           2 LOAD_GLOBAL              0 (NotImplementedError)
             12 CACHE
             14 RAISE_VARARGS            1

Disassembly of <code object IncrementalEncoder at 0x000001E77ECDA630, file "codecs.py", line 180>:
180           0 RESUME                   0
              2 LOAD_NAME                0 (__name__)
              4 STORE_NAME               1 (__module__)
              6 LOAD_CONST               0 ('IncrementalEncoder')
              8 STORE_NAME               2 (__qualname__)

181          10 LOAD_CONST               1 ('\n    An IncrementalEncoder encodes an input in multiple steps. The input can\n    be passed piece by piece to the encode() method. The IncrementalEncoder\n    remembers the state of the encoding process between calls to encode().\n    ')
             12 STORE_NAME               3 (__doc__)

186          14 LOAD_CONST              10 (('strict',))
             16 LOAD_CONST               3 (<code object __init__ at 0x000001E77ECF6100, file "codecs.py", line 186>)
             18 MAKE_FUNCTION            1 (defaults)
             20 STORE_NAME               4 (__init__)

197          22 LOAD_CONST              11 ((False,))
             24 LOAD_CONST               5 (<code object encode at 0x000001E77ED113B0, file "codecs.py", line 197>)
             26 MAKE_FUNCTION            1 (defaults)
             28 STORE_NAME               5 (encode)

203          30 LOAD_CONST               6 (<code object reset at 0x000001E77ED11480, file "codecs.py", line 203>)
             32 MAKE_FUNCTION            0
             34 STORE_NAME               6 (reset)

208          36 LOAD_CONST               7 (<code object getstate at 0x000001E77ED11550, file "codecs.py", line 208>)
             38 MAKE_FUNCTION            0
             40 STORE_NAME               7 (getstate)

214          42 LOAD_CONST               8 (<code object setstate at 0x000001E77ED11620, file "codecs.py", line 214>)
             44 MAKE_FUNCTION            0
             46 STORE_NAME               8 (setstate)
             48 LOAD_CONST               9 (None)
             50 RETURN_VALUE

Disassembly of <code object __init__ at 0x000001E77ECF6100, file "codecs.py", line 186>:
186           0 RESUME                   0

194           2 LOAD_FAST                1 (errors)
              4 LOAD_FAST                0 (self)
              6 STORE_ATTR               0 (errors)

195          16 LOAD_CONST               1 ('')
             18 LOAD_FAST                0 (self)
             20 STORE_ATTR               1 (buffer)
             30 LOAD_CONST               2 (None)
             32 RETURN_VALUE

Disassembly of <code object encode at 0x000001E77ED113B0, file "codecs.py", line 197>:
197           0 RESUME                   0

201           2 LOAD_GLOBAL              0 (NotImplementedError)
             12 CACHE
             14 RAISE_VARARGS            1

Disassembly of <code object reset at 0x000001E77ED11480, file "codecs.py", line 203>:
203           0 RESUME                   0
              2 LOAD_CONST               1 (None)
              4 RETURN_VALUE

Disassembly of <code object getstate at 0x000001E77ED11550, file "codecs.py", line 208>:
208           0 RESUME                   0

212           2 LOAD_CONST               1 (0)
              4 RETURN_VALUE

Disassembly of <code object setstate at 0x000001E77ED11620, file "codecs.py", line 214>:
214           0 RESUME                   0
              2 LOAD_CONST               1 (None)
              4 RETURN_VALUE

Disassembly of <code object BufferedIncrementalEncoder at 0x000001E77ECDA930, file "codecs.py", line 220>:
220           0 RESUME                   0
              2 LOAD_NAME                0 (__name__)
              4 STORE_NAME               1 (__module__)
              6 LOAD_CONST               0 ('BufferedIncrementalEncoder')
              8 STORE_NAME               2 (__qualname__)

221          10 LOAD_CONST               1 ('\n    This subclass of IncrementalEncoder can be used as the baseclass for an\n    incremental encoder if the encoder must keep some of the output in a\n    buffer between calls to encode().\n    ')
             12 STORE_NAME               3 (__doc__)

226          14 LOAD_CONST              11 (('strict',))
             16 LOAD_CONST               3 (<code object __init__ at 0x000001E77ECF2CD0, file "codecs.py", line 226>)
             18 MAKE_FUNCTION            1 (defaults)
             20 STORE_NAME               4 (__init__)

231          22 LOAD_CONST               4 (<code object _buffer_encode at 0x000001E77ED116F0, file "codecs.py", line 231>)
             24 MAKE_FUNCTION            0
             26 STORE_NAME               5 (_buffer_encode)

236          28 LOAD_CONST              12 ((False,))
             30 LOAD_CONST               6 (<code object encode at 0x000001E77ECEDBB0, file "codecs.py", line 236>)
             32 MAKE_FUNCTION            1 (defaults)
             34 STORE_NAME               6 (encode)

244          36 LOAD_CONST               7 (<code object reset at 0x000001E77ECF2DE0, file "codecs.py", line 244>)
             38 MAKE_FUNCTION            0
             40 STORE_NAME               7 (reset)

248          42 LOAD_CONST               8 (<code object getstate at 0x000001E77ECD25D0, file "codecs.py", line 248>)
             44 MAKE_FUNCTION            0
             46 STORE_NAME               8 (getstate)

251          48 LOAD_CONST               9 (<code object setstate at 0x000001E77ECD26B0, file "codecs.py", line 251>)
             50 MAKE_FUNCTION            0
             52 STORE_NAME               9 (setstate)
             54 LOAD_CONST              10 (None)
             56 RETURN_VALUE

Disassembly of <code object __init__ at 0x000001E77ECF2CD0, file "codecs.py", line 226>:
226           0 RESUME                   0

227           2 LOAD_GLOBAL              0 (IncrementalEncoder)
             12 CACHE
             14 STORE_SUBSCR
             18 CACHE
             20 CACHE
             22 CACHE
             24 CACHE
             26 CACHE
             28 CACHE
             30 CACHE
             32 CACHE
             34 CACHE
             36 LOAD_FAST                0 (self)
             38 LOAD_FAST                1 (errors)
             40 UNPACK_SEQUENCE          2
             44 CALL                     2
             52 CACHE
             54 POP_TOP

229          56 LOAD_CONST               1 ('')
             58 LOAD_FAST                0 (self)
             60 STORE_ATTR               2 (buffer)
             70 LOAD_CONST               0 (None)
             72 RETURN_VALUE

Disassembly of <code object _buffer_encode at 0x000001E77ED116F0, file "codecs.py", line 231>:
231           0 RESUME                   0

234           2 LOAD_GLOBAL              0 (NotImplementedError)
             12 CACHE
             14 RAISE_VARARGS            1

Disassembly of <code object encode at 0x000001E77ECEDBB0, file "codecs.py", line 236>:
236           0 RESUME                   0

238           2 LOAD_FAST                0 (self)
              4 LOAD_ATTR                0 (buffer)
             24 STORE_SUBSCR
             28 CACHE
             30 CACHE
             32 CACHE
             34 CACHE
             36 CACHE
             38 CACHE
             40 CACHE
             42 CACHE
             44 CACHE
             46 LOAD_FAST                3 (data)
             48 LOAD_FAST                0 (self)
             50 LOAD_ATTR                2 (_buffer_encode)
             70 CACHE
             72 CACHE
             74 CACHE
             76 UNPACK_SEQUENCE          2
             80 STORE_FAST               4 (result)
             82 STORE_FAST               5 (consumed)

241          84 LOAD_FAST                3 (data)
             86 LOAD_FAST                5 (consumed)
             88 LOAD_CONST               0 (None)
             90 BUILD_SLICE              2
             92 BINARY_SUBSCR
             96 CACHE
             98 CACHE
            100 CACHE
            102 LOAD_FAST                0 (self)
            104 STORE_ATTR               0 (buffer)

242         114 LOAD_FAST                4 (result)
            116 RETURN_VALUE

Disassembly of <code object reset at 0x000001E77ECF2DE0, file "codecs.py", line 244>:
244           0 RESUME                   0

245           2 LOAD_GLOBAL              0 (IncrementalEncoder)
             12 CACHE
             14 STORE_SUBSCR
             18 CACHE
             20 CACHE
             22 CACHE
             24 CACHE
             26 CACHE
             28 CACHE
             30 CACHE
             32 CACHE
             34 CACHE
             36 LOAD_FAST                0 (self)
             38 UNPACK_SEQUENCE          1
             42 CALL                     1
             50 CACHE
             52 POP_TOP

246          54 LOAD_CONST               1 ('')
             56 LOAD_FAST                0 (self)
             58 STORE_ATTR               2 (buffer)
             68 LOAD_CONST               0 (None)
             70 RETURN_VALUE

Disassembly of <code object getstate at 0x000001E77ECD25D0, file "codecs.py", line 248>:
248           0 RESUME                   0

249           2 LOAD_FAST                0 (self)
              4 LOAD_ATTR                0 (buffer)

Disassembly of <code object setstate at 0x000001E77ECD26B0, file "codecs.py", line 251>:
251           0 RESUME                   0

252           2 LOAD_FAST                1 (state)
              4 LOAD_GLOBAL              1 (NULL + buffer)
             14 CACHE
             16 CACHE
             18 CACHE
             20 LOAD_CONST               0 (None)
             22 RETURN_VALUE

Disassembly of <code object IncrementalDecoder at 0x000001E77ECDAB30, file "codecs.py", line 254>:
254           0 RESUME                   0
              2 LOAD_NAME                0 (__name__)
              4 STORE_NAME               1 (__module__)
              6 LOAD_CONST               0 ('IncrementalDecoder')
              8 STORE_NAME               2 (__qualname__)

255          10 LOAD_CONST               1 ('\n    An IncrementalDecoder decodes an input in multiple steps. The input can\n    be passed piece by piece to the decode() method. The IncrementalDecoder\n    remembers the state of the decoding process between calls to decode().\n    ')
             12 STORE_NAME               3 (__doc__)

260          14 LOAD_CONST              10 (('strict',))
             16 LOAD_CONST               3 (<code object __init__ at 0x000001E77ECD2790, file "codecs.py", line 260>)
             18 MAKE_FUNCTION            1 (defaults)
             20 STORE_NAME               4 (__init__)

270          22 LOAD_CONST              11 ((False,))
             24 LOAD_CONST               5 (<code object decode at 0x000001E77ED117C0, file "codecs.py", line 270>)
             26 MAKE_FUNCTION            1 (defaults)
             28 STORE_NAME               5 (decode)

276          30 LOAD_CONST               6 (<code object reset at 0x000001E77ED11890, file "codecs.py", line 276>)
             32 MAKE_FUNCTION            0
             34 STORE_NAME               6 (reset)

281          36 LOAD_CONST               7 (<code object getstate at 0x000001E77ED11960, file "codecs.py", line 281>)
             38 MAKE_FUNCTION            0
             40 STORE_NAME               7 (getstate)

295          42 LOAD_CONST               8 (<code object setstate at 0x000001E77ED11A30, file "codecs.py", line 295>)
             44 MAKE_FUNCTION            0
             46 STORE_NAME               8 (setstate)
             48 LOAD_CONST               9 (None)
             50 RETURN_VALUE

Disassembly of <code object __init__ at 0x000001E77ECD2790, file "codecs.py", line 260>:
260           0 RESUME                   0

268           2 LOAD_FAST                1 (errors)
              4 LOAD_FAST                0 (self)
              6 STORE_ATTR               0 (errors)
             16 LOAD_CONST               1 (None)
             18 RETURN_VALUE

Disassembly of <code object decode at 0x000001E77ED117C0, file "codecs.py", line 270>:
270           0 RESUME                   0

274           2 LOAD_GLOBAL              0 (NotImplementedError)
             12 CACHE
             14 RAISE_VARARGS            1

Disassembly of <code object reset at 0x000001E77ED11890, file "codecs.py", line 276>:
276           0 RESUME                   0
              2 LOAD_CONST               1 (None)
              4 RETURN_VALUE

Disassembly of <code object getstate at 0x000001E77ED11960, file "codecs.py", line 281>:
281           0 RESUME                   0

293           2 LOAD_CONST               1 ((b'', 0))
              4 RETURN_VALUE

Disassembly of <code object setstate at 0x000001E77ED11A30, file "codecs.py", line 295>:
295           0 RESUME                   0
              2 LOAD_CONST               1 (None)
              4 RETURN_VALUE

Disassembly of <code object BufferedIncrementalDecoder at 0x000001E77ECDAC30, file "codecs.py", line 303>:
303           0 RESUME                   0
              2 LOAD_NAME                0 (__name__)
              4 STORE_NAME               1 (__module__)
              6 LOAD_CONST               0 ('BufferedIncrementalDecoder')
              8 STORE_NAME               2 (__qualname__)

304          10 LOAD_CONST               1 ('\n    This subclass of IncrementalDecoder can be used as the baseclass for an\n    incremental decoder if the decoder must be able to handle incomplete\n    byte sequences.\n    ')
             12 STORE_NAME               3 (__doc__)

309          14 LOAD_CONST              11 (('strict',))
             16 LOAD_CONST               3 (<code object __init__ at 0x000001E77ECF2EF0, file "codecs.py", line 309>)
             18 MAKE_FUNCTION            1 (defaults)
             20 STORE_NAME               4 (__init__)

314          22 LOAD_CONST               4 (<code object _buffer_decode at 0x000001E77ED11B00, file "codecs.py", line 314>)
             24 MAKE_FUNCTION            0
             26 STORE_NAME               5 (_buffer_decode)

319          28 LOAD_CONST              12 ((False,))
             30 LOAD_CONST               6 (<code object decode at 0x000001E77ECEDCF0, file "codecs.py", line 319>)
             32 MAKE_FUNCTION            1 (defaults)
             34 STORE_NAME               6 (decode)

327          36 LOAD_CONST               7 (<code object reset at 0x000001E77ECF3000, file "codecs.py", line 327>)
             38 MAKE_FUNCTION            0
             40 STORE_NAME               7 (reset)

331          42 LOAD_CONST               8 (<code object getstate at 0x000001E77ECD2A30, file "codecs.py", line 331>)
             44 MAKE_FUNCTION            0
             46 STORE_NAME               8 (getstate)

335          48 LOAD_CONST               9 (<code object setstate at 0x000001E77ECD2B10, file "codecs.py", line 335>)
             50 MAKE_FUNCTION            0
             52 STORE_NAME               9 (setstate)
             54 LOAD_CONST              10 (None)
             56 RETURN_VALUE

Disassembly of <code object __init__ at 0x000001E77ECF2EF0, file "codecs.py", line 309>:
309           0 RESUME                   0

310           2 LOAD_GLOBAL              0 (IncrementalDecoder)
             12 CACHE
             14 STORE_SUBSCR
             18 CACHE
             20 CACHE
             22 CACHE
             24 CACHE
             26 CACHE
             28 CACHE
             30 CACHE
             32 CACHE
             34 CACHE
             36 LOAD_FAST                0 (self)
             38 LOAD_FAST                1 (errors)
             40 UNPACK_SEQUENCE          2
             44 CALL                     2
             52 CACHE
             54 POP_TOP

312          56 LOAD_CONST               1 (b'')
             58 LOAD_FAST                0 (self)
             60 STORE_ATTR               2 (buffer)
             70 LOAD_CONST               0 (None)
             72 RETURN_VALUE

Disassembly of <code object _buffer_decode at 0x000001E77ED11B00, file "codecs.py", line 314>:
314           0 RESUME                   0

317           2 LOAD_GLOBAL              0 (NotImplementedError)
             12 CACHE
             14 RAISE_VARARGS            1

Disassembly of <code object decode at 0x000001E77ECEDCF0, file "codecs.py", line 319>:
319           0 RESUME                   0

321           2 LOAD_FAST                0 (self)
              4 LOAD_ATTR                0 (buffer)
             24 STORE_SUBSCR
             28 CACHE
             30 CACHE
             32 CACHE
             34 CACHE
             36 CACHE
             38 CACHE
             40 CACHE
             42 CACHE
             44 CACHE
             46 LOAD_FAST                3 (data)
             48 LOAD_FAST                0 (self)
             50 LOAD_ATTR                2 (_buffer_decode)
             70 CACHE
             72 CACHE
             74 CACHE
             76 UNPACK_SEQUENCE          2
             80 STORE_FAST               4 (result)
             82 STORE_FAST               5 (consumed)

324          84 LOAD_FAST                3 (data)
             86 LOAD_FAST                5 (consumed)
             88 LOAD_CONST               0 (None)
             90 BUILD_SLICE              2
             92 BINARY_SUBSCR
             96 CACHE
             98 CACHE
            100 CACHE
            102 LOAD_FAST                0 (self)
            104 STORE_ATTR               0 (buffer)

325         114 LOAD_FAST                4 (result)
            116 RETURN_VALUE

Disassembly of <code object reset at 0x000001E77ECF3000, file "codecs.py", line 327>:
327           0 RESUME                   0

328           2 LOAD_GLOBAL              0 (IncrementalDecoder)
             12 CACHE
             14 STORE_SUBSCR
             18 CACHE
             20 CACHE
             22 CACHE
             24 CACHE
             26 CACHE
             28 CACHE
             30 CACHE
             32 CACHE
             34 CACHE
             36 LOAD_FAST                0 (self)
             38 UNPACK_SEQUENCE          1
             42 CALL                     1
             50 CACHE
             52 POP_TOP

329          54 LOAD_CONST               1 (b'')
             56 LOAD_FAST                0 (self)
             58 STORE_ATTR               2 (buffer)
             68 LOAD_CONST               0 (None)
             70 RETURN_VALUE

Disassembly of <code object getstate at 0x000001E77ECD2A30, file "codecs.py", line 331>:
331           0 RESUME                   0

333           2 LOAD_FAST                0 (self)
              4 LOAD_ATTR                0 (buffer)

Disassembly of <code object setstate at 0x000001E77ECD2B10, file "codecs.py", line 335>:
335           0 RESUME                   0

337           2 LOAD_FAST                1 (state)
              4 LOAD_CONST               1 (0)
              6 BINARY_SUBSCR
             10 CACHE
             12 CACHE
             14 CACHE
             16 LOAD_FAST                0 (self)
             18 STORE_ATTR               0 (buffer)
             28 LOAD_CONST               0 (None)
             30 RETURN_VALUE

Disassembly of <code object StreamWriter at 0x000001E77ECF3110, file "codecs.py", line 346>:
346           0 RESUME                   0
              2 LOAD_NAME                0 (__name__)
              4 STORE_NAME               1 (__module__)
              6 LOAD_CONST               0 ('StreamWriter')
              8 STORE_NAME               2 (__qualname__)

348          10 LOAD_CONST              13 (('strict',))
             12 LOAD_CONST               2 (<code object __init__ at 0x000001E77ECF62E0, file "codecs.py", line 348>)
             14 MAKE_FUNCTION            1 (defaults)
             16 STORE_NAME               3 (__init__)

373          18 LOAD_CONST               3 (<code object write at 0x000001E77ECEDE30, file "codecs.py", line 373>)
             20 MAKE_FUNCTION            0
             22 STORE_NAME               4 (write)

380          24 LOAD_CONST               4 (<code object writelines at 0x000001E77ECCEF70, file "codecs.py", line 380>)
             26 MAKE_FUNCTION            0
             28 STORE_NAME               5 (writelines)

387          30 LOAD_CONST               5 (<code object reset at 0x000001E77ED11BD0, file "codecs.py", line 387>)
             32 MAKE_FUNCTION            0
             34 STORE_NAME               6 (reset)

399          36 LOAD_CONST              14 ((0,))
             38 LOAD_CONST               7 (<code object seek at 0x000001E77ED18420, file "codecs.py", line 399>)
             40 MAKE_FUNCTION            1 (defaults)
             42 STORE_NAME               7 (seek)

405          44 LOAD_NAME                8 (getattr)

404          46 BUILD_TUPLE              1
             48 LOAD_CONST               8 (<code object __getattr__ at 0x000001E77ECF63D0, file "codecs.py", line 404>)
             50 MAKE_FUNCTION            1 (defaults)
             52 STORE_NAME               9 (__getattr__)

411          54 LOAD_CONST               9 (<code object __enter__ at 0x000001E77ED11CA0, file "codecs.py", line 411>)
             56 MAKE_FUNCTION            0
             58 STORE_NAME              10 (__enter__)

414          60 LOAD_CONST              10 (<code object __exit__ at 0x000001E77ECDAD30, file "codecs.py", line 414>)
             62 MAKE_FUNCTION            0
             64 STORE_NAME              11 (__exit__)

417          66 LOAD_CONST              11 (<code object __reduce_ex__ at 0x000001E77ECDAE30, file "codecs.py", line 417>)
             68 MAKE_FUNCTION            0
             70 STORE_NAME              12 (__reduce_ex__)
             72 LOAD_CONST              12 (None)
             74 RETURN_VALUE

Disassembly of <code object __init__ at 0x000001E77ECF62E0, file "codecs.py", line 348>:
348           0 RESUME                   0

370           2 LOAD_FAST                1 (stream)
              4 LOAD_FAST                0 (self)
              6 STORE_ATTR               0 (stream)

371          16 LOAD_FAST                2 (errors)
             18 LOAD_FAST                0 (self)
             20 STORE_ATTR               1 (errors)
             30 LOAD_CONST               1 (None)
             32 RETURN_VALUE

Disassembly of <code object write at 0x000001E77ECEDE30, file "codecs.py", line 373>:
373           0 RESUME                   0

377           2 LOAD_FAST                0 (self)
              4 STORE_SUBSCR
              8 CACHE
             10 CACHE
             12 CACHE
             14 CACHE
             16 CACHE
             18 CACHE
             20 CACHE
             22 CACHE
             24 CACHE
             26 LOAD_FAST                1 (object)
             28 LOAD_FAST                0 (self)
             30 LOAD_ATTR                1 (NULL|self + encode)
             50 CACHE
             52 CACHE
             54 UNPACK_SEQUENCE          2
             58 STORE_FAST               2 (data)
             60 STORE_FAST               3 (consumed)

378          62 LOAD_FAST                0 (self)
             64 LOAD_ATTR                2 (errors)
             84 CACHE
             86 CACHE
             88 CACHE
             90 CACHE
             92 CACHE
             94 CACHE
             96 LOAD_FAST                2 (data)
             98 UNPACK_SEQUENCE          1
            102 CALL                     1
            110 CACHE
            112 POP_TOP
            114 LOAD_CONST               1 (None)
            116 RETURN_VALUE

Disassembly of <code object writelines at 0x000001E77ECCEF70, file "codecs.py", line 380>:
380           0 RESUME                   0

385           2 LOAD_FAST                0 (self)
              4 STORE_SUBSCR
              8 CACHE
             10 CACHE
             12 CACHE
             14 CACHE
             16 CACHE
             18 CACHE
             20 CACHE
             22 CACHE
             24 CACHE
             26 LOAD_CONST               1 ('')
             28 STORE_SUBSCR
             32 CACHE
             34 CACHE
             36 CACHE
             38 CACHE
             40 CACHE
             42 CACHE
             44 CACHE
             46 CACHE
             48 CACHE
             50 LOAD_FAST                1 (list)
             52 UNPACK_SEQUENCE          1
             56 CALL                     1
             64 CACHE
             66 UNPACK_SEQUENCE          1
             70 CALL                     1
             78 CACHE
             80 POP_TOP
             82 LOAD_CONST               2 (None)
             84 RETURN_VALUE

Disassembly of <code object reset at 0x000001E77ED11BD0, file "codecs.py", line 387>:
387           0 RESUME                   0

397           2 LOAD_CONST               1 (None)
              4 RETURN_VALUE

Disassembly of <code object seek at 0x000001E77ED18420, file "codecs.py", line 399>:
399           0 RESUME                   0

400           2 LOAD_FAST                0 (self)
              4 LOAD_ATTR                0 (stream)
             24 CACHE
             26 CACHE
             28 CACHE
             30 CACHE
             32 CACHE
             34 CACHE
             36 LOAD_FAST                1 (offset)
             38 LOAD_FAST                2 (whence)
             40 UNPACK_SEQUENCE          2
             44 CALL                     2
             52 CACHE
             54 POP_TOP

401          56 LOAD_FAST                2 (whence)
             58 LOAD_CONST               1 (0)
             60 COMPARE_OP               2 (<)
             64 CACHE
             66 POP_JUMP_IF_FALSE       28 (to 124)
             68 LOAD_FAST                1 (offset)
             70 LOAD_CONST               1 (0)
             72 COMPARE_OP               2 (<)
             76 CACHE
             78 POP_JUMP_IF_FALSE       24 (to 128)

402          80 LOAD_FAST                0 (self)
             82 STORE_SUBSCR
             86 CACHE
             88 CACHE
             90 CACHE
             92 CACHE
             94 CACHE
             96 CACHE
             98 CACHE
            100 CACHE
            102 CACHE
            104 UNPACK_SEQUENCE          0
            108 CALL                     0
            116 CACHE
            118 POP_TOP
            120 LOAD_CONST               0 (None)
            122 RETURN_VALUE

401     >>  124 LOAD_CONST               0 (None)
            126 RETURN_VALUE
        >>  128 LOAD_CONST               0 (None)
            130 RETURN_VALUE

Disassembly of <code object __getattr__ at 0x000001E77ECF63D0, file "codecs.py", line 404>:
404           0 RESUME                   0

409           2 PUSH_NULL
              4 LOAD_FAST                2 (getattr)
              6 LOAD_FAST                0 (self)
              8 LOAD_ATTR                0 (stream)
             28 CACHE
             30 CACHE
             32 CACHE
             34 RETURN_VALUE

Disassembly of <code object __enter__ at 0x000001E77ED11CA0, file "codecs.py", line 411>:
411           0 RESUME                   0

412           2 LOAD_FAST                0 (self)
              4 RETURN_VALUE

Disassembly of <code object __exit__ at 0x000001E77ECDAD30, file "codecs.py", line 414>:
414           0 RESUME                   0

415           2 LOAD_FAST                0 (self)
              4 LOAD_ATTR                0 (stream)
             24 CACHE
             26 CACHE
             28 CACHE
             30 CACHE
             32 CACHE
             34 CACHE
             36 UNPACK_SEQUENCE          0
             40 CALL                     0
             48 CACHE
             50 POP_TOP
             52 LOAD_CONST               0 (None)
             54 RETURN_VALUE

Disassembly of <code object __reduce_ex__ at 0x000001E77ECDAE30, file "codecs.py", line 417>:
417           0 RESUME                   0

418           2 LOAD_GLOBAL              1 (NULL + TypeError)
             12 CACHE
             14 LOAD_CONST               1 ("can't serialize %s")
             16 LOAD_FAST                0 (self)
             18 LOAD_ATTR                1 (NULL|self + TypeError)
             38 BINARY_OP                6 (%)
             42 UNPACK_SEQUENCE          1
             46 CALL                     1
             54 CACHE
             56 RAISE_VARARGS            1

Disassembly of <code object StreamReader at 0x000001E77ECBF0E0, file "codecs.py", line 422>:
422           0 RESUME                   0
              2 LOAD_NAME                0 (__name__)
              4 STORE_NAME               1 (__module__)
              6 LOAD_CONST               0 ('StreamReader')
              8 STORE_NAME               2 (__qualname__)

424          10 LOAD_NAME                3 (str)
             12 STORE_NAME               4 (charbuffertype)

426          14 LOAD_CONST              20 (('strict',))
             16 LOAD_CONST               2 (<code object __init__ at 0x000001E77ED18570, file "codecs.py", line 426>)
             18 MAKE_FUNCTION            1 (defaults)
             20 STORE_NAME               5 (__init__)

451          22 LOAD_CONST              20 (('strict',))
             24 LOAD_CONST               3 (<code object decode at 0x000001E77ED11D70, file "codecs.py", line 451>)
             26 MAKE_FUNCTION            1 (defaults)
             28 STORE_NAME               6 (decode)

454          30 LOAD_CONST              21 ((-1, -1, False))
             32 LOAD_CONST               6 (<code object read at 0x000001E77EF29BF0, file "codecs.py", line 454>)
             34 MAKE_FUNCTION            1 (defaults)
             36 STORE_NAME               7 (read)

534          38 LOAD_CONST              22 ((None, True))
             40 LOAD_CONST               9 (<code object readline at 0x000001E77EF516D0, file "codecs.py", line 534>)
             42 MAKE_FUNCTION            1 (defaults)
             44 STORE_NAME               8 (readline)

609          46 LOAD_CONST              22 ((None, True))
             48 LOAD_CONST              10 (<code object readlines at 0x000001E77ECCF090, file "codecs.py", line 609>)
             50 MAKE_FUNCTION            1 (defaults)
             52 STORE_NAME               9 (readlines)

624          54 LOAD_CONST              11 (<code object reset at 0x000001E77ECDAF30, file "codecs.py", line 624>)
             56 MAKE_FUNCTION            0
             58 STORE_NAME              10 (reset)

637          60 LOAD_CONST              23 ((0,))
             62 LOAD_CONST              13 (<code object seek at 0x000001E77ECBEFB0, file "codecs.py", line 637>)
             64 MAKE_FUNCTION            1 (defaults)
             66 STORE_NAME              11 (seek)

645          68 LOAD_CONST              14 (<code object __next__ at 0x000001E77ECDB030, file "codecs.py", line 645>)
             70 MAKE_FUNCTION            0
             72 STORE_NAME              12 (__next__)

653          74 LOAD_CONST              15 (<code object __iter__ at 0x000001E77ED11E40, file "codecs.py", line 653>)
             76 MAKE_FUNCTION            0
             78 STORE_NAME              13 (__iter__)

657          80 LOAD_NAME               14 (getattr)

656          82 BUILD_TUPLE              1
             84 LOAD_CONST              16 (<code object __getattr__ at 0x000001E77ECF65B0, file "codecs.py", line 656>)
             86 MAKE_FUNCTION            1 (defaults)
             88 STORE_NAME              15 (__getattr__)

663          90 LOAD_CONST              17 (<code object __enter__ at 0x000001E77ED11F10, file "codecs.py", line 663>)
             92 MAKE_FUNCTION            0
             94 STORE_NAME              16 (__enter__)

666          96 LOAD_CONST              18 (<code object __exit__ at 0x000001E77ECDB130, file "codecs.py", line 666>)
             98 MAKE_FUNCTION            0
            100 STORE_NAME              17 (__exit__)

669         102 LOAD_CONST              19 (<code object __reduce_ex__ at 0x000001E77ECDB230, file "codecs.py", line 669>)
            104 MAKE_FUNCTION            0
            106 STORE_NAME              18 (__reduce_ex__)
            108 LOAD_CONST               7 (None)
            110 RETURN_VALUE

Disassembly of <code object __init__ at 0x000001E77ED18570, file "codecs.py", line 426>:
426           0 RESUME                   0

444           2 LOAD_FAST                1 (stream)
              4 LOAD_FAST                0 (self)
              6 STORE_ATTR               0 (stream)

445          16 LOAD_FAST                2 (errors)
             18 LOAD_FAST                0 (self)
             20 STORE_ATTR               1 (errors)

446          30 LOAD_CONST               1 (b'')
             32 LOAD_FAST                0 (self)
             34 STORE_ATTR               2 (bytebuffer)

447          44 LOAD_FAST                0 (self)
             46 STORE_SUBSCR
             50 CACHE
             52 CACHE
             54 CACHE
             56 CACHE
             58 CACHE
             60 CACHE
             62 CACHE
             64 CACHE
             66 CACHE
             68 UNPACK_SEQUENCE          0
             72 CALL                     0
             80 CACHE
             82 LOAD_FAST                0 (self)
             84 STORE_ATTR               4 (_empty_charbuffer)

448          94 LOAD_FAST                0 (self)
             96 LOAD_ATTR                4 (bytebuffer)
            116 CACHE

449         118 LOAD_CONST               2 (None)
            120 LOAD_FAST                0 (self)
            122 STORE_ATTR               6 (linebuffer)
            132 LOAD_CONST               2 (None)
            134 RETURN_VALUE

Disassembly of <code object decode at 0x000001E77ED11D70, file "codecs.py", line 451>:
451           0 RESUME                   0

452           2 LOAD_GLOBAL              0 (NotImplementedError)
             12 CACHE
             14 RAISE_VARARGS            1

Disassembly of <code object read at 0x000001E77EF29BF0, file "codecs.py", line 454>:
454           0 RESUME                   0

482           2 LOAD_FAST                0 (self)
              4 LOAD_ATTR                0 (linebuffer)
             24 CACHE
             26 CACHE
             28 STORE_SUBSCR
             32 CACHE
             34 CACHE
             36 CACHE
             38 CACHE
             40 CACHE
             42 CACHE
             44 CACHE
             46 CACHE
             48 CACHE
             50 LOAD_FAST                0 (self)
             52 LOAD_ATTR                0 (linebuffer)
             72 CACHE
             74 CACHE
             76 LOAD_FAST                0 (self)
             78 STORE_ATTR               3 (charbuffer)

484          88 LOAD_CONST               1 (None)
             90 LOAD_FAST                0 (self)
             92 STORE_ATTR               0 (linebuffer)

486         102 LOAD_FAST                2 (chars)
            104 LOAD_CONST               2 (0)
            106 COMPARE_OP               0 (<)
            110 CACHE
            112 POP_JUMP_IF_FALSE        2 (to 118)

489         114 LOAD_FAST                1 (size)
            116 STORE_FAST               2 (chars)

492     >>  118 NOP

494     >>  120 LOAD_FAST                2 (chars)
            122 LOAD_CONST               2 (0)
            124 COMPARE_OP               5 (<)
            128 CACHE
            130 POP_JUMP_IF_FALSE       25 (to 182)

495         132 LOAD_GLOBAL              9 (NULL + len)
            142 CACHE
            144 LOAD_FAST                0 (self)
            146 LOAD_ATTR                3 (NULL|self + _empty_charbuffer)
            166 CACHE
            168 CACHE
            170 LOAD_FAST                2 (chars)
            172 COMPARE_OP               5 (<)
            176 CACHE
            178 POP_JUMP_IF_FALSE        1 (to 182)

496         180 JUMP_FORWARD           251 (to 684)

498     >>  182 LOAD_FAST                1 (size)
            184 LOAD_CONST               2 (0)
            186 COMPARE_OP               0 (<)
            190 CACHE
            192 POP_JUMP_IF_FALSE       26 (to 246)

499         194 LOAD_FAST                0 (self)
            196 LOAD_ATTR                5 (NULL|self + join)
            216 CACHE
            218 CACHE
            220 CACHE
            222 CACHE
            224 CACHE
            226 CACHE
            228 UNPACK_SEQUENCE          0
            232 CALL                     0
            240 CACHE
            242 STORE_FAST               4 (newdata)
            244 JUMP_FORWARD            26 (to 298)

501     >>  246 LOAD_FAST                0 (self)
            248 LOAD_ATTR                5 (NULL|self + join)
            268 CACHE
            270 CACHE
            272 CACHE
            274 CACHE
            276 CACHE
            278 CACHE
            280 LOAD_FAST                1 (size)
            282 UNPACK_SEQUENCE          1
            286 CALL                     1
            294 CACHE
            296 STORE_FAST               4 (newdata)

503     >>  298 LOAD_FAST                0 (self)
            300 LOAD_ATTR                7 (NULL|self + charbuffer)
            320 POP_JUMP_IF_TRUE         1 (to 324)

505         322 JUMP_FORWARD           180 (to 684)

506     >>  324 NOP

507         326 LOAD_FAST                0 (self)
            328 STORE_SUBSCR
            332 CACHE
            334 CACHE
            336 CACHE
            338 CACHE
            340 CACHE
            342 CACHE
            344 CACHE
            346 CACHE
            348 CACHE
            350 LOAD_FAST                5 (data)
            352 LOAD_FAST                0 (self)
            354 LOAD_ATTR                9 (NULL|self + len)
            374 CACHE
            376 CACHE
            378 UNPACK_SEQUENCE          2
            382 STORE_FAST               6 (newchars)
            384 STORE_FAST               7 (decodedbytes)
            386 JUMP_FORWARD           112 (to 612)
        >>  388 PUSH_EXC_INFO

508         390 LOAD_GLOBAL             20 (UnicodeDecodeError)
            400 CACHE
            402 CHECK_EXC_MATCH
            404 POP_JUMP_IF_FALSE       99 (to 604)
            406 STORE_FAST               8 (exc)

509         408 LOAD_FAST                3 (firstline)
            410 POP_JUMP_IF_FALSE       86 (to 584)

511         412 LOAD_FAST                0 (self)
            414 STORE_SUBSCR
            418 CACHE
            420 CACHE
            422 CACHE
            424 CACHE
            426 CACHE
            428 CACHE
            430 CACHE
            432 CACHE
            434 CACHE
            436 LOAD_FAST                5 (data)
            438 LOAD_CONST               1 (None)
            440 LOAD_FAST                8 (exc)
            442 LOAD_ATTR               11 (NULL|self + stream)
            462 CACHE
            464 LOAD_FAST                0 (self)
            466 LOAD_ATTR                9 (NULL|self + len)
            486 CACHE
            488 CACHE

510         490 UNPACK_SEQUENCE          2
            494 STORE_FAST               6 (newchars)
            496 STORE_FAST               7 (decodedbytes)

512         498 LOAD_FAST                6 (newchars)
            500 STORE_SUBSCR
            504 CACHE
            506 CACHE
            508 CACHE
            510 CACHE
            512 CACHE
            514 CACHE
            516 CACHE
            518 CACHE
            520 CACHE
            522 LOAD_CONST               3 (True)
            524 KW_NAMES                 4 (('keepends',))
            526 UNPACK_SEQUENCE          1
            530 CALL                     1
            538 CACHE
            540 STORE_FAST               9 (lines)

513         542 LOAD_GLOBAL              9 (NULL + len)
            552 CACHE
            554 LOAD_FAST                9 (lines)
            556 UNPACK_SEQUENCE          1
            560 CALL                     1
            568 CACHE
            570 LOAD_CONST               5 (1)
            572 COMPARE_OP               1 (<)
            576 CACHE
            578 POP_JUMP_IF_FALSE        1 (to 582)

514         580 RAISE_VARARGS            0

513     >>  582 JUMP_FORWARD             1 (to 586)

516     >>  584 RAISE_VARARGS            0

513     >>  586 POP_EXCEPT
            588 LOAD_CONST               1 (None)
            590 STORE_FAST               8 (exc)
            592 DELETE_FAST              8 (exc)
            594 JUMP_FORWARD             8 (to 612)
        >>  596 LOAD_CONST               1 (None)
            598 STORE_FAST               8 (exc)
            600 DELETE_FAST              8 (exc)
            602 RERAISE                  1

508     >>  604 RERAISE                  0
        >>  606 COPY                     3
            608 POP_EXCEPT
            610 RERAISE                  1

518     >>  612 LOAD_FAST                5 (data)
            614 LOAD_FAST                7 (decodedbytes)
            616 LOAD_CONST               1 (None)
            618 BUILD_SLICE              2
            620 BINARY_SUBSCR
            624 CACHE
            626 CACHE
            628 CACHE
            630 LOAD_FAST                0 (self)
            632 STORE_ATTR               7 (bytebuffer)

520         642 LOAD_FAST                0 (self)
            644 COPY                     1
            646 LOAD_ATTR                3 (NULL|self + _empty_charbuffer)
            666 CACHE
            668 CACHE
            670 CACHE
            672 CACHE

522         674 LOAD_FAST                4 (newdata)
            676 POP_JUMP_IF_TRUE         1 (to 680)

523         678 JUMP_FORWARD             2 (to 684)

492     >>  680 EXTENDED_ARG             1
            682 JUMP_BACKWARD          282 (to 120)

524     >>  684 LOAD_FAST                2 (chars)
            686 LOAD_CONST               2 (0)
            688 COMPARE_OP               0 (<)
            692 CACHE
            694 POP_JUMP_IF_FALSE       20 (to 736)

526         696 LOAD_FAST                0 (self)
            698 LOAD_ATTR                3 (NULL|self + _empty_charbuffer)
            718 CACHE
            720 CACHE
            722 LOAD_FAST                0 (self)
            724 STORE_ATTR               3 (charbuffer)
            734 JUMP_FORWARD            35 (to 806)

530     >>  736 LOAD_FAST                0 (self)
            738 LOAD_ATTR                3 (NULL|self + _empty_charbuffer)
            758 CACHE
            760 CACHE
            762 CACHE
            764 STORE_FAST              10 (result)

531         766 LOAD_FAST                0 (self)
            768 LOAD_ATTR                3 (NULL|self + _empty_charbuffer)
            788 CACHE
            790 CACHE
            792 CACHE
            794 LOAD_FAST                0 (self)
            796 STORE_ATTR               3 (charbuffer)

532     >>  806 LOAD_FAST               10 (result)
            808 RETURN_VALUE
ExceptionTable:
  326 to 384 -> 388 [0]
  388 to 406 -> 606 [1] lasti
  408 to 584 -> 596 [1] lasti
  596 to 604 -> 606 [1] lasti

Disassembly of <code object readline at 0x000001E77EF516D0, file "codecs.py", line 534>:
534           0 RESUME                   0

545           2 LOAD_FAST                0 (self)
              4 LOAD_ATTR                0 (linebuffer)
             24 CACHE
             26 CACHE
             28 LOAD_CONST               1 (0)
             30 BINARY_SUBSCR
             34 CACHE
             36 CACHE
             38 CACHE
             40 STORE_FAST               3 (line)

547          42 LOAD_FAST                0 (self)
             44 LOAD_ATTR                0 (linebuffer)
             64 CACHE
             66 CACHE
             68 CACHE
             70 LOAD_FAST                0 (self)
             72 LOAD_ATTR                0 (linebuffer)
             92 CACHE
             94 CACHE
             96 LOAD_CONST               2 (1)
             98 COMPARE_OP               2 (<)
            102 CACHE
            104 POP_JUMP_IF_FALSE       25 (to 156)

551         106 LOAD_FAST                0 (self)
            108 LOAD_ATTR                0 (linebuffer)
            128 CACHE
            130 LOAD_FAST                0 (self)
            132 STORE_ATTR               2 (charbuffer)

552         142 LOAD_CONST               3 (None)
            144 LOAD_FAST                0 (self)
            146 STORE_ATTR               0 (linebuffer)

553     >>  156 LOAD_FAST                2 (keepends)
            158 POP_JUMP_IF_TRUE        28 (to 216)

554         160 LOAD_FAST                3 (line)
            162 STORE_SUBSCR
            166 CACHE
            168 CACHE
            170 CACHE
            172 CACHE
            174 CACHE
            176 CACHE
            178 CACHE
            180 CACHE
            182 CACHE
            184 LOAD_CONST               4 (False)
            186 KW_NAMES                 5 (('keepends',))
            188 UNPACK_SEQUENCE          1
            192 CALL                     1
            200 CACHE
            202 LOAD_CONST               1 (0)
            204 BINARY_SUBSCR
            208 CACHE
            210 CACHE
            212 CACHE
            214 STORE_FAST               3 (line)

555     >>  216 LOAD_FAST                3 (line)
            218 RETURN_VALUE

557         220 LOAD_FAST                1 (size)
            222 LOAD_GLOBAL              1 (NULL + linebuffer)
            232 CACHE
            234 CACHE
            236 CACHE
            238 CACHE
            240 STORE_FAST               3 (line)

560         242 NOP

561     >>  244 LOAD_FAST                0 (self)
            246 STORE_SUBSCR
            250 CACHE
            252 CACHE
            254 CACHE
            256 CACHE
            258 CACHE
            260 CACHE
            262 CACHE
            264 CACHE
            266 CACHE
            268 LOAD_FAST                4 (readsize)
            270 LOAD_CONST               7 (True)
            272 KW_NAMES                 8 (('firstline',))
            274 UNPACK_SEQUENCE          2
            278 CALL                     2
            286 CACHE
            288 STORE_FAST               5 (data)

562         290 LOAD_FAST                5 (data)
            292 POP_JUMP_IF_FALSE      110 (to 514)

566         294 LOAD_GLOBAL             13 (NULL + isinstance)
            304 CACHE
            306 LOAD_FAST                5 (data)
            308 LOAD_GLOBAL             14 (str)
            318 CACHE
            320 UNPACK_SEQUENCE          2
            324 CALL                     2
            332 CACHE
            334 POP_JUMP_IF_FALSE       21 (to 378)
            336 LOAD_FAST                5 (data)
            338 STORE_SUBSCR
            342 CACHE
            344 CACHE
            346 CACHE
            348 CACHE
            350 CACHE
            352 CACHE
            354 CACHE
            356 CACHE
            358 CACHE
            360 LOAD_CONST               9 ('\r')
            362 UNPACK_SEQUENCE          1
            366 CALL                     1
            374 CACHE
            376 POP_JUMP_IF_TRUE        42 (to 462)

567     >>  378 LOAD_GLOBAL             13 (NULL + isinstance)
            388 CACHE
            390 LOAD_FAST                5 (data)
            392 LOAD_GLOBAL             18 (bytes)
            402 CACHE
            404 UNPACK_SEQUENCE          2
            408 CALL                     2
            416 CACHE

566         418 POP_JUMP_IF_FALSE       47 (to 514)

567         420 LOAD_FAST                5 (data)
            422 STORE_SUBSCR
            426 CACHE
            428 CACHE
            430 CACHE
            432 CACHE
            434 CACHE
            436 CACHE
            438 CACHE
            440 CACHE
            442 CACHE
            444 LOAD_CONST              10 (b'\r')
            446 UNPACK_SEQUENCE          1
            450 CALL                     1
            458 CACHE

566         460 POP_JUMP_IF_FALSE       26 (to 514)

568     >>  462 LOAD_FAST                5 (data)
            464 LOAD_FAST                0 (self)
            466 STORE_SUBSCR
            470 CACHE
            472 CACHE
            474 CACHE
            476 CACHE
            478 CACHE
            480 CACHE
            482 CACHE
            484 CACHE
            486 CACHE
            488 LOAD_CONST               2 (1)
            490 LOAD_CONST               2 (1)
            492 KW_NAMES                11 (('size', 'chars'))
            494 UNPACK_SEQUENCE          2
            498 CALL                     2
            506 CACHE
            508 BINARY_OP               13 (+=)
            512 STORE_FAST               5 (data)

570     >>  514 LOAD_FAST                3 (line)
            516 LOAD_FAST                5 (data)
            518 BINARY_OP               13 (+=)
            522 STORE_FAST               3 (line)

571         524 LOAD_FAST                3 (line)
            526 STORE_SUBSCR
            530 CACHE
            532 CACHE
            534 CACHE
            536 CACHE
            538 CACHE
            540 CACHE
            542 CACHE
            544 CACHE
            546 CACHE
            548 LOAD_CONST               7 (True)
            550 KW_NAMES                 5 (('keepends',))
            552 UNPACK_SEQUENCE          1
            556 CALL                     1
            564 CACHE
            566 STORE_FAST               6 (lines)

572         568 LOAD_FAST                6 (lines)
            570 POP_JUMP_IF_FALSE      240 (to 1052)

573         572 LOAD_GLOBAL              3 (NULL + len)
            582 CACHE
            584 LOAD_FAST                6 (lines)
            586 UNPACK_SEQUENCE          1
            590 CALL                     1
            598 CACHE
            600 LOAD_CONST               2 (1)
            602 COMPARE_OP               4 (<)
            606 CACHE
            608 POP_JUMP_IF_FALSE      118 (to 846)

576         610 LOAD_FAST                6 (lines)
            612 LOAD_CONST               1 (0)
            614 BINARY_SUBSCR
            618 CACHE
            620 CACHE
            622 CACHE
            624 STORE_FAST               3 (line)

577         626 LOAD_FAST                6 (lines)
            628 LOAD_CONST               1 (0)
            630 DELETE_SUBSCR

578         632 LOAD_GLOBAL              3 (NULL + len)
            642 CACHE
            644 LOAD_FAST                6 (lines)
            646 UNPACK_SEQUENCE          1
            650 CALL                     1
            658 CACHE
            660 LOAD_CONST               2 (1)
            662 COMPARE_OP               4 (<)
            666 CACHE
            668 POP_JUMP_IF_FALSE       36 (to 742)

580         670 LOAD_FAST                6 (lines)
            672 LOAD_CONST              12 (-1)
            674 COPY                     2
            676 COPY                     2
            678 BINARY_SUBSCR
            682 CACHE
            684 CACHE
            686 CACHE
            688 LOAD_FAST                0 (self)
            690 LOAD_ATTR                2 (len)
            710 CACHE

581         712 LOAD_FAST                6 (lines)
            714 LOAD_FAST                0 (self)
            716 STORE_ATTR               0 (linebuffer)

582         726 LOAD_CONST               3 (None)
            728 LOAD_FAST                0 (self)
            730 STORE_ATTR               2 (charbuffer)
            740 JUMP_FORWARD            21 (to 784)

585     >>  742 LOAD_FAST                6 (lines)
            744 LOAD_CONST               1 (0)
            746 BINARY_SUBSCR
            750 CACHE
            752 CACHE
            754 CACHE
            756 LOAD_FAST                0 (self)
            758 LOAD_ATTR                2 (len)
            778 CACHE
            780 CACHE
            782 CACHE

586     >>  784 LOAD_FAST                2 (keepends)
            786 POP_JUMP_IF_TRUE        28 (to 844)

587         788 LOAD_FAST                3 (line)
            790 STORE_SUBSCR
            794 CACHE
            796 CACHE
            798 CACHE
            800 CACHE
            802 CACHE
            804 CACHE
            806 CACHE
            808 CACHE
            810 CACHE
            812 LOAD_CONST               4 (False)
            814 KW_NAMES                 5 (('keepends',))
            816 UNPACK_SEQUENCE          1
            820 CALL                     1
            828 CACHE
            830 LOAD_CONST               1 (0)
            832 BINARY_SUBSCR
            836 CACHE
            838 CACHE
            840 CACHE
            842 STORE_FAST               3 (line)

588     >>  844 JUMP_FORWARD           153 (to 1152)

589     >>  846 LOAD_FAST                6 (lines)
            848 LOAD_CONST               1 (0)
            850 BINARY_SUBSCR
            854 CACHE
            856 CACHE
            858 CACHE
            860 STORE_FAST               7 (line0withend)

590         862 LOAD_FAST                6 (lines)
            864 LOAD_CONST               1 (0)
            866 BINARY_SUBSCR
            870 CACHE
            872 CACHE
            874 CACHE
            876 STORE_SUBSCR
            880 CACHE
            882 CACHE
            884 CACHE
            886 CACHE
            888 CACHE
            890 CACHE
            892 CACHE
            894 CACHE
            896 CACHE
            898 LOAD_CONST               4 (False)
            900 KW_NAMES                 5 (('keepends',))
            902 UNPACK_SEQUENCE          1
            906 CALL                     1
            914 CACHE
            916 LOAD_CONST               1 (0)
            918 BINARY_SUBSCR
            922 CACHE
            924 CACHE
            926 CACHE
            928 STORE_FAST               8 (line0withoutend)

591         930 LOAD_FAST                7 (line0withend)
            932 LOAD_FAST                8 (line0withoutend)
            934 COMPARE_OP               3 (<)
            938 CACHE
            940 POP_JUMP_IF_FALSE       55 (to 1052)

593         942 LOAD_FAST                0 (self)
            944 LOAD_ATTR                4 (charbuffer)
            964 CACHE
            966 CACHE
            968 CACHE
            970 CACHE
            972 CACHE
            974 CACHE
            976 LOAD_FAST                6 (lines)
            978 LOAD_CONST               2 (1)
            980 LOAD_CONST               3 (None)
            982 BUILD_SLICE              2
            984 BINARY_SUBSCR
            988 CACHE
            990 CACHE
            992 CACHE
            994 UNPACK_SEQUENCE          1
            998 CALL                     1
           1006 CACHE

594        1008 LOAD_FAST                0 (self)
           1010 LOAD_ATTR                2 (len)
           1030 CACHE
           1032 CACHE
           1034 CACHE

595        1036 LOAD_FAST                2 (keepends)
           1038 POP_JUMP_IF_FALSE        3 (to 1046)

596        1040 LOAD_FAST                7 (line0withend)
           1042 STORE_FAST               3 (line)
           1044 JUMP_FORWARD             2 (to 1050)

598     >> 1046 LOAD_FAST                8 (line0withoutend)
           1048 STORE_FAST               3 (line)

599     >> 1050 JUMP_FORWARD            50 (to 1152)

601     >> 1052 LOAD_FAST                5 (data)
           1054 POP_JUMP_IF_FALSE        2 (to 1060)
           1056 LOAD_FAST                1 (size)
           1058 POP_JUMP_IF_NONE        33 (to 1126)

602     >> 1060 LOAD_FAST                3 (line)
           1062 POP_JUMP_IF_FALSE       30 (to 1124)
           1064 LOAD_FAST                2 (keepends)
           1066 POP_JUMP_IF_TRUE        28 (to 1124)

603        1068 LOAD_FAST                3 (line)
           1070 STORE_SUBSCR
           1074 CACHE
           1076 CACHE
           1078 CACHE
           1080 CACHE
           1082 CACHE
           1084 CACHE
           1086 CACHE
           1088 CACHE
           1090 CACHE
           1092 LOAD_CONST               4 (False)
           1094 KW_NAMES                 5 (('keepends',))
           1096 UNPACK_SEQUENCE          1
           1100 CALL                     1
           1108 CACHE
           1110 LOAD_CONST               1 (0)
           1112 BINARY_SUBSCR
           1116 CACHE
           1118 CACHE
           1120 CACHE
           1122 STORE_FAST               3 (line)

604     >> 1124 JUMP_FORWARD            13 (to 1152)

605     >> 1126 LOAD_FAST                4 (readsize)
           1128 LOAD_CONST              13 (8000)
           1130 COMPARE_OP               0 (<)
           1134 CACHE
           1136 POP_JUMP_IF_FALSE        5 (to 1148)

606        1138 LOAD_FAST                4 (readsize)
           1140 LOAD_CONST              14 (2)
           1142 BINARY_OP               18 (*=)
           1146 STORE_FAST               4 (readsize)

560     >> 1148 EXTENDED_ARG             1
           1150 JUMP_BACKWARD          454 (to 244)

607     >> 1152 LOAD_FAST                3 (line)
           1154 RETURN_VALUE

Disassembly of <code object readlines at 0x000001E77ECCF090, file "codecs.py", line 609>:
609           0 RESUME                   0

621           2 LOAD_FAST                0 (self)
              4 STORE_SUBSCR
              8 CACHE
             10 CACHE
             12 CACHE
             14 CACHE
             16 CACHE
             18 CACHE
             20 CACHE
             22 CACHE
             24 CACHE
             26 UNPACK_SEQUENCE          0
             30 CALL                     0
             38 CACHE
             40 STORE_FAST               3 (data)

622          42 LOAD_FAST                3 (data)
             44 STORE_SUBSCR
             48 CACHE
             50 CACHE
             52 CACHE
             54 CACHE
             56 CACHE
             58 CACHE
             60 CACHE
             62 CACHE
             64 CACHE
             66 LOAD_FAST                2 (keepends)
             68 UNPACK_SEQUENCE          1
             72 CALL                     1
             80 CACHE
             82 RETURN_VALUE

Disassembly of <code object reset at 0x000001E77ECDAF30, file "codecs.py", line 624>:
624           0 RESUME                   0

633           2 LOAD_CONST               1 (b'')
              4 LOAD_FAST                0 (self)
              6 STORE_ATTR               0 (bytebuffer)

634          16 LOAD_FAST                0 (self)
             18 LOAD_ATTR                1 (NULL|self + bytebuffer)
             38 CACHE

635          40 LOAD_CONST               2 (None)
             42 LOAD_FAST                0 (self)
             44 STORE_ATTR               3 (linebuffer)
             54 LOAD_CONST               2 (None)
             56 RETURN_VALUE

Disassembly of <code object seek at 0x000001E77ECBEFB0, file "codecs.py", line 637>:
637           0 RESUME                   0

642           2 LOAD_FAST                0 (self)
              4 LOAD_ATTR                0 (stream)
             24 CACHE
             26 CACHE
             28 CACHE
             30 CACHE
             32 CACHE
             34 CACHE
             36 LOAD_FAST                1 (offset)
             38 LOAD_FAST                2 (whence)
             40 UNPACK_SEQUENCE          2
             44 CALL                     2
             52 CACHE
             54 POP_TOP

643          56 LOAD_FAST                0 (self)
             58 STORE_SUBSCR
             62 CACHE
             64 CACHE
             66 CACHE
             68 CACHE
             70 CACHE
             72 CACHE
             74 CACHE
             76 CACHE
             78 CACHE
             80 UNPACK_SEQUENCE          0
             84 CALL                     0
             92 CACHE
             94 POP_TOP
             96 LOAD_CONST               1 (None)
             98 RETURN_VALUE

Disassembly of <code object __next__ at 0x000001E77ECDB030, file "codecs.py", line 645>:
645           0 RESUME                   0

648           2 LOAD_FAST                0 (self)
              4 STORE_SUBSCR
              8 CACHE
             10 CACHE
             12 CACHE
             14 CACHE
             16 CACHE
             18 CACHE
             20 CACHE
             22 CACHE
             24 CACHE
             26 UNPACK_SEQUENCE          0
             30 CALL                     0
             38 CACHE
             40 STORE_FAST               1 (line)

649          42 LOAD_FAST                1 (line)
             44 POP_JUMP_IF_FALSE        2 (to 50)

650          46 LOAD_FAST                1 (line)
             48 RETURN_VALUE

651     >>   50 LOAD_GLOBAL              2 (StopIteration)
             60 CACHE
             62 RAISE_VARARGS            1

Disassembly of <code object __iter__ at 0x000001E77ED11E40, file "codecs.py", line 653>:
653           0 RESUME                   0

654           2 LOAD_FAST                0 (self)
              4 RETURN_VALUE

Disassembly of <code object __getattr__ at 0x000001E77ECF65B0, file "codecs.py", line 656>:
656           0 RESUME                   0

661           2 PUSH_NULL
              4 LOAD_FAST                2 (getattr)
              6 LOAD_FAST                0 (self)
              8 LOAD_ATTR                0 (stream)
             28 CACHE
             30 CACHE
             32 CACHE
             34 RETURN_VALUE

Disassembly of <code object __enter__ at 0x000001E77ED11F10, file "codecs.py", line 663>:
663           0 RESUME                   0

664           2 LOAD_FAST                0 (self)
              4 RETURN_VALUE

Disassembly of <code object __exit__ at 0x000001E77ECDB130, file "codecs.py", line 666>:
666           0 RESUME                   0

667           2 LOAD_FAST                0 (self)
              4 LOAD_ATTR                0 (stream)
             24 CACHE
             26 CACHE
             28 CACHE
             30 CACHE
             32 CACHE
             34 CACHE
             36 UNPACK_SEQUENCE          0
             40 CALL                     0
             48 CACHE
             50 POP_TOP
             52 LOAD_CONST               0 (None)
             54 RETURN_VALUE

Disassembly of <code object __reduce_ex__ at 0x000001E77ECDB230, file "codecs.py", line 669>:
669           0 RESUME                   0

670           2 LOAD_GLOBAL              1 (NULL + TypeError)
             12 CACHE
             14 LOAD_CONST               1 ("can't serialize %s")
             16 LOAD_FAST                0 (self)
             18 LOAD_ATTR                1 (NULL|self + TypeError)
             38 BINARY_OP                6 (%)
             42 UNPACK_SEQUENCE          1
             46 CALL                     1
             54 CACHE
             56 RAISE_VARARGS            1

Disassembly of <code object StreamReaderWriter at 0x000001E77ECEE1F0, file "codecs.py", line 674>:
674           0 RESUME                   0
              2 LOAD_NAME                0 (__name__)
              4 STORE_NAME               1 (__module__)
              6 LOAD_CONST               0 ('StreamReaderWriter')
              8 STORE_NAME               2 (__qualname__)

676          10 LOAD_CONST               1 (' StreamReaderWriter instances allow wrapping streams which\n        work in both read and write modes.\n\n        The design is such that one can use the factory functions\n        returned by the codec.lookup() function to construct the\n        instance.\n\n    ')
             12 STORE_NAME               3 (__doc__)

685          14 LOAD_CONST               2 ('unknown')
             16 STORE_NAME               4 (encoding)

687          18 LOAD_CONST              21 (('strict',))
             20 LOAD_CONST               4 (<code object __init__ at 0x000001E77ECBF340, file "codecs.py", line 687>)
             22 MAKE_FUNCTION            1 (defaults)
             24 STORE_NAME               5 (__init__)

705          26 LOAD_CONST              22 ((-1,))
             28 LOAD_CONST               6 (<code object read at 0x000001E77ECDB330, file "codecs.py", line 705>)
             30 MAKE_FUNCTION            1 (defaults)
             32 STORE_NAME               6 (read)

709          34 LOAD_CONST              23 ((None,))
             36 LOAD_CONST               8 (<code object readline at 0x000001E77ECDB430, file "codecs.py", line 709>)
             38 MAKE_FUNCTION            1 (defaults)
             40 STORE_NAME               7 (readline)

713          42 LOAD_CONST              23 ((None,))
             44 LOAD_CONST               9 (<code object readlines at 0x000001E77ECDB530, file "codecs.py", line 713>)
             46 MAKE_FUNCTION            1 (defaults)
             48 STORE_NAME               8 (readlines)

717          50 LOAD_CONST              10 (<code object __next__ at 0x000001E77ECF6790, file "codecs.py", line 717>)
             52 MAKE_FUNCTION            0
             54 STORE_NAME               9 (__next__)

722          56 LOAD_CONST              11 (<code object __iter__ at 0x000001E77ED11FE0, file "codecs.py", line 722>)
             58 MAKE_FUNCTION            0
             60 STORE_NAME              10 (__iter__)

725          62 LOAD_CONST              12 (<code object write at 0x000001E77ECDB630, file "codecs.py", line 725>)
             64 MAKE_FUNCTION            0
             66 STORE_NAME              11 (write)

729          68 LOAD_CONST              13 (<code object writelines at 0x000001E77ECDB730, file "codecs.py", line 729>)
             70 MAKE_FUNCTION            0
             72 STORE_NAME              12 (writelines)

733          74 LOAD_CONST              14 (<code object reset at 0x000001E77ECBF470, file "codecs.py", line 733>)
             76 MAKE_FUNCTION            0
             78 STORE_NAME              13 (reset)

738          80 LOAD_CONST              24 ((0,))
             82 LOAD_CONST              16 (<code object seek at 0x000001E77ECB9830, file "codecs.py", line 738>)
             84 MAKE_FUNCTION            1 (defaults)
             86 STORE_NAME              14 (seek)

745          88 LOAD_NAME               15 (getattr)

744          90 BUILD_TUPLE              1
             92 LOAD_CONST              17 (<code object __getattr__ at 0x000001E77ECF6970, file "codecs.py", line 744>)
             94 MAKE_FUNCTION            1 (defaults)
             96 STORE_NAME              16 (__getattr__)

753          98 LOAD_CONST              18 (<code object __enter__ at 0x000001E77ED120B0, file "codecs.py", line 753>)
            100 MAKE_FUNCTION            0
            102 STORE_NAME              17 (__enter__)

756         104 LOAD_CONST              19 (<code object __exit__ at 0x000001E77ECDB830, file "codecs.py", line 756>)
            106 MAKE_FUNCTION            0
            108 STORE_NAME              18 (__exit__)

759         110 LOAD_CONST              20 (<code object __reduce_ex__ at 0x000001E77ECDB930, file "codecs.py", line 759>)
            112 MAKE_FUNCTION            0
            114 STORE_NAME              19 (__reduce_ex__)
            116 LOAD_CONST               7 (None)
            118 RETURN_VALUE

Disassembly of <code object __init__ at 0x000001E77ECBF340, file "codecs.py", line 687>:
687           0 RESUME                   0

700           2 LOAD_FAST                1 (stream)
              4 LOAD_FAST                0 (self)
              6 STORE_ATTR               0 (stream)

701          16 PUSH_NULL
             18 LOAD_FAST                2 (Reader)
             20 LOAD_FAST                1 (stream)
             22 LOAD_FAST                4 (errors)
             24 UNPACK_SEQUENCE          2
             28 CALL                     2
             36 CACHE
             38 LOAD_FAST                0 (self)
             40 STORE_ATTR               1 (reader)

702          50 PUSH_NULL
             52 LOAD_FAST                3 (Writer)
             54 LOAD_FAST                1 (stream)
             56 LOAD_FAST                4 (errors)
             58 UNPACK_SEQUENCE          2
             62 CALL                     2
             70 CACHE
             72 LOAD_FAST                0 (self)
             74 STORE_ATTR               2 (writer)

703          84 LOAD_FAST                4 (errors)
             86 LOAD_FAST                0 (self)
             88 STORE_ATTR               3 (errors)
             98 LOAD_CONST               1 (None)
            100 RETURN_VALUE

Disassembly of <code object read at 0x000001E77ECDB330, file "codecs.py", line 705>:
705           0 RESUME                   0

707           2 LOAD_FAST                0 (self)
              4 LOAD_ATTR                0 (reader)
             24 CACHE
             26 CACHE
             28 CACHE
             30 CACHE
             32 CACHE
             34 CACHE
             36 LOAD_FAST                1 (size)
             38 UNPACK_SEQUENCE          1
             42 CALL                     1
             50 CACHE
             52 RETURN_VALUE

Disassembly of <code object readline at 0x000001E77ECDB430, file "codecs.py", line 709>:
709           0 RESUME                   0

711           2 LOAD_FAST                0 (self)
              4 LOAD_ATTR                0 (reader)
             24 CACHE
             26 CACHE
             28 CACHE
             30 CACHE
             32 CACHE
             34 CACHE
             36 LOAD_FAST                1 (size)
             38 UNPACK_SEQUENCE          1
             42 CALL                     1
             50 CACHE
             52 RETURN_VALUE

Disassembly of <code object readlines at 0x000001E77ECDB530, file "codecs.py", line 713>:
713           0 RESUME                   0

715           2 LOAD_FAST                0 (self)
              4 LOAD_ATTR                0 (reader)
             24 CACHE
             26 CACHE
             28 CACHE
             30 CACHE
             32 CACHE
             34 CACHE
             36 LOAD_FAST                1 (sizehint)
             38 UNPACK_SEQUENCE          1
             42 CALL                     1
             50 CACHE
             52 RETURN_VALUE

Disassembly of <code object __next__ at 0x000001E77ECF6790, file "codecs.py", line 717>:
717           0 RESUME                   0

720           2 LOAD_GLOBAL              1 (NULL + next)
             12 CACHE
             14 LOAD_FAST                0 (self)
             16 LOAD_ATTR                1 (NULL|self + next)
             36 CACHE
             38 CACHE
             40 RETURN_VALUE

Disassembly of <code object __iter__ at 0x000001E77ED11FE0, file "codecs.py", line 722>:
722           0 RESUME                   0

723           2 LOAD_FAST                0 (self)
              4 RETURN_VALUE

Disassembly of <code object write at 0x000001E77ECDB630, file "codecs.py", line 725>:
725           0 RESUME                   0

727           2 LOAD_FAST                0 (self)
              4 LOAD_ATTR                0 (writer)
             24 CACHE
             26 CACHE
             28 CACHE
             30 CACHE
             32 CACHE
             34 CACHE
             36 LOAD_FAST                1 (data)
             38 UNPACK_SEQUENCE          1
             42 CALL                     1
             50 CACHE
             52 RETURN_VALUE

Disassembly of <code object writelines at 0x000001E77ECDB730, file "codecs.py", line 729>:
729           0 RESUME                   0

731           2 LOAD_FAST                0 (self)
              4 LOAD_ATTR                0 (writer)
             24 CACHE
             26 CACHE
             28 CACHE
             30 CACHE
             32 CACHE
             34 CACHE
             36 LOAD_FAST                1 (list)
             38 UNPACK_SEQUENCE          1
             42 CALL                     1
             50 CACHE
             52 RETURN_VALUE

Disassembly of <code object reset at 0x000001E77ECBF470, file "codecs.py", line 733>:
733           0 RESUME                   0

735           2 LOAD_FAST                0 (self)
              4 LOAD_ATTR                0 (reader)
             24 CACHE
             26 CACHE
             28 CACHE
             30 CACHE
             32 CACHE
             34 CACHE
             36 UNPACK_SEQUENCE          0
             40 CALL                     0
             48 CACHE
             50 POP_TOP

736          52 LOAD_FAST                0 (self)
             54 LOAD_ATTR                2 (reset)
             74 CACHE
             76 CACHE
             78 CACHE
             80 CACHE
             82 CACHE
             84 CACHE
             86 UNPACK_SEQUENCE          0
             90 CALL                     0
             98 CACHE
            100 POP_TOP
            102 LOAD_CONST               0 (None)
            104 RETURN_VALUE

Disassembly of <code object seek at 0x000001E77ECB9830, file "codecs.py", line 738>:
738           0 RESUME                   0

739           2 LOAD_FAST                0 (self)
              4 LOAD_ATTR                0 (stream)
             24 CACHE
             26 CACHE
             28 CACHE
             30 CACHE
             32 CACHE
             34 CACHE
             36 LOAD_FAST                1 (offset)
             38 LOAD_FAST                2 (whence)
             40 UNPACK_SEQUENCE          2
             44 CALL                     2
             52 CACHE
             54 POP_TOP

740          56 LOAD_FAST                0 (self)
             58 LOAD_ATTR                2 (seek)
             78 CACHE
             80 CACHE
             82 CACHE
             84 CACHE
             86 CACHE
             88 CACHE
             90 UNPACK_SEQUENCE          0
             94 CALL                     0
            102 CACHE
            104 POP_TOP

741         106 LOAD_FAST                2 (whence)
            108 LOAD_CONST               1 (0)
            110 COMPARE_OP               2 (<)
            114 CACHE
            116 POP_JUMP_IF_FALSE       33 (to 184)
            118 LOAD_FAST                1 (offset)
            120 LOAD_CONST               1 (0)
            122 COMPARE_OP               2 (<)
            126 CACHE
            128 POP_JUMP_IF_FALSE       29 (to 188)

742         130 LOAD_FAST                0 (self)
            132 LOAD_ATTR                4 (reader)
            152 CACHE
            154 CACHE
            156 CACHE
            158 CACHE
            160 CACHE
            162 CACHE
            164 UNPACK_SEQUENCE          0
            168 CALL                     0
            176 CACHE
            178 POP_TOP
            180 LOAD_CONST               0 (None)
            182 RETURN_VALUE

741     >>  184 LOAD_CONST               0 (None)
            186 RETURN_VALUE
        >>  188 LOAD_CONST               0 (None)
            190 RETURN_VALUE

Disassembly of <code object __getattr__ at 0x000001E77ECF6970, file "codecs.py", line 744>:
744           0 RESUME                   0

749           2 PUSH_NULL
              4 LOAD_FAST                2 (getattr)
              6 LOAD_FAST                0 (self)
              8 LOAD_ATTR                0 (stream)
             28 CACHE
             30 CACHE
             32 CACHE
             34 RETURN_VALUE

Disassembly of <code object __enter__ at 0x000001E77ED120B0, file "codecs.py", line 753>:
753           0 RESUME                   0

754           2 LOAD_FAST                0 (self)
              4 RETURN_VALUE

Disassembly of <code object __exit__ at 0x000001E77ECDB830, file "codecs.py", line 756>:
756           0 RESUME                   0

757           2 LOAD_FAST                0 (self)
              4 LOAD_ATTR                0 (stream)
             24 CACHE
             26 CACHE
             28 CACHE
             30 CACHE
             32 CACHE
             34 CACHE
             36 UNPACK_SEQUENCE          0
             40 CALL                     0
             48 CACHE
             50 POP_TOP
             52 LOAD_CONST               0 (None)
             54 RETURN_VALUE

Disassembly of <code object __reduce_ex__ at 0x000001E77ECDB930, file "codecs.py", line 759>:
759           0 RESUME                   0

760           2 LOAD_GLOBAL              1 (NULL + TypeError)
             12 CACHE
             14 LOAD_CONST               1 ("can't serialize %s")
             16 LOAD_FAST                0 (self)
             18 LOAD_ATTR                1 (NULL|self + TypeError)
             38 BINARY_OP                6 (%)
             42 UNPACK_SEQUENCE          1
             46 CALL                     1
             54 CACHE
             56 RAISE_VARARGS            1

Disassembly of <code object StreamRecoder at 0x000001E77ECEE6F0, file "codecs.py", line 764>:
764           0 RESUME                   0
              2 LOAD_NAME                0 (__name__)
              4 STORE_NAME               1 (__module__)
              6 LOAD_CONST               0 ('StreamRecoder')
              8 STORE_NAME               2 (__qualname__)

766          10 LOAD_CONST               1 (' StreamRecoder instances translate data from one encoding to another.\n\n        They use the complete set of APIs returned by the\n        codecs.lookup() function to implement their task.\n\n        Data written to the StreamRecoder is first decoded into an\n        intermediate format (depending on the "decode" codec) and then\n        written to the underlying stream using an instance of the provided\n        Writer class.\n\n        In the other direction, data is read from the underlying stream using\n        a Reader instance and then encoded and returned to the caller.\n\n    ')
             12 STORE_NAME               3 (__doc__)

781          14 LOAD_CONST               2 ('unknown')
             16 STORE_NAME               4 (data_encoding)

782          18 LOAD_CONST               2 ('unknown')
             20 STORE_NAME               5 (file_encoding)

785          22 NOP

784          24 LOAD_CONST              21 (('strict',))
             26 LOAD_CONST               4 (<code object __init__ at 0x000001E77ED186C0, file "codecs.py", line 784>)
             28 MAKE_FUNCTION            1 (defaults)
             30 STORE_NAME               6 (__init__)

812          32 LOAD_CONST              22 ((-1,))
             34 LOAD_CONST               6 (<code object read at 0x000001E77ECEE330, file "codecs.py", line 812>)
             36 MAKE_FUNCTION            1 (defaults)
             38 STORE_NAME               7 (read)

818          40 LOAD_CONST              23 ((None,))
             42 LOAD_CONST               8 (<code object readline at 0x000001E77ECB6CC0, file "codecs.py", line 818>)
             44 MAKE_FUNCTION            1 (defaults)
             46 STORE_NAME               8 (readline)

827          48 LOAD_CONST              23 ((None,))
             50 LOAD_CONST               9 (<code object readlines at 0x000001E77EC863F0, file "codecs.py", line 827>)
             52 MAKE_FUNCTION            1 (defaults)
             54 STORE_NAME               9 (readlines)

833          56 LOAD_CONST              10 (<code object __next__ at 0x000001E77ECBF5A0, file "codecs.py", line 833>)
             58 MAKE_FUNCTION            0
             60 STORE_NAME              10 (__next__)

840          62 LOAD_CONST              11 (<code object __iter__ at 0x000001E77ED12320, file "codecs.py", line 840>)
             64 MAKE_FUNCTION            0
             66 STORE_NAME              11 (__iter__)

843          68 LOAD_CONST              12 (<code object write at 0x000001E77ECEE470, file "codecs.py", line 843>)
             70 MAKE_FUNCTION            0
             72 STORE_NAME              12 (write)

848          74 LOAD_CONST              13 (<code object writelines at 0x000001E77EC86550, file "codecs.py", line 848>)
             76 MAKE_FUNCTION            0
             78 STORE_NAME              13 (writelines)

854          80 LOAD_CONST              14 (<code object reset at 0x000001E77ECBF6D0, file "codecs.py", line 854>)
             82 MAKE_FUNCTION            0
             84 STORE_NAME              14 (reset)

859          86 LOAD_CONST              24 ((0,))
             88 LOAD_CONST              16 (<code object seek at 0x000001E77ECEE5B0, file "codecs.py", line 859>)
             90 MAKE_FUNCTION            1 (defaults)
             92 STORE_NAME              15 (seek)

866          94 LOAD_NAME               16 (getattr)

865          96 BUILD_TUPLE              1
             98 LOAD_CONST              17 (<code object __getattr__ at 0x000001E77ECF6B50, file "codecs.py", line 865>)
            100 MAKE_FUNCTION            1 (defaults)
            102 STORE_NAME              17 (__getattr__)

872         104 LOAD_CONST              18 (<code object __enter__ at 0x000001E77ED123F0, file "codecs.py", line 872>)
            106 MAKE_FUNCTION            0
            108 STORE_NAME              18 (__enter__)

875         110 LOAD_CONST              19 (<code object __exit__ at 0x000001E77ECDBA30, file "codecs.py", line 875>)
            112 MAKE_FUNCTION            0
            114 STORE_NAME              19 (__exit__)

878         116 LOAD_CONST              20 (<code object __reduce_ex__ at 0x000001E77ECDBB30, file "codecs.py", line 878>)
            118 MAKE_FUNCTION            0
            120 STORE_NAME              20 (__reduce_ex__)
            122 LOAD_CONST               7 (None)
            124 RETURN_VALUE

Disassembly of <code object __init__ at 0x000001E77ED186C0, file "codecs.py", line 784>:
784           0 RESUME                   0

805           2 LOAD_FAST                1 (stream)
              4 LOAD_FAST                0 (self)
              6 STORE_ATTR               0 (stream)

806          16 LOAD_FAST                2 (encode)
             18 LOAD_FAST                0 (self)
             20 STORE_ATTR               1 (encode)

807          30 LOAD_FAST                3 (decode)
             32 LOAD_FAST                0 (self)
             34 STORE_ATTR               2 (decode)

808          44 PUSH_NULL
             46 LOAD_FAST                4 (Reader)
             48 LOAD_FAST                1 (stream)
             50 LOAD_FAST                6 (errors)
             52 UNPACK_SEQUENCE          2
             56 CALL                     2
             64 CACHE
             66 LOAD_FAST                0 (self)
             68 STORE_ATTR               3 (reader)

809          78 PUSH_NULL
             80 LOAD_FAST                5 (Writer)
             82 LOAD_FAST                1 (stream)
             84 LOAD_FAST                6 (errors)
             86 UNPACK_SEQUENCE          2
             90 CALL                     2
             98 CACHE
            100 LOAD_FAST                0 (self)
            102 STORE_ATTR               4 (writer)

810         112 LOAD_FAST                6 (errors)
            114 LOAD_FAST                0 (self)
            116 STORE_ATTR               5 (errors)
            126 LOAD_CONST               1 (None)
            128 RETURN_VALUE

Disassembly of <code object read at 0x000001E77ECEE330, file "codecs.py", line 812>:
812           0 RESUME                   0

814           2 LOAD_FAST                0 (self)
              4 LOAD_ATTR                0 (reader)
             24 CACHE
             26 CACHE
             28 CACHE
             30 CACHE
             32 CACHE
             34 CACHE
             36 LOAD_FAST                1 (size)
             38 UNPACK_SEQUENCE          1
             42 CALL                     1
             50 CACHE
             52 STORE_FAST               2 (data)

815          54 LOAD_FAST                0 (self)
             56 STORE_SUBSCR
             60 CACHE
             62 CACHE
             64 CACHE
             66 CACHE
             68 CACHE
             70 CACHE
             72 CACHE
             74 CACHE
             76 CACHE
             78 LOAD_FAST                2 (data)
             80 LOAD_FAST                0 (self)
             82 LOAD_ATTR                3 (NULL|self + read)
            102 CACHE
            104 CACHE
            106 UNPACK_SEQUENCE          2
            110 STORE_FAST               2 (data)
            112 STORE_FAST               3 (bytesencoded)

816         114 LOAD_FAST                2 (data)
            116 RETURN_VALUE

Disassembly of <code object readline at 0x000001E77ECB6CC0, file "codecs.py", line 818>:
818           0 RESUME                   0

820           2 LOAD_FAST                1 (size)
              4 POP_JUMP_IF_NOT_NONE    26 (to 58)

821           6 LOAD_FAST                0 (self)
              8 LOAD_ATTR                0 (reader)
             28 CACHE
             30 CACHE
             32 CACHE
             34 CACHE
             36 CACHE
             38 CACHE
             40 UNPACK_SEQUENCE          0
             44 CALL                     0
             52 CACHE
             54 STORE_FAST               2 (data)
             56 JUMP_FORWARD            26 (to 110)

823     >>   58 LOAD_FAST                0 (self)
             60 LOAD_ATTR                0 (reader)
             80 CACHE
             82 CACHE
             84 CACHE
             86 CACHE
             88 CACHE
             90 CACHE
             92 LOAD_FAST                1 (size)
             94 UNPACK_SEQUENCE          1
             98 CALL                     1
            106 CACHE
            108 STORE_FAST               2 (data)

824     >>  110 LOAD_FAST                0 (self)
            112 STORE_SUBSCR
            116 CACHE
            118 CACHE
            120 CACHE
            122 CACHE
            124 CACHE
            126 CACHE
            128 CACHE
            130 CACHE
            132 CACHE
            134 LOAD_FAST                2 (data)
            136 LOAD_FAST                0 (self)
            138 LOAD_ATTR                3 (NULL|self + readline)
            158 CACHE
            160 CACHE
            162 UNPACK_SEQUENCE          2
            166 STORE_FAST               2 (data)
            168 STORE_FAST               3 (bytesencoded)

825         170 LOAD_FAST                2 (data)
            172 RETURN_VALUE

Disassembly of <code object readlines at 0x000001E77EC863F0, file "codecs.py", line 827>:
827           0 RESUME                   0

829           2 LOAD_FAST                0 (self)
              4 LOAD_ATTR                0 (reader)
             24 CACHE
             26 CACHE
             28 CACHE
             30 CACHE
             32 CACHE
             34 CACHE
             36 UNPACK_SEQUENCE          0
             40 CALL                     0
             48 CACHE
             50 STORE_FAST               2 (data)

830          52 LOAD_FAST                0 (self)
             54 STORE_SUBSCR
             58 CACHE
             60 CACHE
             62 CACHE
             64 CACHE
             66 CACHE
             68 CACHE
             70 CACHE
             72 CACHE
             74 CACHE
             76 LOAD_FAST                2 (data)
             78 LOAD_FAST                0 (self)
             80 LOAD_ATTR                3 (NULL|self + read)
            100 CACHE
            102 CACHE
            104 UNPACK_SEQUENCE          2
            108 STORE_FAST               2 (data)
            110 STORE_FAST               3 (bytesencoded)

831         112 LOAD_FAST                2 (data)
            114 STORE_SUBSCR
            118 CACHE
            120 CACHE
            122 CACHE
            124 CACHE
            126 CACHE
            128 CACHE
            130 CACHE
            132 CACHE
            134 CACHE
            136 LOAD_CONST               1 (True)
            138 KW_NAMES                 2 (('keepends',))
            140 UNPACK_SEQUENCE          1
            144 CALL                     1
            152 CACHE
            154 RETURN_VALUE

Disassembly of <code object __next__ at 0x000001E77ECBF5A0, file "codecs.py", line 833>:
833           0 RESUME                   0

836           2 LOAD_GLOBAL              1 (NULL + next)
             12 CACHE
             14 LOAD_FAST                0 (self)
             16 LOAD_ATTR                1 (NULL|self + next)
             36 CACHE
             38 CACHE
             40 STORE_FAST               1 (data)

837          42 LOAD_FAST                0 (self)
             44 STORE_SUBSCR
             48 CACHE
             50 CACHE
             52 CACHE
             54 CACHE
             56 CACHE
             58 CACHE
             60 CACHE
             62 CACHE
             64 CACHE
             66 LOAD_FAST                1 (data)
             68 LOAD_FAST                0 (self)
             70 LOAD_ATTR                3 (NULL|self + reader)
             90 CACHE
             92 CACHE
             94 UNPACK_SEQUENCE          2
             98 STORE_FAST               1 (data)
            100 STORE_FAST               2 (bytesencoded)

838         102 LOAD_FAST                1 (data)
            104 RETURN_VALUE

Disassembly of <code object __iter__ at 0x000001E77ED12320, file "codecs.py", line 840>:
840           0 RESUME                   0

841           2 LOAD_FAST                0 (self)
              4 RETURN_VALUE

Disassembly of <code object write at 0x000001E77ECEE470, file "codecs.py", line 843>:
843           0 RESUME                   0

845           2 LOAD_FAST                0 (self)
              4 STORE_SUBSCR
              8 CACHE
             10 CACHE
             12 CACHE
             14 CACHE
             16 CACHE
             18 CACHE
             20 CACHE
             22 CACHE
             24 CACHE
             26 LOAD_FAST                1 (data)
             28 LOAD_FAST                0 (self)
             30 LOAD_ATTR                1 (NULL|self + decode)
             50 CACHE
             52 CACHE
             54 UNPACK_SEQUENCE          2
             58 STORE_FAST               1 (data)
             60 STORE_FAST               2 (bytesdecoded)

846          62 LOAD_FAST                0 (self)
             64 LOAD_ATTR                2 (errors)
             84 CACHE
             86 CACHE
             88 CACHE
             90 CACHE
             92 CACHE
             94 CACHE
             96 LOAD_FAST                1 (data)
             98 UNPACK_SEQUENCE          1
            102 CALL                     1
            110 CACHE
            112 RETURN_VALUE

Disassembly of <code object writelines at 0x000001E77EC86550, file "codecs.py", line 848>:
848           0 RESUME                   0

850           2 LOAD_CONST               1 (b'')
              4 STORE_SUBSCR
              8 CACHE
             10 CACHE
             12 CACHE
             14 CACHE
             16 CACHE
             18 CACHE
             20 CACHE
             22 CACHE
             24 CACHE
             26 LOAD_FAST                1 (list)
             28 UNPACK_SEQUENCE          1
             32 CALL                     1
             40 CACHE
             42 STORE_FAST               2 (data)

851          44 LOAD_FAST                0 (self)
             46 STORE_SUBSCR
             50 CACHE
             52 CACHE
             54 CACHE
             56 CACHE
             58 CACHE
             60 CACHE
             62 CACHE
             64 CACHE
             66 CACHE
             68 LOAD_FAST                2 (data)
             70 LOAD_FAST                0 (self)
             72 LOAD_ATTR                2 (decode)
             92 CACHE
             94 CACHE
             96 UNPACK_SEQUENCE          2
            100 STORE_FAST               2 (data)
            102 STORE_FAST               3 (bytesdecoded)

852         104 LOAD_FAST                0 (self)
            106 LOAD_ATTR                3 (NULL|self + decode)
            126 CACHE
            128 CACHE
            130 CACHE
            132 CACHE
            134 CACHE
            136 CACHE
            138 LOAD_FAST                2 (data)
            140 UNPACK_SEQUENCE          1
            144 CALL                     1
            152 CACHE
            154 RETURN_VALUE

Disassembly of <code object reset at 0x000001E77ECBF6D0, file "codecs.py", line 854>:
854           0 RESUME                   0

856           2 LOAD_FAST                0 (self)
              4 LOAD_ATTR                0 (reader)
             24 CACHE
             26 CACHE
             28 CACHE
             30 CACHE
             32 CACHE
             34 CACHE
             36 UNPACK_SEQUENCE          0
             40 CALL                     0
             48 CACHE
             50 POP_TOP

857          52 LOAD_FAST                0 (self)
             54 LOAD_ATTR                2 (reset)
             74 CACHE
             76 CACHE
             78 CACHE
             80 CACHE
             82 CACHE
             84 CACHE
             86 UNPACK_SEQUENCE          0
             90 CALL                     0
             98 CACHE
            100 POP_TOP
            102 LOAD_CONST               0 (None)
            104 RETURN_VALUE

Disassembly of <code object seek at 0x000001E77ECEE5B0, file "codecs.py", line 859>:
859           0 RESUME                   0

862           2 LOAD_FAST                0 (self)
              4 LOAD_ATTR                0 (reader)
             24 CACHE
             26 CACHE
             28 CACHE
             30 CACHE
             32 CACHE
             34 CACHE
             36 LOAD_FAST                1 (offset)
             38 LOAD_FAST                2 (whence)
             40 UNPACK_SEQUENCE          2
             44 CALL                     2
             52 CACHE
             54 POP_TOP

863          56 LOAD_FAST                0 (self)
             58 LOAD_ATTR                2 (seek)
             78 CACHE
             80 CACHE
             82 CACHE
             84 CACHE
             86 CACHE
             88 CACHE
             90 LOAD_FAST                1 (offset)
             92 LOAD_FAST                2 (whence)
             94 UNPACK_SEQUENCE          2
             98 CALL                     2
            106 CACHE
            108 POP_TOP
            110 LOAD_CONST               0 (None)
            112 RETURN_VALUE

Disassembly of <code object __getattr__ at 0x000001E77ECF6B50, file "codecs.py", line 865>:
865           0 RESUME                   0

870           2 PUSH_NULL
              4 LOAD_FAST                2 (getattr)
              6 LOAD_FAST                0 (self)
              8 LOAD_ATTR                0 (stream)
             28 CACHE
             30 CACHE
             32 CACHE
             34 RETURN_VALUE

Disassembly of <code object __enter__ at 0x000001E77ED123F0, file "codecs.py", line 872>:
872           0 RESUME                   0

873           2 LOAD_FAST                0 (self)
              4 RETURN_VALUE

Disassembly of <code object __exit__ at 0x000001E77ECDBA30, file "codecs.py", line 875>:
875           0 RESUME                   0

876           2 LOAD_FAST                0 (self)
              4 LOAD_ATTR                0 (stream)
             24 CACHE
             26 CACHE
             28 CACHE
             30 CACHE
             32 CACHE
             34 CACHE
             36 UNPACK_SEQUENCE          0
             40 CALL                     0
             48 CACHE
             50 POP_TOP
             52 LOAD_CONST               0 (None)
             54 RETURN_VALUE

Disassembly of <code object __reduce_ex__ at 0x000001E77ECDBB30, file "codecs.py", line 878>:
878           0 RESUME                   0

879           2 LOAD_GLOBAL              1 (NULL + TypeError)
             12 CACHE
             14 LOAD_CONST               1 ("can't serialize %s")
             16 LOAD_FAST                0 (self)
             18 LOAD_ATTR                1 (NULL|self + TypeError)
             38 BINARY_OP                6 (%)
             42 UNPACK_SEQUENCE          1
             46 CALL                     1
             54 CACHE
             56 RAISE_VARARGS            1

Disassembly of <code object open at 0x000001E77ECFCF60, file "codecs.py", line 883>:
883           0 RESUME                   0

914           2 LOAD_FAST                2 (encoding)
              4 POP_JUMP_IF_NONE         9 (to 24)

915           6 LOAD_CONST               2 ('b')
              8 LOAD_FAST                1 (mode)
             10 CONTAINS_OP              1
             12 POP_JUMP_IF_FALSE        5 (to 24)

917          14 LOAD_FAST                1 (mode)
             16 LOAD_CONST               2 ('b')
             18 BINARY_OP                0 (+)
             22 STORE_FAST               1 (mode)

918     >>   24 LOAD_GLOBAL              1 (NULL + builtins)
             34 CACHE
             36 LOAD_ATTR                1 (NULL|self + builtins)
             56 CALL                     3
             64 CACHE
             66 STORE_FAST               5 (file)

919          68 LOAD_FAST                2 (encoding)
             70 POP_JUMP_IF_NOT_NONE     2 (to 76)

920          72 LOAD_FAST                5 (file)
             74 RETURN_VALUE

922     >>   76 NOP

923          78 LOAD_GLOBAL              5 (NULL + lookup)
             88 CACHE
             90 LOAD_FAST                2 (encoding)
             92 UNPACK_SEQUENCE          1
             96 CALL                     1
            104 CACHE
            106 STORE_FAST               6 (info)

924         108 LOAD_GLOBAL              7 (NULL + StreamReaderWriter)
            118 CACHE
            120 LOAD_FAST                5 (file)
            122 LOAD_FAST                6 (info)
            124 LOAD_ATTR                4 (lookup)
            144 CACHE
            146 LOAD_FAST                3 (errors)
            148 UNPACK_SEQUENCE          4
            152 CALL                     4
            160 CACHE
            162 STORE_FAST               7 (srw)

926         164 LOAD_FAST                2 (encoding)
            166 LOAD_FAST                7 (srw)
            168 STORE_ATTR               6 (encoding)

927         178 LOAD_FAST                7 (srw)
            180 RETURN_VALUE
        >>  182 PUSH_EXC_INFO

928         184 POP_TOP

929         186 LOAD_FAST                5 (file)
            188 STORE_SUBSCR
            192 CACHE
            194 CACHE
            196 CACHE
            198 CACHE
            200 CACHE
            202 CACHE
            204 CACHE
            206 CACHE
            208 CACHE
            210 UNPACK_SEQUENCE          0
            214 CALL                     0
            222 CACHE
            224 POP_TOP

930         226 RAISE_VARARGS            0
        >>  228 COPY                     3
            230 POP_EXCEPT
            232 RERAISE                  1
ExceptionTable:
  78 to 178 -> 182 [0]
  182 to 226 -> 228 [1] lasti

Disassembly of <code object EncodedFile at 0x000001E77ECB99B0, file "codecs.py", line 932>:
932           0 RESUME                   0

957           2 LOAD_FAST                2 (file_encoding)
              4 POP_JUMP_IF_NOT_NONE     2 (to 10)

958           6 LOAD_FAST                1 (data_encoding)
              8 STORE_FAST               2 (file_encoding)

959     >>   10 LOAD_GLOBAL              1 (NULL + lookup)
             20 CACHE
             22 LOAD_FAST                1 (data_encoding)
             24 UNPACK_SEQUENCE          1
             28 CALL                     1
             36 CACHE
             38 STORE_FAST               4 (data_info)

960          40 LOAD_GLOBAL              1 (NULL + lookup)
             50 CACHE
             52 LOAD_FAST                2 (file_encoding)
             54 UNPACK_SEQUENCE          1
             58 CALL                     1
             66 CACHE
             68 STORE_FAST               5 (file_info)

961          70 LOAD_GLOBAL              3 (NULL + StreamRecoder)
             80 CACHE
             82 LOAD_FAST                0 (file)
             84 LOAD_FAST                4 (data_info)
             86 LOAD_ATTR                2 (StreamRecoder)
            106 CACHE

962         108 LOAD_FAST                5 (file_info)
            110 LOAD_ATTR                4 (encode)
            130 CACHE
            132 LOAD_FAST                3 (errors)

961         134 UNPACK_SEQUENCE          6
            138 CALL                     6
            146 CACHE
            148 STORE_FAST               6 (sr)

964         150 LOAD_FAST                1 (data_encoding)
            152 LOAD_FAST                6 (sr)
            154 STORE_ATTR               6 (data_encoding)

965         164 LOAD_FAST                2 (file_encoding)
            166 LOAD_FAST                6 (sr)
            168 STORE_ATTR               7 (file_encoding)

966         178 LOAD_FAST                6 (sr)
            180 RETURN_VALUE

Disassembly of <code object getencoder at 0x000001E77ECF6C40, file "codecs.py", line 970>:
970           0 RESUME                   0

978           2 LOAD_GLOBAL              1 (NULL + lookup)
             12 CACHE
             14 LOAD_FAST                0 (encoding)
             16 UNPACK_SEQUENCE          1
             20 CALL                     1
             28 CACHE
             30 LOAD_ATTR                1 (NULL|self + lookup)

Disassembly of <code object getdecoder at 0x000001E77ECF6D30, file "codecs.py", line 980>:
980           0 RESUME                   0

988           2 LOAD_GLOBAL              1 (NULL + lookup)
             12 CACHE
             14 LOAD_FAST                0 (encoding)
             16 UNPACK_SEQUENCE          1
             20 CALL                     1
             28 CACHE
             30 LOAD_ATTR                1 (NULL|self + lookup)

Disassembly of <code object getincrementalencoder at 0x000001E77ECF3330, file "codecs.py", line 990>:
 990           0 RESUME                   0

 999           2 LOAD_GLOBAL              1 (NULL + lookup)
              12 CACHE
              14 LOAD_FAST                0 (encoding)
              16 UNPACK_SEQUENCE          1
              20 CALL                     1
              28 CACHE
              30 LOAD_ATTR                1 (NULL|self + lookup)
              50 CACHE
              52 CACHE
              54 CACHE
              56 CACHE
              58 LOAD_FAST                0 (encoding)
              60 UNPACK_SEQUENCE          1
              64 CALL                     1
              72 CACHE
              74 RAISE_VARARGS            1

1002          76 LOAD_FAST                1 (encoder)
              78 RETURN_VALUE

Disassembly of <code object getincrementaldecoder at 0x000001E77ECF3440, file "codecs.py", line 1004>:
1004           0 RESUME                   0

1013           2 LOAD_GLOBAL              1 (NULL + lookup)
              12 CACHE
              14 LOAD_FAST                0 (encoding)
              16 UNPACK_SEQUENCE          1
              20 CALL                     1
              28 CACHE