# MAIN APPLICATION CODE OBJECT
# Position: 7736828
# Filename: _strptime.py
# Function: <module>
# Args: 0
# Locals: 0
# Stack size: 5
# Flags: 0

# BYTECODE DISASSEMBLY:
==================================================
  0           0 RESUME                   0

  1           2 LOAD_CONST               0 ('Strptime-related classes and functions.\n\nCLASSES:\n    LocaleTime -- Discovers and stores locale-specific time information\n    TimeRE -- Creates regexes for pattern matching a string of text containing\n                time information\n\nFUNCTIONS:\n    _getlang -- Figure out what language is being used for the locale\n    strptime -- Calculates the time struct represented by the passed-in string\n\n')
              4 STORE_NAME               0 (__doc__)

 13           6 LOAD_CONST               1 (0)
              8 LOAD_CONST               2 (None)
             10 IMPORT_NAME              1 (time)
             12 STORE_NAME               1 (time)

 14          14 LOAD_CONST               1 (0)
             16 LOAD_CONST               2 (None)
             18 IMPORT_NAME              2 (locale)
             20 STORE_NAME               2 (locale)

 15          22 LOAD_CONST               1 (0)
             24 LOAD_CONST               2 (None)
             26 IMPORT_NAME              3 (calendar)
             28 STORE_NAME               3 (calendar)

 16          30 LOAD_CONST               1 (0)
             32 LOAD_CONST               3 (('compile',))
             34 IMPORT_NAME              4 (re)
             36 IMPORT_FROM              5 (compile)
             38 STORE_NAME               6 (re_compile)
             40 POP_TOP

 17          42 LOAD_CONST               1 (0)
             44 LOAD_CONST               4 (('IGNORECASE',))
             46 IMPORT_NAME              4 (re)
             48 IMPORT_FROM              7 (IGNORECASE)
             50 STORE_NAME               7 (IGNORECASE)
             52 POP_TOP

 18          54 LOAD_CONST               1 (0)
             56 LOAD_CONST               5 (('escape',))
             58 IMPORT_NAME              4 (re)
             60 IMPORT_FROM              8 (escape)
             62 STORE_NAME               9 (re_escape)
             64 POP_TOP

 19          66 LOAD_CONST               1 (0)
             68 LOAD_CONST               6 (('date', 'timedelta', 'timezone'))
             70 IMPORT_NAME             10 (datetime)
             72 IMPORT_FROM             11 (date)
             74 STORE_NAME              12 (datetime_date)
             76 IMPORT_FROM             13 (timedelta)
             78 STORE_NAME              14 (datetime_timedelta)
             80 IMPORT_FROM             15 (timezone)
             82 STORE_NAME              16 (datetime_timezone)
             84 POP_TOP

 22          86 LOAD_CONST               1 (0)
             88 LOAD_CONST               7 (('allocate_lock',))
             90 IMPORT_NAME             17 (_thread)
             92 IMPORT_FROM             18 (allocate_lock)
             94 STORE_NAME              19 (_thread_allocate_lock)
             96 POP_TOP

 24          98 BUILD_LIST               0
            100 STORE_NAME              20 (__all__)

 26         102 LOAD_CONST               8 (<code object _getlang at 0x000001E77EC1D930, file "_strptime.py", line 26>)
            104 MAKE_FUNCTION            0
            106 STORE_NAME              21 (_getlang)

 30         108 PUSH_NULL
            110 LOAD_BUILD_CLASS
            112 LOAD_CONST               9 (<code object LocaleTime at 0x000001E77EC1E730, file "_strptime.py", line 30>)
            114 MAKE_FUNCTION            0
            116 LOAD_CONST              10 ('LocaleTime')
            118 LOAD_NAME               22 (object)
            120 UNPACK_SEQUENCE          3
            124 CALL                     3
            132 CACHE
            134 STORE_NAME              23 (LocaleTime)

170         136 PUSH_NULL
            138 LOAD_BUILD_CLASS
            140 LOAD_CONST              11 (<code object TimeRE at 0x000001E77EC78430, file "_strptime.py", line 170>)
            142 MAKE_FUNCTION            0
            144 LOAD_CONST              12 ('TimeRE')
            146 LOAD_NAME               24 (dict)
            148 UNPACK_SEQUENCE          3
            152 CALL                     3
            160 CACHE
            162 STORE_NAME              25 (TimeRE)

265         164 PUSH_NULL
            166 LOAD_NAME               19 (_thread_allocate_lock)
            168 UNPACK_SEQUENCE          0
            172 CALL                     0
            180 CACHE
            182 STORE_NAME              26 (_cache_lock)

268         184 PUSH_NULL
            186 LOAD_NAME               25 (TimeRE)
            188 UNPACK_SEQUENCE          0
            192 CALL                     0
            200 CACHE
            202 STORE_GLOBAL            27 (_TimeRE_cache)

269         204 LOAD_CONST              13 (5)
            206 STORE_NAME              28 (_CACHE_MAX_SIZE)

270         208 BUILD_MAP                0
            210 STORE_GLOBAL            29 (_regex_cache)

272         212 LOAD_CONST              14 (<code object _calc_julian_from_U_or_W at 0x000001E77E7231B0, file "_strptime.py", line 272>)
            214 MAKE_FUNCTION            0
            216 STORE_NAME              30 (_calc_julian_from_U_or_W)

293         218 LOAD_CONST              15 (<code object _calc_julian_from_V at 0x000001E77E72A5B0, file "_strptime.py", line 293>)
            220 MAKE_FUNCTION            0
            222 STORE_NAME              31 (_calc_julian_from_V)

309         224 LOAD_CONST              20 (('%a %b %d %H:%M:%S %Y',))
            226 LOAD_CONST              17 (<code object _strptime at 0x000001E77E8AC3E0, file "_strptime.py", line 309>)
            228 MAKE_FUNCTION            1 (defaults)
            230 STORE_NAME              32 (_strptime)

558         232 LOAD_CONST              20 (('%a %b %d %H:%M:%S %Y',))
            234 LOAD_CONST              18 (<code object _strptime_time at 0x000001E77EC39BB0, file "_strptime.py", line 558>)
            236 MAKE_FUNCTION            1 (defaults)
            238 STORE_NAME              33 (_strptime_time)

564         240 LOAD_CONST              20 (('%a %b %d %H:%M:%S %Y',))
            242 LOAD_CONST              19 (<code object _strptime_datetime at 0x000001E77E6DEDB0, file "_strptime.py", line 564>)
            244 MAKE_FUNCTION            1 (defaults)
            246 STORE_NAME              34 (_strptime_datetime)
            248 LOAD_CONST               2 (None)
            250 RETURN_VALUE

Disassembly of <code object _getlang at 0x000001E77EC1D930, file "_strptime.py", line 26>:
 26           0 RESUME                   0

 28           2 LOAD_GLOBAL              1 (NULL + locale)
             12 CACHE
             14 LOAD_ATTR                1 (NULL|self + locale)
             34 CACHE
             36 LOAD_ATTR                2 (getlocale)
             56 CACHE
             58 CACHE
             60 RETURN_VALUE

Disassembly of <code object LocaleTime at 0x000001E77EC1E730, file "_strptime.py", line 30>:
 30           0 RESUME                   0
              2 LOAD_NAME                0 (__name__)
              4 STORE_NAME               1 (__module__)
              6 LOAD_CONST               0 ('LocaleTime')
              8 STORE_NAME               2 (__qualname__)

 31          10 LOAD_CONST               1 ('Stores and handles locale-specific information related to time.\n\n    ATTRIBUTES:\n        f_weekday -- full weekday names (7-item list)\n        a_weekday -- abbreviated weekday names (7-item list)\n        f_month -- full month names (13-item list; dummy value in [0], which\n                    is added by code)\n        a_month -- abbreviated month names (13-item list, dummy value in\n                    [0], which is added by code)\n        am_pm -- AM/PM representation (2-item list)\n        LC_date_time -- format string for date/time representation (string)\n        LC_date -- format string for date representation (string)\n        LC_time -- format string for time representation (string)\n        timezone -- daylight- and non-daylight-savings timezone representation\n                    (2-item list of sets)\n        lang -- Language used by instance (2-item tuple)\n    ')
             12 STORE_NAME               3 (__doc__)

 49          14 LOAD_CONST               2 (<code object __init__ at 0x000001E77E9FBFC0, file "_strptime.py", line 49>)
             16 MAKE_FUNCTION            0
             18 STORE_NAME               4 (__init__)

 80          20 LOAD_CONST               3 (<code object __calc_weekday at 0x000001E77EC4C810, file "_strptime.py", line 80>)
             22 MAKE_FUNCTION            0
             24 STORE_NAME               5 (_LocaleTime__calc_weekday)

 88          26 LOAD_CONST               4 (<code object __calc_month at 0x000001E77EC4CC00, file "_strptime.py", line 88>)
             28 MAKE_FUNCTION            0
             30 STORE_NAME               6 (_LocaleTime__calc_month)

 95          32 LOAD_CONST               5 (<code object __calc_am_pm at 0x000001E77EC70800, file "_strptime.py", line 95>)
             34 MAKE_FUNCTION            0
             36 STORE_NAME               7 (_LocaleTime__calc_am_pm)

107          38 LOAD_CONST               6 (<code object __calc_date_time at 0x000001E77E9B78C0, file "_strptime.py", line 107>)
             40 MAKE_FUNCTION            0
             42 STORE_NAME               8 (_LocaleTime__calc_date_time)

152          44 LOAD_CONST               7 (<code object __calc_timezone at 0x000001E77E9EFBD0, file "_strptime.py", line 152>)
             46 MAKE_FUNCTION            0
             48 STORE_NAME               9 (_LocaleTime__calc_timezone)
             50 LOAD_CONST               8 (None)
             52 RETURN_VALUE

Disassembly of <code object __init__ at 0x000001E77E9FBFC0, file "_strptime.py", line 49>:
 49           0 RESUME                   0

 69           2 LOAD_GLOBAL              1 (NULL + _getlang)
             12 CACHE
             14 UNPACK_SEQUENCE          0
             18 CALL                     0
             26 CACHE
             28 LOAD_FAST                0 (self)
             30 STORE_ATTR               1 (lang)

 70          40 LOAD_FAST                0 (self)
             42 STORE_SUBSCR
             46 CACHE
             48 CACHE
             50 CACHE
             52 CACHE
             54 CACHE
             56 CACHE
             58 CACHE
             60 CACHE
             62 CACHE
             64 UNPACK_SEQUENCE          0
             68 CALL                     0
             76 CACHE
             78 POP_TOP

 71          80 LOAD_FAST                0 (self)
             82 STORE_SUBSCR
             86 CACHE
             88 CACHE
             90 CACHE
             92 CACHE
             94 CACHE
             96 CACHE
             98 CACHE
            100 CACHE
            102 CACHE
            104 UNPACK_SEQUENCE          0
            108 CALL                     0
            116 CACHE
            118 POP_TOP

 72         120 LOAD_FAST                0 (self)
            122 STORE_SUBSCR
            126 CACHE
            128 CACHE
            130 CACHE
            132 CACHE
            134 CACHE
            136 CACHE
            138 CACHE
            140 CACHE
            142 CACHE
            144 UNPACK_SEQUENCE          0
            148 CALL                     0
            156 CACHE
            158 POP_TOP

 73         160 LOAD_FAST                0 (self)
            162 STORE_SUBSCR
            166 CACHE
            168 CACHE
            170 CACHE
            172 CACHE
            174 CACHE
            176 CACHE
            178 CACHE
            180 CACHE
            182 CACHE
            184 UNPACK_SEQUENCE          0
            188 CALL                     0
            196 CACHE
            198 POP_TOP

 74         200 LOAD_FAST                0 (self)
            202 STORE_SUBSCR
            206 CACHE
            208 CACHE
            210 CACHE
            212 CACHE
            214 CACHE
            216 CACHE
            218 CACHE
            220 CACHE
            222 CACHE
            224 UNPACK_SEQUENCE          0
            228 CALL                     0
            236 CACHE
            238 POP_TOP

 75         240 LOAD_GLOBAL              1 (NULL + _getlang)
            250 CACHE
            252 UNPACK_SEQUENCE          0
            256 CALL                     0
            264 CACHE
            266 LOAD_FAST                0 (self)
            268 LOAD_ATTR                1 (NULL|self + _getlang)
            288 CACHE
            290 CACHE
            292 CACHE
            294 CACHE
            296 CACHE
            298 LOAD_CONST               1 ('locale changed during initialization')
            300 UNPACK_SEQUENCE          1
            304 CALL                     1
            312 CACHE
            314 RAISE_VARARGS            1

 77         316 LOAD_GLOBAL             16 (time)
            326 CACHE
            328 LOAD_ATTR                9 (NULL|self + _LocaleTime__calc_am_pm)
            348 CACHE
            350 COMPARE_OP               3 (<)
            354 CACHE
            356 POP_JUMP_IF_TRUE        21 (to 400)
            358 LOAD_GLOBAL             16 (time)
            368 CACHE
            370 LOAD_ATTR               10 (_LocaleTime__calc_timezone)
            390 CACHE
            392 COMPARE_OP               3 (<)
            396 CACHE
            398 POP_JUMP_IF_FALSE       15 (to 430)

 78     >>  400 LOAD_GLOBAL             15 (NULL + ValueError)
            410 CACHE
            412 LOAD_CONST               2 ('timezone changed during initialization')
            414 UNPACK_SEQUENCE          1
            418 CALL                     1
            426 CACHE
            428 RAISE_VARARGS            1

 77     >>  430 LOAD_CONST               3 (None)
            432 RETURN_VALUE

Disassembly of <code object __calc_weekday at 0x000001E77EC4C810, file "_strptime.py", line 80>:
 80           0 RESUME                   0

 83           2 LOAD_CONST               1 (<code object <listcomp> at 0x000001E77EC55A10, file "_strptime.py", line 83>)
              4 MAKE_FUNCTION            0
              6 LOAD_GLOBAL              1 (NULL + range)
             16 CACHE
             18 LOAD_CONST               2 (7)
             20 UNPACK_SEQUENCE          1
             24 CALL                     1
             32 CACHE
             34 GET_ITER
             36 UNPACK_SEQUENCE          0
             40 CALL                     0
             48 CACHE
             50 STORE_FAST               1 (a_weekday)

 84          52 LOAD_CONST               3 (<code object <listcomp> at 0x000001E77EC55B30, file "_strptime.py", line 84>)
             54 MAKE_FUNCTION            0
             56 LOAD_GLOBAL              1 (NULL + range)
             66 CACHE
             68 LOAD_CONST               2 (7)
             70 UNPACK_SEQUENCE          1
             74 CALL                     1
             82 CACHE
             84 GET_ITER
             86 UNPACK_SEQUENCE          0
             90 CALL                     0
             98 CACHE
            100 STORE_FAST               2 (f_weekday)

 85         102 LOAD_FAST                1 (a_weekday)
            104 LOAD_FAST                0 (self)
            106 STORE_ATTR               1 (a_weekday)

 86         116 LOAD_FAST                2 (f_weekday)
            118 LOAD_FAST                0 (self)
            120 STORE_ATTR               2 (f_weekday)
            130 LOAD_CONST               0 (None)
            132 RETURN_VALUE

Disassembly of <code object <listcomp> at 0x000001E77EC55A10, file "_strptime.py", line 83>:
 83           0 RESUME                   0
              2 BUILD_LIST               0
              4 LOAD_FAST                0 (.0)
        >>    6 FOR_ITER                38 (to 86)
             10 LOAD_GLOBAL              0 (calendar)
             20 CACHE
             22 LOAD_ATTR                1 (NULL|self + calendar)
             42 CACHE
             44 STORE_SUBSCR
             48 CACHE
             50 CACHE
             52 CACHE
             54 CACHE
             56 CACHE
             58 CACHE
             60 CACHE
             62 CACHE
             64 CACHE
             66 UNPACK_SEQUENCE          0
             70 CALL                     0
             78 CACHE
             80 LIST_APPEND              2
             82 JUMP_BACKWARD           39 (to 6)
             84 RETURN_VALUE

Disassembly of <code object <listcomp> at 0x000001E77EC55B30, file "_strptime.py", line 84>:
 84           0 RESUME                   0
              2 BUILD_LIST               0
              4 LOAD_FAST                0 (.0)
        >>    6 FOR_ITER                38 (to 86)
             10 LOAD_GLOBAL              0 (calendar)
             20 CACHE
             22 LOAD_ATTR                1 (NULL|self + calendar)
             42 CACHE
             44 STORE_SUBSCR
             48 CACHE
             50 CACHE
             52 CACHE
             54 CACHE
             56 CACHE
             58 CACHE
             60 CACHE
             62 CACHE
             64 CACHE
             66 UNPACK_SEQUENCE          0
             70 CALL                     0
             78 CACHE
             80 LIST_APPEND              2
             82 JUMP_BACKWARD           39 (to 6)
             84 RETURN_VALUE

Disassembly of <code object __calc_month at 0x000001E77EC4CC00, file "_strptime.py", line 88>:
 88           0 RESUME                   0

 90           2 LOAD_CONST               1 (<code object <listcomp> at 0x000001E77EC55C50, file "_strptime.py", line 90>)
              4 MAKE_FUNCTION            0
              6 LOAD_GLOBAL              1 (NULL + range)
             16 CACHE
             18 LOAD_CONST               2 (13)
             20 UNPACK_SEQUENCE          1
             24 CALL                     1
             32 CACHE
             34 GET_ITER
             36 UNPACK_SEQUENCE          0
             40 CALL                     0
             48 CACHE
             50 STORE_FAST               1 (a_month)

 91          52 LOAD_CONST               3 (<code object <listcomp> at 0x000001E77EC55D70, file "_strptime.py", line 91>)
             54 MAKE_FUNCTION            0
             56 LOAD_GLOBAL              1 (NULL + range)
             66 CACHE
             68 LOAD_CONST               2 (13)
             70 UNPACK_SEQUENCE          1
             74 CALL                     1
             82 CACHE
             84 GET_ITER
             86 UNPACK_SEQUENCE          0
             90 CALL                     0
             98 CACHE
            100 STORE_FAST               2 (f_month)

 92         102 LOAD_FAST                1 (a_month)
            104 LOAD_FAST                0 (self)
            106 STORE_ATTR               1 (a_month)

 93         116 LOAD_FAST                2 (f_month)
            118 LOAD_FAST                0 (self)
            120 STORE_ATTR               2 (f_month)
            130 LOAD_CONST               0 (None)
            132 RETURN_VALUE

Disassembly of <code object <listcomp> at 0x000001E77EC55C50, file "_strptime.py", line 90>:
 90           0 RESUME                   0
              2 BUILD_LIST               0
              4 LOAD_FAST                0 (.0)
        >>    6 FOR_ITER                38 (to 86)
             10 LOAD_GLOBAL              0 (calendar)
             20 CACHE
             22 LOAD_ATTR                1 (NULL|self + calendar)
             42 CACHE
             44 STORE_SUBSCR
             48 CACHE
             50 CACHE
             52 CACHE
             54 CACHE
             56 CACHE
             58 CACHE
             60 CACHE
             62 CACHE
             64 CACHE
             66 UNPACK_SEQUENCE          0
             70 CALL                     0
             78 CACHE
             80 LIST_APPEND              2
             82 JUMP_BACKWARD           39 (to 6)
             84 RETURN_VALUE

Disassembly of <code object <listcomp> at 0x000001E77EC55D70, file "_strptime.py", line 91>:
 91           0 RESUME                   0
              2 BUILD_LIST               0
              4 LOAD_FAST                0 (.0)
        >>    6 FOR_ITER                38 (to 86)
             10 LOAD_GLOBAL              0 (calendar)
             20 CACHE
             22 LOAD_ATTR                1 (NULL|self + calendar)
             42 CACHE
             44 STORE_SUBSCR
             48 CACHE
             50 CACHE
             52 CACHE
             54 CACHE
             56 CACHE
             58 CACHE
             60 CACHE
             62 CACHE
             64 CACHE
             66 UNPACK_SEQUENCE          0
             70 CALL                     0
             78 CACHE
             80 LIST_APPEND              2
             82 JUMP_BACKWARD           39 (to 6)
             84 RETURN_VALUE

Disassembly of <code object __calc_am_pm at 0x000001E77EC70800, file "_strptime.py", line 95>:
 95           0 RESUME                   0

101           2 BUILD_LIST               0
              4 STORE_FAST               1 (am_pm)

102           6 LOAD_CONST               1 ((1, 22))
              8 GET_ITER
        >>   10 FOR_ITER                89 (to 192)

103          14 LOAD_GLOBAL              1 (NULL + time)
             24 CACHE
             26 LOAD_ATTR                1 (NULL|self + time)
             46 LOAD_CONST               6 (55)
             48 LOAD_CONST               7 (2)
             50 LOAD_CONST               8 (76)
             52 LOAD_CONST               9 (0)
             54 BUILD_TUPLE              9
             56 UNPACK_SEQUENCE          1
             60 CALL                     1
             68 CACHE
             70 STORE_FAST               3 (time_tuple)

104          72 LOAD_FAST                1 (am_pm)
             74 STORE_SUBSCR
             78 CACHE
             80 CACHE
             82 CACHE
             84 CACHE
             86 CACHE
             88 CACHE
             90 CACHE
             92 CACHE
             94 CACHE
             96 LOAD_GLOBAL              1 (NULL + time)
            106 CACHE
            108 LOAD_ATTR                3 (NULL|self + struct_time)
            128 CACHE
            130 CACHE
            132 CACHE
            134 CACHE
            136 STORE_SUBSCR
            140 CACHE
            142 CACHE
            144 CACHE
            146 CACHE
            148 CACHE
            150 CACHE
            152 CACHE
            154 CACHE
            156 CACHE
            158 UNPACK_SEQUENCE          0
            162 CALL                     0
            170 CACHE
            172 UNPACK_SEQUENCE          1
            176 CALL                     1
            184 CACHE
            186 POP_TOP
            188 JUMP_BACKWARD           90 (to 10)

105         190 LOAD_FAST                1 (am_pm)
        >>  192 LOAD_FAST                0 (self)
            194 STORE_ATTR               5 (am_pm)
            204 LOAD_CONST               0 (None)
            206 RETURN_VALUE

Disassembly of <code object __calc_date_time at 0x000001E77E9B78C0, file "_strptime.py", line 107>:
107           0 RESUME                   0

115           2 LOAD_GLOBAL              1 (NULL + time)
             12 CACHE
             14 LOAD_ATTR                1 (NULL|self + time)
             34 CACHE
             36 CACHE
             38 CACHE
             40 STORE_FAST               1 (time_tuple)

116          42 BUILD_LIST               0
             44 LOAD_CONST               2 ((None, None, None))
             46 LIST_EXTEND              1
             48 STORE_FAST               2 (date_time)

117          50 LOAD_GLOBAL              1 (NULL + time)
             60 CACHE
             62 LOAD_ATTR                2 (struct_time)
             82 CACHE
             84 CACHE
             86 CACHE
             88 CACHE
             90 STORE_SUBSCR
             94 CACHE
             96 CACHE
             98 CACHE
            100 CACHE
            102 CACHE
            104 CACHE
            106 CACHE
            108 CACHE
            110 CACHE
            112 UNPACK_SEQUENCE          0
            116 CALL                     0
            124 CACHE
            126 LOAD_FAST                2 (date_time)
            128 LOAD_CONST               4 (0)
            130 STORE_SUBSCR

118         134 LOAD_GLOBAL              1 (NULL + time)
            144 CACHE
            146 LOAD_ATTR                2 (struct_time)
            166 CACHE
            168 CACHE
            170 CACHE
            172 CACHE
            174 STORE_SUBSCR
            178 CACHE
            180 CACHE
            182 CACHE
            184 CACHE
            186 CACHE
            188 CACHE
            190 CACHE
            192 CACHE
            194 CACHE
            196 UNPACK_SEQUENCE          0
            200 CALL                     0
            208 CACHE
            210 LOAD_FAST                2 (date_time)
            212 LOAD_CONST               6 (1)
            214 STORE_SUBSCR

119         218 LOAD_GLOBAL              1 (NULL + time)
            228 CACHE
            230 LOAD_ATTR                2 (struct_time)
            250 CACHE
            252 CACHE
            254 CACHE
            256 CACHE
            258 STORE_SUBSCR
            262 CACHE
            264 CACHE
            266 CACHE
            268 CACHE
            270 CACHE
            272 CACHE
            274 CACHE
            276 CACHE
            278 CACHE
            280 UNPACK_SEQUENCE          0
            284 CALL                     0
            292 CACHE
            294 LOAD_FAST                2 (date_time)
            296 LOAD_CONST               8 (2)
            298 STORE_SUBSCR

120         302 LOAD_CONST               9 (('%', '%%'))
            304 LOAD_FAST                0 (self)
            306 LOAD_ATTR                4 (strftime)
            326 CACHE
            328 LOAD_CONST              10 ('%A')
            330 BUILD_TUPLE              2

121         332 LOAD_FAST                0 (self)
            334 LOAD_ATTR                5 (NULL|self + strftime)
            354 CACHE
            356 LOAD_CONST              12 ('%B')
            358 BUILD_TUPLE              2
            360 LOAD_FAST                0 (self)
            362 LOAD_ATTR                6 (lower)
            382 CACHE
            384 LOAD_CONST              13 ('%a')
            386 BUILD_TUPLE              2

122         388 LOAD_FAST                0 (self)
            390 LOAD_ATTR                7 (NULL|self + lower)
            410 CACHE
            412 LOAD_CONST              14 ('%b')
            414 BUILD_TUPLE              2
            416 LOAD_FAST                0 (self)
            418 LOAD_ATTR                8 (f_weekday)
            438 CACHE
            440 LOAD_CONST              15 ('%p')
            442 BUILD_TUPLE              2

123         444 LOAD_CONST              16 (('1999', '%Y'))
            446 LOAD_CONST              17 (('99', '%y'))
            448 LOAD_CONST              18 (('22', '%H'))

124         450 LOAD_CONST              19 (('44', '%M'))
            452 LOAD_CONST              20 (('55', '%S'))
            454 LOAD_CONST              21 (('76', '%j'))

125         456 LOAD_CONST              22 (('17', '%d'))
            458 LOAD_CONST              23 (('03', '%m'))
            460 LOAD_CONST              24 (('3', '%m'))

127         462 LOAD_CONST              25 (('2', '%w'))
            464 LOAD_CONST              26 (('10', '%I'))

120         466 BUILD_LIST              17
            468 STORE_FAST               3 (replacement_pairs)

128         470 LOAD_FAST                3 (replacement_pairs)
            472 STORE_SUBSCR
            476 CACHE
            478 CACHE
            480 CACHE
            482 CACHE
            484 CACHE
            486 CACHE
            488 CACHE
            490 CACHE
            492 CACHE
            494 LOAD_CONST              27 (<code object <listcomp> at 0x000001E77EBDBD70, file "_strptime.py", line 128>)
            496 MAKE_FUNCTION            0
            498 LOAD_FAST                0 (self)
            500 LOAD_ATTR               10 (f_month)
            520 CACHE
            522 CACHE
            524 CACHE
            526 UNPACK_SEQUENCE          1
            530 CALL                     1
            538 CACHE
            540 POP_TOP

130         542 LOAD_CONST              28 (((0, '%c'), (1, '%x'), (2, '%X')))
            544 GET_ITER
        >>  546 FOR_ITER               118 (to 786)
            550 CACHE
            552 STORE_FAST               4 (offset)
            554 STORE_FAST               5 (directive)

131         556 LOAD_FAST                2 (date_time)
            558 LOAD_FAST                4 (offset)
            560 BINARY_SUBSCR
            564 CACHE
            566 CACHE
            568 CACHE
            570 STORE_FAST               6 (current_format)

132         572 LOAD_FAST                3 (replacement_pairs)
            574 GET_ITER
        >>  576 FOR_ITER                29 (to 638)
            580 CACHE
            582 STORE_FAST               7 (old)
            584 STORE_FAST               8 (new)

137         586 LOAD_FAST                7 (old)
            588 POP_JUMP_IF_FALSE       22 (to 634)

138         590 LOAD_FAST                6 (current_format)
            592 STORE_SUBSCR
            596 CACHE
            598 CACHE
            600 CACHE
            602 CACHE
            604 CACHE
            606 CACHE
            608 CACHE
            610 CACHE
            612 CACHE
            614 LOAD_FAST                7 (old)
            616 LOAD_FAST                8 (new)
            618 UNPACK_SEQUENCE          2
            622 CALL                     2
            630 CACHE
            632 STORE_FAST               6 (current_format)
        >>  634 JUMP_BACKWARD           30 (to 576)

142         636 LOAD_GLOBAL              1 (NULL + time)
            646 CACHE
            648 LOAD_ATTR                1 (NULL|self + time)
            668 CACHE
            670 CACHE
            672 CACHE
            674 STORE_FAST               1 (time_tuple)

143         676 LOAD_CONST              30 ('00')
            678 LOAD_GLOBAL              1 (NULL + time)
            688 CACHE
            690 LOAD_ATTR                2 (struct_time)
            710 CACHE
            712 CACHE
            714 CACHE
            716 CACHE
            718 CONTAINS_OP              0
            720 POP_JUMP_IF_FALSE        3 (to 728)

144         722 LOAD_CONST              31 ('%W')
            724 STORE_FAST               9 (U_W)
            726 JUMP_FORWARD             2 (to 732)

146     >>  728 LOAD_CONST              32 ('%U')
            730 STORE_FAST               9 (U_W)

147     >>  732 LOAD_FAST                6 (current_format)
            734 STORE_SUBSCR
            738 CACHE
            740 CACHE
            742 CACHE
            744 CACHE
            746 CACHE
            748 CACHE
            750 CACHE
            752 CACHE
            754 CACHE
            756 LOAD_CONST              33 ('11')
            758 LOAD_FAST                9 (U_W)
            760 UNPACK_SEQUENCE          2
            764 CALL                     2
            772 CACHE
            774 LOAD_FAST                2 (date_time)
            776 LOAD_FAST                4 (offset)
            778 STORE_SUBSCR
            782 JUMP_BACKWARD          119 (to 546)

148         784 LOAD_FAST                2 (date_time)
        >>  786 LOAD_CONST               4 (0)
            788 BINARY_SUBSCR
            792 CACHE
            794 CACHE
            796 CACHE
            798 LOAD_FAST                0 (self)
            800 STORE_ATTR              12 (LC_date_time)

149         810 LOAD_FAST                2 (date_time)
            812 LOAD_CONST               6 (1)
            814 BINARY_SUBSCR
            818 CACHE
            820 CACHE
            822 CACHE
            824 LOAD_FAST                0 (self)
            826 STORE_ATTR              13 (LC_date)

150         836 LOAD_FAST                2 (date_time)
            838 LOAD_CONST               8 (2)
            840 BINARY_SUBSCR
            844 CACHE
            846 CACHE
            848 CACHE
            850 LOAD_FAST                0 (self)
            852 STORE_ATTR              14 (LC_time)
            862 LOAD_CONST               0 (None)
            864 RETURN_VALUE

Disassembly of <code object <listcomp> at 0x000001E77EBDBD70, file "_strptime.py", line 128>:
128           0 RESUME                   0
              2 BUILD_LIST               0
              4 LOAD_FAST                0 (.0)
        >>    6 FOR_ITER                11 (to 32)

129          10 LOAD_FAST                1 (tz_values)

128          12 GET_ITER
        >>   14 FOR_ITER                 6 (to 30)

128          18 LOAD_FAST                2 (tz)
             20 LOAD_CONST               0 ('%Z')
             22 BUILD_TUPLE              2
             24 LIST_APPEND              3
             26 JUMP_BACKWARD            7 (to 14)
             28 JUMP_BACKWARD           12 (to 6)
        >>   30 RETURN_VALUE

Disassembly of <code object __calc_timezone at 0x000001E77E9EFBD0, file "_strptime.py", line 152>:
152           0 RESUME                   0

156           2 NOP

157           4 LOAD_GLOBAL              1 (NULL + time)
             14 CACHE
             16 LOAD_ATTR                1 (NULL|self + time)
             36 CACHE
             38 CACHE
             40 POP_TOP
             42 JUMP_FORWARD            16 (to 76)
        >>   44 PUSH_EXC_INFO

158          46 LOAD_GLOBAL              4 (AttributeError)
             56 CACHE
             58 CHECK_EXC_MATCH
             60 POP_JUMP_IF_FALSE        3 (to 68)
             62 POP_TOP

159          64 POP_EXCEPT
             66 JUMP_FORWARD             4 (to 76)

158     >>   68 RERAISE                  0
        >>   70 COPY                     3
             72 POP_EXCEPT
             74 RERAISE                  1

160     >>   76 LOAD_GLOBAL              0 (time)
             86 CACHE
             88 LOAD_ATTR                3 (NULL|self + tzset)
            108 CACHE

161         110 LOAD_GLOBAL              0 (time)
            120 CACHE
            122 LOAD_ATTR                4 (AttributeError)
            142 CACHE

162         144 LOAD_GLOBAL             11 (NULL + frozenset)
            154 CACHE
            156 LOAD_CONST               1 ('utc')
            158 LOAD_CONST               2 ('gmt')
            160 LOAD_FAST                0 (self)
            162 LOAD_ATTR                3 (NULL|self + tzset)
            182 CACHE
            184 STORE_SUBSCR
            188 CACHE
            190 CACHE
            192 CACHE
            194 CACHE
            196 CACHE
            198 CACHE
            200 CACHE
            202 CACHE
            204 CACHE
            206 UNPACK_SEQUENCE          0
            210 CALL                     0
            218 CACHE
            220 BUILD_SET                3
            222 UNPACK_SEQUENCE          1
            226 CALL                     1
            234 CACHE
            236 STORE_FAST               1 (no_saving)

163         238 LOAD_FAST                0 (self)
            240 LOAD_ATTR                4 (AttributeError)
            260 CACHE
            262 CACHE
            264 LOAD_FAST                0 (self)
            266 LOAD_ATTR                3 (NULL|self + tzset)
            286 CACHE
            288 STORE_SUBSCR
            292 CACHE
            294 CACHE
            296 CACHE
            298 CACHE
            300 CACHE
            302 CACHE
            304 CACHE
            306 CACHE
            308 CACHE
            310 UNPACK_SEQUENCE          0
            314 CALL                     0
            322 CACHE
            324 BUILD_SET                1
            326 UNPACK_SEQUENCE          1
            330 CALL                     1
            338 CACHE
            340 STORE_FAST               2 (has_saving)
            342 JUMP_FORWARD            14 (to 372)

166         344 LOAD_GLOBAL             11 (NULL + frozenset)
            354 CACHE
            356 UNPACK_SEQUENCE          0
            360 CALL                     0
            368 CACHE
            370 STORE_FAST               2 (has_saving)

167     >>  372 LOAD_FAST                1 (no_saving)
            374 LOAD_FAST                2 (has_saving)
            376 BUILD_TUPLE              2
            378 LOAD_FAST                0 (self)
            380 STORE_ATTR               7 (timezone)
            390 LOAD_CONST               0 (None)
            392 RETURN_VALUE
ExceptionTable:
  4 to 40 -> 44 [0]
  44 to 62 -> 70 [1] lasti
  68 to 68 -> 70 [1] lasti

Disassembly of <code object TimeRE at 0x000001E77EC78430, file "_strptime.py", line 170>:
              0 MAKE_CELL                0 (__class__)

170           2 RESUME                   0
              4 LOAD_NAME                0 (__name__)
              6 STORE_NAME               1 (__module__)
              8 LOAD_CONST               0 ('TimeRE')
             10 STORE_NAME               2 (__qualname__)

171          12 LOAD_CONST               1 ('Handle conversion from format directives to regexes.')
             14 STORE_NAME               3 (__doc__)

173          16 LOAD_CONST               7 ((None,))
             18 LOAD_CLOSURE             0 (__class__)
             20 BUILD_TUPLE              1
             22 LOAD_CONST               3 (<code object __init__ at 0x000001E77E8A9430, file "_strptime.py", line 173>)
             24 MAKE_FUNCTION            9 (defaults, closure)
             26 STORE_NAME               4 (__init__)

219          28 LOAD_CONST               4 (<code object __seqToRE at 0x000001E77E79EFA0, file "_strptime.py", line 219>)
             30 MAKE_FUNCTION            0
             32 STORE_NAME               5 (_TimeRE__seqToRE)

238          34 LOAD_CONST               5 (<code object pattern at 0x000001E77EC48630, file "_strptime.py", line 238>)
             36 MAKE_FUNCTION            0
             38 STORE_NAME               6 (pattern)

261          40 LOAD_CONST               6 (<code object compile at 0x000001E77EC55E90, file "_strptime.py", line 261>)
             42 MAKE_FUNCTION            0
             44 STORE_NAME               7 (compile)
             46 LOAD_CLOSURE             0 (__class__)
             48 COPY                     1
             50 STORE_NAME               8 (__classcell__)
             52 RETURN_VALUE

Disassembly of <code object __init__ at 0x000001E77E8A9430, file "_strptime.py", line 173>:
              0 COPY_FREE_VARS           1

173           2 RESUME                   0

179           4 LOAD_FAST                1 (locale_time)
              6 POP_JUMP_IF_FALSE        8 (to 24)

180           8 LOAD_FAST                1 (locale_time)
             10 LOAD_FAST                0 (self)
             12 STORE_ATTR               0 (locale_time)
             22 JUMP_FORWARD            19 (to 62)

182     >>   24 LOAD_GLOBAL              3 (NULL + LocaleTime)
             34 CACHE
             36 UNPACK_SEQUENCE          0
             40 CALL                     0
             48 CACHE
             50 LOAD_FAST                0 (self)
             52 STORE_ATTR               0 (locale_time)

183     >>   62 LOAD_GLOBAL              5 (NULL + super)
             72 CACHE
             74 UNPACK_SEQUENCE          0
             78 CALL                     0
             86 CACHE
             88 STORE_FAST               2 (base)

184          90 LOAD_FAST                2 (base)
             92 STORE_SUBSCR
             96 CACHE
             98 CACHE
            100 CACHE
            102 CACHE
            104 CACHE
            106 CACHE
            108 CACHE
            110 CACHE
            112 CACHE
            114 BUILD_MAP                0

186         116 LOAD_CONST               1 ('d')
            118 LOAD_CONST               2 ('(?P<d>3[0-1]|[1-2]\\d|0[1-9]|[1-9]| [1-9])')

184         120 MAP_ADD                  1

187         122 LOAD_CONST               3 ('f')
            124 LOAD_CONST               4 ('(?P<f>[0-9]{1,6})')

184         126 MAP_ADD                  1

188         128 LOAD_CONST               5 ('H')
            130 LOAD_CONST               6 ('(?P<H>2[0-3]|[0-1]\\d|\\d)')

184         132 MAP_ADD                  1

189         134 LOAD_CONST               7 ('I')
            136 LOAD_CONST               8 ('(?P<I>1[0-2]|0[1-9]|[1-9])')

184         138 MAP_ADD                  1

190         140 LOAD_CONST               9 ('G')
            142 LOAD_CONST              10 ('(?P<G>\\d\\d\\d\\d)')

184         144 MAP_ADD                  1

191         146 LOAD_CONST              11 ('j')
            148 LOAD_CONST              12 ('(?P<j>36[0-6]|3[0-5]\\d|[1-2]\\d\\d|0[1-9]\\d|00[1-9]|[1-9]\\d|0[1-9]|[1-9])')

184         150 MAP_ADD                  1

192         152 LOAD_CONST              13 ('m')
            154 LOAD_CONST              14 ('(?P<m>1[0-2]|0[1-9]|[1-9])')

184         156 MAP_ADD                  1

193         158 LOAD_CONST              15 ('M')
            160 LOAD_CONST              16 ('(?P<M>[0-5]\\d|\\d)')

184         162 MAP_ADD                  1

194         164 LOAD_CONST              17 ('S')
            166 LOAD_CONST              18 ('(?P<S>6[0-1]|[0-5]\\d|\\d)')

184         168 MAP_ADD                  1

195         170 LOAD_CONST              19 ('U')
            172 LOAD_CONST              20 ('(?P<U>5[0-3]|[0-4]\\d|\\d)')

184         174 MAP_ADD                  1

196         176 LOAD_CONST              21 ('w')
            178 LOAD_CONST              22 ('(?P<w>[0-6])')

184         180 MAP_ADD                  1

197         182 LOAD_CONST              23 ('u')
            184 LOAD_CONST              24 ('(?P<u>[1-7])')

184         186 MAP_ADD                  1

198         188 LOAD_CONST              25 ('V')
            190 LOAD_CONST              26 ('(?P<V>5[0-3]|0[1-9]|[1-4]\\d|\\d)')

184         192 MAP_ADD                  1

200         194 LOAD_CONST              27 ('y')
            196 LOAD_CONST              28 ('(?P<y>\\d\\d)')

184         198 MAP_ADD                  1

203         200 LOAD_CONST              29 ('Y')
            202 LOAD_CONST              30 ('(?P<Y>\\d\\d\\d\\d)')

184         204 MAP_ADD                  1

204         206 LOAD_CONST              31 ('z')
            208 LOAD_CONST              32 ('(?P<z>[+-]\\d\\d:?[0-5]\\d(:?[0-5]\\d(\\.\\d{1,6})?)?|(?-i:Z))')

184         210 MAP_ADD                  1

205         212 LOAD_CONST              33 ('A')
            214 LOAD_FAST                0 (self)
            216 STORE_SUBSCR
            220 CACHE
            222 CACHE
            224 CACHE
            226 CACHE
            228 CACHE
            230 CACHE
            232 CACHE
            234 CACHE
            236 CACHE
            238 LOAD_FAST                0 (self)
            240 LOAD_ATTR                0 (locale_time)
            260 LOAD_CONST              33 ('A')
            262 UNPACK_SEQUENCE          2
            266 CALL                     2
            274 CACHE

184         276 MAP_ADD                  1

206         278 LOAD_FAST                0 (self)
            280 STORE_SUBSCR
            284 CACHE
            286 CACHE
            288 CACHE
            290 CACHE
            292 CACHE
            294 CACHE
            296 CACHE
            298 CACHE
            300 CACHE
            302 LOAD_FAST                0 (self)
            304 LOAD_ATTR                0 (locale_time)
            324 LOAD_CONST              34 ('a')
            326 UNPACK_SEQUENCE          2
            330 CALL                     2
            338 CACHE

207         340 LOAD_FAST                0 (self)
            342 STORE_SUBSCR
            346 CACHE
            348 CACHE
            350 CACHE
            352 CACHE
            354 CACHE
            356 CACHE
            358 CACHE
            360 CACHE
            362 CACHE
            364 LOAD_FAST                0 (self)
            366 LOAD_ATTR                0 (locale_time)
            386 LOAD_CONST              35 (1)
            388 LOAD_CONST              36 (None)
            390 BUILD_SLICE              2
            392 BINARY_SUBSCR
            396 CACHE
            398 CACHE
            400 CACHE
            402 LOAD_CONST              37 ('B')
            404 UNPACK_SEQUENCE          2
            408 CALL                     2
            416 CACHE

208         418 LOAD_FAST                0 (self)
            420 STORE_SUBSCR
            424 CACHE
            426 CACHE
            428 CACHE
            430 CACHE
            432 CACHE
            434 CACHE
            436 CACHE
            438 CACHE
            440 CACHE
            442 LOAD_FAST                0 (self)
            444 LOAD_ATTR                0 (locale_time)
            464 LOAD_CONST              35 (1)
            466 LOAD_CONST              36 (None)
            468 BUILD_SLICE              2
            470 BINARY_SUBSCR
            474 CACHE
            476 CACHE
            478 CACHE
            480 LOAD_CONST              38 ('b')
            482 UNPACK_SEQUENCE          2
            486 CALL                     2
            494 CACHE

209         496 LOAD_FAST                0 (self)
            498 STORE_SUBSCR
            502 CACHE
            504 CACHE
            506 CACHE
            508 CACHE
            510 CACHE
            512 CACHE
            514 CACHE
            516 CACHE
            518 CACHE
            520 LOAD_FAST                0 (self)
            522 LOAD_ATTR                0 (locale_time)
            542 LOAD_CONST              39 ('p')
            544 UNPACK_SEQUENCE          2
            548 CALL                     2
            556 CACHE

210         558 LOAD_FAST                0 (self)
            560 STORE_SUBSCR
            564 CACHE
            566 CACHE
            568 CACHE
            570 CACHE
            572 CACHE
            574 CACHE
            576 CACHE
            578 CACHE
            580 CACHE
            582 LOAD_CONST              40 (<code object <genexpr> at 0x000001E77EC366A0, file "_strptime.py", line 210>)
            584 MAKE_FUNCTION            0
            586 LOAD_FAST                0 (self)
            588 LOAD_ATTR                0 (locale_time)
            608 GET_ITER
            610 UNPACK_SEQUENCE          0
            614 CALL                     0
            622 CACHE

212         624 LOAD_CONST              41 ('Z')

210         626 UNPACK_SEQUENCE          2
            630 CALL                     2
            638 CACHE

213         640 LOAD_CONST              42 ('%')

184         642 LOAD_CONST              43 (('a', 'B', 'b', 'p', 'Z', '%'))
            644 BUILD_CONST_KEY_MAP      6
            646 DICT_UPDATE              1
            648 UNPACK_SEQUENCE          1
            652 CALL                     1
            660 CACHE
            662 POP_TOP

214         664 LOAD_FAST                2 (base)
            666 STORE_SUBSCR
            670 CACHE
            672 CACHE
            674 CACHE
            676 CACHE
            678 CACHE
            680 CACHE
            682 CACHE
            684 CACHE
            686 CACHE
            688 LOAD_CONST              44 ('W')
            690 LOAD_FAST                2 (base)
            692 STORE_SUBSCR
            696 CACHE
            698 CACHE
            700 CACHE
            702 CACHE
            704 CACHE
            706 CACHE
            708 CACHE
            710 CACHE
            712 CACHE
            714 LOAD_CONST              19 ('U')
            716 UNPACK_SEQUENCE          1
            720 CALL                     1
            728 CACHE
            730 STORE_SUBSCR
            734 CACHE
            736 CACHE
            738 CACHE
            740 CACHE
            742 CACHE
            744 CACHE
            746 CACHE
            748 CACHE
            750 CACHE
            752 LOAD_CONST              19 ('U')
            754 LOAD_CONST              44 ('W')
            756 UNPACK_SEQUENCE          2
            760 CALL                     2
            768 CACHE
            770 UNPACK_SEQUENCE          2
            774 CALL                     2
            782 CACHE
            784 POP_TOP

215         786 LOAD_FAST                2 (base)
            788 STORE_SUBSCR
            792 CACHE
            794 CACHE
            796 CACHE
            798 CACHE
            800 CACHE
            802 CACHE
            804 CACHE
            806 CACHE
            808 CACHE
            810 LOAD_CONST              45 ('c')
            812 LOAD_FAST                0 (self)
            814 STORE_SUBSCR
            818 CACHE
            820 CACHE
            822 CACHE
            824 CACHE
            826 CACHE
            828 CACHE
            830 CACHE
            832 CACHE
            834 CACHE
            836 LOAD_FAST                0 (self)
            838 LOAD_ATTR                0 (locale_time)
            858 UNPACK_SEQUENCE          1
            862 CALL                     1
            870 CACHE
            872 UNPACK_SEQUENCE          2
            876 CALL                     2
            884 CACHE
            886 POP_TOP

216         888 LOAD_FAST                2 (base)
            890 STORE_SUBSCR
            894 CACHE
            896 CACHE
            898 CACHE
            900 CACHE
            902 CACHE
            904 CACHE
            906 CACHE
            908 CACHE
            910 CACHE
            912 LOAD_CONST              46 ('x')
            914 LOAD_FAST                0 (self)
            916 STORE_SUBSCR
            920 CACHE
            922 CACHE
            924 CACHE
            926 CACHE
            928 CACHE
            930 CACHE
            932 CACHE
            934 CACHE
            936 CACHE
            938 LOAD_FAST                0 (self)
            940 LOAD_ATTR                0 (locale_time)
            960 UNPACK_SEQUENCE          1
            964 CALL                     1
            972 CACHE
            974 UNPACK_SEQUENCE          2
            978 CALL                     2
            986 CACHE
            988 POP_TOP

217         990 LOAD_FAST                2 (base)
            992 STORE_SUBSCR
            996 CACHE
            998 CACHE
           1000 CACHE
           1002 CACHE
           1004 CACHE
           1006 CACHE
           1008 CACHE
           1010 CACHE
           1012 CACHE
           1014 LOAD_CONST              47 ('X')
           1016 LOAD_FAST                0 (self)
           1018 STORE_SUBSCR
           1022 CACHE
           1024 CACHE
           1026 CACHE
           1028 CACHE
           1030 CACHE
           1032 CACHE
           1034 CACHE
           1036 CACHE
           1038 CACHE
           1040 LOAD_FAST                0 (self)
           1042 LOAD_ATTR                0 (locale_time)
           1062 UNPACK_SEQUENCE          1
           1066 CALL                     1
           1074 CACHE
           1076 UNPACK_SEQUENCE          2
           1080 CALL                     2
           1088 CACHE
           1090 POP_TOP
           1092 LOAD_CONST              36 (None)
           1094 RETURN_VALUE

Disassembly of <code object <genexpr> at 0x000001E77EC366A0, file "_strptime.py", line 210>:
210           0 RETURN_GENERATOR
              2 POP_TOP
              4 RESUME                   0
              6 LOAD_FAST                0 (.0)
        >>    8 FOR_ITER                11 (to 34)

211          12 LOAD_FAST                1 (tz_names)

210          14 GET_ITER
        >>   16 FOR_ITER                 6 (to 32)

210          20 LOAD_FAST                2 (tz)
             22 LOAD_FAST                0 (.0)
             24 RESUME                   1
             26 POP_TOP
             28 JUMP_BACKWARD            7 (to 16)
             30 JUMP_BACKWARD           12 (to 8)
        >>   32 LOAD_CONST               0 (None)
        >>   34 RETURN_VALUE

Disassembly of <code object __seqToRE at 0x000001E77E79EFA0, file "_strptime.py", line 219>:
219           0 RESUME                   0

228           2 LOAD_GLOBAL              1 (NULL + sorted)
             12 CACHE
             14 LOAD_FAST                1 (to_convert)
             16 LOAD_GLOBAL              2 (len)
             26 CACHE
             28 LOAD_CONST               1 (True)
             30 KW_NAMES                 2 (('key', 'reverse'))
             32 UNPACK_SEQUENCE          3
             36 CALL                     3
             44 CACHE
             46 STORE_FAST               1 (to_convert)

229          48 LOAD_FAST                1 (to_convert)
             50 GET_ITER
        >>   52 FOR_ITER                10 (to 76)

230          56 LOAD_FAST                3 (value)
             58 LOAD_CONST               3 ('')
             60 COMPARE_OP               3 (<)
             64 CACHE
             66 POP_JUMP_IF_FALSE        2 (to 72)

231          68 POP_TOP
             70 JUMP_FORWARD             3 (to 78)

230     >>   72 JUMP_BACKWARD           11 (to 52)

233          74 LOAD_CONST               3 ('')
        >>   76 RETURN_VALUE

234     >>   78 LOAD_CONST               4 ('|')
             80 STORE_SUBSCR
             84 CACHE
             86 CACHE
             88 CACHE
             90 CACHE
             92 CACHE
             94 CACHE
             96 CACHE
             98 CACHE
            100 CACHE
            102 LOAD_CONST               5 (<code object <genexpr> at 0x000001E77EC1F230, file "_strptime.py", line 234>)
            104 MAKE_FUNCTION            0
            106 LOAD_FAST                1 (to_convert)
            108 GET_ITER
            110 UNPACK_SEQUENCE          0
            114 CALL                     0
            122 CACHE
            124 UNPACK_SEQUENCE          1
            128 CALL                     1
            136 CACHE
            138 STORE_FAST               4 (regex)
            140 LOAD_CONST               6 ('(?P<')

235         142 LOAD_FAST                2 (directive)
            144 FORMAT_VALUE             1 (str)
            146 LOAD_CONST               7 ('>')
            148 LOAD_FAST                4 (regex)
            150 FORMAT_VALUE             1 (str)
            152 BUILD_STRING             4
            154 STORE_FAST               4 (regex)

236         156 LOAD_CONST               8 ('%s)')
            158 LOAD_FAST                4 (regex)
            160 BINARY_OP                6 (%)
            164 RETURN_VALUE

Disassembly of <code object <genexpr> at 0x000001E77EC1F230, file "_strptime.py", line 234>:
234           0 RETURN_GENERATOR
              2 POP_TOP
              4 RESUME                   0
              6 LOAD_FAST                0 (.0)
        >>    8 FOR_ITER                19 (to 50)
             12 LOAD_GLOBAL              1 (NULL + re_escape)
             22 CACHE
             24 LOAD_FAST                1 (stuff)
             26 UNPACK_SEQUENCE          1
             30 CALL                     1
             38 CACHE
             40 LOAD_FAST                0 (.0)
             42 RESUME                   1
             44 POP_TOP
             46 JUMP_BACKWARD           20 (to 8)
             48 LOAD_CONST               0 (None)
        >>   50 RETURN_VALUE

Disassembly of <code object pattern at 0x000001E77EC48630, file "_strptime.py", line 238>:
238           0 RESUME                   0

245           2 LOAD_CONST               1 ('')
              4 STORE_FAST               2 (processed_format)

249           6 LOAD_GLOBAL              1 (NULL + re_compile)
             16 CACHE
             18 LOAD_CONST               2 ('([\\\\.^$*+?\\(\\){}\\[\\]|])')
             20 UNPACK_SEQUENCE          1
             24 CALL                     1
             32 CACHE
             34 STORE_FAST               3 (regex_chars)

250          36 LOAD_FAST                3 (regex_chars)
             38 STORE_SUBSCR
             42 CACHE
             44 CACHE
             46 CACHE
             48 CACHE
             50 CACHE
             52 CACHE
             54 CACHE
             56 CACHE
             58 CACHE
             60 LOAD_CONST               3 ('\\\\\\1')
             62 LOAD_FAST                1 (format)
             64 UNPACK_SEQUENCE          2
             68 CALL                     2
             76 CACHE
             78 STORE_FAST               1 (format)

251          80 LOAD_GLOBAL              1 (NULL + re_compile)
             90 CACHE
             92 LOAD_CONST               4 ('\\s+')
             94 UNPACK_SEQUENCE          1
             98 CALL                     1
            106 CACHE
            108 STORE_FAST               4 (whitespace_replacement)

252         110 LOAD_FAST                4 (whitespace_replacement)
            112 STORE_SUBSCR
            116 CACHE
            118 CACHE
            120 CACHE
            122 CACHE
            124 CACHE
            126 CACHE
            128 CACHE
            130 CACHE
            132 CACHE
            134 LOAD_CONST               5 ('\\\\s+')
            136 LOAD_FAST                1 (format)
            138 UNPACK_SEQUENCE          2
            142 CALL                     2
            150 CACHE
            152 STORE_FAST               1 (format)

253         154 LOAD_CONST               6 ('%')
            156 LOAD_FAST                1 (format)
            158 CONTAINS_OP              0
            160 POP_JUMP_IF_FALSE       72 (to 306)

254         162 LOAD_FAST                1 (format)
            164 STORE_SUBSCR
            168 CACHE
            170 CACHE
            172 CACHE
            174 CACHE
            176 CACHE
            178 CACHE
            180 CACHE
            182 CACHE
            184 CACHE
            186 LOAD_CONST               6 ('%')
            188 UNPACK_SEQUENCE          1
            192 CALL                     1
            200 CACHE
            202 LOAD_CONST               7 (1)
            204 BINARY_OP                0 (+)
            208 STORE_FAST               5 (directive_index)

255         210 LOAD_FAST                2 (processed_format)
            212 FORMAT_VALUE             1 (str)

256         214 LOAD_FAST                1 (format)
            216 LOAD_CONST               8 (None)
            218 LOAD_FAST                5 (directive_index)
            220 LOAD_CONST               7 (1)
            222 BINARY_OP               10 (-)
            226 BUILD_SLICE              2
            228 BINARY_SUBSCR
            232 CACHE
            234 CACHE
            236 CACHE
            238 FORMAT_VALUE             1 (str)

257         240 LOAD_FAST                0 (self)
            242 LOAD_FAST                1 (format)
            244 LOAD_FAST                5 (directive_index)
            246 BINARY_SUBSCR
            250 CACHE
            252 CACHE
            254 CACHE
            256 BINARY_SUBSCR
            260 CACHE
            262 CACHE
            264 CACHE
            266 FORMAT_VALUE             1 (str)

255         268 BUILD_STRING             3
            270 STORE_FAST               2 (processed_format)

258         272 LOAD_FAST                1 (format)
            274 LOAD_FAST                5 (directive_index)
            276 LOAD_CONST               7 (1)
            278 BINARY_OP                0 (+)
            282 LOAD_CONST               8 (None)
            284 BUILD_SLICE              2
            286 BINARY_SUBSCR
            290 CACHE
            292 CACHE
            294 CACHE
            296 STORE_FAST               1 (format)

253         298 LOAD_CONST               6 ('%')
            300 LOAD_FAST                1 (format)
            302 CONTAINS_OP              0
