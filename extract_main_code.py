#!/usr/bin/env python3
"""
Extract main application code, skipping standard library modules
"""
import struct
import os
import sys
import marshal
import dis
import types

def is_stdlib_module(filename):
    """Check if this is a standard library module"""
    if not filename:
        return False
        
    stdlib_patterns = [
        '__future__', '__hello__', '__phello__',
        '_aix_support', '_bootsubprocess', '_collections_abc', '_compat_pickle',
        '_compression', '_markupbase', '_osx_support', '_py_abc', '_pydecimal',
        '_pyio', '_sitebuiltins', '_strptime', '_threading_local', '_weakrefset',
        'abc', 'argparse', 'ast', 'base64', 'bisect', 'calendar', 'codecs',
        'collections', 'contextlib', 'copy', 'copyreg', 'csv', 'datetime',
        'decimal', 'difflib', 'dis', 'email', 'encodings', 'enum', 'fnmatch',
        'fractions', 'functools', 'getopt', 'gettext', 'glob', 'gzip',
        'hashlib', 'heapq', 'html', 'http', 'importlib', 'inspect', 'io',
        'itertools', 'json', 'keyword', 'linecache', 'locale', 'logging',
        'marshal', 'math', 'mimetypes', 'numbers', 'operator', 'optparse',
        'os', 'pathlib', 'pickle', 'pkgutil', 'platform', 'plistlib',
        'posixpath', 'pprint', 'py_compile', 'pyclbr', 'pydoc', 'queue',
        're', 'reprlib', 'runpy', 'shlex', 'shutil', 'site', 'socket',
        'socketserver', 'ssl', 'stat', 'string', 'stringprep', 'struct',
        'subprocess', 'sys', 'tarfile', 'tempfile', 'textwrap', 'threading',
        'time', 'token', 'tokenize', 'traceback', 'types', 'typing',
        'urllib', 'uuid', 'warnings', 'weakref', 'xml', 'zipfile', 'zipimport'
    ]
    
    filename_lower = filename.lower()
    for pattern in stdlib_patterns:
        if pattern in filename_lower:
            return True
    return False

def find_application_code(data, max_objects=100):
    """Find application-specific code objects"""
    marshal_candidates = []
    
    # Look for marshal data pattern
    pattern = b'\xe3'  # TYPE_CODE
    
    offset = 0
    count = 0
    total_found = 0
    
    while total_found < 1000 and count < max_objects:  # Search more but keep fewer
        pos = data.find(pattern, offset)
        if pos == -1:
            break
            
        # Try to unmarshal from this position
        try:
            remaining_data = data[pos:]
            if len(remaining_data) > 100:  # Need reasonable amount of data
                obj = marshal.loads(remaining_data)
                if isinstance(obj, types.CodeType):
                    total_found += 1
                    
                    # Check if this is application code
                    filename = getattr(obj, 'co_filename', '')
                    if not is_stdlib_module(filename):
                        marshal_candidates.append({
                            'position': pos,
                            'code_object': obj,
                            'pattern': pattern
                        })
                        print(f"Found application code {count+1} at position {pos}: {filename}")
                        count += 1
                    else:
                        if total_found % 50 == 0:  # Progress indicator
                            print(f"Scanned {total_found} code objects, found {count} application objects...")
        except:
            pass
            
        offset = pos + 1
        
    return marshal_candidates

def extract_application_code(exe_path, output_dir, max_objects=50):
    """Extract application-specific code objects"""
    print(f"Extracting application code from {exe_path}")
    
    with open(exe_path, 'rb') as f:
        data = f.read()
        
    print(f"File size: {len(data)} bytes")
    
    # Create output directory
    os.makedirs(output_dir, exist_ok=True)
    
    # Find application code
    marshal_candidates = find_application_code(data, max_objects)
    
    print(f"\nFound {len(marshal_candidates)} application code objects")
    
    extracted_count = 0
    for i, candidate in enumerate(marshal_candidates):
        try:
            code_obj = candidate['code_object']
            filename = f"app_{i+1:03d}"
            
            # Get original filename if available
            if hasattr(code_obj, 'co_filename') and code_obj.co_filename:
                orig_name = os.path.basename(code_obj.co_filename)
                if orig_name and orig_name not in ['<string>', '<frozen>', '<built-in>']:
                    filename = orig_name.replace('.py', '').replace('.', '_').replace('\\', '_').replace('/', '_')
                    
            print(f"Processing {filename}")
            print(f"  Original: {getattr(code_obj, 'co_filename', 'unknown')}")
            print(f"  Function: {getattr(code_obj, 'co_name', 'unknown')}")
            
            # Try to use uncompyle6 properly
            try:
                import uncompyle6.main
                output_path = os.path.join(output_dir, filename + '.py')
                with open(output_path, 'w', encoding='utf-8') as f:
                    f.write(f"# Decompiled from position {candidate['position']}\n")
                    f.write(f"# Original filename: {getattr(code_obj, 'co_filename', 'unknown')}\n")
                    f.write(f"# Function name: {getattr(code_obj, 'co_name', 'unknown')}\n\n")
                    
                    # Use uncompyle6 properly
                    uncompyle6.main.decompile(3.11, code_obj, f)  # Assume Python 3.11
                print(f"  ✓ Decompiled to {output_path}")
                extracted_count += 1
            except Exception as e:
                print(f"  ✗ Decompilation failed: {e}")
                
                # Fallback to disassembly with more details
                output_path = os.path.join(output_dir, filename + '_disasm.txt')
                with open(output_path, 'w', encoding='utf-8') as f:
                    f.write(f"# Code object from position {candidate['position']}\n")
                    f.write(f"# Filename: {getattr(code_obj, 'co_filename', 'unknown')}\n")
                    f.write(f"# Name: {getattr(code_obj, 'co_name', 'unknown')}\n")
                    f.write(f"# Args: {getattr(code_obj, 'co_argcount', 0)}\n")
                    f.write(f"# Locals: {getattr(code_obj, 'co_nlocals', 0)}\n")
                    f.write(f"# Stack size: {getattr(code_obj, 'co_stacksize', 0)}\n")
                    f.write(f"# Flags: {getattr(code_obj, 'co_flags', 0)}\n\n")
                    
                    # Disassemble
                    dis.dis(code_obj, file=f)
                    
                    # Extract constants with more detail
                    if hasattr(code_obj, 'co_consts'):
                        f.write("\n\n# Constants:\n")
                        for j, const in enumerate(code_obj.co_consts):
                            if isinstance(const, str):
                                if len(const) < 100:
                                    f.write(f"# {j}: {repr(const)}\n")
                                else:
                                    f.write(f"# {j}: <string length {len(const)}>\n")
                            elif isinstance(const, (int, float, bool, type(None))):
                                f.write(f"# {j}: {repr(const)}\n")
                            elif isinstance(const, types.CodeType):
                                f.write(f"# {j}: <code object {const.co_name}>\n")
                            else:
                                f.write(f"# {j}: {type(const).__name__}\n")
                                
                    # Extract names
                    if hasattr(code_obj, 'co_names'):
                        f.write("\n\n# Names:\n")
                        for j, name in enumerate(code_obj.co_names):
                            f.write(f"# {j}: {repr(name)}\n")
                            
                    # Extract variable names
                    if hasattr(code_obj, 'co_varnames'):
                        f.write("\n\n# Variable names:\n")
                        for j, name in enumerate(code_obj.co_varnames):
                            f.write(f"# {j}: {repr(name)}\n")
                            
                print(f"  ✓ Disassembled to {output_path}")
                extracted_count += 1
                
        except Exception as e:
            print(f"  ✗ Failed to process code object {i+1}: {e}")
    
    print(f"\nExtracted {extracted_count} application code objects to {output_dir}")
    
    # Create a summary file
    summary_path = os.path.join(output_dir, 'APP_SUMMARY.txt')
    with open(summary_path, 'w', encoding='utf-8') as f:
        f.write(f"Application Code Extraction Summary\n")
        f.write(f"===================================\n\n")
        f.write(f"Source file: {exe_path}\n")
        f.write(f"Application code objects found: {len(marshal_candidates)}\n")
        f.write(f"Successfully extracted: {extracted_count}\n\n")
        
        f.write("Application code objects:\n")
        for i, candidate in enumerate(marshal_candidates):
            code_obj = candidate['code_object']
            f.write(f"{i+1:3d}. Position: {candidate['position']:8d}, ")
            f.write(f"File: {getattr(code_obj, 'co_filename', 'unknown')}, ")
            f.write(f"Name: {getattr(code_obj, 'co_name', 'unknown')}\n")
    
    print(f"Summary saved to {summary_path}")

if __name__ == "__main__":
    if len(sys.argv) != 3:
        print("Usage: python extract_main_code.py <exe_file> <output_dir>")
        sys.exit(1)
        
    exe_path = sys.argv[1]
    output_dir = sys.argv[2]
    
    extract_application_code(exe_path, output_dir)
