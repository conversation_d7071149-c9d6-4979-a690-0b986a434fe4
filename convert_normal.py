import numpy as np
import cv2
import logging
from pathlib import Path
from dataclasses import dataclass
from typing import List, Optional
import concurrent.futures
from abc import ABC, abstractmethod

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class Config:
    output_path: Path
    output_format: str
    dry_run: bool
    processing: dict
    
    def should_generate_texture(self, texture_name: str) -> bool:
        return texture_name in self.processing.get('textures', [])

class ImgProcError(Exception):
    @staticmethod
    def normal_map_error(msg: str) -> 'ImgProcError':
        return ImgProcError(f"Normal map error: {msg}")
    
    @staticmethod
    def image_save(path: Path) -> 'ImgProcError':
        return ImgProcError(f"Failed to save image: {path}")

def calculate_normal_maps(dataset, config: Config) -> None:
    """计算法线贴图（texture02和texture05）"""
    logger.info("Calculating normal maps...")
    
    if config.should_generate_texture("texture02"):
        calculate_world_normal_maps(dataset, config)
        
    if config.should_generate_texture("texture05"):
        calculate_tangent_normal_maps(dataset, config)

def calculate_world_normal_maps(dataset, config: Config) -> None:
    """计算世界空间法线贴图（texture02）"""
    logger.info("Calculating world normal maps (texture02)...")
    
    with concurrent.futures.ProcessPoolExecutor() as executor:
        futures = []
        for camera in dataset.cameras:
            futures.append(
                executor.submit(process_camera_world_normals, camera, config)
            )
        concurrent.futures.wait(futures)

def photometric_stereo(images: List[np.ndarray], light_directions: List[np.ndarray]) -> np.ndarray:
    """光度立体法实现"""
    if len(images) != len(light_directions):
        raise ImgProcError.normal_map_error("图像数量必须与光照方向数量匹配")
        
    if len(images) < 3:
        raise ImgProcError.normal_map_error("光度立体法至少需要3张图像")
    
    # 获取图像尺寸
    height, width = images[0].shape[:2]
    
    # 验证所有图像尺寸一致
    for img in images[1:]:
        if img.shape[:2] != (height, width):
            raise ImgProcError.normal_map_error("所有图像尺寸必须相同")
    
    # 创建法线贴图
    normal_map = np.zeros((height, width, 3), dtype=np.uint8)
    
    # 构建光照矩阵
    light_matrix = np.array(light_directions)
    
    # 处理每个像素
    for y in range(height):
        for x in range(width):
            normal = calculate_normal_at_pixel(images, light_matrix, x, y)
            
            # 转换法线从[-1, 1]到[0, 255]范围
            normal_color = ((normal + 1.0) * 127.5).astype(np.uint8)
            normal_map[y, x] = normal_color
    
    return normal_map

def calculate_normal_at_pixel(
    images: List[np.ndarray],
    light_matrix: np.ndarray,
    x: int,
    y: int
) -> np.ndarray:
    """计算单个像素的法线向量"""
    intensities = []
    
    # 收集像素强度
    for img in images:
        # OpenCV读取的图像是BGR格式，转换为灰度图
        intensity = np.mean(img[y, x]) / 255.0  # 转换为[0,1]范围
        intensities.append(intensity)
    
    intensities = np.array(intensities)
    
    # 求解法线方程 L * n = I
    try:
        normal = np.linalg.solve(light_matrix.T @ light_matrix, 
                               light_matrix.T @ intensities)
        # 归一化法线
        normal = normal / np.linalg.norm(normal)
        return normal
    except np.linalg.LinAlgError:
        # 如果矩阵奇异，返回默认法线
        return np.array([0.0, 0.0, 1.0])

def smooth_normal_map(normal_map: np.ndarray, factor: float) -> np.ndarray:
    """平滑法线贴图"""
    kernel_size = int(factor * 5.0)
    kernel_size = max(1, min(10, kernel_size))
    
    # 使用OpenCV的高斯滤波
    kernel_size = kernel_size * 2 + 1  # 确保是奇数
    return cv2.GaussianBlur(normal_map, (kernel_size, kernel_size), 0)

def world_to_tangent_space(
    world_normal_map: np.ndarray,
    tangent: np.ndarray,
    bitangent: np.ndarray,
    normal: np.ndarray
) -> np.ndarray:
    """将世界空间法线转换到切线空间"""
    # 构建TBN矩阵
    tbn_matrix = np.array([
        [tangent[0], bitangent[0], normal[0]],
        [tangent[1], bitangent[1], normal[1]],
        [tangent[2], bitangent[2], normal[2]]
    ])
    
    try:
        tbn_inverse = np.linalg.inv(tbn_matrix)
    except np.linalg.LinAlgError:
        raise ImgProcError.math_error("无法求逆TBN矩阵")
    
    height, width = world_normal_map.shape[:2]
    result = np.zeros_like(world_normal_map)
    
    # 转换每个像素
    for y in range(height):
        for x in range(width):
            pixel = world_normal_map[y, x]
            world_normal = (pixel / 127.5) - 1.0
            
            # 转换到切线空间
            tangent_normal = tbn_inverse @ world_normal
            
            # 转换回[0, 255]范围
            result[y, x] = ((tangent_normal + 1.0) * 127.5).clip(0, 255)
    
    return result