# Code object from position 7317306
# Filename: _compression.py
# Name: <module>
# Args: 0
# Locals: 0

  0           0 RESUME                   0

  1           2 LOAD_CONST               0 ('Internal classes used by the gzip, lzma and bz2 modules')
              4 STORE_NAME               0 (__doc__)

  3           6 LOAD_CONST               1 (0)
              8 LOAD_CONST               2 (None)
             10 IMPORT_NAME              1 (io)
             12 STORE_NAME               1 (io)

  4          14 LOAD_CONST               1 (0)
             16 LOAD_CONST               2 (None)
             18 IMPORT_NAME              2 (sys)
             20 STORE_NAME               2 (sys)

  6          22 LOAD_NAME                1 (io)
             24 LOAD_ATTR                3 (NULL|self + io)
             44 LOAD_CONST               4 ('BaseStream')
             46 LOAD_NAME                1 (io)
             48 LOAD_ATTR                5 (NULL|self + sys)
             68 CACHE
             70 CACHE
             72 STORE_NAME               6 (BaseStream)

 33          74 PUSH_NULL
             76 LOAD_BUILD_CLASS
             78 LOAD_CONST               5 (<code object DecompressReader at 0x000001A2D019E760, file "_compression.py", line 33>)
             80 MAKE_FUNCTION            0
             82 LOAD_CONST               6 ('DecompressReader')
             84 LOAD_NAME                1 (io)
             86 LOAD_ATTR                7 (NULL|self + DEFAULT_BUFFER_SIZE)
            106 CACHE
            108 CACHE
            110 STORE_NAME               8 (DecompressReader)
            112 LOAD_CONST               2 (None)
            114 RETURN_VALUE

Disassembly of <code object BaseStream at 0x000001A2D01156B0, file "_compression.py", line 9>:
  9           0 RESUME                   0
              2 LOAD_NAME                0 (__name__)
              4 STORE_NAME               1 (__module__)
              6 LOAD_CONST               0 ('BaseStream')
              8 STORE_NAME               2 (__qualname__)

 10          10 LOAD_CONST               1 ('Mode-checking helper functions.')
             12 STORE_NAME               3 (__doc__)

 12          14 LOAD_CONST               2 (<code object _check_not_closed at 0x000001A2D059A130, file "_compression.py", line 12>)
             16 MAKE_FUNCTION            0
             18 STORE_NAME               4 (_check_not_closed)

 16          20 LOAD_CONST               3 (<code object _check_can_read at 0x000001A2D05F0810, file "_compression.py", line 16>)
             22 MAKE_FUNCTION            0
             24 STORE_NAME               5 (_check_can_read)

 20          26 LOAD_CONST               4 (<code object _check_can_write at 0x000001A2D05F1230, file "_compression.py", line 20>)
             28 MAKE_FUNCTION            0
             30 STORE_NAME               6 (_check_can_write)

 24          32 LOAD_CONST               5 (<code object _check_can_seek at 0x000001A2D01CC310, file "_compression.py", line 24>)
             34 MAKE_FUNCTION            0
             36 STORE_NAME               7 (_check_can_seek)
             38 LOAD_CONST               6 (None)
             40 RETURN_VALUE

Disassembly of <code object _check_not_closed at 0x000001A2D059A130, file "_compression.py", line 12>:
 12           0 RESUME                   0

 13           2 LOAD_FAST                0 (self)
              4 LOAD_ATTR                0 (closed)
             24 CACHE
             26 CACHE
             28 LOAD_CONST               1 ('I/O operation on closed file')
             30 UNPACK_SEQUENCE          1
             34 CALL                     1
             42 CACHE
             44 RAISE_VARARGS            1

 13          46 LOAD_CONST               0 (None)
             48 RETURN_VALUE

Disassembly of <code object _check_can_read at 0x000001A2D05F0810, file "_compression.py", line 16>:
 16           0 RESUME                   0

 17           2 LOAD_FAST                0 (self)
              4 STORE_SUBSCR
              8 CACHE
             10 CACHE
             12 CACHE
             14 CACHE
             16 CACHE
             18 CACHE
             20 CACHE
             22 CACHE
             24 CACHE
             26 UNPACK_SEQUENCE          0
             30 CALL                     0
             38 CACHE
             40 POP_JUMP_IF_TRUE        20 (to 82)

 18          42 LOAD_GLOBAL              3 (NULL + io)
             52 CACHE
             54 LOAD_ATTR                2 (io)
             74 CACHE
             76 CACHE
             78 CACHE
             80 RAISE_VARARGS            1

 17     >>   82 LOAD_CONST               0 (None)
             84 RETURN_VALUE

Disassembly of <code object _check_can_write at 0x000001A2D05F1230, file "_compression.py", line 20>:
 20           0 RESUME                   0

 21           2 LOAD_FAST                0 (self)
              4 STORE_SUBSCR
              8 CACHE
             10 CACHE
             12 CACHE
             14 CACHE
             16 CACHE
             18 CACHE
             20 CACHE
             22 CACHE
             24 CACHE
             26 UNPACK_SEQUENCE          0
             30 CALL                     0
             38 CACHE
             40 POP_JUMP_IF_TRUE        20 (to 82)

 22          42 LOAD_GLOBAL              3 (NULL + io)
             52 CACHE
             54 LOAD_ATTR                2 (io)
             74 CACHE
             76 CACHE
             78 CACHE
             80 RAISE_VARARGS            1

 21     >>   82 LOAD_CONST               0 (None)
             84 RETURN_VALUE

Disassembly of <code object _check_can_seek at 0x000001A2D01CC310, file "_compression.py", line 24>:
 24           0 RESUME                   0

 25           2 LOAD_FAST                0 (self)
              4 STORE_SUBSCR
              8 CACHE
             10 CACHE
             12 CACHE
             14 CACHE
             16 CACHE
             18 CACHE
             20 CACHE
             22 CACHE
             24 CACHE
             26 UNPACK_SEQUENCE          0
             30 CALL                     0
             38 CACHE
             40 POP_JUMP_IF_TRUE        20 (to 82)

 26          42 LOAD_GLOBAL              3 (NULL + io)
             52 CACHE
             54 LOAD_ATTR                2 (io)
             74 CACHE
             76 CACHE
             78 CACHE
             80 RAISE_VARARGS            1

 28     >>   82 LOAD_FAST                0 (self)
             84 STORE_SUBSCR
             88 CACHE
             90 CACHE
             92 CACHE
             94 CACHE
             96 CACHE
             98 CACHE
            100 CACHE
            102 CACHE
            104 CACHE
            106 UNPACK_SEQUENCE          0
            110 CALL                     0
            118 CACHE
            120 POP_JUMP_IF_TRUE        20 (to 162)

 29         122 LOAD_GLOBAL              3 (NULL + io)
            132 CACHE
            134 LOAD_ATTR                2 (io)
            154 CACHE
            156 CACHE
            158 CACHE
            160 RAISE_VARARGS            1

 28     >>  162 LOAD_CONST               0 (None)
            164 RETURN_VALUE

Disassembly of <code object DecompressReader at 0x000001A2D019E760, file "_compression.py", line 33>:
              0 MAKE_CELL                0 (__class__)

 33           2 RESUME                   0
              4 LOAD_NAME                0 (__name__)
              6 STORE_NAME               1 (__module__)
              8 LOAD_CONST               0 ('DecompressReader')
             10 STORE_NAME               2 (__qualname__)

 34          12 LOAD_CONST               1 ('Adapts the decompressor API to a RawIOBase reader API')
             14 STORE_NAME               3 (__doc__)

 36          16 LOAD_CONST               2 (<code object readable at 0x000001A2D01CB020, file "_compression.py", line 36>)
             18 MAKE_FUNCTION            0
             20 STORE_NAME               4 (readable)

 39          22 LOAD_CONST              14 (((),))
             24 LOAD_CONST               4 (<code object __init__ at 0x000001A2D0110C90, file "_compression.py", line 39>)
             26 MAKE_FUNCTION            1 (defaults)
             28 STORE_NAME               5 (__init__)

 59          30 LOAD_CLOSURE             0 (__class__)
             32 BUILD_TUPLE              1
             34 LOAD_CONST               5 (<code object close at 0x000001A2D05F1350, file "_compression.py", line 59>)
             36 MAKE_FUNCTION            8 (closure)
             38 STORE_NAME               6 (close)

 63          40 LOAD_CONST               6 (<code object seekable at 0x000001A2D059A230, file "_compression.py", line 63>)
             42 MAKE_FUNCTION            0
             44 STORE_NAME               7 (seekable)

 66          46 LOAD_CONST               7 (<code object readinto at 0x000001A2D00F9830, file "_compression.py", line 66>)
             48 MAKE_FUNCTION            0
             50 STORE_NAME               8 (readinto)

 72          52 LOAD_CONST              15 ((-1,))
             54 LOAD_CONST               9 (<code object read at 0x000001A2CDE72A60, file "_compression.py", line 72>)
             56 MAKE_FUNCTION            1 (defaults)
             58 STORE_NAME               9 (read)

113          60 LOAD_CONST              10 (<code object readall at 0x000001A2D010D6F0, file "_compression.py", line 113>)
             62 MAKE_FUNCTION            0
             64 STORE_NAME              10 (readall)

124          66 LOAD_CONST              11 (<code object _rewind at 0x000001A2D0105290, file "_compression.py", line 124>)
             68 MAKE_FUNCTION            0
             70 STORE_NAME              11 (_rewind)

130          72 LOAD_NAME               12 (io)
             74 LOAD_ATTR               13 (NULL|self + close)
             94 MAKE_FUNCTION            0
             96 STORE_NAME              15 (tell)
             98 LOAD_CLOSURE             0 (__class__)
            100 COPY                     1
            102 STORE_NAME              16 (__classcell__)
            104 RETURN_VALUE

Disassembly of <code object readable at 0x000001A2D01CB020, file "_compression.py", line 36>:
 36           0 RESUME                   0

 37           2 LOAD_CONST               1 (True)
              4 RETURN_VALUE

Disassembly of <code object __init__ at 0x000001A2D0110C90, file "_compression.py", line 39>:
 39           0 RESUME                   0

 40           2 LOAD_FAST                1 (fp)
              4 LOAD_FAST                0 (self)
              6 STORE_ATTR               0 (_fp)

 41          16 LOAD_CONST               1 (False)
             18 LOAD_FAST                0 (self)
             20 STORE_ATTR               1 (_eof)

 42          30 LOAD_CONST               2 (0)
             32 LOAD_FAST                0 (self)
             34 STORE_ATTR               2 (_pos)

 45          44 LOAD_CONST               3 (-1)
             46 LOAD_FAST                0 (self)
             48 STORE_ATTR               3 (_size)

 51          58 LOAD_FAST                2 (decomp_factory)
             60 LOAD_FAST                0 (self)
             62 STORE_ATTR               4 (_decomp_factory)

 52          72 LOAD_FAST                4 (decomp_args)
             74 LOAD_FAST                0 (self)
             76 STORE_ATTR               5 (_decomp_args)

 53          86 PUSH_NULL
             88 LOAD_FAST                0 (self)
             90 LOAD_ATTR                4 (_pos)
            110 CACHE
            112 CACHE
            114 CACHE
            116 DICT_MERGE               1
            118 CALL_FUNCTION_EX         1
            120 LOAD_FAST                0 (self)
            122 STORE_ATTR               6 (_decompressor)

 57         132 LOAD_FAST                3 (trailing_error)
            134 LOAD_FAST                0 (self)
            136 STORE_ATTR               7 (_trailing_error)
            146 LOAD_CONST               0 (None)
            148 RETURN_VALUE

Disassembly of <code object close at 0x000001A2D05F1350, file "_compression.py", line 59>:
              0 COPY_FREE_VARS           1

 59           2 RESUME                   0

 60           4 LOAD_CONST               0 (None)
              6 LOAD_FAST                0 (self)
              8 STORE_ATTR               0 (_decompressor)

 61          18 LOAD_GLOBAL              3 (NULL + super)
             28 CACHE
             30 UNPACK_SEQUENCE          0
             34 CALL                     0
             42 CACHE
             44 STORE_SUBSCR
             48 CACHE
             50 CACHE
             52 CACHE
             54 CACHE
             56 CACHE
             58 CACHE
             60 CACHE
             62 CACHE
             64 CACHE
             66 UNPACK_SEQUENCE          0
             70 CALL                     0
             78 CACHE
             80 RETURN_VALUE

Disassembly of <code object seekable at 0x000001A2D059A230, file "_compression.py", line 63>:
 63           0 RESUME                   0

 64           2 LOAD_FAST                0 (self)
              4 LOAD_ATTR                0 (_fp)
             24 CACHE
             26 CACHE
             28 CACHE
             30 CACHE
             32 CACHE
             34 CACHE
             36 UNPACK_SEQUENCE          0
             40 CALL                     0
             48 CACHE
             50 RETURN_VALUE

Disassembly of <code object readinto at 0x000001A2D00F9830, file "_compression.py", line 66>:
 66           0 RESUME                   0

 67           2 LOAD_GLOBAL              1 (NULL + memoryview)
             12 CACHE
             14 LOAD_FAST                1 (b)
             16 UNPACK_SEQUENCE          1
             20 CALL                     1
             28 CACHE
             30 BEFORE_WITH
             32 STORE_FAST               2 (view)
             34 LOAD_FAST                2 (view)
             36 STORE_SUBSCR
             40 CACHE
             42 CACHE
             44 CACHE
             46 CACHE
             48 CACHE
             50 CACHE
             52 CACHE
             54 CACHE
             56 CACHE
             58 LOAD_CONST               1 ('B')
             60 UNPACK_SEQUENCE          1
             64 CALL                     1
             72 CACHE
             74 BEFORE_WITH
             76 STORE_FAST               3 (byte_view)

 68          78 LOAD_FAST                0 (self)
             80 STORE_SUBSCR
             84 CACHE
             86 CACHE
             88 CACHE
             90 CACHE
             92 CACHE
             94 CACHE
             96 CACHE
             98 CACHE
            100 CACHE
            102 LOAD_GLOBAL              7 (NULL + len)
            112 CACHE
            114 LOAD_FAST                3 (byte_view)
            116 UNPACK_SEQUENCE          1
            120 CALL                     1
            128 CACHE
            130 UNPACK_SEQUENCE          1
            134 CALL                     1
            142 CACHE
            144 STORE_FAST               4 (data)

 69         146 LOAD_FAST                4 (data)
            148 LOAD_FAST                3 (byte_view)
            150 LOAD_CONST               0 (None)
            152 LOAD_GLOBAL              7 (NULL + len)
            162 CACHE
            164 LOAD_FAST                4 (data)
            166 UNPACK_SEQUENCE          1
            170 CALL                     1
            178 CACHE
            180 BUILD_SLICE              2
            182 STORE_SUBSCR

 67         186 LOAD_CONST               0 (None)
            188 LOAD_CONST               0 (None)
            190 LOAD_CONST               0 (None)
            192 UNPACK_SEQUENCE          2
            196 CALL                     2
            204 CACHE
            206 POP_TOP
            208 JUMP_FORWARD            11 (to 232)
        >>  210 PUSH_EXC_INFO
            212 WITH_EXCEPT_START
            214 POP_JUMP_IF_TRUE         4 (to 224)
            216 RERAISE                  2
        >>  218 COPY                     3
            220 POP_EXCEPT
            222 RERAISE                  1
        >>  224 POP_TOP
            226 POP_EXCEPT
            228 POP_TOP
            230 POP_TOP
        >>  232 LOAD_CONST               0 (None)
            234 LOAD_CONST               0 (None)
            236 LOAD_CONST               0 (None)
            238 UNPACK_SEQUENCE          2
            242 CALL                     2
            250 CACHE
            252 POP_TOP
            254 JUMP_FORWARD            11 (to 278)
        >>  256 PUSH_EXC_INFO
            258 WITH_EXCEPT_START
            260 POP_JUMP_IF_TRUE         4 (to 270)
            262 RERAISE                  2
        >>  264 COPY                     3
            266 POP_EXCEPT
            268 RERAISE                  1
        >>  270 POP_TOP
            272 POP_EXCEPT
            274 POP_TOP
            276 POP_TOP

 70     >>  278 LOAD_GLOBAL              7 (NULL + len)
            288 CACHE
            290 LOAD_FAST                4 (data)
            292 UNPACK_SEQUENCE          1
            296 CALL                     1
            304 CACHE
            306 RETURN_VALUE
ExceptionTable:
  32 to 74 -> 256 [1] lasti
  76 to 184 -> 210 [2] lasti
  186 to 208 -> 256 [1] lasti
  210 to 216 -> 218 [4] lasti
  218 to 222 -> 256 [1] lasti
  224 to 224 -> 218 [4] lasti
  226 to 230 -> 256 [1] lasti
  256 to 262 -> 264 [3] lasti
  270 to 270 -> 264 [3] lasti

Disassembly of <code object read at 0x000001A2CDE72A60, file "_compression.py", line 72>:
 72           0 RESUME                   0

 73           2 LOAD_FAST                1 (size)
              4 LOAD_CONST               1 (0)
              6 COMPARE_OP               0 (<)
             10 CACHE
             12 POP_JUMP_IF_FALSE       20 (to 54)

 74          14 LOAD_FAST                0 (self)
             16 STORE_SUBSCR
             20 CACHE
             22 CACHE
             24 CACHE
             26 CACHE
             28 CACHE
             30 CACHE
             32 CACHE
             34 CACHE
             36 CACHE
             38 UNPACK_SEQUENCE          0
             42 CALL                     0
             50 CACHE
             52 RETURN_VALUE

 76     >>   54 LOAD_FAST                1 (size)
             56 POP_JUMP_IF_FALSE        7 (to 72)
             58 LOAD_FAST                0 (self)
             60 LOAD_ATTR                1 (NULL|self + readall)

 81          80 NOP

 82     >>   82 LOAD_FAST                0 (self)
             84 LOAD_ATTR                2 (_eof)
            104 POP_JUMP_IF_FALSE      114 (to 334)

 83         106 LOAD_FAST                0 (self)
            108 LOAD_ATTR                2 (_eof)
            128 LOAD_GLOBAL             30 (_size)
            138 CACHE
            140 CACHE
            142 STORE_SUBSCR
            146 CACHE
            148 CACHE
            150 CACHE
            152 CACHE
            154 CACHE
            156 CACHE
            158 CACHE
            160 CACHE
            162 CACHE
            164 LOAD_GLOBAL             14 (BUFFER_SIZE)
            174 CACHE
            176 UNPACK_SEQUENCE          1
            180 CALL                     1
            188 CACHE

 83         190 STORE_FAST               3 (rawblock)

 85         192 LOAD_FAST                3 (rawblock)
            194 POP_JUMP_IF_TRUE         1 (to 198)

 86         196 JUMP_FORWARD           162 (to 522)

 88     >>  198 PUSH_NULL
            200 LOAD_FAST                0 (self)
            202 LOAD_ATTR                8 (unused_data)
            222 CACHE
            224 CACHE
            226 CACHE

 88         228 DICT_MERGE               1
            230 CALL_FUNCTION_EX         1
            232 LOAD_FAST                0 (self)
            234 STORE_ATTR               2 (_decompressor)

 90         244 NOP

 91         246 LOAD_FAST                0 (self)
            248 LOAD_ATTR                2 (_eof)
            268 CACHE
            270 CACHE
            272 CACHE
            274 CACHE
            276 CACHE
            278 CACHE
            280 LOAD_FAST                3 (rawblock)
            282 LOAD_FAST                1 (size)
            284 UNPACK_SEQUENCE          2
            288 CALL                     2
            296 CACHE
            298 STORE_FAST               2 (data)
            300 JUMP_FORWARD           106 (to 514)
        >>  302 PUSH_EXC_INFO

 92         304 LOAD_FAST                0 (self)
            306 LOAD_ATTR               11 (NULL|self + _fp)

 92         326 RERAISE                  0
        >>  328 COPY                     3
            330 POP_EXCEPT
            332 RERAISE                  1

 96     >>  334 LOAD_FAST                0 (self)
            336 LOAD_ATTR                2 (_eof)
            356 POP_JUMP_IF_FALSE       49 (to 456)

 97         358 LOAD_FAST                0 (self)
            360 LOAD_ATTR                5 (NULL|self + _decompressor)
            380 CACHE
            382 CACHE
            384 CACHE
            386 CACHE
            388 CACHE
            390 CACHE
            392 LOAD_GLOBAL             14 (BUFFER_SIZE)
            402 CACHE
            404 UNPACK_SEQUENCE          1
            408 CALL                     1
            416 CACHE
            418 STORE_FAST               3 (rawblock)

 98         420 LOAD_FAST                3 (rawblock)
            422 POP_JUMP_IF_TRUE        15 (to 454)

 99         424 LOAD_GLOBAL             27 (NULL + EOFError)
            434 CACHE
            436 LOAD_CONST               4 ('Compressed file ended before the end-of-stream marker was reached')
            438 UNPACK_SEQUENCE          1
            442 CALL                     1
            450 CACHE
            452 RAISE_VARARGS            1

 98     >>  454 JUMP_FORWARD             2 (to 460)

102     >>  456 LOAD_CONST               2 (b'')
            458 STORE_FAST               3 (rawblock)

103     >>  460 LOAD_FAST                0 (self)
            462 LOAD_ATTR                2 (_eof)
            482 CACHE
            484 CACHE
            486 CACHE
            488 CACHE
            490 CACHE
            492 CACHE
            494 LOAD_FAST                3 (rawblock)
            496 LOAD_FAST                1 (size)
            498 UNPACK_SEQUENCE          2
            502 CALL                     2
            510 CACHE
            512 STORE_FAST               2 (data)

104     >>  514 LOAD_FAST                2 (data)
            516 POP_JUMP_IF_FALSE        1 (to 520)

105         518 JUMP_FORWARD             1 (to 522)

 81     >>  520 JUMP_BACKWARD          220 (to 82)

106     >>  522 LOAD_FAST                2 (data)
            524 POP_JUMP_IF_TRUE        21 (to 568)

107         526 LOAD_CONST               3 (True)
            528 LOAD_FAST                0 (self)
            530 STORE_ATTR               1 (_eof)

108         540 LOAD_FAST                0 (self)
            542 LOAD_ATTR               14 (BUFFER_SIZE)
            562 CACHE

109         564 LOAD_CONST               2 (b'')
            566 RETURN_VALUE

110     >>  568 LOAD_FAST                0 (self)
            570 COPY                     1
            572 LOAD_ATTR               14 (BUFFER_SIZE)
            592 CACHE
            594 LOAD_FAST                2 (data)
            596 UNPACK_SEQUENCE          1
            600 CALL                     1
            608 CACHE
            610 BINARY_OP               13 (+=)
            614 SWAP                     2
            616 STORE_ATTR              14 (_pos)

111         626 LOAD_FAST                2 (data)
            628 RETURN_VALUE
ExceptionTable:
  246 to 298 -> 302 [0]
  302 to 320 -> 328 [1] lasti
  326 to 326 -> 328 [1] lasti

Disassembly of <code object readall at 0x000001A2D010D6F0, file "_compression.py", line 113>:
113           0 RESUME                   0

114           2 BUILD_LIST               0
              4 STORE_FAST               1 (chunks)

118           6 LOAD_FAST                0 (self)
              8 STORE_SUBSCR
             12 CACHE
             14 CACHE
             16 CACHE
             18 CACHE
             20 CACHE
             22 CACHE
             24 CACHE
             26 CACHE
             28 CACHE
             30 LOAD_GLOBAL              2 (sys)
             40 CACHE
             42 LOAD_ATTR                2 (sys)
             62 CACHE
             64 CACHE
             66 COPY                     1
             68 STORE_FAST               2 (data)
             70 POP_JUMP_IF_FALSE       54 (to 180)

119          72 LOAD_FAST                1 (chunks)
             74 STORE_SUBSCR
             78 CACHE
             80 CACHE
             82 CACHE
             84 CACHE
             86 CACHE
             88 CACHE
             90 CACHE
             92 CACHE
             94 CACHE
             96 LOAD_FAST                2 (data)
             98 UNPACK_SEQUENCE          1
            102 CALL                     1
            110 CACHE
            112 POP_TOP

118         114 LOAD_FAST                0 (self)
            116 STORE_SUBSCR
            120 CACHE
            122 CACHE
            124 CACHE
            126 CACHE
            128 CACHE
            130 CACHE
            132 CACHE
            134 CACHE
            136 CACHE
            138 LOAD_GLOBAL              2 (sys)
            148 CACHE
            150 LOAD_ATTR                2 (sys)
            170 CACHE
            172 CACHE
            174 COPY                     1
            176 STORE_FAST               2 (data)
