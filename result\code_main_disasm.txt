# MAIN APPLICATION CODE OBJECT
# Position: 8203114
# Filename: code.py
# Function: <module>
# Args: 0
# Locals: 0
# Stack size: 5
# Flags: 0

# BYTECODE DISASSEMBLY:
==================================================
  0           0 RESUME                   0

  1           2 LOAD_CONST               0 ("Utilities needed to emulate <PERSON>'s interactive interpreter.\n\n")
              4 STORE_NAME               0 (__doc__)

  8           6 LOAD_CONST               1 (0)
              8 LOAD_CONST               2 (None)
             10 IMPORT_NAME              1 (sys)
             12 STORE_NAME               1 (sys)

  9          14 LOAD_CONST               1 (0)
             16 LOAD_CONST               2 (None)
             18 IMPORT_NAME              2 (traceback)
             20 STORE_NAME               2 (traceback)

 10          22 LOAD_CONST               1 (0)
             24 LOAD_CONST               3 (('CommandCompiler', 'compile_command'))
             26 IMPORT_NAME              3 (codeop)
             28 IMPORT_FROM              4 (CommandCompiler)
             30 STORE_NAME               4 (CommandCompiler)
             32 IMPORT_FROM              5 (compile_command)
             34 STORE_NAME               5 (compile_command)
             36 POP_TOP

 12          38 BUILD_LIST               0
             40 LOAD_CONST               4 (('InteractiveInterpreter', 'InteractiveConsole', 'interact', 'compile_command'))
             42 LIST_EXTEND              1
             44 STORE_NAME               6 (__all__)

 15          46 PUSH_NULL
             48 LOAD_BUILD_CLASS
             50 LOAD_CONST               5 (<code object InteractiveInterpreter at 0x000001E77ECDA730, file "code.py", line 15>)
             52 MAKE_FUNCTION            0
             54 LOAD_CONST               6 ('InteractiveInterpreter')
             56 UNPACK_SEQUENCE          2
             60 CALL                     2
             68 CACHE
             70 STORE_NAME               7 (InteractiveInterpreter)

162          72 PUSH_NULL
             74 LOAD_BUILD_CLASS
             76 LOAD_CONST               7 (<code object InteractiveConsole at 0x000001E77ECDAA30, file "code.py", line 162>)
             78 MAKE_FUNCTION            0
             80 LOAD_CONST               8 ('InteractiveConsole')
             82 LOAD_NAME                7 (InteractiveInterpreter)
             84 UNPACK_SEQUENCE          3
             88 CALL                     3
             96 CACHE
             98 STORE_NAME               8 (InteractiveConsole)

278         100 LOAD_CONST              16 ((None, None, None, None))
            102 LOAD_CONST               9 (<code object interact at 0x000001E77ED182D0, file "code.py", line 278>)
            104 MAKE_FUNCTION            1 (defaults)
            106 STORE_NAME               9 (interact)

304         108 LOAD_NAME               10 (__name__)
            110 LOAD_CONST              10 ('__main__')
            112 COMPARE_OP               2 (<)
            116 CACHE
            118 POP_JUMP_IF_FALSE      100 (to 320)

305         120 LOAD_CONST               1 (0)
            122 LOAD_CONST               2 (None)
            124 IMPORT_NAME             11 (argparse)
            126 STORE_NAME              11 (argparse)

307         128 PUSH_NULL
            130 LOAD_NAME               11 (argparse)
            132 LOAD_ATTR               12 (__all__)
            152 CACHE
            154 CACHE
            156 STORE_NAME              13 (parser)

308         158 LOAD_NAME               13 (parser)
            160 STORE_SUBSCR
            164 CACHE
            166 CACHE
            168 CACHE
            170 CACHE
            172 CACHE
            174 CACHE
            176 CACHE
            178 CACHE
            180 CACHE
            182 LOAD_CONST              11 ('-q')
            184 LOAD_CONST              12 ('store_true')

309         186 LOAD_CONST              13 ("don't print version and copyright messages")

308         188 KW_NAMES                14 (('action', 'help'))
            190 UNPACK_SEQUENCE          3
            194 CALL                     3
            202 CACHE
            204 POP_TOP

310         206 LOAD_NAME               13 (parser)
            208 STORE_SUBSCR
            212 CACHE
            214 CACHE
            216 CACHE
            218 CACHE
            220 CACHE
            222 CACHE
            224 CACHE
            226 CACHE
            228 CACHE
            230 UNPACK_SEQUENCE          0
            234 CALL                     0
            242 CACHE
            244 STORE_NAME              16 (args)

311         246 LOAD_NAME               16 (args)
            248 LOAD_ATTR               17 (NULL|self + InteractiveConsole)
            268 CACHE
            270 CACHE
            272 LOAD_ATTR               19 (NULL|self + interact)
            292 STORE_NAME              20 (banner)

315         294 PUSH_NULL
            296 LOAD_NAME                9 (interact)
            298 LOAD_NAME               20 (banner)
            300 UNPACK_SEQUENCE          1
            304 CALL                     1
            312 CACHE
            314 POP_TOP
            316 LOAD_CONST               2 (None)
            318 RETURN_VALUE

304     >>  320 LOAD_CONST               2 (None)
            322 RETURN_VALUE

Disassembly of <code object InteractiveInterpreter at 0x000001E77ECDA730, file "code.py", line 15>:
 15           0 RESUME                   0
              2 LOAD_NAME                0 (__name__)
              4 STORE_NAME               1 (__module__)
              6 LOAD_CONST               0 ('InteractiveInterpreter')
              8 STORE_NAME               2 (__qualname__)

 16          10 LOAD_CONST               1 ("Base class for InteractiveConsole.\n\n    This class deals with parsing and interpreter state (the user's\n    namespace); it doesn't deal with input buffering or prompting or\n    input file naming (the filename is always passed in explicitly).\n\n    ")
             12 STORE_NAME               3 (__doc__)

 24          14 LOAD_CONST              11 ((None,))
             16 LOAD_CONST               3 (<code object __init__ at 0x000001E77ECF2AB0, file "code.py", line 24>)
             18 MAKE_FUNCTION            1 (defaults)
             20 STORE_NAME               4 (__init__)

 38          22 LOAD_CONST              12 (('<input>', 'single'))
             24 LOAD_CONST               6 (<code object runsource at 0x000001E77EC728D0, file "code.py", line 38>)
             26 MAKE_FUNCTION            1 (defaults)
             28 STORE_NAME               5 (runsource)

 77          30 LOAD_CONST               7 (<code object runcode at 0x000001E77ECEDA70, file "code.py", line 77>)
             32 MAKE_FUNCTION            0
             34 STORE_NAME               6 (runcode)

 96          36 LOAD_CONST              11 ((None,))
             38 LOAD_CONST               8 (<code object showsyntaxerror at 0x000001E77EF28920, file "code.py", line 96>)
             40 MAKE_FUNCTION            1 (defaults)
             42 STORE_NAME               7 (showsyntaxerror)

131          44 LOAD_CONST               9 (<code object showtraceback at 0x000001E77E890150, file "code.py", line 131>)
             46 MAKE_FUNCTION            0
             48 STORE_NAME               8 (showtraceback)

152          50 LOAD_CONST              10 (<code object write at 0x000001E77ECF2BC0, file "code.py", line 152>)
             52 MAKE_FUNCTION            0
             54 STORE_NAME               9 (write)
             56 LOAD_CONST               2 (None)
             58 RETURN_VALUE

Disassembly of <code object __init__ at 0x000001E77ECF2AB0, file "code.py", line 24>:
 24           0 RESUME                   0

 33           2 LOAD_FAST                1 (locals)
              4 POP_JUMP_IF_NOT_NONE     5 (to 16)

 34           6 LOAD_CONST               2 ('__console__')
              8 LOAD_CONST               1 (None)
             10 LOAD_CONST               3 (('__name__', '__doc__'))
             12 BUILD_CONST_KEY_MAP      2
             14 STORE_FAST               1 (locals)

 35     >>   16 LOAD_FAST                1 (locals)
             18 LOAD_FAST                0 (self)
             20 STORE_ATTR               0 (locals)

 36          30 LOAD_GLOBAL              3 (NULL + CommandCompiler)
             40 CACHE
             42 UNPACK_SEQUENCE          0
             46 CALL                     0
             54 CACHE
             56 LOAD_FAST                0 (self)
             58 STORE_ATTR               2 (compile)
             68 LOAD_CONST               1 (None)
             70 RETURN_VALUE

Disassembly of <code object runsource at 0x000001E77EC728D0, file "code.py", line 38>:
 38           0 RESUME                   0

 62           2 NOP

 63           4 LOAD_FAST                0 (self)
              6 STORE_SUBSCR
             10 CACHE
             12 CACHE
             14 CACHE
             16 CACHE
             18 CACHE
             20 CACHE
             22 CACHE
             24 CACHE
             26 CACHE
             28 LOAD_FAST                1 (source)
             30 LOAD_FAST                2 (filename)
             32 LOAD_FAST                3 (symbol)
             34 UNPACK_SEQUENCE          3
             38 CALL                     3
             46 CACHE
             48 STORE_FAST               4 (code)
             50 JUMP_FORWARD            51 (to 154)
        >>   52 PUSH_EXC_INFO

 64          54 LOAD_GLOBAL              2 (OverflowError)
             64 CACHE
             66 LOAD_GLOBAL              4 (SyntaxError)
             76 CACHE
             78 LOAD_GLOBAL              6 (ValueError)
             88 CACHE
             90 BUILD_TUPLE              3
             92 CHECK_EXC_MATCH
             94 POP_JUMP_IF_FALSE       25 (to 146)
             96 POP_TOP

 66          98 LOAD_FAST                0 (self)
            100 STORE_SUBSCR
            104 CACHE
            106 CACHE
            108 CACHE
            110 CACHE
            112 CACHE
            114 CACHE
            116 CACHE
            118 CACHE
            120 CACHE
            122 LOAD_FAST                2 (filename)
            124 UNPACK_SEQUENCE          1
            128 CALL                     1
            136 CACHE
            138 POP_TOP

 67         140 POP_EXCEPT
            142 LOAD_CONST               1 (False)
            144 RETURN_VALUE

 64     >>  146 RERAISE                  0
        >>  148 COPY                     3
            150 POP_EXCEPT
            152 RERAISE                  1

 69     >>  154 LOAD_FAST                4 (code)
            156 POP_JUMP_IF_NOT_NONE     2 (to 162)

 71         158 LOAD_CONST               3 (True)
            160 RETURN_VALUE

 74     >>  162 LOAD_FAST                0 (self)
            164 STORE_SUBSCR
            168 CACHE
            170 CACHE
            172 CACHE
            174 CACHE
            176 CACHE
            178 CACHE
            180 CACHE
            182 CACHE
            184 CACHE
            186 LOAD_FAST                4 (code)
            188 UNPACK_SEQUENCE          1
            192 CALL                     1
            200 CACHE
            202 POP_TOP

 75         204 LOAD_CONST               1 (False)
            206 RETURN_VALUE
ExceptionTable:
  4 to 48 -> 52 [0]
  52 to 138 -> 148 [1] lasti
  146 to 146 -> 148 [1] lasti

Disassembly of <code object runcode at 0x000001E77ECEDA70, file "code.py", line 77>:
 77           0 RESUME                   0

 89           2 NOP

 90           4 LOAD_GLOBAL              1 (NULL + exec)
             14 CACHE
             16 LOAD_FAST                1 (code)
             18 LOAD_FAST                0 (self)
             20 LOAD_ATTR                1 (NULL|self + exec)
             40 CACHE
             42 CACHE
             44 POP_TOP
             46 LOAD_CONST               1 (None)
             48 RETURN_VALUE
        >>   50 PUSH_EXC_INFO

 91          52 LOAD_GLOBAL              4 (SystemExit)
             62 CACHE
             64 CHECK_EXC_MATCH
             66 POP_JUMP_IF_FALSE        2 (to 72)
             68 POP_TOP

 92          70 RAISE_VARARGS            0

 93     >>   72 POP_TOP

 94          74 LOAD_FAST                0 (self)
             76 STORE_SUBSCR
             80 CACHE
             82 CACHE
             84 CACHE
             86 CACHE
             88 CACHE
             90 CACHE
             92 CACHE
             94 CACHE
             96 CACHE
             98 UNPACK_SEQUENCE          0
            102 CALL                     0
            110 CACHE
            112 POP_TOP
            114 POP_EXCEPT
            116 LOAD_CONST               1 (None)
            118 RETURN_VALUE
        >>  120 COPY                     3
            122 POP_EXCEPT
            124 RERAISE                  1
ExceptionTable:
  4 to 44 -> 50 [0]
  50 to 112 -> 120 [1] lasti

Disassembly of <code object showsyntaxerror at 0x000001E77EF28920, file "code.py", line 96>:
 96           0 RESUME                   0

108           2 LOAD_GLOBAL              1 (NULL + sys)
             12 CACHE
             14 LOAD_ATTR                1 (NULL|self + sys)
             34 CACHE
             36 CACHE
             38 UNPACK_SEQUENCE          3
             42 STORE_FAST               2 (type)
             44 STORE_FAST               3 (value)
             46 STORE_FAST               4 (tb)

109          48 LOAD_FAST                2 (type)
             50 LOAD_GLOBAL              0 (sys)
             60 CACHE
             62 STORE_ATTR               2 (last_type)

110          72 LOAD_FAST                3 (value)
             74 LOAD_GLOBAL              0 (sys)
             84 CACHE
             86 STORE_ATTR               3 (last_value)

111          96 LOAD_FAST                4 (tb)
             98 LOAD_GLOBAL              0 (sys)
            108 CACHE
            110 STORE_ATTR               4 (last_traceback)

112         120 LOAD_FAST                1 (filename)
            122 POP_JUMP_IF_FALSE       74 (to 272)
            124 LOAD_FAST                2 (type)
            126 LOAD_GLOBAL             10 (SyntaxError)
            136 CACHE
            138 IS_OP                    0
            140 POP_JUMP_IF_FALSE       65 (to 272)

114         142 NOP

115         144 LOAD_FAST                3 (value)
            146 LOAD_ATTR                6 (last_value)
            166 STORE_FAST               6 (dummy_filename)
            168 STORE_FAST               7 (lineno)
            170 STORE_FAST               8 (offset)
            172 STORE_FAST               9 (line)

121         174 LOAD_GLOBAL             11 (NULL + SyntaxError)
            184 CACHE
            186 LOAD_FAST                5 (msg)
            188 LOAD_FAST                1 (filename)
            190 LOAD_FAST                7 (lineno)
            192 LOAD_FAST                8 (offset)
            194 LOAD_FAST                9 (line)
            196 BUILD_TUPLE              4
            198 UNPACK_SEQUENCE          2
            202 CALL                     2
            210 CACHE
            212 STORE_FAST               3 (value)

122         214 LOAD_FAST                3 (value)
            216 LOAD_GLOBAL              0 (sys)
            226 CACHE
            228 STORE_ATTR               3 (last_value)
            238 JUMP_FORWARD            16 (to 272)
        >>  240 PUSH_EXC_INFO

116         242 LOAD_GLOBAL             14 (ValueError)
            252 CACHE
            254 CHECK_EXC_MATCH
            256 POP_JUMP_IF_FALSE        3 (to 264)
            258 POP_TOP

118         260 POP_EXCEPT
            262 JUMP_FORWARD             4 (to 272)

116     >>  264 RERAISE                  0
        >>  266 COPY                     3
            268 POP_EXCEPT
            270 RERAISE                  1

123     >>  272 LOAD_GLOBAL              0 (sys)
            282 CACHE
            284 LOAD_ATTR                8 (last_traceback)
            304 CACHE
            306 LOAD_ATTR                9 (NULL|self + last_traceback)
            326 CACHE
            328 CACHE
            330 CACHE
            332 LOAD_ATTR               11 (NULL|self + SyntaxError)
            352 CACHE
            354 CACHE
            356 CACHE
            358 CACHE
            360 STORE_FAST              10 (lines)

125         362 LOAD_FAST                0 (self)
            364 STORE_SUBSCR
            368 CACHE
            370 CACHE
            372 CACHE
            374 CACHE
            376 CACHE
            378 CACHE
            380 CACHE
            382 CACHE
            384 CACHE
            386 LOAD_CONST               1 ('')
            388 STORE_SUBSCR
            392 CACHE
            394 CACHE
            396 CACHE
            398 CACHE
            400 CACHE
            402 CACHE
            404 CACHE
            406 CACHE
            408 CACHE
            410 LOAD_FAST               10 (lines)
            412 UNPACK_SEQUENCE          1
            416 CALL                     1
            424 CACHE
            426 UNPACK_SEQUENCE          1
            430 CALL                     1
            438 CACHE
            440 POP_TOP
            442 LOAD_CONST               2 (None)
            444 RETURN_VALUE

129         446 LOAD_GLOBAL              1 (NULL + sys)
            456 CACHE
            458 LOAD_ATTR                8 (last_traceback)
            478 CALL                     3
            486 CACHE
            488 POP_TOP
            490 LOAD_CONST               2 (None)
            492 RETURN_VALUE
ExceptionTable:
  144 to 172 -> 240 [0]
  240 to 258 -> 266 [1] lasti
  264 to 264 -> 266 [1] lasti

Disassembly of <code object showtraceback at 0x000001E77E890150, file "code.py", line 131>:
131           0 RESUME                   0

139           2 LOAD_GLOBAL              1 (NULL + sys)
             12 CACHE
             14 LOAD_ATTR                1 (NULL|self + sys)
             34 CACHE
             36 CACHE
             38 COPY                     1
             40 UNPACK_SEQUENCE          3
             44 LOAD_GLOBAL              0 (sys)
             54 CACHE
             56 STORE_ATTR               2 (last_type)
             66 LOAD_GLOBAL              0 (sys)
             76 CACHE
             78 STORE_ATTR               3 (last_value)
             88 STORE_FAST               1 (last_tb)
             90 STORE_FAST               2 (ei)

140          92 LOAD_FAST                1 (last_tb)
             94 LOAD_GLOBAL              0 (sys)
            104 CACHE
            106 STORE_ATTR               4 (last_traceback)

141         116 NOP

142         118 LOAD_GLOBAL             11 (NULL + traceback)
            128 CACHE
            130 LOAD_ATTR                6 (last_value)
            150 CACHE
            152 CACHE
            154 LOAD_FAST                2 (ei)
            156 LOAD_CONST               2 (1)
            158 BINARY_SUBSCR
            162 CACHE
            164 CACHE
            166 CACHE
            168 LOAD_FAST                1 (last_tb)
            170 LOAD_ATTR                7 (NULL|self + last_value)
            190 CACHE
            192 CACHE
            194 STORE_FAST               3 (lines)

143         196 LOAD_GLOBAL              0 (sys)
            206 CACHE
            208 LOAD_ATTR                8 (last_traceback)
            228 CACHE
            230 LOAD_ATTR                9 (NULL|self + last_traceback)
            250 CACHE
            252 CACHE
            254 CACHE
            256 CACHE
            258 CACHE
            260 CACHE
            262 CACHE
            264 CACHE
            266 CACHE
            268 LOAD_CONST               3 ('')
            270 STORE_SUBSCR
            274 CACHE
            276 CACHE
            278 CACHE
            280 CACHE
            282 CACHE
            284 CACHE
            286 CACHE
            288 CACHE
            290 CACHE
            292 LOAD_FAST                3 (lines)
            294 UNPACK_SEQUENCE          1
            298 CALL                     1
            306 CACHE
            308 UNPACK_SEQUENCE          1
            312 CALL                     1
            320 CACHE
            322 POP_TOP
            324 JUMP_FORWARD            34 (to 394)

148         326 LOAD_GLOBAL              1 (NULL + sys)
            336 CACHE
            338 LOAD_ATTR                8 (last_traceback)
            358 CACHE
            360 CACHE
            362 LOAD_FAST                2 (ei)
            364 LOAD_CONST               2 (1)
            366 BINARY_SUBSCR
            370 CACHE
            372 CACHE
            374 CACHE
            376 LOAD_FAST                1 (last_tb)
            378 UNPACK_SEQUENCE          3
            382 CALL                     3
            390 CACHE
            392 POP_TOP

150     >>  394 LOAD_CONST               4 (None)
            396 COPY                     1
            398 STORE_FAST               1 (last_tb)
            400 STORE_FAST               2 (ei)
            402 LOAD_CONST               4 (None)
            404 RETURN_VALUE
        >>  406 PUSH_EXC_INFO
            408 LOAD_CONST               4 (None)
            410 COPY                     1
            412 STORE_FAST               1 (last_tb)
            414 STORE_FAST               2 (ei)
            416 RERAISE                  0
        >>  418 COPY                     3
            420 POP_EXCEPT
            422 RERAISE                  1
ExceptionTable:
  118 to 392 -> 406 [0]
  406 to 416 -> 418 [1] lasti

Disassembly of <code object write at 0x000001E77ECF2BC0, file "code.py", line 152>:
152           0 RESUME                   0

159           2 LOAD_GLOBAL              0 (sys)
             12 CACHE
             14 LOAD_ATTR                1 (NULL|self + sys)
             34 CACHE
             36 CACHE
             38 CACHE
             40 CACHE
             42 CACHE
             44 CACHE
             46 LOAD_FAST                1 (data)
             48 UNPACK_SEQUENCE          1
             52 CALL                     1
             60 CACHE
             62 POP_TOP
             64 LOAD_CONST               1 (None)
             66 RETURN_VALUE

Disassembly of <code object InteractiveConsole at 0x000001E77ECDAA30, file "code.py", line 162>:
162           0 RESUME                   0
              2 LOAD_NAME                0 (__name__)
              4 STORE_NAME               1 (__module__)
              6 LOAD_CONST               0 ('InteractiveConsole')
              8 STORE_NAME               2 (__qualname__)

163          10 LOAD_CONST               1 ('Closely emulate the behavior of the interactive Python interpreter.\n\n    This class builds on InteractiveInterpreter and adds prompting\n    using the familiar sys.ps1 and sys.ps2, and input buffering.\n\n    ')
             12 STORE_NAME               3 (__doc__)

170          14 LOAD_CONST              10 ((None, '<console>'))
             16 LOAD_CONST               4 (<code object __init__ at 0x000001E77ECED930, file "code.py", line 170>)
             18 MAKE_FUNCTION            1 (defaults)
             20 STORE_NAME               4 (__init__)

184          22 LOAD_CONST               5 (<code object resetbuffer at 0x000001E77ECD2330, file "code.py", line 184>)
             24 MAKE_FUNCTION            0
             26 STORE_NAME               5 (resetbuffer)

188          28 LOAD_CONST              11 ((None, None))
             30 LOAD_CONST               6 (<code object interact at 0x000001E77EF44590, file "code.py", line 188>)
             32 MAKE_FUNCTION            1 (defaults)
             34 STORE_NAME               6 (interact)

242          36 LOAD_CONST               7 (<code object push at 0x000001E77EC72A60, file "code.py", line 242>)
             38 MAKE_FUNCTION            0
             40 STORE_NAME               7 (push)

263          42 LOAD_CONST              12 (('',))
             44 LOAD_CONST               9 (<code object raw_input at 0x000001E77ECD2410, file "code.py", line 263>)
             46 MAKE_FUNCTION            1 (defaults)
             48 STORE_NAME               8 (raw_input)
             50 LOAD_CONST               2 (None)
             52 RETURN_VALUE

Disassembly of <code object __init__ at 0x000001E77ECED930, file "code.py", line 170>:
170           0 RESUME                   0

180           2 LOAD_GLOBAL              0 (InteractiveInterpreter)
             12 CACHE
             14 STORE_SUBSCR
             18 CACHE
             20 CACHE
             22 CACHE
             24 CACHE
             26 CACHE
             28 CACHE
             30 CACHE
             32 CACHE
             34 CACHE
             36 LOAD_FAST                0 (self)
             38 LOAD_FAST                1 (locals)
             40 UNPACK_SEQUENCE          2
             44 CALL                     2
             52 CACHE
             54 POP_TOP

181          56 LOAD_FAST                2 (filename)
             58 LOAD_FAST                0 (self)
             60 STORE_ATTR               2 (filename)

182          70 LOAD_FAST                0 (self)
             72 STORE_SUBSCR
             76 CACHE
             78 CACHE
             80 CACHE
             82 CACHE
             84 CACHE
             86 CACHE
             88 CACHE
             90 CACHE
             92 CACHE
             94 UNPACK_SEQUENCE          0
             98 CALL                     0
            106 CACHE
            108 POP_TOP
            110 LOAD_CONST               1 (None)
            112 RETURN_VALUE

Disassembly of <code object resetbuffer at 0x000001E77ECD2330, file "code.py", line 184>:
184           0 RESUME                   0

186           2 BUILD_LIST               0
              4 LOAD_FAST                0 (self)
              6 STORE_ATTR               0 (buffer)
             16 LOAD_CONST               1 (None)
             18 RETURN_VALUE

Disassembly of <code object interact at 0x000001E77EF44590, file "code.py", line 188>:
188           0 RESUME                   0

204           2 NOP

205           4 LOAD_GLOBAL              0 (sys)
             14 CACHE
             16 LOAD_ATTR                1 (NULL|self + sys)
             36 CACHE
             38 CACHE
             40 CACHE
             42 CACHE
             44 CHECK_EXC_MATCH
             46 POP_JUMP_IF_FALSE       15 (to 78)
             48 POP_TOP

207          50 LOAD_CONST               1 ('>>> ')
             52 LOAD_GLOBAL              0 (sys)
             62 CACHE
             64 STORE_ATTR               1 (ps1)
             74 POP_EXCEPT
             76 JUMP_FORWARD             4 (to 86)

206     >>   78 RERAISE                  0
        >>   80 COPY                     3
             82 POP_EXCEPT
             84 RERAISE                  1

208     >>   86 NOP

209          88 LOAD_GLOBAL              0 (sys)
             98 CACHE
            100 LOAD_ATTR                3 (NULL|self + ps1)
            120 CACHE
            122 CACHE
            124 CACHE
            126 CACHE
            128 CHECK_EXC_MATCH
            130 POP_JUMP_IF_FALSE       15 (to 162)
            132 POP_TOP

211         134 LOAD_CONST               2 ('... ')
            136 LOAD_GLOBAL              0 (sys)
            146 CACHE
            148 STORE_ATTR               3 (ps2)
            158 POP_EXCEPT
            160 JUMP_FORWARD             4 (to 170)

210     >>  162 RERAISE                  0
        >>  164 COPY                     3
            166 POP_EXCEPT
            168 RERAISE                  1

212     >>  170 LOAD_CONST               3 ('Type "help", "copyright", "credits" or "license" for more information.')
            172 STORE_FAST               3 (cprt)

213         174 LOAD_FAST                1 (banner)
            176 POP_JUMP_IF_NOT_NONE    65 (to 308)

214         178 LOAD_FAST                0 (self)
            180 STORE_SUBSCR
            184 CACHE
            186 CACHE
            188 CACHE
            190 CACHE
            192 CACHE
            194 CACHE
            196 CACHE
            198 CACHE
            200 CACHE
            202 LOAD_CONST               5 ('Python ')

215         204 LOAD_GLOBAL              0 (sys)
            214 CACHE
            216 LOAD_ATTR                5 (NULL|self + AttributeError)
            236 CACHE
            238 CACHE
            240 CACHE
            242 LOAD_ATTR                6 (ps2)

216         262 LOAD_FAST                0 (self)
            264 LOAD_ATTR                7 (NULL|self + ps2)
            284 FORMAT_VALUE             1 (str)
            286 LOAD_CONST               9 (')\n')

214         288 BUILD_STRING             9
            290 UNPACK_SEQUENCE          1
            294 CALL                     1
            302 CACHE
            304 POP_TOP
            306 JUMP_FORWARD            39 (to 386)

217     >>  308 LOAD_FAST                1 (banner)
            310 POP_JUMP_IF_FALSE       37 (to 386)

218         312 LOAD_FAST                0 (self)
            314 STORE_SUBSCR
            318 CACHE
            320 CACHE
            322 CACHE
            324 CACHE
            326 CACHE
            328 CACHE
            330 CACHE
            332 CACHE
            334 CACHE
            336 LOAD_CONST              10 ('%s\n')
            338 LOAD_GLOBAL             19 (NULL + str)
            348 CACHE
            350 LOAD_FAST                1 (banner)
            352 UNPACK_SEQUENCE          1
            356 CALL                     1
            364 CACHE
            366 BINARY_OP                6 (%)
            370 UNPACK_SEQUENCE          1
            374 CALL                     1
            382 CACHE
            384 POP_TOP

219     >>  386 LOAD_CONST              11 (0)
            388 STORE_FAST               4 (more)

220         390 NOP

221     >>  392 NOP

222         394 LOAD_FAST                4 (more)
            396 POP_JUMP_IF_FALSE       13 (to 424)

223         398 LOAD_GLOBAL              0 (sys)
            408 CACHE
            410 LOAD_ATTR                3 (NULL|self + ps1)
            430 CACHE
            432 CACHE
            434 CACHE
            436 LOAD_ATTR                1 (NULL|self + sys)
            456 CACHE
            458 CACHE
            460 CACHE
            462 CACHE
            464 CACHE
            466 CACHE
            468 CACHE
            470 CACHE
            472 CACHE
            474 LOAD_FAST                5 (prompt)
            476 UNPACK_SEQUENCE          1
            480 CALL                     1
            488 CACHE
            490 STORE_FAST               6 (line)

232         492 LOAD_FAST                0 (self)
            494 STORE_SUBSCR
            498 CACHE
            500 CACHE
            502 CACHE
            504 CACHE
            506 CACHE
            508 CACHE
            510 CACHE
            512 CACHE
            514 CACHE
            516 LOAD_FAST                6 (line)
            518 UNPACK_SEQUENCE          1
            522 CALL                     1
            530 CACHE
            532 STORE_FAST               4 (more)
            534 JUMP_FORWARD            37 (to 610)
        >>  536 PUSH_EXC_INFO

228         538 LOAD_GLOBAL             24 (EOFError)
            548 CACHE
            550 CHECK_EXC_MATCH
            552 POP_JUMP_IF_FALSE       24 (to 602)
            554 POP_TOP

229         556 LOAD_FAST                0 (self)
            558 STORE_SUBSCR
            562 CACHE
            564 CACHE
            566 CACHE
            568 CACHE
            570 CACHE
            572 CACHE
            574 CACHE
            576 CACHE
            578 CACHE
            580 LOAD_CONST               7 ('\n')
            582 UNPACK_SEQUENCE          1
            586 CALL                     1
            594 CACHE
            596 POP_TOP

230         598 POP_EXCEPT
            600 JUMP_FORWARD            65 (to 732)

228     >>  602 RERAISE                  0
        >>  604 COPY                     3
            606 POP_EXCEPT
            608 RERAISE                  1

232     >>  610 JUMP_FORWARD            59 (to 730)
        >>  612 PUSH_EXC_INFO

233         614 LOAD_GLOBAL             26 (KeyboardInterrupt)
            624 CACHE
            626 CHECK_EXC_MATCH
            628 POP_JUMP_IF_FALSE       46 (to 722)
            630 POP_TOP

234         632 LOAD_FAST                0 (self)
            634 STORE_SUBSCR
            638 CACHE
            640 CACHE
            642 CACHE
            644 CACHE
            646 CACHE
            648 CACHE
            650 CACHE
            652 CACHE
            654 CACHE
            656 LOAD_CONST              13 ('\nKeyboardInterrupt\n')
            658 UNPACK_SEQUENCE          1
            662 CALL                     1
            670 CACHE
            672 POP_TOP

235         674 LOAD_FAST                0 (self)
            676 STORE_SUBSCR
            680 CACHE
            682 CACHE
            684 CACHE
            686 CACHE
            688 CACHE
            690 CACHE
            692 CACHE
            694 CACHE
            696 CACHE
            698 UNPACK_SEQUENCE          0
            702 CALL                     0
            710 CACHE
            712 POP_TOP

236         714 LOAD_CONST              11 (0)
            716 STORE_FAST               4 (more)
            718 POP_EXCEPT
            720 JUMP_FORWARD             4 (to 730)

233     >>  722 RERAISE                  0
        >>  724 COPY                     3
            726 POP_EXCEPT
            728 RERAISE                  1

220     >>  730 JUMP_BACKWARD          170 (to 392)

237     >>  732 LOAD_FAST                2 (exitmsg)
            734 POP_JUMP_IF_NOT_NONE    36 (to 808)

238         736 LOAD_FAST                0 (self)
            738 STORE_SUBSCR
            742 CACHE
            744 CACHE
            746 CACHE
            748 CACHE
            750 CACHE
            752 CACHE
            754 CACHE
            756 CACHE
            758 CACHE
            760 LOAD_CONST              14 ('now exiting %s...\n')
            762 LOAD_FAST                0 (self)
            764 LOAD_ATTR                7 (NULL|self + ps2)
            784 BINARY_OP                6 (%)
            788 UNPACK_SEQUENCE          1
            792 CALL                     1
            800 CACHE
            802 POP_TOP
            804 LOAD_CONST               4 (None)
            806 RETURN_VALUE

239     >>  808 LOAD_FAST                2 (exitmsg)
            810 LOAD_CONST              15 ('')
            812 COMPARE_OP               3 (<)
            816 CACHE
            818 POP_JUMP_IF_FALSE       26 (to 872)

240         820 LOAD_FAST                0 (self)
            822 STORE_SUBSCR
            826 CACHE
            828 CACHE
            830 CACHE
            832 CACHE
            834 CACHE
            836 CACHE
            838 CACHE
            840 CACHE
            842 CACHE
            844 LOAD_CONST              10 ('%s\n')
            846 LOAD_FAST                2 (exitmsg)
            848 BINARY_OP                6 (%)
            852 UNPACK_SEQUENCE          1
            856 CALL                     1
            864 CACHE
            866 POP_TOP
            868 LOAD_CONST               4 (None)
            870 RETURN_VALUE

239     >>  872 LOAD_CONST               4 (None)
            874 RETURN_VALUE
ExceptionTable:
  4 to 26 -> 30 [0]
  30 to 72 -> 80 [1] lasti
  78 to 78 -> 80 [1] lasti
  88 to 110 -> 114 [0]
  114 to 156 -> 164 [1] lasti
  162 to 162 -> 164 [1] lasti
  394 to 446 -> 612 [0]
  450 to 490 -> 536 [0]
  492 to 534 -> 612 [0]
  536 to 596 -> 604 [1] lasti
  598 to 598 -> 612 [0]
  602 to 602 -> 604 [1] lasti
  604 to 608 -> 612 [0]
  612 to 716 -> 724 [1] lasti
  722 to 722 -> 724 [1] lasti

Disassembly of <code object push at 0x000001E77EC72A60, file "code.py", line 242>:
242           0 RESUME                   0

256           2 LOAD_FAST                0 (self)
              4 LOAD_ATTR                0 (buffer)
             24 CACHE
             26 CACHE
             28 CACHE
             30 CACHE
             32 CACHE
             34 CACHE
             36 LOAD_FAST                1 (line)
             38 UNPACK_SEQUENCE          1
             42 CALL                     1
             50 CACHE
             52 POP_TOP

257          54 LOAD_CONST               1 ('\n')
             56 STORE_SUBSCR
             60 CACHE
             62 CACHE
             64 CACHE
             66 CACHE
             68 CACHE
             70 CACHE
             72 CACHE
             74 CACHE
             76 CACHE
             78 LOAD_FAST                0 (self)
             80 LOAD_ATTR                0 (buffer)
            100 CACHE
            102 CACHE
            104 STORE_FAST               2 (source)

258         106 LOAD_FAST                0 (self)
            108 STORE_SUBSCR
            112 CACHE
            114 CACHE
            116 CACHE
            118 CACHE
            120 CACHE
            122 CACHE
            124 CACHE
            126 CACHE
            128 CACHE
            130 LOAD_FAST                2 (source)
            132 LOAD_FAST                0 (self)
            134 LOAD_ATTR                4 (join)
            154 CACHE
            156 CACHE
            158 STORE_FAST               3 (more)

259         160 LOAD_FAST                3 (more)
            162 POP_JUMP_IF_TRUE        20 (to 204)

260         164 LOAD_FAST                0 (self)
            166 STORE_SUBSCR
            170 CACHE
            172 CACHE
            174 CACHE
            176 CACHE
            178 CACHE
            180 CACHE
            182 CACHE
            184 CACHE
            186 CACHE
            188 UNPACK_SEQUENCE          0
            192 CALL                     0
            200 CACHE
            202 POP_TOP

261     >>  204 LOAD_FAST                3 (more)
            206 RETURN_VALUE

Disassembly of <code object raw_input at 0x000001E77ECD2410, file "code.py", line 263>:
263           0 RESUME                   0

274           2 LOAD_GLOBAL              1 (NULL + input)
             12 CACHE
             14 LOAD_FAST                1 (prompt)
             16 UNPACK_SEQUENCE          1
             20 CALL                     1
             28 CACHE
             30 RETURN_VALUE

Disassembly of <code object interact at 0x000001E77ED182D0, file "code.py", line 278>:
278           0 RESUME                   0

293           2 LOAD_GLOBAL              1 (NULL + InteractiveConsole)
             12 CACHE
             14 LOAD_FAST                2 (local)
             16 UNPACK_SEQUENCE          1
             20 CALL                     1
             28 CACHE
             30 STORE_FAST               4 (console)

294          32 LOAD_FAST                1 (readfunc)
             34 POP_JUMP_IF_NONE         8 (to 52)

295          36 LOAD_FAST                1 (readfunc)
             38 LOAD_FAST                4 (console)
             40 STORE_ATTR               1 (raw_input)
             50 JUMP_FORWARD            22 (to 96)

297     >>   52 NOP

298          54 LOAD_CONST               2 (0)
             56 LOAD_CONST               1 (None)
             58 IMPORT_NAME              2 (readline)
             60 STORE_FAST               5 (readline)
             62 JUMP_FORWARD            16 (to 96)
        >>   64 PUSH_EXC_INFO

299          66 LOAD_GLOBAL              6 (ImportError)
             76 CACHE
             78 CHECK_EXC_MATCH
             80 POP_JUMP_IF_FALSE        3 (to 88)
             82 POP_TOP

300          84 POP_EXCEPT
             86 JUMP_FORWARD             4 (to 96)

299     >>   88 RERAISE                  0
        >>   90 COPY                     3
             92 POP_EXCEPT
             94 RERAISE                  1

301     >>   96 LOAD_FAST                4 (console)
             98 STORE_SUBSCR
            102 CACHE
            104 CACHE
            106 CACHE
            108 CACHE
            110 CACHE
            112 CACHE
            114 CACHE
            116 CACHE
            118 CACHE
            120 LOAD_FAST                0 (banner)
            122 LOAD_FAST                3 (exitmsg)
            124 UNPACK_SEQUENCE          2
            128 CALL                     2
            136 CACHE
            138 POP_TOP
            140 LOAD_CONST               1 (None)
            142 RETURN_VALUE
ExceptionTable:
  54 to 60 -> 64 [0]
  64 to 82 -> 90 [1] lasti
  88 to 88 -> 90 [1] lasti


# CONSTANTS:
==================================================
# 0: "Utilities needed to emulate Python's interactive interpreter.\n\n"
# 1: 0
# 2: None
# 3: tuple - ('CommandCompiler', 'compile_command')
# 4: tuple - ('InteractiveInterpreter', 'InteractiveConsole', 'interact', 'compile_command')
# 5: <code object InteractiveInterpreter from code.py>
# 6: 'InteractiveInterpreter'
# 7: <code object InteractiveConsole from code.py>
# 8: 'InteractiveConsole'
# 9: <code object interact from code.py>
# 10: '__main__'
# 11: '-q'
# 12: 'store_true'
# 13: "don't print version and copyright messages"
# 14: tuple - ('action', 'help')
# 15: ''
# 16: tuple - (None, None, None, None)


# NAMES (global/attribute references):
==================================================
# 0: '__doc__'
# 1: 'sys'
# 2: 'traceback'
# 3: 'codeop'
# 4: 'CommandCompiler'
# 5: 'compile_command'
# 6: '__all__'
# 7: 'InteractiveInterpreter'
# 8: 'InteractiveConsole'
# 9: 'interact'
# 10: '__name__'
# 11: 'argparse'
# 12: 'ArgumentParser'
# 13: 'parser'
# 14: 'add_argument'
# 15: 'parse_args'
# 16: 'args'
# 17: 'q'
# 18: 'flags'
# 19: 'quiet'
# 20: 'banner'


# VARIABLE NAMES (local variables):
==================================================


# FREE VARIABLES:
==================================================
