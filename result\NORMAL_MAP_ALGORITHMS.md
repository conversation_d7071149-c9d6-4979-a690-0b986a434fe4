# 法线贴图算法详细说明

## 概述

本文档详细说明了从 `imgproc.exe` 反编译并重构的两个核心法线贴图生成算法：

1. **`create_object_normal_map`** - 基于光度立体法的物体法线贴图生成
2. **`create_tangent_normal_map`** - 基于梯度的切线空间法线贴图生成

## 1. 物体法线贴图生成 (create_object_normal_map)

### 算法原理

该算法使用**光度立体法 (Photometric Stereo)** 从多个不同光照方向的图像中重建表面法线。

#### 数学基础

光度立体法基于朗伯反射模型：
```
I = ρ * n · l
```
其中：
- `I` = 观察到的像素强度
- `ρ` = 表面反射率（albedo）
- `n` = 表面法线向量
- `l` = 光照方向向量

#### 输入要求

- **6个方向性光照图像**，对应以下光照方向：
  1. 左侧光照: `(-1, 0, 1)`
  2. 右侧光照: `(1, 0, 1)`
  3. 顶部光照: `(0, -1, 1)`
  4. 底部光照: `(0, 1, 1)`
  5. 正面光照: `(0, 0, 1)`
  6. 背面光照: `(0, 0, -1)`

#### 算法步骤

1. **图像预处理**
   ```python
   # 转换为灰度图
   gray_img = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
   
   # 强度归一化到 [0, 1]
   normalized_img = normalize_intensity(gray_img)
   ```

2. **构建光照矩阵**
   ```python
   light_directions = np.array([
       [-1.0, 0.0, 1.0],   # 左
       [1.0, 0.0, 1.0],    # 右
       [0.0, -1.0, 1.0],   # 上
       [0.0, 1.0, 1.0],    # 下
       [0.0, 0.0, 1.0],    # 前
       [0.0, 0.0, -1.0]    # 后
   ])
   ```

3. **逐像素法线计算**
   ```python
   # 对每个像素求解线性方程组: L * n = I
   normal = np.linalg.lstsq(light_directions, intensities, rcond=None)[0]
   
   # 归一化法线向量
   normal = normal / np.linalg.norm(normal)
   ```

4. **输出格式转换**
   ```python
   # 从 [-1, 1] 转换到 [0, 255] BGR格式
   bgr_map = ((normal_map + 1.0) * 0.5 * 255.0).astype(np.uint8)
   bgr_map = bgr_map[:, :, [2, 1, 0]]  # RGB -> BGR
   ```

### 优势与限制

**优势：**
- 高精度的法线重建
- 适用于复杂几何形状
- 能处理细节丰富的表面

**限制：**
- 需要6张精确对齐的图像
- 假设朗伯反射表面
- 对阴影区域处理有限

## 2. 切线空间法线贴图生成 (create_tangent_normal_map)

### 算法原理

该算法从**高度图 (Height Map)** 通过**梯度计算**生成切线空间法线贴图。

#### 数学基础

表面法线通过高度场的梯度计算：
```
∇h = (∂h/∂x, ∂h/∂y)
n = normalize((-∂h/∂x, -∂h/∂y, 1))
```

#### 输入要求

- **单张高度图**（优选16位，支持8位）
- 灰度图像，亮度表示高度

#### 算法步骤

1. **图像预处理**
   ```python
   # 转换为灰度图并归一化
   if image.dtype == np.uint16:
       height_map = image.astype(np.float32) / 65535.0
   elif image.dtype == np.uint8:
       height_map = image.astype(np.float32) / 255.0
   ```

2. **高度图平滑**
   ```python
   # 应用高斯模糊减少噪声
   height_map = cv2.GaussianBlur(height_map, (kernel_size, kernel_size), sigma)
   ```

3. **梯度计算**
   ```python
   # 使用Sobel算子计算X和Y方向梯度
   grad_x = cv2.Sobel(height_map, cv2.CV_32F, 1, 0, ksize=kernel_size)
   grad_y = cv2.Sobel(height_map, cv2.CV_32F, 0, 1, ksize=kernel_size)
   ```

4. **法线向量构建**
   ```python
   # 切线空间法线: X = -grad_x, Y = -grad_y, Z = 1
   normal_map[:, :, 0] = -grad_x  # X分量 (红色通道)
   normal_map[:, :, 1] = -grad_y  # Y分量 (绿色通道)
   normal_map[:, :, 2] = 1.0      # Z分量 (蓝色通道)
   ```

5. **向量归一化**
   ```python
   # 归一化每个法线向量
   for y in range(height):
       for x in range(width):
           normal = normal_map[y, x]
           length = np.linalg.norm(normal)
           if length > 0:
               normal_map[y, x] = normal / length
   ```

6. **输出格式转换**
   ```python
   # 从 [-1, 1] 转换到 [0, 1]
   normal_map = (normal_map + 1.0) * 0.5
   
   # 确保Z分量 > 0.5 (向外指向)
   normal_map[:, :, 2] = np.maximum(normal_map[:, :, 2], 0.5)
   ```

### 参数说明

- **`blur_size`**: 高斯模糊核大小，控制平滑程度
- **`kernel_size`**: Sobel算子核大小，影响梯度计算精度
- **`sigma`**: 高斯模糊标准差，控制模糊强度
- **`remove_blue`**: 是否移除蓝色通道（Z分量）

### 优势与限制

**优势：**
- 只需单张高度图
- 计算效率高
- 参数可调节，适应不同需求

**限制：**
- 依赖高度图质量
- 对噪声敏感
- 无法处理悬垂结构

## 3. 实现细节

### 错误处理

```python
# 图像加载验证
if image is None:
    print(f"Image not found: {image_path}")
    return False

# 数据类型验证
if image.dtype not in [np.uint8, np.uint16]:
    print("Image is not 8bit or 16bit")
    return False

# 矩阵奇异性检查
try:
    normal = np.linalg.lstsq(light_directions, intensities, rcond=None)[0]
except np.linalg.LinAlgError:
    normal = np.array([0.0, 0.0, 1.0])  # 默认向上法线
```

### 性能优化

1. **向量化操作**: 尽可能使用NumPy向量化操作
2. **内存管理**: 及时释放大型数组
3. **进度指示**: 长时间操作提供进度反馈

### 质量控制

```python
def validate_normal_map(normal_map: np.ndarray) -> bool:
    # 检查形状
    if len(normal_map.shape) != 3 or normal_map.shape[2] != 3:
        return False
    
    # 检查数值范围
    if np.min(normal_map) < -1.1 or np.max(normal_map) > 1.1:
        return False
    
    # 检查NaN和无穷值
    if np.any(np.isnan(normal_map)) or np.any(np.isinf(normal_map)):
        return False
    
    return True
```

## 4. 使用示例

### 物体法线贴图
```bash
python reconstructed_main.py --function create_object_normal_map \
    --directional_images img1.tif img2.tif img3.tif img4.tif img5.tif img6.tif \
    --output object_normal.png
```

### 切线法线贴图
```bash
python reconstructed_main.py --function create_tangent_normal_map \
    --image1 height_map.tif \
    --output tangent_normal.png \
    --blur_size 5 \
    --kernel_size 3 \
    --sigma 1.0
```

## 5. 测试与验证

使用提供的测试脚本验证算法：
```bash
python test_normal_maps.py
```

测试脚本会：
- 生成合成测试数据
- 验证两种算法的输出
- 检查结果的有效性
- 提供性能基准

---

**注意**: 这些算法是基于对原始 `imgproc.exe` 的逆向工程重构的，实际实现可能与原始版本有所差异，但核心算法原理保持一致。
