# MAIN APPLICATION CODE OBJECT
# Position: 7786979
# Filename: abc.py
# Function: <module>
# Args: 0
# Locals: 0
# Stack size: 5
# Flags: 0

# BYTECODE DISASSEMBLY:
==================================================
  0           0 RESUME                   0

  4           2 LOAD_CONST               0 ('Abstract Base Classes (ABCs) according to PEP 3119.')
              4 STORE_NAME               0 (__doc__)

  7           6 LOAD_CONST               1 (<code object abstractmethod at 0x000001E77EC5D450, file "abc.py", line 7>)
              8 MAKE_FUNCTION            0
             10 STORE_NAME               1 (abstractmethod)

 28          12 PUSH_NULL
             14 LOAD_BUILD_CLASS
             16 LOAD_CONST               2 (<code object abstractclassmethod at 0x000001E77EC36B50, file "abc.py", line 28>)
             18 MAKE_FUNCTION            0
             20 LOAD_CONST               3 ('abstractclassmethod')
             22 LOAD_NAME                2 (classmethod)
             24 UNPACK_SEQUENCE          3
             28 CALL                     3
             36 CACHE
             38 STORE_NAME               3 (abstractclassmethod)

 48          40 PUSH_NULL
             42 LOAD_BUILD_CLASS
             44 LOAD_CONST               4 (<code object abstractstaticmethod at 0x000001E77EC36D30, file "abc.py", line 48>)
             46 MAKE_FUNCTION            0
             48 LOAD_CONST               5 ('abstractstaticmethod')
             50 LOAD_NAME                4 (staticmethod)
             52 UNPACK_SEQUENCE          3
             56 CALL                     3
             64 CACHE
             66 STORE_NAME               5 (abstractstaticmethod)

 68          68 PUSH_NULL
             70 LOAD_BUILD_CLASS
             72 LOAD_CONST               6 (<code object abstractproperty at 0x000001E77EC5D530, file "abc.py", line 68>)
             74 MAKE_FUNCTION            0
             76 LOAD_CONST               7 ('abstractproperty')
             78 LOAD_NAME                6 (property)
             80 UNPACK_SEQUENCE          3
             84 CALL                     3
             92 CACHE
             94 STORE_NAME               7 (abstractproperty)

 84          96 NOP

 85          98 LOAD_CONST               8 (0)
            100 LOAD_CONST               9 (('get_cache_token', '_abc_init', '_abc_register', '_abc_instancecheck', '_abc_subclasscheck', '_get_dump', '_reset_registry', '_reset_caches'))
            102 IMPORT_NAME              8 (_abc)
            104 IMPORT_FROM              9 (get_cache_token)
            106 STORE_NAME               9 (get_cache_token)
            108 IMPORT_FROM             10 (_abc_init)
            110 STORE_NAME              10 (_abc_init)
            112 IMPORT_FROM             11 (_abc_register)
            114 STORE_NAME              11 (_abc_register)
            116 IMPORT_FROM             12 (_abc_instancecheck)
            118 STORE_NAME              12 (_abc_instancecheck)
            120 IMPORT_FROM             13 (_abc_subclasscheck)
            122 STORE_NAME              13 (_abc_subclasscheck)
            124 IMPORT_FROM             14 (_get_dump)
            126 STORE_NAME              14 (_get_dump)
            128 IMPORT_FROM             15 (_reset_registry)
            130 STORE_NAME              15 (_reset_registry)
            132 IMPORT_FROM             16 (_reset_caches)
            134 STORE_NAME              16 (_reset_caches)
            136 POP_TOP

 92         138 PUSH_NULL
            140 LOAD_BUILD_CLASS
            142 LOAD_CONST              10 (<code object ABCMeta at 0x000001E77EC46230, file "abc.py", line 92>)
            144 MAKE_FUNCTION            0
            146 LOAD_CONST              11 ('ABCMeta')
            148 LOAD_NAME               17 (type)
            150 UNPACK_SEQUENCE          3
            154 CALL                     3
            162 CACHE
            164 STORE_NAME              18 (ABCMeta)
            166 JUMP_FORWARD            26 (to 220)
        >>  168 PUSH_EXC_INFO

 88         170 LOAD_NAME               19 (ImportError)
            172 CHECK_EXC_MATCH
            174 POP_JUMP_IF_FALSE       18 (to 212)
            176 POP_TOP

 89         178 LOAD_CONST               8 (0)
            180 LOAD_CONST              12 (('ABCMeta', 'get_cache_token'))
            182 IMPORT_NAME             20 (_py_abc)
            184 IMPORT_FROM             18 (ABCMeta)
            186 STORE_NAME              18 (ABCMeta)
            188 IMPORT_FROM              9 (get_cache_token)
            190 STORE_NAME               9 (get_cache_token)
            192 POP_TOP

 90         194 LOAD_CONST              13 ('abc')
            196 LOAD_NAME               18 (ABCMeta)
            198 STORE_ATTR              21 (__module__)
            208 POP_EXCEPT
            210 JUMP_FORWARD             4 (to 220)

 88     >>  212 RERAISE                  0
        >>  214 COPY                     3
            216 POP_EXCEPT
            218 RERAISE                  1

146     >>  220 LOAD_CONST              14 (<code object update_abstractmethods at 0x000001E77E890B10, file "abc.py", line 146>)
            222 MAKE_FUNCTION            0
            224 STORE_NAME              22 (update_abstractmethods)

184         226 PUSH_NULL
            228 LOAD_BUILD_CLASS
            230 LOAD_CONST              15 (<code object ABC at 0x000001E77EC5D610, file "abc.py", line 184>)
            232 MAKE_FUNCTION            0
            234 LOAD_CONST              16 ('ABC')
            236 LOAD_NAME               18 (ABCMeta)
            238 KW_NAMES                17 (('metaclass',))
            240 UNPACK_SEQUENCE          3
            244 CALL                     3
            252 CACHE
            254 STORE_NAME              23 (ABC)
            256 LOAD_CONST              18 (None)
            258 RETURN_VALUE
ExceptionTable:
  98 to 136 -> 168 [0]
  168 to 206 -> 214 [1] lasti
  212 to 212 -> 214 [1] lasti

Disassembly of <code object abstractmethod at 0x000001E77EC5D450, file "abc.py", line 7>:
  7           0 RESUME                   0

 24           2 LOAD_CONST               1 (True)
              4 LOAD_FAST                0 (funcobj)
              6 STORE_ATTR               0 (__isabstractmethod__)

 25          16 LOAD_FAST                0 (funcobj)
             18 RETURN_VALUE

Disassembly of <code object abstractclassmethod at 0x000001E77EC36B50, file "abc.py", line 28>:
              0 MAKE_CELL                0 (__class__)

 28           2 RESUME                   0
              4 LOAD_NAME                0 (__name__)
              6 STORE_NAME               1 (__module__)
              8 LOAD_CONST               0 ('abstractclassmethod')
             10 STORE_NAME               2 (__qualname__)

 29          12 LOAD_CONST               1 ("A decorator indicating abstract classmethods.\n\n    Deprecated, use 'classmethod' with 'abstractmethod' instead:\n\n        class C(ABC):\n            @classmethod\n            @abstractmethod\n            def my_abstract_classmethod(cls, ...):\n                ...\n\n    ")
             14 STORE_NAME               3 (__doc__)

 41          16 LOAD_CONST               2 (True)
             18 STORE_NAME               4 (__isabstractmethod__)

 43          20 LOAD_CLOSURE             0 (__class__)
             22 BUILD_TUPLE              1
             24 LOAD_CONST               3 (<code object __init__ at 0x000001E77EC561F0, file "abc.py", line 43>)
             26 MAKE_FUNCTION            8 (closure)
             28 STORE_NAME               5 (__init__)
             30 LOAD_CLOSURE             0 (__class__)
             32 COPY                     1
             34 STORE_NAME               6 (__classcell__)
             36 RETURN_VALUE

Disassembly of <code object __init__ at 0x000001E77EC561F0, file "abc.py", line 43>:
              0 COPY_FREE_VARS           1

 43           2 RESUME                   0

 44           4 LOAD_CONST               1 (True)
              6 LOAD_FAST                1 (callable)
              8 STORE_ATTR               0 (__isabstractmethod__)

 45          18 LOAD_GLOBAL              3 (NULL + super)
             28 CACHE
             30 UNPACK_SEQUENCE          0
             34 CALL                     0
             42 CACHE
             44 STORE_SUBSCR
             48 CACHE
             50 CACHE
             52 CACHE
             54 CACHE
             56 CACHE
             58 CACHE
             60 CACHE
             62 CACHE
             64 CACHE
             66 LOAD_FAST                1 (callable)
             68 UNPACK_SEQUENCE          1
             72 CALL                     1
             80 CACHE
             82 POP_TOP
             84 LOAD_CONST               0 (None)
             86 RETURN_VALUE

Disassembly of <code object abstractstaticmethod at 0x000001E77EC36D30, file "abc.py", line 48>:
              0 MAKE_CELL                0 (__class__)

 48           2 RESUME                   0
              4 LOAD_NAME                0 (__name__)
              6 STORE_NAME               1 (__module__)
              8 LOAD_CONST               0 ('abstractstaticmethod')
             10 STORE_NAME               2 (__qualname__)

 49          12 LOAD_CONST               1 ("A decorator indicating abstract staticmethods.\n\n    Deprecated, use 'staticmethod' with 'abstractmethod' instead:\n\n        class C(ABC):\n            @staticmethod\n            @abstractmethod\n            def my_abstract_staticmethod(...):\n                ...\n\n    ")
             14 STORE_NAME               3 (__doc__)

 61          16 LOAD_CONST               2 (True)
             18 STORE_NAME               4 (__isabstractmethod__)

 63          20 LOAD_CLOSURE             0 (__class__)
             22 BUILD_TUPLE              1
             24 LOAD_CONST               3 (<code object __init__ at 0x000001E77EC56C10, file "abc.py", line 63>)
             26 MAKE_FUNCTION            8 (closure)
             28 STORE_NAME               5 (__init__)
             30 LOAD_CLOSURE             0 (__class__)
             32 COPY                     1
             34 STORE_NAME               6 (__classcell__)
             36 RETURN_VALUE

Disassembly of <code object __init__ at 0x000001E77EC56C10, file "abc.py", line 63>:
              0 COPY_FREE_VARS           1

 63           2 RESUME                   0

 64           4 LOAD_CONST               1 (True)
              6 LOAD_FAST                1 (callable)
              8 STORE_ATTR               0 (__isabstractmethod__)

 65          18 LOAD_GLOBAL              3 (NULL + super)
             28 CACHE
             30 UNPACK_SEQUENCE          0
             34 CALL                     0
             42 CACHE
             44 STORE_SUBSCR
             48 CACHE
             50 CACHE
             52 CACHE
             54 CACHE
             56 CACHE
             58 CACHE
             60 CACHE
             62 CACHE
             64 CACHE
             66 LOAD_FAST                1 (callable)
             68 UNPACK_SEQUENCE          1
             72 CALL                     1
             80 CACHE
             82 POP_TOP
             84 LOAD_CONST               0 (None)
             86 RETURN_VALUE

Disassembly of <code object abstractproperty at 0x000001E77EC5D530, file "abc.py", line 68>:
 68           0 RESUME                   0
              2 LOAD_NAME                0 (__name__)
              4 STORE_NAME               1 (__module__)
              6 LOAD_CONST               0 ('abstractproperty')
              8 STORE_NAME               2 (__qualname__)

 69          10 LOAD_CONST               1 ("A decorator indicating abstract properties.\n\n    Deprecated, use 'property' with 'abstractmethod' instead:\n\n        class C(ABC):\n            @property\n            @abstractmethod\n            def my_abstract_property(self):\n                ...\n\n    ")
             12 STORE_NAME               3 (__doc__)

 81          14 LOAD_CONST               2 (True)
             16 STORE_NAME               4 (__isabstractmethod__)
             18 LOAD_CONST               3 (None)
             20 RETURN_VALUE

Disassembly of <code object ABCMeta at 0x000001E77EC46230, file "abc.py", line 92>:
              0 MAKE_CELL                0 (__class__)

 92           2 RESUME                   0
              4 LOAD_NAME                0 (__name__)
              6 STORE_NAME               1 (__module__)
              8 LOAD_CONST               0 ('ABCMeta')
             10 STORE_NAME               2 (__qualname__)

 93          12 LOAD_CONST               1 ("Metaclass for defining Abstract Base Classes (ABCs).\n\n        Use this metaclass to create an ABC.  An ABC can be subclassed\n        directly, and then acts as a mix-in class.  You can also register\n        unrelated concrete classes (even built-in classes) and unrelated\n        ABCs as 'virtual subclasses' -- these and their descendants will\n        be considered subclasses of the registering ABC by the built-in\n        issubclass() function, but the registering ABC won't show up in\n        their MRO (Method Resolution Order) nor will method\n        implementations defined by the registering ABC be callable (not\n        even via super()).\n        ")
             14 STORE_NAME               3 (__doc__)

105          16 LOAD_CLOSURE             0 (__class__)
             18 BUILD_TUPLE              1
             20 LOAD_CONST               2 (<code object __new__ at 0x000001E77EC56D30, file "abc.py", line 105>)
             22 MAKE_FUNCTION            8 (closure)
             24 STORE_NAME               4 (__new__)

110          26 LOAD_CONST               3 (<code object register at 0x000001E77EC37000, file "abc.py", line 110>)
             28 MAKE_FUNCTION            0
             30 STORE_NAME               5 (register)

117          32 LOAD_CONST               4 (<code object __instancecheck__ at 0x000001E77EC373C0, file "abc.py", line 117>)
             34 MAKE_FUNCTION            0
             36 STORE_NAME               6 (__instancecheck__)

121          38 LOAD_CONST               5 (<code object __subclasscheck__ at 0x000001E77EC37690, file "abc.py", line 121>)
             40 MAKE_FUNCTION            0
             42 STORE_NAME               7 (__subclasscheck__)

125          44 LOAD_CONST              10 ((None,))
             46 LOAD_CONST               7 (<code object _dump_registry at 0x000001E77C48B880, file "abc.py", line 125>)
             48 MAKE_FUNCTION            1 (defaults)
             50 STORE_NAME               8 (_dump_registry)

137          52 LOAD_CONST               8 (<code object _abc_registry_clear at 0x000001E77EC37780, file "abc.py", line 137>)
             54 MAKE_FUNCTION            0
             56 STORE_NAME               9 (_abc_registry_clear)

141          58 LOAD_CONST               9 (<code object _abc_caches_clear at 0x000001E77EC37870, file "abc.py", line 141>)
             60 MAKE_FUNCTION            0
             62 STORE_NAME              10 (_abc_caches_clear)
             64 LOAD_CLOSURE             0 (__class__)
             66 COPY                     1
             68 STORE_NAME              11 (__classcell__)
             70 RETURN_VALUE

Disassembly of <code object __new__ at 0x000001E77EC56D30, file "abc.py", line 105>:
              0 COPY_FREE_VARS           1

105           2 RESUME                   0

106           4 PUSH_NULL
              6 LOAD_GLOBAL              1 (NULL + super)
             16 CACHE
             18 UNPACK_SEQUENCE          0
             22 CALL                     0
             30 CACHE
             32 LOAD_ATTR                1 (NULL|self + super)
             52 BUILD_MAP                0
             54 LOAD_FAST                4 (kwargs)
             56 DICT_MERGE               1
             58 CALL_FUNCTION_EX         1
             60 STORE_FAST               5 (cls)

107          62 LOAD_GLOBAL              5 (NULL + _abc_init)
             72 CACHE
             74 LOAD_FAST                5 (cls)
             76 UNPACK_SEQUENCE          1
             80 CALL                     1
             88 CACHE
             90 POP_TOP

108          92 LOAD_FAST                5 (cls)
             94 RETURN_VALUE

Disassembly of <code object register at 0x000001E77EC37000, file "abc.py", line 110>:
110           0 RESUME                   0

115           2 LOAD_GLOBAL              1 (NULL + _abc_register)
             12 CACHE
             14 LOAD_FAST                0 (cls)
             16 LOAD_FAST                1 (subclass)
             18 UNPACK_SEQUENCE          2
             22 CALL                     2
             30 CACHE
             32 RETURN_VALUE

Disassembly of <code object __instancecheck__ at 0x000001E77EC373C0, file "abc.py", line 117>:
117           0 RESUME                   0

119           2 LOAD_GLOBAL              1 (NULL + _abc_instancecheck)
             12 CACHE
             14 LOAD_FAST                0 (cls)
             16 LOAD_FAST                1 (instance)
             18 UNPACK_SEQUENCE          2
             22 CALL                     2
             30 CACHE
             32 RETURN_VALUE

Disassembly of <code object __subclasscheck__ at 0x000001E77EC37690, file "abc.py", line 121>:
121           0 RESUME                   0

123           2 LOAD_GLOBAL              1 (NULL + _abc_subclasscheck)
             12 CACHE
             14 LOAD_FAST                0 (cls)
             16 LOAD_FAST                1 (subclass)
             18 UNPACK_SEQUENCE          2
             22 CALL                     2
             30 CACHE
             32 RETURN_VALUE

Disassembly of <code object _dump_registry at 0x000001E77C48B880, file "abc.py", line 125>:
125           0 RESUME                   0

127           2 LOAD_GLOBAL              1 (NULL + print)
             12 CACHE
             14 LOAD_CONST               1 ('Class: ')
             16 LOAD_FAST                0 (cls)
             18 LOAD_ATTR                1 (NULL|self + print)
             38 CACHE
             40 CACHE
             42 CACHE
             44 FORMAT_VALUE             0
             46 BUILD_STRING             4
             48 LOAD_FAST                1 (file)
             50 KW_NAMES                 3 (('file',))
             52 UNPACK_SEQUENCE          2
             56 CALL                     2
             64 CACHE
             66 POP_TOP

128          68 LOAD_GLOBAL              1 (NULL + print)
             78 CACHE
             80 LOAD_CONST               4 ('Inv. counter: ')
             82 LOAD_GLOBAL              7 (NULL + get_cache_token)
             92 CACHE
             94 UNPACK_SEQUENCE          0
             98 CALL                     0
            106 CACHE
            108 FORMAT_VALUE             0
            110 BUILD_STRING             2
            112 LOAD_FAST                1 (file)
            114 KW_NAMES                 3 (('file',))
            116 UNPACK_SEQUENCE          2
            120 CALL                     2
            128 CACHE
            130 POP_TOP

130         132 LOAD_GLOBAL              9 (NULL + _get_dump)
            142 CACHE
            144 LOAD_FAST                0 (cls)
            146 UNPACK_SEQUENCE          1
            150 CALL                     1
            158 CACHE

129         160 UNPACK_SEQUENCE          4
            164 STORE_FAST               2 (_abc_registry)
            166 STORE_FAST               3 (_abc_cache)
            168 STORE_FAST               4 (_abc_negative_cache)

130         170 STORE_FAST               5 (_abc_negative_cache_version)

131         172 LOAD_GLOBAL              1 (NULL + print)
            182 CACHE
            184 LOAD_CONST               5 ('_abc_registry: ')
            186 LOAD_FAST                2 (_abc_registry)
            188 FORMAT_VALUE             2 (repr)
            190 BUILD_STRING             2
            192 LOAD_FAST                1 (file)
            194 KW_NAMES                 3 (('file',))
            196 UNPACK_SEQUENCE          2
            200 CALL                     2
            208 CACHE
            210 POP_TOP

132         212 LOAD_GLOBAL              1 (NULL + print)
            222 CACHE
            224 LOAD_CONST               6 ('_abc_cache: ')
            226 LOAD_FAST                3 (_abc_cache)
            228 FORMAT_VALUE             2 (repr)
            230 BUILD_STRING             2
            232 LOAD_FAST                1 (file)
            234 KW_NAMES                 3 (('file',))
            236 UNPACK_SEQUENCE          2
            240 CALL                     2
            248 CACHE
            250 POP_TOP

133         252 LOAD_GLOBAL              1 (NULL + print)
            262 CACHE
            264 LOAD_CONST               7 ('_abc_negative_cache: ')
            266 LOAD_FAST                4 (_abc_negative_cache)
            268 FORMAT_VALUE             2 (repr)
            270 BUILD_STRING             2
            272 LOAD_FAST                1 (file)
            274 KW_NAMES                 3 (('file',))
            276 UNPACK_SEQUENCE          2
            280 CALL                     2
            288 CACHE
            290 POP_TOP

134         292 LOAD_GLOBAL              1 (NULL + print)
            302 CACHE
            304 LOAD_CONST               8 ('_abc_negative_cache_version: ')
            306 LOAD_FAST                5 (_abc_negative_cache_version)
            308 FORMAT_VALUE             2 (repr)
            310 BUILD_STRING             2

135         312 LOAD_FAST                1 (file)

134         314 KW_NAMES                 3 (('file',))
            316 UNPACK_SEQUENCE          2
            320 CALL                     2
            328 CACHE
            330 POP_TOP
            332 LOAD_CONST               9 (None)
            334 RETURN_VALUE

Disassembly of <code object _abc_registry_clear at 0x000001E77EC37780, file "abc.py", line 137>:
137           0 RESUME                   0

139           2 LOAD_GLOBAL              1 (NULL + _reset_registry)
             12 CACHE
             14 LOAD_FAST                0 (cls)
             16 UNPACK_SEQUENCE          1
             20 CALL                     1
             28 CACHE
             30 POP_TOP
             32 LOAD_CONST               1 (None)
             34 RETURN_VALUE

Disassembly of <code object _abc_caches_clear at 0x000001E77EC37870, file "abc.py", line 141>:
141           0 RESUME                   0

143           2 LOAD_GLOBAL              1 (NULL + _reset_caches)
             12 CACHE
             14 LOAD_FAST                0 (cls)
             16 UNPACK_SEQUENCE          1
             20 CALL                     1
             28 CACHE
             30 POP_TOP
             32 LOAD_CONST               1 (None)
             34 RETURN_VALUE

Disassembly of <code object update_abstractmethods at 0x000001E77E890B10, file "abc.py", line 146>:
146           0 RESUME                   0

162           2 LOAD_GLOBAL              1 (NULL + hasattr)
             12 CACHE
             14 LOAD_FAST                0 (cls)
             16 LOAD_CONST               1 ('__abstractmethods__')
             18 UNPACK_SEQUENCE          2
             22 CALL                     2
             30 CACHE
             32 POP_JUMP_IF_TRUE         2 (to 38)

166          34 LOAD_FAST                0 (cls)
             36 RETURN_VALUE

168     >>   38 LOAD_GLOBAL              3 (NULL + set)
             48 CACHE
             50 UNPACK_SEQUENCE          0
             54 CALL                     0
             62 CACHE
             64 STORE_FAST               1 (abstracts)

171          66 LOAD_FAST                0 (cls)
             68 LOAD_ATTR                2 (set)
             88 CACHE
             90 CACHE
             92 CACHE
             94 CACHE
             96 LOAD_FAST                2 (scls)
             98 LOAD_CONST               1 ('__abstractmethods__')
            100 LOAD_CONST               2 (())
            102 UNPACK_SEQUENCE          3
            106 CALL                     3
            114 CACHE
            116 GET_ITER
        >>  118 FOR_ITER                57 (to 236)

173         122 LOAD_GLOBAL              7 (NULL + getattr)
            132 CACHE
            134 LOAD_FAST                0 (cls)
            136 LOAD_FAST                3 (name)
            138 LOAD_CONST               3 (None)
            140 UNPACK_SEQUENCE          3
            144 CALL                     3
            152 CACHE
            154 STORE_FAST               4 (value)

174         156 LOAD_GLOBAL              7 (NULL + getattr)
            166 CACHE
            168 LOAD_FAST                4 (value)
            170 LOAD_CONST               4 ('__isabstractmethod__')
            172 LOAD_CONST               5 (False)
            174 UNPACK_SEQUENCE          3
            178 CALL                     3
            186 CACHE
            188 POP_JUMP_IF_FALSE       21 (to 232)

175         190 LOAD_FAST                1 (abstracts)
            192 STORE_SUBSCR
            196 CACHE
            198 CACHE
            200 CACHE
            202 CACHE
            204 CACHE
            206 CACHE
            208 CACHE
            210 CACHE
            212 CACHE
            214 LOAD_FAST                3 (name)
            216 UNPACK_SEQUENCE          1
            220 CALL                     1
            228 CACHE
            230 POP_TOP
        >>  232 JUMP_BACKWARD           58 (to 118)

172         234 JUMP_BACKWARD           78 (to 80)

177     >>  236 LOAD_FAST                0 (cls)
            238 LOAD_ATTR                5 (NULL|self + __bases__)
            258 CACHE
            260 CACHE
            262 CACHE
            264 CACHE
            266 CACHE
            268 CACHE
            270 UNPACK_SEQUENCE          0
            274 CALL                     0
            282 CACHE
            284 GET_ITER
        >>  286 FOR_ITER                43 (to 376)
            290 CACHE
            292 STORE_FAST               3 (name)
            294 STORE_FAST               4 (value)

178         296 LOAD_GLOBAL              7 (NULL + getattr)
            306 CACHE
            308 LOAD_FAST                4 (value)
            310 LOAD_CONST               4 ('__isabstractmethod__')
            312 LOAD_CONST               5 (False)
            314 UNPACK_SEQUENCE          3
            318 CALL                     3
            326 CACHE
            328 POP_JUMP_IF_FALSE       21 (to 372)

179         330 LOAD_FAST                1 (abstracts)
            332 STORE_SUBSCR
            336 CACHE
            338 CACHE
            340 CACHE
            342 CACHE
            344 CACHE
            346 CACHE
            348 CACHE
            350 CACHE
            352 CACHE
            354 LOAD_FAST                3 (name)
            356 UNPACK_SEQUENCE          1
            360 CALL                     1
            368 CACHE
            370 POP_TOP
        >>  372 JUMP_BACKWARD           44 (to 286)

180         374 LOAD_GLOBAL             15 (NULL + frozenset)
            384 CACHE
            386 LOAD_FAST                1 (abstracts)
            388 UNPACK_SEQUENCE          1
            392 CALL                     1
            400 CACHE
            402 LOAD_FAST                0 (cls)
            404 STORE_ATTR               8 (__abstractmethods__)

181         414 LOAD_FAST                0 (cls)
            416 RETURN_VALUE

Disassembly of <code object ABC at 0x000001E77EC5D610, file "abc.py", line 184>:
184           0 RESUME                   0
              2 LOAD_NAME                0 (__name__)
              4 STORE_NAME               1 (__module__)
              6 LOAD_CONST               0 ('ABC')
              8 STORE_NAME               2 (__qualname__)

185          10 LOAD_CONST               1 ('Helper class that provides a standard way to create an ABC using\n    inheritance.\n    ')
             12 STORE_NAME               3 (__doc__)

188          14 LOAD_CONST               2 (())
             16 STORE_NAME               4 (__slots__)
             18 LOAD_CONST               3 (None)
             20 RETURN_VALUE


# CONSTANTS:
==================================================
# 0: 'Abstract Base Classes (ABCs) according to PEP 3119.'
# 1: <code object abstractmethod from abc.py>
# 2: <code object abstractclassmethod from abc.py>
# 3: 'abstractclassmethod'
# 4: <code object abstractstaticmethod from abc.py>
# 5: 'abstractstaticmethod'
# 6: <code object abstractproperty from abc.py>
# 7: 'abstractproperty'
# 8: 0
# 9: tuple - ('get_cache_token', '_abc_init', '_abc_register', '_abc_instancecheck', '_abc_subclasscheck', '_get_
# 10: <code object ABCMeta from abc.py>
# 11: 'ABCMeta'
# 12: tuple - ('ABCMeta', 'get_cache_token')
# 13: 'abc'
# 14: <code object update_abstractmethods from abc.py>
# 15: <code object ABC from abc.py>
# 16: 'ABC'
# 17: tuple - ('metaclass',)
# 18: None


# NAMES (global/attribute references):
==================================================
# 0: '__doc__'
# 1: 'abstractmethod'
# 2: 'classmethod'
# 3: 'abstractclassmethod'
# 4: 'staticmethod'
# 5: 'abstractstaticmethod'
# 6: 'property'
# 7: 'abstractproperty'
# 8: '_abc'
# 9: 'get_cache_token'
# 10: '_abc_init'
# 11: '_abc_register'
# 12: '_abc_instancecheck'
# 13: '_abc_subclasscheck'
# 14: '_get_dump'
# 15: '_reset_registry'
# 16: '_reset_caches'
# 17: 'type'
# 18: 'ABCMeta'
# 19: 'ImportError'
# 20: '_py_abc'
# 21: '__module__'
# 22: 'update_abstractmethods'
# 23: 'ABC'


# VARIABLE NAMES (local variables):
==================================================


# FREE VARIABLES:
==================================================
