# Code object from position 8441092
# Filename: configparser.py
# Name: <module>
# Args: 0
# Locals: 0
# Stack size: 5
# Flags: 0

   0           0 RESUME                   0

   1           2 LOAD_CONST               0 ('Configuration file parser.\n\nA configuration file consists of sections, lead by a "[section]" header,\nand followed by "name: value" entries, with continuations and such in\nthe style of RFC 822.\n\nIntrinsic defaults can be specified by passing them into the\nConfigParser constructor as a dictionary.\n\nclass:\n\nConfigParser -- responsible for parsing a list of\n                    configuration files, and managing the parsed database.\n\n    methods:\n\n    __init__(defaults=None, dict_type=_default_dict, allow_no_value=False,\n             delimiters=(\'=\', \':\'), comment_prefixes=(\'#\', \';\'),\n             inline_comment_prefixes=None, strict=True,\n             empty_lines_in_values=True, default_section=\'DEFAULT\',\n             interpolation=<unset>, converters=<unset>):\n\n        Create the parser. When `defaults` is given, it is initialized into the\n        dictionary or intrinsic defaults. The keys must be strings, the values\n        must be appropriate for %()s string interpolation.\n\n        When `dict_type` is given, it will be used to create the dictionary\n        objects for the list of sections, for the options within a section, and\n        for the default values.\n\n        When `delimiters` is given, it will be used as the set of substrings\n        that divide keys from values.\n\n        When `comment_prefixes` is given, it will be used as the set of\n        substrings that prefix comments in empty lines. Comments can be\n        indented.\n\n        When `inline_comment_prefixes` is given, it will be used as the set of\n        substrings that prefix comments in non-empty lines.\n\n        When `strict` is True, the parser won\'t allow for any section or option\n        duplicates while reading from a single source (file, string or\n        dictionary). Default is True.\n\n        When `empty_lines_in_values` is False (default: True), each empty line\n        marks the end of an option. Otherwise, internal empty lines of\n        a multiline option are kept as part of the value.\n\n        When `allow_no_value` is True (default: False), options without\n        values are accepted; the value presented for these is None.\n\n        When `default_section` is given, the name of the special section is\n        named accordingly. By default it is called ``"DEFAULT"`` but this can\n        be customized to point to any other valid section name. Its current\n        value can be retrieved using the ``parser_instance.default_section``\n        attribute and may be modified at runtime.\n\n        When `interpolation` is given, it should be an Interpolation subclass\n        instance. It will be used as the handler for option value\n        pre-processing when using getters. RawConfigParser objects don\'t do\n        any sort of interpolation, whereas ConfigParser uses an instance of\n        BasicInterpolation. The library also provides a ``zc.buildout``\n        inspired ExtendedInterpolation implementation.\n\n        When `converters` is given, it should be a dictionary where each key\n        represents the name of a type converter and each value is a callable\n        implementing the conversion from string to the desired datatype. Every\n        converter gets its corresponding get*() method on the parser object and\n        section proxies.\n\n    sections()\n        Return all the configuration section names, sans DEFAULT.\n\n    has_section(section)\n        Return whether the given section exists.\n\n    has_option(section, option)\n        Return whether the given option exists in the given section.\n\n    options(section)\n        Return list of configuration options for the named section.\n\n    read(filenames, encoding=None)\n        Read and parse the iterable of named configuration files, given by\n        name.  A single filename is also allowed.  Non-existing files\n        are ignored.  Return list of successfully read files.\n\n    read_file(f, filename=None)\n        Read and parse one configuration file, given as a file object.\n        The filename defaults to f.name; it is only used in error\n        messages (if f has no `name` attribute, the string `<???>` is used).\n\n    read_string(string)\n        Read configuration from a given string.\n\n    read_dict(dictionary)\n        Read configuration from a dictionary. Keys are section names,\n        values are dictionaries with keys and values that should be present\n        in the section. If the used dictionary type preserves order, sections\n        and their keys will be added in order. Values are automatically\n        converted to strings.\n\n    get(section, option, raw=False, vars=None, fallback=_UNSET)\n        Return a string value for the named option.  All % interpolations are\n        expanded in the return values, based on the defaults passed into the\n        constructor and the DEFAULT section.  Additional substitutions may be\n        provided using the `vars` argument, which must be a dictionary whose\n        contents override any pre-existing defaults. If `option` is a key in\n        `vars`, the value from `vars` is used.\n\n    getint(section, options, raw=False, vars=None, fallback=_UNSET)\n        Like get(), but convert value to an integer.\n\n    getfloat(section, options, raw=False, vars=None, fallback=_UNSET)\n        Like get(), but convert value to a float.\n\n    getboolean(section, options, raw=False, vars=None, fallback=_UNSET)\n        Like get(), but convert value to a boolean (currently case\n        insensitively defined as 0, false, no, off for False, and 1, true,\n        yes, on for True).  Returns False or True.\n\n    items(section=_UNSET, raw=False, vars=None)\n        If section is given, return a list of tuples with (name, value) for\n        each option in the section. Otherwise, return a list of tuples with\n        (section_name, section_proxy) for each section, including DEFAULTSECT.\n\n    remove_section(section)\n        Remove the given file section and all its options.\n\n    remove_option(section, option)\n        Remove the given option from the given section.\n\n    set(section, option, value)\n        Set the given option.\n\n    write(fp, space_around_delimiters=True)\n        Write the configuration state in .ini format. If\n        `space_around_delimiters` is True (the default), delimiters\n        between keys and values are surrounded by spaces.\n')
               4 STORE_NAME               0 (__doc__)

 142           6 LOAD_CONST               1 (0)
               8 LOAD_CONST               2 (('MutableMapping',))
              10 IMPORT_NAME              1 (collections.abc)
              12 IMPORT_FROM              2 (MutableMapping)
              14 STORE_NAME               2 (MutableMapping)
              16 POP_TOP

 143          18 LOAD_CONST               1 (0)
              20 LOAD_CONST               3 (('ChainMap',))
              22 IMPORT_NAME              3 (collections)
              24 IMPORT_FROM              4 (ChainMap)
              26 STORE_NAME               5 (_ChainMap)
              28 POP_TOP

 144          30 LOAD_CONST               1 (0)
              32 LOAD_CONST               4 (None)
              34 IMPORT_NAME              6 (functools)
              36 STORE_NAME               6 (functools)

 145          38 LOAD_CONST               1 (0)
              40 LOAD_CONST               4 (None)
              42 IMPORT_NAME              7 (io)
              44 STORE_NAME               7 (io)

 146          46 LOAD_CONST               1 (0)
              48 LOAD_CONST               4 (None)
              50 IMPORT_NAME              8 (itertools)
              52 STORE_NAME               8 (itertools)

 147          54 LOAD_CONST               1 (0)
              56 LOAD_CONST               4 (None)
              58 IMPORT_NAME              9 (os)
              60 STORE_NAME               9 (os)

 148          62 LOAD_CONST               1 (0)
              64 LOAD_CONST               4 (None)
              66 IMPORT_NAME             10 (re)
              68 STORE_NAME              10 (re)

 149          70 LOAD_CONST               1 (0)
              72 LOAD_CONST               4 (None)
              74 IMPORT_NAME             11 (sys)
              76 STORE_NAME              11 (sys)

 150          78 LOAD_CONST               1 (0)
              80 LOAD_CONST               4 (None)
              82 IMPORT_NAME             12 (warnings)
              84 STORE_NAME              12 (warnings)

 152          86 BUILD_LIST               0
              88 LOAD_CONST               5 (('NoSectionError', 'DuplicateOptionError', 'DuplicateSectionError', 'NoOptionError', 'InterpolationError', 'InterpolationDepthError', 'InterpolationMissingOptionError', 'InterpolationSyntaxError', 'ParsingError', 'MissingSectionHeaderError', 'ConfigParser', 'SafeConfigParser', 'RawConfigParser', 'Interpolation', 'BasicInterpolation', 'ExtendedInterpolation', 'LegacyInterpolation', 'SectionProxy', 'ConverterMapping', 'DEFAULTSECT', 'MAX_INTERPOLATION_DEPTH'))
              90 LIST_EXTEND              1
              92 STORE_NAME              13 (__all__)

 161          94 LOAD_NAME               14 (dict)
              96 STORE_NAME              15 (_default_dict)

 162          98 LOAD_CONST               6 ('DEFAULT')
             100 STORE_NAME              16 (DEFAULTSECT)

 164         102 LOAD_CONST               7 (10)
             104 STORE_NAME              17 (MAX_INTERPOLATION_DEPTH)

 169         106 PUSH_NULL
             108 LOAD_BUILD_CLASS
             110 LOAD_CONST               8 (<code object Error at 0x000001B2A767E970, file "configparser.py", line 169>)
             112 MAKE_FUNCTION            0
             114 LOAD_CONST               9 ('Error')
             116 LOAD_NAME               18 (Exception)
             118 UNPACK_SEQUENCE          3
             122 CALL                     3
             130 CACHE
             132 STORE_NAME              19 (Error)

 182         134 PUSH_NULL
             136 LOAD_BUILD_CLASS
             138 LOAD_CONST              10 (<code object NoSectionError at 0x000001B2A7688FF0, file "configparser.py", line 182>)
             140 MAKE_FUNCTION            0
             142 LOAD_CONST              11 ('NoSectionError')
             144 LOAD_NAME               19 (Error)
             146 UNPACK_SEQUENCE          3
             150 CALL                     3
             158 CACHE
             160 STORE_NAME              20 (NoSectionError)

 191         162 PUSH_NULL
             164 LOAD_BUILD_CLASS
             166 LOAD_CONST              12 (<code object DuplicateSectionError at 0x000001B2A7688C70, file "configparser.py", line 191>)
             168 MAKE_FUNCTION            0
             170 LOAD_CONST              13 ('DuplicateSectionError')
             172 LOAD_NAME               19 (Error)
             174 UNPACK_SEQUENCE          3
             178 CALL                     3
             186 CACHE
             188 STORE_NAME              21 (DuplicateSectionError)

 217         190 PUSH_NULL
             192 LOAD_BUILD_CLASS
             194 LOAD_CONST              14 (<code object DuplicateOptionError at 0x000001B2A76891B0, file "configparser.py", line 217>)
             196 MAKE_FUNCTION            0
             198 LOAD_CONST              15 ('DuplicateOptionError')
             200 LOAD_NAME               19 (Error)
             202 UNPACK_SEQUENCE          3
             206 CALL                     3
             214 CACHE
             216 STORE_NAME              22 (DuplicateOptionError)

 244         218 PUSH_NULL
             220 LOAD_BUILD_CLASS
             222 LOAD_CONST              16 (<code object NoOptionError at 0x000001B2A7688490, file "configparser.py", line 244>)
             224 MAKE_FUNCTION            0
             226 LOAD_CONST              17 ('NoOptionError')
             228 LOAD_NAME               19 (Error)
             230 UNPACK_SEQUENCE          3
             234 CALL                     3
             242 CACHE
             244 STORE_NAME              23 (NoOptionError)

 255         246 PUSH_NULL
             248 LOAD_BUILD_CLASS
             250 LOAD_CONST              18 (<code object InterpolationError at 0x000001B2A768A090, file "configparser.py", line 255>)
             252 MAKE_FUNCTION            0
             254 LOAD_CONST              19 ('InterpolationError')
             256 LOAD_NAME               19 (Error)
             258 UNPACK_SEQUENCE          3
             262 CALL                     3
             270 CACHE
             272 STORE_NAME              24 (InterpolationError)

 265         274 PUSH_NULL
             276 LOAD_BUILD_CLASS
             278 LOAD_CONST              20 (<code object InterpolationMissingOptionError at 0x000001B2A7688D50, file "configparser.py", line 265>)
             280 MAKE_FUNCTION            0
             282 LOAD_CONST              21 ('InterpolationMissingOptionError')
             284 LOAD_NAME               24 (InterpolationError)
             286 UNPACK_SEQUENCE          3
             290 CALL                     3
             298 CACHE
             300 STORE_NAME              25 (InterpolationMissingOptionError)

 277         302 PUSH_NULL
             304 LOAD_BUILD_CLASS
             306 LOAD_CONST              22 (<code object InterpolationSyntaxError at 0x000001B2A76888F0, file "configparser.py", line 277>)
             308 MAKE_FUNCTION            0
             310 LOAD_CONST              23 ('InterpolationSyntaxError')
             312 LOAD_NAME               24 (InterpolationError)
             314 UNPACK_SEQUENCE          3
             318 CALL                     3
             326 CACHE
             328 STORE_NAME              26 (InterpolationSyntaxError)

 285         330 PUSH_NULL
             332 LOAD_BUILD_CLASS
             334 LOAD_CONST              24 (<code object InterpolationDepthError at 0x000001B2A7689290, file "configparser.py", line 285>)
             336 MAKE_FUNCTION            0
             338 LOAD_CONST              25 ('InterpolationDepthError')
             340 LOAD_NAME               24 (InterpolationError)
             342 UNPACK_SEQUENCE          3
             346 CALL                     3
             354 CACHE
             356 STORE_NAME              27 (InterpolationDepthError)

 298         358 PUSH_NULL
             360 LOAD_BUILD_CLASS
             362 LOAD_CONST              26 (<code object ParsingError at 0x000001B2A76D9110, file "configparser.py", line 298>)
             364 MAKE_FUNCTION            0
             366 LOAD_CONST              27 ('ParsingError')
             368 LOAD_NAME               19 (Error)
             370 UNPACK_SEQUENCE          3
             374 CALL                     3
             382 CACHE
             384 STORE_NAME              28 (ParsingError)

 341         386 PUSH_NULL
             388 LOAD_BUILD_CLASS
             390 LOAD_CONST              28 (<code object MissingSectionHeaderError at 0x000001B2A7689DF0, file "configparser.py", line 341>)
             392 MAKE_FUNCTION            0
             394 LOAD_CONST              29 ('MissingSectionHeaderError')
             396 LOAD_NAME               28 (ParsingError)
             398 UNPACK_SEQUENCE          3
             402 CALL                     3
             410 CACHE
             412 STORE_NAME              29 (MissingSectionHeaderError)

 358         414 PUSH_NULL
             416 LOAD_NAME               30 (object)
             418 UNPACK_SEQUENCE          0
             422 CALL                     0
             430 CACHE
             432 STORE_NAME              31 (_UNSET)

 361         434 PUSH_NULL
             436 LOAD_BUILD_CLASS
             438 LOAD_CONST              30 (<code object Interpolation at 0x000001B2A767EC40, file "configparser.py", line 361>)
             440 MAKE_FUNCTION            0
             442 LOAD_CONST              31 ('Interpolation')
             444 UNPACK_SEQUENCE          2
             448 CALL                     2
             456 CACHE
             458 STORE_NAME              32 (Interpolation)

 377         460 PUSH_NULL
             462 LOAD_BUILD_CLASS
             464 LOAD_CONST              32 (<code object BasicInterpolation at 0x000001B2A76ADDF0, file "configparser.py", line 377>)
             466 MAKE_FUNCTION            0
             468 LOAD_CONST              33 ('BasicInterpolation')
             470 LOAD_NAME               32 (Interpolation)
             472 UNPACK_SEQUENCE          3
             476 CALL                     3
             484 CACHE
             486 STORE_NAME              33 (BasicInterpolation)

 449         488 PUSH_NULL
             490 LOAD_BUILD_CLASS
             492 LOAD_CONST              34 (<code object ExtendedInterpolation at 0x000001B2A76ADF00, file "configparser.py", line 449>)
             494 MAKE_FUNCTION            0
             496 LOAD_CONST              35 ('ExtendedInterpolation')
             498 LOAD_NAME               32 (Interpolation)
             500 UNPACK_SEQUENCE          3
             504 CALL                     3
             512 CACHE
             514 STORE_NAME              34 (ExtendedInterpolation)

 523         516 PUSH_NULL
             518 LOAD_BUILD_CLASS
             520 LOAD_CONST              36 (<code object LegacyInterpolation at 0x000001B2A725E9C0, file "configparser.py", line 523>)
             522 MAKE_FUNCTION            0
             524 LOAD_CONST              37 ('LegacyInterpolation')
             526 LOAD_NAME               32 (Interpolation)
             528 UNPACK_SEQUENCE          3
             532 CALL                     3
             540 CACHE
             542 STORE_NAME              35 (LegacyInterpolation)

 570         544 PUSH_NULL
             546 LOAD_BUILD_CLASS
             548 LOAD_CONST              38 (<code object RawConfigParser at 0x000001B2A5061C90, file "configparser.py", line 570>)
             550 MAKE_FUNCTION            0
             552 LOAD_CONST              39 ('RawConfigParser')
             554 LOAD_NAME                2 (MutableMapping)
             556 UNPACK_SEQUENCE          3
             560 CALL                     3
             568 CACHE
             570 STORE_NAME              36 (RawConfigParser)

1213         572 PUSH_NULL
             574 LOAD_BUILD_CLASS
             576 LOAD_CONST              40 (<code object ConfigParser at 0x000001B2A76ADAC0, file "configparser.py", line 1213>)
             578 MAKE_FUNCTION            0
             580 LOAD_CONST              41 ('ConfigParser')
             582 LOAD_NAME               36 (RawConfigParser)
             584 UNPACK_SEQUENCE          3
             588 CALL                     3
             596 CACHE
             598 STORE_NAME              37 (ConfigParser)

1245         600 PUSH_NULL
             602 LOAD_BUILD_CLASS
             604 LOAD_CONST              42 (<code object SafeConfigParser at 0x000001B2A767D3E0, file "configparser.py", line 1245>)
             606 MAKE_FUNCTION            0
             608 LOAD_CONST              43 ('SafeConfigParser')
             610 LOAD_NAME               37 (ConfigParser)
             612 UNPACK_SEQUENCE          3
             616 CALL                     3
             624 CACHE
             626 STORE_NAME              38 (SafeConfigParser)

1258         628 PUSH_NULL
             630 LOAD_BUILD_CLASS
             632 LOAD_CONST              44 (<code object SectionProxy at 0x000001B2A71C78A0, file "configparser.py", line 1258>)
             634 MAKE_FUNCTION            0
             636 LOAD_CONST              45 ('SectionProxy')
             638 LOAD_NAME                2 (MutableMapping)
             640 UNPACK_SEQUENCE          3
             644 CALL                     3
             652 CACHE
             654 STORE_NAME              39 (SectionProxy)

1328         656 PUSH_NULL
             658 LOAD_BUILD_CLASS
             660 LOAD_CONST              46 (<code object ConverterMapping at 0x000001B2A76D9E90, file "configparser.py", line 1328>)
             662 MAKE_FUNCTION            0
             664 LOAD_CONST              47 ('ConverterMapping')
             666 LOAD_NAME                2 (MutableMapping)
             668 UNPACK_SEQUENCE          3
             672 CALL                     3
             680 CACHE
             682 STORE_NAME              40 (ConverterMapping)
             684 LOAD_CONST               4 (None)
             686 RETURN_VALUE

Disassembly of <code object Error at 0x000001B2A767E970, file "configparser.py", line 169>:
169           0 RESUME                   0
              2 LOAD_NAME                0 (__name__)
              4 STORE_NAME               1 (__module__)
              6 LOAD_CONST               0 ('Error')
              8 STORE_NAME               2 (__qualname__)

170          10 LOAD_CONST               1 ('Base class for ConfigParser exceptions.')
             12 STORE_NAME               3 (__doc__)

172          14 LOAD_CONST               6 (('',))
             16 LOAD_CONST               3 (<code object __init__ at 0x000001B2A76AD680, file "configparser.py", line 172>)
             18 MAKE_FUNCTION            1 (defaults)
             20 STORE_NAME               4 (__init__)

176          22 LOAD_CONST               4 (<code object __repr__ at 0x000001B2A7679550, file "configparser.py", line 176>)
             24 MAKE_FUNCTION            0
             26 STORE_NAME               5 (__repr__)

179          28 LOAD_NAME                5 (__repr__)
             30 STORE_NAME               6 (__str__)
             32 LOAD_CONST               5 (None)
             34 RETURN_VALUE

Disassembly of <code object __init__ at 0x000001B2A76AD680, file "configparser.py", line 172>:
172           0 RESUME                   0

173           2 LOAD_FAST                1 (msg)
              4 LOAD_FAST                0 (self)
              6 STORE_ATTR               0 (message)

174          16 LOAD_GLOBAL              2 (Exception)
             26 CACHE
             28 STORE_SUBSCR
             32 CACHE
             34 CACHE
             36 CACHE
             38 CACHE
             40 CACHE
             42 CACHE
             44 CACHE
             46 CACHE
             48 CACHE
             50 LOAD_FAST                0 (self)
             52 LOAD_FAST                1 (msg)
             54 UNPACK_SEQUENCE          2
             58 CALL                     2
             66 CACHE
             68 POP_TOP
             70 LOAD_CONST               0 (None)
             72 RETURN_VALUE

Disassembly of <code object __repr__ at 0x000001B2A7679550, file "configparser.py", line 176>:
176           0 RESUME                   0

177           2 LOAD_FAST                0 (self)
              4 LOAD_ATTR                0 (message)

Disassembly of <code object NoSectionError at 0x000001B2A7688FF0, file "configparser.py", line 182>:
182           0 RESUME                   0
              2 LOAD_NAME                0 (__name__)
              4 STORE_NAME               1 (__module__)
              6 LOAD_CONST               0 ('NoSectionError')
              8 STORE_NAME               2 (__qualname__)

183          10 LOAD_CONST               1 ('Raised when no section matches a requested option.')
             12 STORE_NAME               3 (__doc__)

185          14 LOAD_CONST               2 (<code object __init__ at 0x000001B2A76D8810, file "configparser.py", line 185>)
             16 MAKE_FUNCTION            0
             18 STORE_NAME               4 (__init__)
             20 LOAD_CONST               3 (None)
             22 RETURN_VALUE

Disassembly of <code object __init__ at 0x000001B2A76D8810, file "configparser.py", line 185>:
185           0 RESUME                   0

186           2 LOAD_GLOBAL              0 (Error)
             12 CACHE
             14 STORE_SUBSCR
             18 CACHE
             20 CACHE
             22 CACHE
             24 CACHE
             26 CACHE
             28 CACHE
             30 CACHE
             32 CACHE
             34 CACHE
             36 LOAD_FAST                0 (self)
             38 LOAD_CONST               1 ('No section: ')
             40 LOAD_FAST                1 (section)
             42 FORMAT_VALUE             2 (repr)
             44 BUILD_STRING             2
             46 UNPACK_SEQUENCE          2
             50 CALL                     2
             58 CACHE
             60 POP_TOP

187          62 LOAD_FAST                1 (section)
             64 LOAD_FAST                0 (self)
             66 STORE_ATTR               2 (section)

188          76 LOAD_FAST                1 (section)
             78 BUILD_TUPLE              1
             80 LOAD_FAST                0 (self)
             82 STORE_ATTR               3 (args)
             92 LOAD_CONST               0 (None)
             94 RETURN_VALUE

Disassembly of <code object DuplicateSectionError at 0x000001B2A7688C70, file "configparser.py", line 191>:
191           0 RESUME                   0
              2 LOAD_NAME                0 (__name__)
              4 STORE_NAME               1 (__module__)
              6 LOAD_CONST               0 ('DuplicateSectionError')
              8 STORE_NAME               2 (__qualname__)

192          10 LOAD_CONST               1 ('Raised when a section is repeated in an input source.\n\n    Possible repetitions that raise this exception are: multiple creation\n    using the API or in strict parsers when a section is found more than once\n    in a single input file, string or dictionary.\n    ')
             12 STORE_NAME               3 (__doc__)

199          14 LOAD_CONST               4 ((None, None))
             16 LOAD_CONST               3 (<code object __init__ at 0x000001B2A507FBB0, file "configparser.py", line 199>)
             18 MAKE_FUNCTION            1 (defaults)
             20 STORE_NAME               4 (__init__)
             22 LOAD_CONST               2 (None)
             24 RETURN_VALUE

Disassembly of <code object __init__ at 0x000001B2A507FBB0, file "configparser.py", line 199>:
199           0 RESUME                   0

200           2 LOAD_GLOBAL              1 (NULL + repr)
             12 CACHE
             14 LOAD_FAST                1 (section)
             16 UNPACK_SEQUENCE          1
             20 CALL                     1
             28 CACHE
             30 LOAD_CONST               1 (' already exists')
             32 BUILD_LIST               2
             34 STORE_FAST               4 (msg)

201          36 LOAD_FAST                2 (source)
             38 POP_JUMP_IF_NONE       104 (to 248)

202          40 LOAD_CONST               2 ('While reading from ')
             42 LOAD_GLOBAL              1 (NULL + repr)
             52 CACHE
             54 LOAD_FAST                2 (source)
             56 UNPACK_SEQUENCE          1
             60 CALL                     1
             68 CACHE
             70 BUILD_LIST               2
             72 STORE_FAST               5 (message)

203          74 LOAD_FAST                3 (lineno)
             76 POP_JUMP_IF_NONE        40 (to 158)

204          78 LOAD_FAST                5 (message)
             80 STORE_SUBSCR
             84 CACHE
             86 CACHE
             88 CACHE
             90 CACHE
             92 CACHE
             94 CACHE
             96 CACHE
             98 CACHE
            100 CACHE
            102 LOAD_CONST               3 (' [line {0:2d}]')
            104 STORE_SUBSCR
            108 CACHE
            110 CACHE
            112 CACHE
            114 CACHE
            116 CACHE
            118 CACHE
            120 CACHE
            122 CACHE
            124 CACHE
            126 LOAD_FAST                3 (lineno)
            128 UNPACK_SEQUENCE          1
            132 CALL                     1
            140 CACHE
            142 UNPACK_SEQUENCE          1
            146 CALL                     1
            154 CACHE
            156 POP_TOP

205     >>  158 LOAD_FAST                5 (message)
            160 STORE_SUBSCR
            164 CACHE
            166 CACHE
            168 CACHE
            170 CACHE
            172 CACHE
            174 CACHE
            176 CACHE
            178 CACHE
            180 CACHE
            182 LOAD_CONST               4 (': section ')
            184 UNPACK_SEQUENCE          1
            188 CALL                     1
            196 CACHE
            198 POP_TOP

206         200 LOAD_FAST                5 (message)
            202 STORE_SUBSCR
            206 CACHE
            208 CACHE
            210 CACHE
            212 CACHE
            214 CACHE
            216 CACHE
            218 CACHE
            220 CACHE
            222 CACHE
            224 LOAD_FAST                4 (msg)
            226 UNPACK_SEQUENCE          1
            230 CALL                     1
            238 CACHE
            240 POP_TOP

207         242 LOAD_FAST                5 (message)
            244 STORE_FAST               4 (msg)
            246 JUMP_FORWARD            22 (to 292)

209     >>  248 LOAD_FAST                4 (msg)
            250 STORE_SUBSCR
            254 CACHE
            256 CACHE
            258 CACHE
            260 CACHE
            262 CACHE
            264 CACHE
            266 CACHE
            268 CACHE
            270 CACHE
            272 LOAD_CONST               5 (0)
            274 LOAD_CONST               6 ('Section ')
            276 UNPACK_SEQUENCE          2
            280 CALL                     2
            288 CACHE
            290 POP_TOP

210     >>  292 LOAD_GLOBAL             10 (Error)
            302 CACHE
            304 STORE_SUBSCR
            308 CACHE
            310 CACHE
            312 CACHE
            314 CACHE
            316 CACHE
            318 CACHE
            320 CACHE
            322 CACHE
            324 CACHE
            326 LOAD_FAST                0 (self)
            328 LOAD_CONST               7 ('')
            330 STORE_SUBSCR
            334 CACHE
            336 CACHE
            338 CACHE
            340 CACHE
            342 CACHE
            344 CACHE
            346 CACHE
            348 CACHE
            350 CACHE
            352 LOAD_FAST                4 (msg)
            354 UNPACK_SEQUENCE          1
            358 CALL                     1
            366 CACHE
            368 UNPACK_SEQUENCE          2
            372 CALL                     2
            380 CACHE
            382 POP_TOP

211         384 LOAD_FAST                1 (section)
            386 LOAD_FAST                0 (self)
            388 STORE_ATTR               8 (section)

212         398 LOAD_FAST                2 (source)
            400 LOAD_FAST                0 (self)
            402 STORE_ATTR               9 (source)

213         412 LOAD_FAST                3 (lineno)
            414 LOAD_FAST                0 (self)
            416 STORE_ATTR              10 (lineno)

214         426 LOAD_FAST                1 (section)
            428 LOAD_FAST                2 (source)
            430 LOAD_FAST                3 (lineno)
            432 BUILD_TUPLE              3
            434 LOAD_FAST                0 (self)
            436 STORE_ATTR              11 (args)
            446 LOAD_CONST               0 (None)
            448 RETURN_VALUE

Disassembly of <code object DuplicateOptionError at 0x000001B2A76891B0, file "configparser.py", line 217>:
217           0 RESUME                   0
              2 LOAD_NAME                0 (__name__)
              4 STORE_NAME               1 (__module__)
              6 LOAD_CONST               0 ('DuplicateOptionError')
              8 STORE_NAME               2 (__qualname__)

218          10 LOAD_CONST               1 ('Raised by strict parsers when an option is repeated in an input source.\n\n    Current implementation raises this exception only when an option is found\n    more than once in a single file, string or dictionary.\n    ')
             12 STORE_NAME               3 (__doc__)

224          14 LOAD_CONST               4 ((None, None))
             16 LOAD_CONST               3 (<code object __init__ at 0x000001B2A507FE50, file "configparser.py", line 224>)
             18 MAKE_FUNCTION            1 (defaults)
             20 STORE_NAME               4 (__init__)
             22 LOAD_CONST               2 (None)
             24 RETURN_VALUE

Disassembly of <code object __init__ at 0x000001B2A507FE50, file "configparser.py", line 224>:
224           0 RESUME                   0

225           2 LOAD_GLOBAL              1 (NULL + repr)
             12 CACHE
             14 LOAD_FAST                2 (option)
             16 UNPACK_SEQUENCE          1
             20 CALL                     1
             28 CACHE
             30 LOAD_CONST               1 (' in section ')
             32 LOAD_GLOBAL              1 (NULL + repr)
             42 CACHE
             44 LOAD_FAST                1 (section)
             46 UNPACK_SEQUENCE          1
             50 CALL                     1
             58 CACHE

226          60 LOAD_CONST               2 (' already exists')

225          62 BUILD_LIST               4
             64 STORE_FAST               5 (msg)

227          66 LOAD_FAST                3 (source)
             68 POP_JUMP_IF_NONE       104 (to 278)

228          70 LOAD_CONST               3 ('While reading from ')
             72 LOAD_GLOBAL              1 (NULL + repr)
             82 CACHE
             84 LOAD_FAST                3 (source)
             86 UNPACK_SEQUENCE          1
             90 CALL                     1
             98 CACHE
            100 BUILD_LIST               2
            102 STORE_FAST               6 (message)

229         104 LOAD_FAST                4 (lineno)
            106 POP_JUMP_IF_NONE        40 (to 188)

230         108 LOAD_FAST                6 (message)
            110 STORE_SUBSCR
            114 CACHE
            116 CACHE
            118 CACHE
            120 CACHE
            122 CACHE
            124 CACHE
            126 CACHE
            128 CACHE
            130 CACHE
            132 LOAD_CONST               4 (' [line {0:2d}]')
            134 STORE_SUBSCR
            138 CACHE
            140 CACHE
            142 CACHE
            144 CACHE
            146 CACHE
            148 CACHE
            150 CACHE
            152 CACHE
            154 CACHE
            156 LOAD_FAST                4 (lineno)
            158 UNPACK_SEQUENCE          1
            162 CALL                     1
            170 CACHE
            172 UNPACK_SEQUENCE          1
            176 CALL                     1
            184 CACHE
            186 POP_TOP

231     >>  188 LOAD_FAST                6 (message)
            190 STORE_SUBSCR
            194 CACHE
            196 CACHE
            198 CACHE
            200 CACHE
            202 CACHE
            204 CACHE
            206 CACHE
            208 CACHE
            210 CACHE
            212 LOAD_CONST               5 (': option ')
            214 UNPACK_SEQUENCE          1
            218 CALL                     1
            226 CACHE
            228 POP_TOP

232         230 LOAD_FAST                6 (message)
            232 STORE_SUBSCR
            236 CACHE
            238 CACHE
            240 CACHE
            242 CACHE
            244 CACHE
            246 CACHE
            248 CACHE
            250 CACHE
            252 CACHE
            254 LOAD_FAST                5 (msg)
            256 UNPACK_SEQUENCE          1
            260 CALL                     1
            268 CACHE
            270 POP_TOP

233         272 LOAD_FAST                6 (message)
            274 STORE_FAST               5 (msg)
            276 JUMP_FORWARD            22 (to 322)

235     >>  278 LOAD_FAST                5 (msg)
            280 STORE_SUBSCR
            284 CACHE
            286 CACHE
            288 CACHE
            290 CACHE
            292 CACHE
            294 CACHE
            296 CACHE
            298 CACHE
            300 CACHE
            302 LOAD_CONST               6 (0)
            304 LOAD_CONST               7 ('Option ')
            306 UNPACK_SEQUENCE          2
            310 CALL                     2
            318 CACHE
            320 POP_TOP

236     >>  322 LOAD_GLOBAL             10 (Error)
            332 CACHE
            334 STORE_SUBSCR
            338 CACHE
            340 CACHE
            342 CACHE
            344 CACHE
            346 CACHE
            348 CACHE
            350 CACHE
            352 CACHE
            354 CACHE
            356 LOAD_FAST                0 (self)
            358 LOAD_CONST               8 ('')
            360 STORE_SUBSCR
            364 CACHE
            366 CACHE
            368 CACHE
            370 CACHE
            372 CACHE
            374 CACHE
            376 CACHE
            378 CACHE
            380 CACHE
            382 LOAD_FAST                5 (msg)
            384 UNPACK_SEQUENCE          1
            388 CALL                     1
            396 CACHE
            398 UNPACK_SEQUENCE          2
            402 CALL                     2
            410 CACHE
            412 POP_TOP

237         414 LOAD_FAST                1 (section)
            416 LOAD_FAST                0 (self)
            418 STORE_ATTR               8 (section)

238         428 LOAD_FAST                2 (option)
            430 LOAD_FAST                0 (self)
            432 STORE_ATTR               9 (option)

239         442 LOAD_FAST                3 (source)
            444 LOAD_FAST                0 (self)
            446 STORE_ATTR              10 (source)

240         456 LOAD_FAST                4 (lineno)
            458 LOAD_FAST                0 (self)
            460 STORE_ATTR              11 (lineno)

241         470 LOAD_FAST                1 (section)
            472 LOAD_FAST                2 (option)
            474 LOAD_FAST                3 (source)
            476 LOAD_FAST                4 (lineno)
            478 BUILD_TUPLE              4
            480 LOAD_FAST                0 (self)
            482 STORE_ATTR              12 (args)
            492 LOAD_CONST               0 (None)
            494 RETURN_VALUE

Disassembly of <code object NoOptionError at 0x000001B2A7688490, file "configparser.py", line 244>:
244           0 RESUME                   0
              2 LOAD_NAME                0 (__name__)
              4 STORE_NAME               1 (__module__)
              6 LOAD_CONST               0 ('NoOptionError')
              8 STORE_NAME               2 (__qualname__)

245          10 LOAD_CONST               1 ('A requested option was not found.')
             12 STORE_NAME               3 (__doc__)

247          14 LOAD_CONST               2 (<code object __init__ at 0x000001B2A725B730, file "configparser.py", line 247>)
             16 MAKE_FUNCTION            0
             18 STORE_NAME               4 (__init__)
             20 LOAD_CONST               3 (None)
             22 RETURN_VALUE

Disassembly of <code object __init__ at 0x000001B2A725B730, file "configparser.py", line 247>:
247           0 RESUME                   0

248           2 LOAD_GLOBAL              0 (Error)
             12 CACHE
             14 STORE_SUBSCR
             18 CACHE
             20 CACHE
             22 CACHE
             24 CACHE
             26 CACHE
             28 CACHE
             30 CACHE
             32 CACHE
             34 CACHE
             36 LOAD_FAST                0 (self)
             38 LOAD_CONST               1 ('No option ')

249          40 LOAD_FAST                1 (option)
             42 FORMAT_VALUE             2 (repr)
             44 LOAD_CONST               2 (' in section: ')
             46 LOAD_FAST                2 (section)
             48 FORMAT_VALUE             2 (repr)

248          50 BUILD_STRING             4
             52 UNPACK_SEQUENCE          2
             56 CALL                     2
             64 CACHE
             66 POP_TOP

250          68 LOAD_FAST                1 (option)
             70 LOAD_FAST                0 (self)
             72 STORE_ATTR               2 (option)

251          82 LOAD_FAST                2 (section)
             84 LOAD_FAST                0 (self)
             86 STORE_ATTR               3 (section)

252          96 LOAD_FAST                1 (option)
             98 LOAD_FAST                2 (section)
            100 BUILD_TUPLE              2
            102 LOAD_FAST                0 (self)
            104 STORE_ATTR               4 (args)
            114 LOAD_CONST               0 (None)
            116 RETURN_VALUE

Disassembly of <code object InterpolationError at 0x000001B2A768A090, file "configparser.py", line 255>:
255           0 RESUME                   0
              2 LOAD_NAME                0 (__name__)
              4 STORE_NAME               1 (__module__)
              6 LOAD_CONST               0 ('InterpolationError')
              8 STORE_NAME               2 (__qualname__)

256          10 LOAD_CONST               1 ('Base class for interpolation-related exceptions.')
             12 STORE_NAME               3 (__doc__)

258          14 LOAD_CONST               2 (<code object __init__ at 0x000001B2A725FCC0, file "configparser.py", line 258>)
             16 MAKE_FUNCTION            0
             18 STORE_NAME               4 (__init__)
             20 LOAD_CONST               3 (None)
             22 RETURN_VALUE

Disassembly of <code object __init__ at 0x000001B2A725FCC0, file "configparser.py", line 258>:
258           0 RESUME                   0

259           2 LOAD_GLOBAL              0 (Error)
             12 CACHE
             14 STORE_SUBSCR
             18 CACHE
             20 CACHE
             22 CACHE
             24 CACHE
             26 CACHE
             28 CACHE
             30 CACHE
             32 CACHE
             34 CACHE
             36 LOAD_FAST                0 (self)
             38 LOAD_FAST                3 (msg)
             40 UNPACK_SEQUENCE          2
             44 CALL                     2
             52 CACHE
             54 POP_TOP

260          56 LOAD_FAST                1 (option)
             58 LOAD_FAST                0 (self)
             60 STORE_ATTR               2 (option)

261          70 LOAD_FAST                2 (section)
             72 LOAD_FAST                0 (self)
             74 STORE_ATTR               3 (section)

262          84 LOAD_FAST                1 (option)
             86 LOAD_FAST                2 (section)
             88 LOAD_FAST                3 (msg)
             90 BUILD_TUPLE              3
             92 LOAD_FAST                0 (self)
             94 STORE_ATTR               4 (args)
            104 LOAD_CONST               0 (None)
            106 RETURN_VALUE

Disassembly of <code object InterpolationMissingOptionError at 0x000001B2A7688D50, file "configparser.py", line 265>:
265           0 RESUME                   0
              2 LOAD_NAME                0 (__name__)
              4 STORE_NAME               1 (__module__)
              6 LOAD_CONST               0 ('InterpolationMissingOptionError')
              8 STORE_NAME               2 (__qualname__)

266          10 LOAD_CONST               1 ('A string substitution required a setting which was not available.')
             12 STORE_NAME               3 (__doc__)

268          14 LOAD_CONST               2 (<code object __init__ at 0x000001B2A71D1A50, file "configparser.py", line 268>)
             16 MAKE_FUNCTION            0
             18 STORE_NAME               4 (__init__)
             20 LOAD_CONST               3 (None)
             22 RETURN_VALUE

Disassembly of <code object __init__ at 0x000001B2A71D1A50, file "configparser.py", line 268>:
268           0 RESUME                   0

269           2 LOAD_CONST               1 ('Bad value substitution: option {!r} in section {!r} contains an interpolation key {!r} which is not a valid option name. Raw value: {!r}')

271           4 STORE_SUBSCR
              8 CACHE
             10 CACHE
             12 CACHE
             14 CACHE
             16 CACHE
             18 CACHE
             20 CACHE
             22 CACHE
             24 CACHE
             26 LOAD_FAST                1 (option)
             28 LOAD_FAST                2 (section)
             30 LOAD_FAST                4 (reference)
             32 LOAD_FAST                3 (rawval)
             34 UNPACK_SEQUENCE          4
             38 CALL                     4
             46 CACHE

269          48 STORE_FAST               5 (msg)

272          50 LOAD_GLOBAL              2 (InterpolationError)
             60 CACHE
             62 STORE_SUBSCR
             66 CACHE
             68 CACHE
             70 CACHE
             72 CACHE
             74 CACHE
             76 CACHE
             78 CACHE
             80 CACHE
             82 CACHE
             84 LOAD_FAST                0 (self)
             86 LOAD_FAST                1 (option)
             88 LOAD_FAST                2 (section)
             90 LOAD_FAST                5 (msg)
             92 UNPACK_SEQUENCE          4
             96 CALL                     4
            104 CACHE
            106 POP_TOP

273         108 LOAD_FAST                4 (reference)
            110 LOAD_FAST                0 (self)
            112 STORE_ATTR               3 (reference)

274         122 LOAD_FAST                1 (option)
            124 LOAD_FAST                2 (section)
            126 LOAD_FAST                3 (rawval)
            128 LOAD_FAST                4 (reference)
            130 BUILD_TUPLE              4
            132 LOAD_FAST                0 (self)
            134 STORE_ATTR               4 (args)
            144 LOAD_CONST               0 (None)
            146 RETURN_VALUE

Disassembly of <code object InterpolationSyntaxError at 0x000001B2A76888F0, file "configparser.py", line 277>:
277           0 RESUME                   0
              2 LOAD_NAME                0 (__name__)
              4 STORE_NAME               1 (__module__)
              6 LOAD_CONST               0 ('InterpolationSyntaxError')
              8 STORE_NAME               2 (__qualname__)

278          10 LOAD_CONST               1 ('Raised when the source text contains invalid syntax.\n\n    Current implementation raises this exception when the source text into\n    which substitutions are made does not conform to the required syntax.\n    ')
             12 STORE_NAME               3 (__doc__)
             14 LOAD_CONST               2 (None)
             16 RETURN_VALUE

Disassembly of <code object InterpolationDepthError at 0x000001B2A7689290, file "configparser.py", line 285>:
285           0 RESUME                   0
              2 LOAD_NAME                0 (__name__)
              4 STORE_NAME               1 (__module__)
              6 LOAD_CONST               0 ('InterpolationDepthError')
              8 STORE_NAME               2 (__qualname__)

286          10 LOAD_CONST               1 ('Raised when substitutions are nested too deeply.')
             12 STORE_NAME               3 (__doc__)

288          14 LOAD_CONST               2 (<code object __init__ at 0x000001B2A71C5BC0, file "configparser.py", line 288>)
             16 MAKE_FUNCTION            0
             18 STORE_NAME               4 (__init__)
             20 LOAD_CONST               3 (None)
             22 RETURN_VALUE

Disassembly of <code object __init__ at 0x000001B2A71C5BC0, file "configparser.py", line 288>:
288           0 RESUME                   0

289           2 LOAD_CONST               1 ('Recursion limit exceeded in value substitution: option {!r} in section {!r} contains an interpolation key which cannot be substituted in {} steps. Raw value: {!r}')

292           4 STORE_SUBSCR
              8 CACHE
             10 CACHE
             12 CACHE
             14 CACHE
             16 CACHE
             18 CACHE
             20 CACHE
             22 CACHE
             24 CACHE
             26 LOAD_FAST                1 (option)
             28 LOAD_FAST                2 (section)
             30 LOAD_GLOBAL              2 (MAX_INTERPOLATION_DEPTH)
             40 CACHE

293          42 LOAD_FAST                3 (rawval)

292          44 UNPACK_SEQUENCE          4
             48 CALL                     4
             56 CACHE

289          58 STORE_FAST               4 (msg)

294          60 LOAD_GLOBAL              4 (InterpolationError)
             70 CACHE
             72 STORE_SUBSCR
             76 CACHE
             78 CACHE
             80 CACHE
             82 CACHE
             84 CACHE
             86 CACHE
             88 CACHE
             90 CACHE
             92 CACHE
             94 LOAD_FAST                0 (self)
             96 LOAD_FAST                1 (option)
             98 LOAD_FAST                2 (section)
            100 LOAD_FAST                4 (msg)
            102 UNPACK_SEQUENCE          4
            106 CALL                     4
            114 CACHE
            116 POP_TOP

295         118 LOAD_FAST                1 (option)
            120 LOAD_FAST                2 (section)
            122 LOAD_FAST                3 (rawval)
            124 BUILD_TUPLE              3
            126 LOAD_FAST                0 (self)
            128 STORE_ATTR               4 (args)
            138 LOAD_CONST               0 (None)
            140 RETURN_VALUE

Disassembly of <code object ParsingError at 0x000001B2A76D9110, file "configparser.py", line 298>:
298           0 RESUME                   0
              2 LOAD_NAME                0 (__name__)
              4 STORE_NAME               1 (__module__)
              6 LOAD_CONST               0 ('ParsingError')
              8 STORE_NAME               2 (__qualname__)

299          10 LOAD_CONST               1 ('Raised when a configuration file does not follow legal syntax.')
             12 STORE_NAME               3 (__doc__)

301          14 LOAD_CONST               7 ((None, None))
             16 LOAD_CONST               3 (<code object __init__ at 0x000001B2A761F870, file "configparser.py", line 301>)
             18 MAKE_FUNCTION            1 (defaults)
             20 STORE_NAME               4 (__init__)

316          22 LOAD_NAME                5 (property)

317          24 LOAD_CONST               4 (<code object filename at 0x000001B2A76AD790, file "configparser.py", line 316>)
             26 MAKE_FUNCTION            0

316          28 UNPACK_SEQUENCE          0
             32 CALL                     0
             40 CACHE

317          42 STORE_NAME               6 (filename)

326          44 LOAD_NAME                6 (filename)
             46 LOAD_ATTR                7 (NULL|self + __doc__)
             66 CACHE
             68 CACHE
             70 CACHE
             72 CACHE

327          74 STORE_NAME               6 (filename)

336          76 LOAD_CONST               6 (<code object append at 0x000001B2A725FA60, file "configparser.py", line 336>)
             78 MAKE_FUNCTION            0
             80 STORE_NAME               8 (append)
             82 LOAD_CONST               2 (None)
             84 RETURN_VALUE

Disassembly of <code object __init__ at 0x000001B2A761F870, file "configparser.py", line 301>:
301           0 RESUME                   0

304           2 LOAD_FAST                2 (filename)
              4 POP_JUMP_IF_FALSE       17 (to 40)
              6 LOAD_FAST                1 (source)
              8 POP_JUMP_IF_FALSE       15 (to 40)

305          10 LOAD_GLOBAL              1 (NULL + ValueError)
             20 CACHE
             22 LOAD_CONST               1 ("Cannot specify both `filename' and `source'. Use `source'.")
             24 UNPACK_SEQUENCE          1
             28 CALL                     1
             36 CACHE
             38 RAISE_VARARGS            1

307     >>   40 LOAD_FAST                2 (filename)
             42 POP_JUMP_IF_TRUE        17 (to 78)
             44 LOAD_FAST                1 (source)
             46 POP_JUMP_IF_TRUE        15 (to 78)

308          48 LOAD_GLOBAL              1 (NULL + ValueError)
             58 CACHE
             60 LOAD_CONST               2 ("Required argument `source' not given.")
             62 UNPACK_SEQUENCE          1
             66 CALL                     1
             74 CACHE
             76 RAISE_VARARGS            1

309     >>   78 LOAD_FAST                2 (filename)
             80 POP_JUMP_IF_FALSE        2 (to 86)

310          82 LOAD_FAST                2 (filename)
             84 STORE_FAST               1 (source)

311     >>   86 LOAD_GLOBAL              2 (Error)
             96 CACHE
             98 STORE_SUBSCR
            102 CACHE
            104 CACHE
            106 CACHE
            108 CACHE
            110 CACHE
            112 CACHE
            114 CACHE
            116 CACHE
            118 CACHE
            120 LOAD_FAST                0 (self)
            122 LOAD_CONST               3 ('Source contains parsing errors: %r')
            124 LOAD_FAST                1 (source)
            126 BINARY_OP                6 (%)
            130 UNPACK_SEQUENCE          2
            134 CALL                     2
            142 CACHE
            144 POP_TOP

312         146 LOAD_FAST                1 (source)
            148 LOAD_FAST                0 (self)
            150 STORE_ATTR               3 (source)

313         160 BUILD_LIST               0
            162 LOAD_FAST                0 (self)
            164 STORE_ATTR               4 (errors)

314         174 LOAD_FAST                1 (source)
            176 BUILD_TUPLE              1
            178 LOAD_FAST                0 (self)
            180 STORE_ATTR               5 (args)
            190 LOAD_CONST               0 (None)
            192 RETURN_VALUE

Disassembly of <code object filename at 0x000001B2A76AD790, file "configparser.py", line 316>:
316           0 RESUME                   0

319           2 LOAD_GLOBAL              1 (NULL + warnings)
             12 CACHE
             14 LOAD_ATTR                1 (NULL|self + warnings)
             34 CACHE
             36 CACHE
             38 LOAD_CONST               2 (2)

319          40 KW_NAMES                 3 (('stacklevel',))
             42 UNPACK_SEQUENCE          3
             46 CALL                     3
             54 CACHE
             56 POP_TOP

324          58 LOAD_FAST                0 (self)
             60 LOAD_ATTR                3 (NULL|self + warn)

Disassembly of <code object filename at 0x000001B2A76AD8A0, file "configparser.py", line 326>:
326           0 RESUME                   0

329           2 LOAD_GLOBAL              1 (NULL + warnings)
             12 CACHE
             14 LOAD_ATTR                1 (NULL|self + warnings)
             34 CACHE
             36 CACHE
             38 LOAD_CONST               2 (2)

329          40 KW_NAMES                 3 (('stacklevel',))
             42 UNPACK_SEQUENCE          3
             46 CALL                     3
             54 CACHE
             56 POP_TOP

334          58 LOAD_FAST                1 (value)
             60 LOAD_FAST                0 (self)
             62 STORE_ATTR               3 (source)
             72 LOAD_CONST               4 (None)
             74 RETURN_VALUE

Disassembly of <code object append at 0x000001B2A725FA60, file "configparser.py", line 336>:
336           0 RESUME                   0

337           2 LOAD_FAST                0 (self)
              4 LOAD_ATTR                0 (errors)
             24 CACHE
             26 CACHE
             28 CACHE
             30 CACHE
             32 CACHE
             34 CACHE
             36 LOAD_FAST                1 (lineno)
             38 LOAD_FAST                2 (line)
             40 BUILD_TUPLE              2
             42 UNPACK_SEQUENCE          1
             46 CALL                     1
             54 CACHE
             56 POP_TOP

338          58 LOAD_FAST                0 (self)
             60 COPY                     1
             62 LOAD_ATTR                2 (append)
             82 CACHE
             84 BINARY_OP               13 (+=)
             88 SWAP                     2
             90 STORE_ATTR               2 (message)
            100 LOAD_CONST               0 (None)
            102 RETURN_VALUE

Disassembly of <code object MissingSectionHeaderError at 0x000001B2A7689DF0, file "configparser.py", line 341>:
341           0 RESUME                   0
              2 LOAD_NAME                0 (__name__)
              4 STORE_NAME               1 (__module__)
              6 LOAD_CONST               0 ('MissingSectionHeaderError')
              8 STORE_NAME               2 (__qualname__)

342          10 LOAD_CONST               1 ('Raised when a key-value pair is found before any section header.')
             12 STORE_NAME               3 (__doc__)

344          14 LOAD_CONST               2 (<code object __init__ at 0x000001B2A71C5FB0, file "configparser.py", line 344>)
             16 MAKE_FUNCTION            0
             18 STORE_NAME               4 (__init__)
             20 LOAD_CONST               3 (None)
             22 RETURN_VALUE

Disassembly of <code object __init__ at 0x000001B2A71C5FB0, file "configparser.py", line 344>:
344           0 RESUME                   0

345           2 LOAD_GLOBAL              0 (Error)
             12 CACHE
             14 STORE_SUBSCR
             18 CACHE
             20 CACHE
             22 CACHE
             24 CACHE
             26 CACHE
             28 CACHE
             30 CACHE
             32 CACHE
             34 CACHE

346          36 LOAD_FAST                0 (self)

347          38 LOAD_CONST               1 ('File contains no section headers.\nfile: %r, line: %d\n%r')

348          40 LOAD_FAST                1 (filename)
             42 LOAD_FAST                2 (lineno)
             44 LOAD_FAST                3 (line)
             46 BUILD_TUPLE              3

347          48 BINARY_OP                6 (%)

345          52 UNPACK_SEQUENCE          2
             56 CALL                     2
             64 CACHE
             66 POP_TOP

349          68 LOAD_FAST                1 (filename)
             70 LOAD_FAST                0 (self)
             72 STORE_ATTR               2 (source)

350          82 LOAD_FAST                2 (lineno)
             84 LOAD_FAST                0 (self)
             86 STORE_ATTR               3 (lineno)

351          96 LOAD_FAST                3 (line)
             98 LOAD_FAST                0 (self)
            100 STORE_ATTR               4 (line)

352         110 LOAD_FAST                1 (filename)
            112 LOAD_FAST                2 (lineno)
            114 LOAD_FAST                3 (line)
            116 BUILD_TUPLE              3
            118 LOAD_FAST                0 (self)
            120 STORE_ATTR               5 (args)
            130 LOAD_CONST               0 (None)
            132 RETURN_VALUE

Disassembly of <code object Interpolation at 0x000001B2A767EC40, file "configparser.py", line 361>:
361           0 RESUME                   0
              2 LOAD_NAME                0 (__name__)
              4 STORE_NAME               1 (__module__)
              6 LOAD_CONST               0 ('Interpolation')
              8 STORE_NAME               2 (__qualname__)

362          10 LOAD_CONST               1 ('Dummy interpolation that passes the value through with no changes.')
             12 STORE_NAME               3 (__doc__)

364          14 LOAD_CONST               2 (<code object before_get at 0x000001B2A767BC50, file "configparser.py", line 364>)
             16 MAKE_FUNCTION            0
             18 STORE_NAME               4 (before_get)

367          20 LOAD_CONST               3 (<code object before_set at 0x000001B2A7679480, file "configparser.py", line 367>)
             22 MAKE_FUNCTION            0
             24 STORE_NAME               5 (before_set)

370          26 LOAD_CONST               4 (<code object before_read at 0x000001B2A767AF50, file "configparser.py", line 370>)
             28 MAKE_FUNCTION            0
             30 STORE_NAME               6 (before_read)

373          32 LOAD_CONST               5 (<code object before_write at 0x000001B2A7679960, file "configparser.py", line 373>)
             34 MAKE_FUNCTION            0
             36 STORE_NAME               7 (before_write)
             38 LOAD_CONST               6 (None)
             40 RETURN_VALUE

Disassembly of <code object before_get at 0x000001B2A767BC50, file "configparser.py", line 364>:
364           0 RESUME                   0

365           2 LOAD_FAST                4 (value)
              4 RETURN_VALUE

Disassembly of <code object before_set at 0x000001B2A7679480, file "configparser.py", line 367>:
367           0 RESUME                   0

368           2 LOAD_FAST                4 (value)
              4 RETURN_VALUE

Disassembly of <code object before_read at 0x000001B2A767AF50, file "configparser.py", line 370>:
370           0 RESUME                   0

371           2 LOAD_FAST                4 (value)
              4 RETURN_VALUE

Disassembly of <code object before_write at 0x000001B2A7679960, file "configparser.py", line 373>:
373           0 RESUME                   0

374           2 LOAD_FAST                4 (value)
              4 RETURN_VALUE

Disassembly of <code object BasicInterpolation at 0x000001B2A76ADDF0, file "configparser.py", line 377>:
377           0 RESUME                   0
              2 LOAD_NAME                0 (__name__)
              4 STORE_NAME               1 (__module__)
              6 LOAD_CONST               0 ('BasicInterpolation')
              8 STORE_NAME               2 (__qualname__)

378          10 LOAD_CONST               1 ('Interpolation as implemented in the classic ConfigParser.\n\n    The option values can contain format strings which refer to other values in\n    the same section, or values in the special default section.\n\n    For example:\n\n        something: %(dir)s/whatever\n\n    would resolve the "%(dir)s" to the value of dir.  All reference\n    expansions are done late, on demand. If a user needs to use a bare % in\n    a configuration file, she can escape it by writing %%. Other % usage\n    is considered a user error and raises `InterpolationSyntaxError`.')
             12 STORE_NAME               3 (__doc__)

392          14 PUSH_NULL
             16 LOAD_NAME                4 (re)
             18 LOAD_ATTR                5 (NULL|self + __qualname__)
             38 CACHE
             40 CACHE
             42 CACHE
             44 STORE_NAME               6 (_KEYCRE)

394          46 LOAD_CONST               3 (<code object before_get at 0x000001B2A725FDF0, file "configparser.py", line 394>)
             48 MAKE_FUNCTION            0
             50 STORE_NAME               7 (before_get)

399          52 LOAD_CONST               4 (<code object before_set at 0x000001B2A72125B0, file "configparser.py", line 399>)
             54 MAKE_FUNCTION            0
             56 STORE_NAME               8 (before_set)

407          58 LOAD_CONST               5 (<code object _interpolate_some at 0x000001B2A75BE240, file "configparser.py", line 407>)
             60 MAKE_FUNCTION            0
             62 STORE_NAME               9 (_interpolate_some)
             64 LOAD_CONST               6 (None)
             66 RETURN_VALUE

Disassembly of <code object before_get at 0x000001B2A725FDF0, file "configparser.py", line 394>:
394           0 RESUME                   0

395           2 BUILD_LIST               0
              4 STORE_FAST               6 (L)

396           6 LOAD_FAST                0 (self)
              8 STORE_SUBSCR
             12 CACHE
             14 CACHE
             16 CACHE
             18 CACHE
             20 CACHE
             22 CACHE
             24 CACHE
             26 CACHE
             28 CACHE
             30 LOAD_FAST                1 (parser)
             32 LOAD_FAST                3 (option)
             34 LOAD_FAST                6 (L)
             36 LOAD_FAST                4 (value)
             38 LOAD_FAST                2 (section)
             40 LOAD_FAST                5 (defaults)
             42 LOAD_CONST               1 (1)
             44 UNPACK_SEQUENCE          7
             48 CALL                     7
             56 CACHE
             58 POP_TOP

397          60 LOAD_CONST               2 ('')
             62 STORE_SUBSCR
             66 CACHE
             68 CACHE
             70 CACHE
             72 CACHE
             74 CACHE
             76 CACHE
             78 CACHE
             80 CACHE
             82 CACHE
             84 LOAD_FAST                6 (L)
             86 UNPACK_SEQUENCE          1
             90 CALL                     1
             98 CACHE
            100 RETURN_VALUE

Disassembly of <code object before_set at 0x000001B2A72125B0, file "configparser.py", line 399>:
399           0 RESUME                   0

400           2 LOAD_FAST                4 (value)
              4 STORE_SUBSCR
              8 CACHE
             10 CACHE
             12 CACHE
             14 CACHE
             16 CACHE
             18 CACHE
             20 CACHE
             22 CACHE
             24 CACHE
             26 LOAD_CONST               1 ('%%')
             28 LOAD_CONST               2 ('')
             30 UNPACK_SEQUENCE          2
             34 CALL                     2
             42 CACHE
             44 STORE_FAST               5 (tmp_value)

401          46 LOAD_FAST                0 (self)
             48 LOAD_ATTR                1 (NULL|self + replace)
             68 CACHE
             70 CACHE
             72 CACHE
             74 CACHE
             76 CACHE
             78 CACHE
             80 LOAD_CONST               2 ('')
             82 LOAD_FAST                5 (tmp_value)
             84 UNPACK_SEQUENCE          2
             88 CALL                     2
             96 CACHE
             98 STORE_FAST               5 (tmp_value)

402         100 LOAD_CONST               3 ('%')
            102 LOAD_FAST                5 (tmp_value)
            104 CONTAINS_OP              0
            106 POP_JUMP_IF_FALSE       39 (to 186)

403         108 LOAD_GLOBAL              7 (NULL + ValueError)
            118 CACHE
            120 LOAD_CONST               4 ('invalid interpolation syntax in %r at position %d')

404         122 LOAD_FAST                4 (value)
            124 LOAD_FAST                5 (tmp_value)
            126 STORE_SUBSCR
            130 CACHE
            132 CACHE
            134 CACHE
            136 CACHE
            138 CACHE
            140 CACHE
            142 CACHE
            144 CACHE
            146 CACHE
            148 LOAD_CONST               3 ('%')
            150 UNPACK_SEQUENCE          1
            154 CALL                     1
            162 CACHE
            164 BUILD_TUPLE              2

403         166 BINARY_OP                6 (%)
            170 UNPACK_SEQUENCE          1
            174 CALL                     1
            182 CACHE
            184 RAISE_VARARGS            1

405     >>  186 LOAD_FAST                4 (value)
            188 RETURN_VALUE

Disassembly of <code object _interpolate_some at 0x000001B2A75BE240, file "configparser.py", line 407>:
407           0 RESUME                   0

409           2 LOAD_FAST                1 (parser)
              4 STORE_SUBSCR
              8 CACHE
             10 CACHE
             12 CACHE
             14 CACHE
             16 CACHE
             18 CACHE
             20 CACHE
             22 CACHE
             24 CACHE
             26 LOAD_FAST                5 (section)
             28 LOAD_FAST                2 (option)
             30 LOAD_CONST               1 (True)
             32 LOAD_FAST                4 (rest)
             34 KW_NAMES                 2 (('raw', 'fallback'))
             36 UNPACK_SEQUENCE          4
             40 CALL                     4
             48 CACHE
             50 STORE_FAST               8 (rawval)

410          52 LOAD_FAST                7 (depth)
             54 LOAD_GLOBAL              2 (MAX_INTERPOLATION_DEPTH)
             64 CACHE
             66 COMPARE_OP               4 (<)
             70 CACHE
             72 POP_JUMP_IF_FALSE       17 (to 108)

411          74 LOAD_GLOBAL              5 (NULL + InterpolationDepthError)
             84 CACHE
             86 LOAD_FAST                2 (option)
             88 LOAD_FAST                5 (section)
             90 LOAD_FAST                8 (rawval)
             92 UNPACK_SEQUENCE          3
             96 CALL                     3
            104 CACHE
            106 RAISE_VARARGS            1

412     >>  108 LOAD_FAST                4 (rest)
            110 EXTENDED_ARG             1
            112 POP_JUMP_IF_FALSE      390 (to 894)

413         114 LOAD_FAST                4 (rest)
            116 STORE_SUBSCR
            120 CACHE
            122 CACHE
            124 CACHE
            126 CACHE
            128 CACHE
            130 CACHE
            132 CACHE
            134 CACHE
            136 CACHE
            138 LOAD_CONST               3 ('%')
            140 UNPACK_SEQUENCE          1
            144 CALL                     1
            152 CACHE
            154 STORE_FAST               9 (p)

414         156 LOAD_FAST                9 (p)
            158 LOAD_CONST               4 (0)
            160 COMPARE_OP               0 (<)
            164 CACHE
            166 POP_JUMP_IF_FALSE       23 (to 214)

415         168 LOAD_FAST                3 (accum)
            170 STORE_SUBSCR
            174 CACHE
            176 CACHE
            178 CACHE
            180 CACHE
            182 CACHE
            184 CACHE
            186 CACHE
            188 CACHE
            190 CACHE
            192 LOAD_FAST                4 (rest)
            194 UNPACK_SEQUENCE          1
            198 CALL                     1
            206 CACHE
            208 POP_TOP

416         210 LOAD_CONST               0 (None)
            212 RETURN_VALUE

417     >>  214 LOAD_FAST                9 (p)
            216 LOAD_CONST               4 (0)
            218 COMPARE_OP               4 (<)
            222 CACHE
            224 POP_JUMP_IF_FALSE       39 (to 304)

418         226 LOAD_FAST                3 (accum)
            228 STORE_SUBSCR
            232 CACHE
            234 CACHE
            236 CACHE
            238 CACHE
            240 CACHE
            242 CACHE
            244 CACHE
            246 CACHE
            248 CACHE
            250 LOAD_FAST                4 (rest)
            252 LOAD_CONST               0 (None)
            254 LOAD_FAST                9 (p)
            256 BUILD_SLICE              2
            258 BINARY_SUBSCR
            262 CACHE
            264 CACHE
            266 CACHE
            268 UNPACK_SEQUENCE          1
            272 CALL                     1
            280 CACHE
            282 POP_TOP

419         284 LOAD_FAST                4 (rest)
            286 LOAD_FAST                9 (p)
            288 LOAD_CONST               0 (None)
            290 BUILD_SLICE              2
            292 BINARY_SUBSCR
            296 CACHE
            298 CACHE
            300 CACHE
            302 STORE_FAST               4 (rest)

421     >>  304 LOAD_FAST                4 (rest)
            306 LOAD_CONST               5 (1)
            308 LOAD_CONST               6 (2)
            310 BUILD_SLICE              2
            312 BINARY_SUBSCR
            316 CACHE
            318 CACHE
            320 CACHE
            322 STORE_FAST              10 (c)

422         324 LOAD_FAST               10 (c)
            326 LOAD_CONST               3 ('%')
            328 COMPARE_OP               2 (<)
            332 CACHE
            334 POP_JUMP_IF_FALSE       32 (to 400)

423         336 LOAD_FAST                3 (accum)
            338 STORE_SUBSCR
            342 CACHE
            344 CACHE
            346 CACHE
            348 CACHE
            350 CACHE
            352 CACHE
            354 CACHE
            356 CACHE
            358 CACHE
            360 LOAD_CONST               3 ('%')
            362 UNPACK_SEQUENCE          1
            366 CALL                     1
            374 CACHE
            376 POP_TOP

424         378 LOAD_FAST                4 (rest)
            380 LOAD_CONST               6 (2)
            382 LOAD_CONST               0 (None)
            384 BUILD_SLICE              2
            386 BINARY_SUBSCR
            390 CACHE
            392 CACHE
            394 CACHE
            396 STORE_FAST               4 (rest)
            398 JUMP_FORWARD           242 (to 884)

425     >>  400 LOAD_FAST               10 (c)
            402 LOAD_CONST               7 ('(')
            404 COMPARE_OP               2 (<)
            408 CACHE
            410 POP_JUMP_IF_FALSE      216 (to 844)

426         412 LOAD_FAST                0 (self)
            414 LOAD_ATTR                5 (NULL|self + InterpolationDepthError)
            434 CACHE
            436 CACHE
            438 CACHE
            440 CACHE
            442 CACHE
            444 CACHE
            446 LOAD_FAST                4 (rest)
            448 UNPACK_SEQUENCE          1
            452 CALL                     1
            460 CACHE
            462 STORE_FAST              11 (m)

427         464 LOAD_FAST               11 (m)
            466 POP_JUMP_IF_NOT_NONE    20 (to 508)

428         468 LOAD_GLOBAL             15 (NULL + InterpolationSyntaxError)
            478 CACHE
            480 LOAD_FAST                2 (option)
            482 LOAD_FAST                5 (section)

429         484 LOAD_CONST               8 ('bad interpolation variable reference %r')
            486 LOAD_FAST                4 (rest)
            488 BINARY_OP                6 (%)

428         492 UNPACK_SEQUENCE          3
            496 CALL                     3
            504 CACHE
            506 RAISE_VARARGS            1

430     >>  508 LOAD_FAST                1 (parser)
            510 STORE_SUBSCR
            514 CACHE
            516 CACHE
            518 CACHE
            520 CACHE
            522 CACHE
            524 CACHE
            526 CACHE
            528 CACHE
            530 CACHE
            532 LOAD_FAST               11 (m)
            534 STORE_SUBSCR
            538 CACHE
            540 CACHE
            542 CACHE
            544 CACHE
            546 CACHE
            548 CACHE
            550 CACHE
            552 CACHE
            554 CACHE
            556 LOAD_CONST               5 (1)
            558 UNPACK_SEQUENCE          1
            562 CALL                     1
            570 CACHE
            572 UNPACK_SEQUENCE          1
            576 CALL                     1
            584 CACHE
            586 STORE_FAST              12 (var)

431         588 LOAD_FAST                4 (rest)
            590 LOAD_FAST               11 (m)
            592 STORE_SUBSCR
            596 CACHE
            598 CACHE
            600 CACHE
            602 CACHE
            604 CACHE
            606 CACHE
            608 CACHE
            610 CACHE
            612 CACHE
            614 UNPACK_SEQUENCE          0
            618 CALL                     0
            626 CACHE
            628 LOAD_CONST               0 (None)
            630 BUILD_SLICE              2
            632 BINARY_SUBSCR
            636 CACHE
            638 CACHE
            640 CACHE
            642 STORE_FAST               4 (rest)

432         644 NOP

433         646 LOAD_FAST                6 (map)
            648 LOAD_FAST               12 (var)
            650 BINARY_SUBSCR
            654 CACHE
            656 CACHE
            658 CACHE
            660 STORE_FAST              13 (v)
            662 JUMP_FORWARD            33 (to 730)
        >>  664 PUSH_EXC_INFO

434         666 LOAD_GLOBAL             22 (KeyError)
            676 CACHE
            678 CHECK_EXC_MATCH
            680 POP_JUMP_IF_FALSE       20 (to 722)
            682 POP_TOP

435         684 LOAD_GLOBAL             25 (NULL + InterpolationMissingOptionError)
            694 CACHE

436         696 LOAD_FAST                2 (option)
            698 LOAD_FAST                5 (section)
            700 LOAD_FAST                8 (rawval)
            702 LOAD_FAST               12 (var)

435         704 UNPACK_SEQUENCE          4
            708 CALL                     4
            716 CACHE

436         718 LOAD_CONST               0 (None)

435         720 RAISE_VARARGS            2

434     >>  722 RERAISE                  0
        >>  724 COPY                     3
            726 POP_EXCEPT
            728 RERAISE                  1

437     >>  730 LOAD_CONST               3 ('%')
            732 LOAD_FAST               13 (v)
            734 CONTAINS_OP              0
            736 POP_JUMP_IF_FALSE       31 (to 800)

438         738 LOAD_FAST                0 (self)
            740 STORE_SUBSCR
            744 CACHE
            746 CACHE
            748 CACHE
            750 CACHE
            752 CACHE
            754 CACHE
            756 CACHE
            758 CACHE
            760 CACHE
            762 LOAD_FAST                1 (parser)
            764 LOAD_FAST                2 (option)
            766 LOAD_FAST                3 (accum)
            768 LOAD_FAST               13 (v)

439         770 LOAD_FAST                5 (section)
            772 LOAD_FAST                6 (map)
            774 LOAD_FAST                7 (depth)
            776 LOAD_CONST               5 (1)
            778 BINARY_OP                0 (+)

438         782 UNPACK_SEQUENCE          7
            786 CALL                     7
            794 CACHE
            796 POP_TOP
            798 JUMP_FORWARD            42 (to 884)

441     >>  800 LOAD_FAST                3 (accum)
            802 STORE_SUBSCR
            806 CACHE
            808 CACHE
            810 CACHE
            812 CACHE
            814 CACHE
            816 CACHE
            818 CACHE
            820 CACHE
            822 CACHE
            824 LOAD_FAST               13 (v)
            826 UNPACK_SEQUENCE          1
            830 CALL                     1
            838 CACHE
            840 POP_TOP
            842 JUMP_FORWARD            20 (to 884)

443     >>  844 LOAD_GLOBAL             15 (NULL + InterpolationSyntaxError)
            854 CACHE

444         856 LOAD_FAST                2 (option)
            858 LOAD_FAST                5 (section)
            860 LOAD_CONST               9 ("'%' must be followed by '%' or '(', found: ")

446         862 LOAD_FAST                4 (rest)
            864 FORMAT_VALUE             2 (repr)

445         866 BUILD_STRING             2

443         868 UNPACK_SEQUENCE          3
            872 CALL                     3
            880 CACHE
            882 RAISE_VARARGS            1

412     >>  884 LOAD_FAST                4 (rest)
            886 EXTENDED_ARG             1
