#!/usr/bin/env python3
"""
Advanced Python code extraction from executable
"""
import struct
import os
import sys
import marshal
import dis
import types
import re

def find_marshal_data(data):
    """Find potential marshal data by looking for code object patterns"""
    marshal_candidates = []
    
    # Look for patterns that might indicate marshal data
    # Marshal data often starts with specific byte patterns
    patterns = [
        b'\xe3',  # TYPE_CODE
        b'\x63',  # 'c' - code object marker
        b'\x72',  # 'r' - reference
        b'\x74',  # 't' - tuple
        b'\x73',  # 's' - string
    ]
    
    for pattern in patterns:
        offset = 0
        while True:
            pos = data.find(pattern, offset)
            if pos == -1:
                break
                
            # Try to unmarshal from this position
            try:
                remaining_data = data[pos:]
                if len(remaining_data) > 100:  # Need reasonable amount of data
                    obj = marshal.loads(remaining_data)
                    if isinstance(obj, types.CodeType):
                        marshal_candidates.append({
                            'position': pos,
                            'code_object': obj,
                            'pattern': pattern
                        })
                        print(f"Found code object at position {pos} (pattern: {pattern.hex()})")
            except:
                pass
                
            offset = pos + 1
            
    return marshal_candidates

def find_python_strings(data):
    """Find Python-related strings that might indicate code locations"""
    python_patterns = [
        rb'def\s+\w+\s*\(',  # Function definitions
        rb'class\s+\w+',     # Class definitions
        rb'import\s+\w+',    # Import statements
        rb'from\s+\w+\s+import',  # From imports
        rb'if\s+__name__\s*==\s*[\'"]__main__[\'"]',  # Main check
        rb'\.py[\'"]',       # Python file references
    ]
    
    findings = []
    for pattern in python_patterns:
        for match in re.finditer(pattern, data):
            pos = match.start()
            text = match.group().decode('utf-8', errors='ignore')
            findings.append({
                'position': pos,
                'pattern': pattern,
                'text': text,
                'context': data[max(0, pos-50):pos+100].decode('utf-8', errors='ignore')
            })
            
    return findings

def extract_strings_around_position(data, position, radius=1000):
    """Extract readable strings around a position"""
    start = max(0, position - radius)
    end = min(len(data), position + radius)
    chunk = data[start:end]
    
    # Find printable strings
    strings = []
    current_string = b''
    
    for byte in chunk:
        if 32 <= byte <= 126:  # Printable ASCII
            current_string += bytes([byte])
        else:
            if len(current_string) >= 4:  # Minimum string length
                strings.append(current_string.decode('ascii'))
            current_string = b''
            
    if len(current_string) >= 4:
        strings.append(current_string.decode('ascii'))
        
    return strings

def search_for_embedded_zip(data):
    """Look for embedded ZIP files (common in Python executables)"""
    zip_signatures = [
        b'PK\x03\x04',  # Local file header
        b'PK\x01\x02',  # Central directory file header
        b'PK\x05\x06',  # End of central directory
    ]
    
    zip_files = []
    for sig in zip_signatures:
        offset = 0
        while True:
            pos = data.find(sig, offset)
            if pos == -1:
                break
                
            zip_files.append({
                'position': pos,
                'signature': sig,
                'type': 'zip_header'
            })
            offset = pos + 1
            
    return zip_files

def analyze_executable_advanced(exe_path, output_dir):
    """Advanced analysis of executable"""
    print(f"Advanced analysis of {exe_path}")
    
    with open(exe_path, 'rb') as f:
        data = f.read()
        
    print(f"File size: {len(data)} bytes")
    
    # Create output directory
    os.makedirs(output_dir, exist_ok=True)
    
    # 1. Look for marshal data
    print("\n1. Searching for marshal data...")
    marshal_candidates = find_marshal_data(data)
    
    extracted_count = 0
    for i, candidate in enumerate(marshal_candidates):
        try:
            code_obj = candidate['code_object']
            filename = f"marshal_{i+1:03d}"
            
            if hasattr(code_obj, 'co_filename') and code_obj.co_filename:
                orig_name = os.path.basename(code_obj.co_filename)
                if orig_name and orig_name not in ['<string>', '<frozen>', '<built-in>']:
                    filename = orig_name.replace('.py', '').replace('.', '_')
                    
            # Try to decompile
            try:
                import uncompyle6
                output_path = os.path.join(output_dir, filename + '.py')
                with open(output_path, 'w') as f:
                    uncompyle6.decompile(code_obj, f)
                print(f"  Decompiled to {output_path}")
                extracted_count += 1
            except:
                # Fallback to disassembly
                output_path = os.path.join(output_dir, filename + '_disasm.txt')
                with open(output_path, 'w') as f:
                    f.write(f"# Code object from position {candidate['position']}\n")
                    f.write(f"# Filename: {getattr(code_obj, 'co_filename', 'unknown')}\n")
                    f.write(f"# Name: {getattr(code_obj, 'co_name', 'unknown')}\n\n")
                    dis.dis(code_obj, file=f)
                    
                    # Extract constants
                    if hasattr(code_obj, 'co_consts'):
                        f.write("\n\n# Constants:\n")
                        for j, const in enumerate(code_obj.co_consts):
                            f.write(f"# {j}: {repr(const)}\n")
                            
                print(f"  Disassembled to {output_path}")
                extracted_count += 1
                
        except Exception as e:
            print(f"  Failed to process marshal candidate {i+1}: {e}")
    
    # 2. Look for Python strings
    print("\n2. Searching for Python code patterns...")
    python_strings = find_python_strings(data)
    
    if python_strings:
        strings_file = os.path.join(output_dir, 'python_strings.txt')
        with open(strings_file, 'w', encoding='utf-8') as f:
            f.write("Python-related strings found in executable:\n\n")
            for finding in python_strings[:50]:  # Limit to first 50
                f.write(f"Position: {finding['position']}\n")
                f.write(f"Pattern: {finding['pattern']}\n")
                f.write(f"Text: {finding['text']}\n")
                f.write(f"Context: {finding['context']}\n")
                f.write("-" * 80 + "\n")
        print(f"  Saved Python strings to {strings_file}")
    
    # 3. Look for embedded ZIP files
    print("\n3. Searching for embedded ZIP files...")
    zip_files = search_for_embedded_zip(data)
    
    if zip_files:
        print(f"  Found {len(zip_files)} ZIP signatures")
        # Try to extract ZIP files
        for i, zip_info in enumerate(zip_files[:10]):  # Limit to first 10
            try:
                pos = zip_info['position']
                # Try to find the end of the ZIP file
                zip_data = data[pos:]
                
                # Look for end of central directory signature
                end_sig = b'PK\x05\x06'
                end_pos = zip_data.find(end_sig)
                if end_pos != -1:
                    # Extract the ZIP file
                    zip_end = pos + end_pos + 22  # 22 is the minimum size of end record
                    zip_content = data[pos:zip_end]
                    
                    zip_file_path = os.path.join(output_dir, f'embedded_{i+1}.zip')
                    with open(zip_file_path, 'wb') as f:
                        f.write(zip_content)
                    print(f"  Extracted ZIP to {zip_file_path}")
                    
                    # Try to extract the ZIP
                    import zipfile
                    try:
                        with zipfile.ZipFile(zip_file_path, 'r') as zf:
                            extract_dir = os.path.join(output_dir, f'zip_contents_{i+1}')
                            zf.extractall(extract_dir)
                            print(f"  Extracted ZIP contents to {extract_dir}")
                    except:
                        print(f"  Could not extract ZIP contents")
                        
            except Exception as e:
                print(f"  Failed to extract ZIP {i+1}: {e}")
    
    print(f"\nExtracted {extracted_count} code objects")
    print(f"Results saved to {output_dir}")

if __name__ == "__main__":
    if len(sys.argv) != 3:
        print("Usage: python advanced_extract.py <exe_file> <output_dir>")
        sys.exit(1)
        
    exe_path = sys.argv[1]
    output_dir = sys.argv[2]
    
    analyze_executable_advanced(exe_path, output_dir)
