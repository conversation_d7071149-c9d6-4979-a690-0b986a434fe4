# Code object from position 7242941
# Filename: __future__.py
# Name: <module>
# Args: 0
# Locals: 0

  0           0 RESUME                   0

  1           2 LOAD_CONST               0 ('Record of phased-in incompatible language changes.\n\nEach line is of the form:\n\n    FeatureName = "_Feature(" OptionalRelease "," MandatoryRelease ","\n                              CompilerFlag ")"\n\nwhere, normally, OptionalRelease < MandatoryRelease, and both are 5-tuples\nof the same form as sys.version_info:\n\n    (PY_MAJOR_VERSION, # the 2 in 2.1.0a3; an int\n     PY_MINOR_VERSION, # the 1; an int\n     PY_MICRO_VERSION, # the 0; an int\n     PY_RELEASE_LEVEL, # "alpha", "beta", "candidate" or "final"; string\n     PY_RELEASE_SERIAL # the 3; an int\n    )\n\nOptionalRelease records the first release in which\n\n    from __future__ import FeatureName\n\nwas accepted.\n\nIn the case of MandatoryReleases that have not yet occurred,\nMandatoryRelease predicts the release in which the feature will become part\nof the language.\n\nElse MandatoryRelease records when the feature became part of the language;\nin releases at or after that, modules no longer need\n\n    from __future__ import FeatureName\n\nto use the feature in question, but may continue to use such imports.\n\nMandatoryRelease may also be None, meaning that a planned feature got\ndropped or that the release version is undetermined.\n\nInstances of class _Feature have two corresponding methods,\n.getOptionalRelease() and .getMandatoryRelease().\n\nCompilerFlag is the (bitfield) flag that should be passed in the fourth\nargument to the builtin function compile() to enable the feature in\ndynamically compiled code.  This flag is stored in the .compiler_flag\nattribute on _Future instances.  These values must match the appropriate\n#defines of CO_xxx flags in Include/cpython/compile.h.\n\nNo feature line is ever to be deleted from this file.\n')
              4 STORE_NAME               0 (__doc__)

 50           6 BUILD_LIST               0
              8 LOAD_CONST               1 (('nested_scopes', 'generators', 'division', 'absolute_import', 'with_statement', 'print_function', 'unicode_literals', 'barry_as_FLUFL', 'generator_stop', 'annotations'))
             10 LIST_EXTEND              1
             12 STORE_NAME               1 (all_feature_names)

 63          14 LOAD_CONST               2 ('all_feature_names')
             16 BUILD_LIST               1
             18 LOAD_NAME                1 (all_feature_names)
             20 BINARY_OP                0 (+)
             24 STORE_NAME               2 (__all__)

 69          26 LOAD_CONST               3 (16)
             28 STORE_NAME               3 (CO_NESTED)

 70          30 LOAD_CONST               4 (0)
             32 STORE_NAME               4 (CO_GENERATOR_ALLOWED)

 71          34 LOAD_CONST               5 (131072)
             36 STORE_NAME               5 (CO_FUTURE_DIVISION)

 72          38 LOAD_CONST               6 (262144)
             40 STORE_NAME               6 (CO_FUTURE_ABSOLUTE_IMPORT)

 73          42 LOAD_CONST               7 (524288)
             44 STORE_NAME               7 (CO_FUTURE_WITH_STATEMENT)

 74          46 LOAD_CONST               8 (1048576)
             48 STORE_NAME               8 (CO_FUTURE_PRINT_FUNCTION)

 75          50 LOAD_CONST               9 (2097152)
             52 STORE_NAME               9 (CO_FUTURE_UNICODE_LITERALS)

 76          54 LOAD_CONST              10 (4194304)
             56 STORE_NAME              10 (CO_FUTURE_BARRY_AS_BDFL)

 77          58 LOAD_CONST              11 (8388608)
             60 STORE_NAME              11 (CO_FUTURE_GENERATOR_STOP)

 78          62 LOAD_CONST              12 (16777216)
             64 STORE_NAME              12 (CO_FUTURE_ANNOTATIONS)

 81          66 PUSH_NULL
             68 LOAD_BUILD_CLASS
             70 LOAD_CONST              13 (<code object _Feature at 0x000001A2D05C86C0, file "__future__.py", line 81>)
             72 MAKE_FUNCTION            0
             74 LOAD_CONST              14 ('_Feature')
             76 UNPACK_SEQUENCE          2
             80 CALL                     2
             88 CACHE
             90 STORE_NAME              13 (_Feature)

109          92 PUSH_NULL
             94 LOAD_NAME               13 (_Feature)
             96 LOAD_CONST              15 ((2, 1, 0, 'beta', 1))

110          98 LOAD_CONST              16 ((2, 2, 0, 'alpha', 0))

111         100 LOAD_NAME                3 (CO_NESTED)

109         102 UNPACK_SEQUENCE          3
            106 CALL                     3
            114 CACHE
            116 STORE_NAME              14 (nested_scopes)

113         118 PUSH_NULL
            120 LOAD_NAME               13 (_Feature)
            122 LOAD_CONST              17 ((2, 2, 0, 'alpha', 1))

114         124 LOAD_CONST              18 ((2, 3, 0, 'final', 0))

115         126 LOAD_NAME                4 (CO_GENERATOR_ALLOWED)

113         128 UNPACK_SEQUENCE          3
            132 CALL                     3
            140 CACHE
            142 STORE_NAME              15 (generators)

117         144 PUSH_NULL
            146 LOAD_NAME               13 (_Feature)
            148 LOAD_CONST              19 ((2, 2, 0, 'alpha', 2))

118         150 LOAD_CONST              20 ((3, 0, 0, 'alpha', 0))

119         152 LOAD_NAME                5 (CO_FUTURE_DIVISION)

117         154 UNPACK_SEQUENCE          3
            158 CALL                     3
            166 CACHE
            168 STORE_NAME              16 (division)

121         170 PUSH_NULL
            172 LOAD_NAME               13 (_Feature)
            174 LOAD_CONST              21 ((2, 5, 0, 'alpha', 1))

122         176 LOAD_CONST              20 ((3, 0, 0, 'alpha', 0))

123         178 LOAD_NAME                6 (CO_FUTURE_ABSOLUTE_IMPORT)

121         180 UNPACK_SEQUENCE          3
            184 CALL                     3
            192 CACHE
            194 STORE_NAME              17 (absolute_import)

125         196 PUSH_NULL
            198 LOAD_NAME               13 (_Feature)
            200 LOAD_CONST              21 ((2, 5, 0, 'alpha', 1))

126         202 LOAD_CONST              22 ((2, 6, 0, 'alpha', 0))

127         204 LOAD_NAME                7 (CO_FUTURE_WITH_STATEMENT)

125         206 UNPACK_SEQUENCE          3
            210 CALL                     3
            218 CACHE
            220 STORE_NAME              18 (with_statement)

129         222 PUSH_NULL
            224 LOAD_NAME               13 (_Feature)
            226 LOAD_CONST              23 ((2, 6, 0, 'alpha', 2))

130         228 LOAD_CONST              20 ((3, 0, 0, 'alpha', 0))

131         230 LOAD_NAME                8 (CO_FUTURE_PRINT_FUNCTION)

129         232 UNPACK_SEQUENCE          3
            236 CALL                     3
            244 CACHE
            246 STORE_NAME              19 (print_function)

133         248 PUSH_NULL
            250 LOAD_NAME               13 (_Feature)
            252 LOAD_CONST              23 ((2, 6, 0, 'alpha', 2))

134         254 LOAD_CONST              20 ((3, 0, 0, 'alpha', 0))

135         256 LOAD_NAME                9 (CO_FUTURE_UNICODE_LITERALS)

133         258 UNPACK_SEQUENCE          3
            262 CALL                     3
            270 CACHE
            272 STORE_NAME              20 (unicode_literals)

137         274 PUSH_NULL
            276 LOAD_NAME               13 (_Feature)
            278 LOAD_CONST              24 ((3, 1, 0, 'alpha', 2))

138         280 LOAD_CONST              25 ((4, 0, 0, 'alpha', 0))

139         282 LOAD_NAME               10 (CO_FUTURE_BARRY_AS_BDFL)

137         284 UNPACK_SEQUENCE          3
            288 CALL                     3
            296 CACHE
            298 STORE_NAME              21 (barry_as_FLUFL)

141         300 PUSH_NULL
            302 LOAD_NAME               13 (_Feature)
            304 LOAD_CONST              26 ((3, 5, 0, 'beta', 1))

142         306 LOAD_CONST              27 ((3, 7, 0, 'alpha', 0))

143         308 LOAD_NAME               11 (CO_FUTURE_GENERATOR_STOP)

141         310 UNPACK_SEQUENCE          3
            314 CALL                     3
            322 CACHE
            324 STORE_NAME              22 (generator_stop)

145         326 PUSH_NULL
            328 LOAD_NAME               13 (_Feature)
            330 LOAD_CONST              28 ((3, 7, 0, 'beta', 1))

146         332 LOAD_CONST              29 (None)

147         334 LOAD_NAME               12 (CO_FUTURE_ANNOTATIONS)

145         336 UNPACK_SEQUENCE          3
            340 CALL                     3
            348 CACHE
            350 STORE_NAME              23 (annotations)
            352 LOAD_CONST              29 (None)
            354 RETURN_VALUE

Disassembly of <code object _Feature at 0x000001A2D05C86C0, file "__future__.py", line 81>:
 81           0 RESUME                   0
              2 LOAD_NAME                0 (__name__)
              4 STORE_NAME               1 (__module__)
              6 LOAD_CONST               0 ('_Feature')
              8 STORE_NAME               2 (__qualname__)

 83          10 LOAD_CONST               1 (<code object __init__ at 0x000001A2D0117D20, file "__future__.py", line 83>)
             12 MAKE_FUNCTION            0
             14 STORE_NAME               3 (__init__)

 88          16 LOAD_CONST               2 (<code object getOptionalRelease at 0x000001A2D05C2F50, file "__future__.py", line 88>)
             18 MAKE_FUNCTION            0
             20 STORE_NAME               4 (getOptionalRelease)

 95          22 LOAD_CONST               3 (<code object getMandatoryRelease at 0x000001A2D05C2320, file "__future__.py", line 95>)
             24 MAKE_FUNCTION            0
             26 STORE_NAME               5 (getMandatoryRelease)

103          28 LOAD_CONST               4 (<code object __repr__ at 0x000001A2D0143110, file "__future__.py", line 103>)
             30 MAKE_FUNCTION            0
             32 STORE_NAME               6 (__repr__)
             34 LOAD_CONST               5 (None)
             36 RETURN_VALUE

Disassembly of <code object __init__ at 0x000001A2D0117D20, file "__future__.py", line 83>:
 83           0 RESUME                   0

 84           2 LOAD_FAST                1 (optionalRelease)
              4 LOAD_FAST                0 (self)
              6 STORE_ATTR               0 (optional)

 85          16 LOAD_FAST                2 (mandatoryRelease)
             18 LOAD_FAST                0 (self)
             20 STORE_ATTR               1 (mandatory)

 86          30 LOAD_FAST                3 (compiler_flag)
             32 LOAD_FAST                0 (self)
             34 STORE_ATTR               2 (compiler_flag)
             44 LOAD_CONST               0 (None)
             46 RETURN_VALUE

Disassembly of <code object getOptionalRelease at 0x000001A2D05C2F50, file "__future__.py", line 88>:
 88           0 RESUME                   0

 93           2 LOAD_FAST                0 (self)
              4 LOAD_ATTR                0 (optional)

Disassembly of <code object getMandatoryRelease at 0x000001A2D05C2320, file "__future__.py", line 95>:
 95           0 RESUME                   0

101           2 LOAD_FAST                0 (self)
              4 LOAD_ATTR                0 (mandatory)

Disassembly of <code object __repr__ at 0x000001A2D0143110, file "__future__.py", line 103>:
103           0 RESUME                   0

104           2 LOAD_CONST               1 ('_Feature')
              4 LOAD_GLOBAL              1 (NULL + repr)
             14 CACHE
             16 LOAD_FAST                0 (self)
             18 LOAD_ATTR                1 (NULL|self + repr)
             38 CACHE

106          40 LOAD_FAST                0 (self)
             42 LOAD_ATTR                3 (NULL|self + optional)
             62 CACHE
             64 CACHE
             66 CACHE
             68 BINARY_OP                0 (+)
             72 RETURN_VALUE


# Constants:
# 0: str
# 1: tuple
# 2: 'all_feature_names'
# 3: 16
# 4: 0
# 5: 131072
# 6: 262144
# 7: 524288
# 8: 1048576
# 9: 2097152
# 10: 4194304
# 11: 8388608
# 12: 16777216
# 13: code
# 14: '_Feature'
# 15: tuple
# 16: tuple
# 17: tuple
# 18: tuple
# 19: tuple
# 20: tuple
# 21: tuple
# 22: tuple
# 23: tuple
# 24: tuple
# 25: tuple
# 26: tuple
# 27: tuple
# 28: tuple
# 29: None


# Names:
# 0: '__doc__'
# 1: 'all_feature_names'
# 2: '__all__'
# 3: 'CO_NESTED'
# 4: 'CO_GENERATOR_ALLOWED'
# 5: 'CO_FUTURE_DIVISION'
# 6: 'CO_FUTURE_ABSOLUTE_IMPORT'
# 7: 'CO_FUTURE_WITH_STATEMENT'
# 8: 'CO_FUTURE_PRINT_FUNCTION'
# 9: 'CO_FUTURE_UNICODE_LITERALS'
# 10: 'CO_FUTURE_BARRY_AS_BDFL'
# 11: 'CO_FUTURE_GENERATOR_STOP'
# 12: 'CO_FUTURE_ANNOTATIONS'
# 13: '_Feature'
# 14: 'nested_scopes'
# 15: 'generators'
# 16: 'division'
# 17: 'absolute_import'
# 18: 'with_statement'
# 19: 'print_function'
# 20: 'unicode_literals'
# 21: 'barry_as_FLUFL'
# 22: 'generator_stop'
# 23: 'annotations'


# Variable names:
