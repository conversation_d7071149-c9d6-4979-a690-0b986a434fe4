# Code object from position 10207014
# Filename: genericpath.py
# Name: <module>
# Args: 0
# Locals: 0
# Stack size: 2
# Flags: 0

  0           0 RESUME                   0

  1           2 LOAD_CONST               0 ('\nPath operations common to more than one OS\nDo not use directly.  The OS specific modules import the appropriate\nfunctions from this module themselves.\n')
              4 STORE_NAME               0 (__doc__)

  6           6 LOAD_CONST               1 (0)
              8 LOAD_CONST               2 (None)
             10 IMPORT_NAME              1 (os)
             12 STORE_NAME               1 (os)

  7          14 LOAD_CONST               1 (0)
             16 LOAD_CONST               2 (None)
             18 IMPORT_NAME              2 (stat)
             20 STORE_NAME               2 (stat)

  9          22 BUILD_LIST               0
             24 LOAD_CONST               3 (('commonprefix', 'exists', 'getatime', 'getctime', 'getmtime', 'getsize', 'isdir', 'isfile', 'samefile', 'sameopenfile', 'samestat'))
             26 LIST_EXTEND              1
             28 STORE_NAME               3 (__all__)

 16          30 LOAD_CONST               4 (<code object exists at 0x000001B2A76B6D50, file "genericpath.py", line 16>)
             32 MAKE_FUNCTION            0
             34 STORE_NAME               4 (exists)

 27          36 LOAD_CONST               5 (<code object isfile at 0x000001B2A71C6B80, file "genericpath.py", line 27>)
             38 MAKE_FUNCTION            0
             40 STORE_NAME               5 (isfile)

 39          42 LOAD_CONST               6 (<code object isdir at 0x000001B2A71C70C0, file "genericpath.py", line 39>)
             44 MAKE_FUNCTION            0
             46 STORE_NAME               6 (isdir)

 48          48 LOAD_CONST               7 (<code object getsize at 0x000001B2A8A0EA30, file "genericpath.py", line 48>)
             50 MAKE_FUNCTION            0
             52 STORE_NAME               7 (getsize)

 53          54 LOAD_CONST               8 (<code object getmtime at 0x000001B2A8A0EB30, file "genericpath.py", line 53>)
             56 MAKE_FUNCTION            0
             58 STORE_NAME               8 (getmtime)

 58          60 LOAD_CONST               9 (<code object getatime at 0x000001B2A8A0ED30, file "genericpath.py", line 58>)
             62 MAKE_FUNCTION            0
             64 STORE_NAME               9 (getatime)

 63          66 LOAD_CONST              10 (<code object getctime at 0x000001B2A8A0EC30, file "genericpath.py", line 63>)
             68 MAKE_FUNCTION            0
             70 STORE_NAME              10 (getctime)

 69          72 LOAD_CONST              11 (<code object commonprefix at 0x000001B2A8A1D030, file "genericpath.py", line 69>)
             74 MAKE_FUNCTION            0
             76 STORE_NAME              11 (commonprefix)

 87          78 LOAD_CONST              12 (<code object samestat at 0x000001B2A76AECD0, file "genericpath.py", line 87>)
             80 MAKE_FUNCTION            0
             82 STORE_NAME              12 (samestat)

 94          84 LOAD_CONST              13 (<code object samefile at 0x000001B2A8A08170, file "genericpath.py", line 94>)
             86 MAKE_FUNCTION            0
             88 STORE_NAME              13 (samefile)

107          90 LOAD_CONST              14 (<code object sameopenfile at 0x000001B2A8A092F0, file "genericpath.py", line 107>)
             92 MAKE_FUNCTION            0
             94 STORE_NAME              14 (sameopenfile)

121          96 LOAD_CONST              15 (<code object _splitext at 0x000001B2A8A1CE30, file "genericpath.py", line 121>)
             98 MAKE_FUNCTION            0
            100 STORE_NAME              15 (_splitext)

144         102 LOAD_CONST              16 (<code object _check_arg_types at 0x000001B2A71CE250, file "genericpath.py", line 144>)
            104 MAKE_FUNCTION            0
            106 STORE_NAME              16 (_check_arg_types)
            108 LOAD_CONST               2 (None)
            110 RETURN_VALUE

Disassembly of <code object exists at 0x000001B2A76B6D50, file "genericpath.py", line 16>:
 16           0 RESUME                   0

 18           2 NOP

 19           4 LOAD_GLOBAL              1 (NULL + os)
             14 CACHE
             16 LOAD_ATTR                1 (NULL|self + os)
             36 CACHE
             38 CACHE
             40 CACHE
             42 POP_TOP
             44 JUMP_FORWARD            24 (to 94)
        >>   46 PUSH_EXC_INFO

 20          48 LOAD_GLOBAL              4 (OSError)
             58 CACHE
             60 LOAD_GLOBAL              6 (ValueError)
             70 CACHE
             72 BUILD_TUPLE              2
             74 CHECK_EXC_MATCH
             76 POP_JUMP_IF_FALSE        4 (to 86)
             78 POP_TOP

 21          80 POP_EXCEPT
             82 LOAD_CONST               1 (False)
             84 RETURN_VALUE

 20     >>   86 RERAISE                  0
        >>   88 COPY                     3
             90 POP_EXCEPT
             92 RERAISE                  1

 22     >>   94 LOAD_CONST               2 (True)
             96 RETURN_VALUE
ExceptionTable:
  4 to 42 -> 46 [0]
  46 to 78 -> 88 [1] lasti
  86 to 86 -> 88 [1] lasti

Disassembly of <code object isfile at 0x000001B2A71C6B80, file "genericpath.py", line 27>:
 27           0 RESUME                   0

 29           2 NOP

 30           4 LOAD_GLOBAL              1 (NULL + os)
             14 CACHE
             16 LOAD_ATTR                1 (NULL|self + os)
             36 CACHE
             38 CACHE
             40 CACHE
             42 STORE_FAST               1 (st)
             44 JUMP_FORWARD            24 (to 94)
        >>   46 PUSH_EXC_INFO

 31          48 LOAD_GLOBAL              4 (OSError)
             58 CACHE
             60 LOAD_GLOBAL              6 (ValueError)
             70 CACHE
             72 BUILD_TUPLE              2
             74 CHECK_EXC_MATCH
             76 POP_JUMP_IF_FALSE        4 (to 86)
             78 POP_TOP

 32          80 POP_EXCEPT
             82 LOAD_CONST               1 (False)
             84 RETURN_VALUE

 31     >>   86 RERAISE                  0
        >>   88 COPY                     3
             90 POP_EXCEPT
             92 RERAISE                  1

 33     >>   94 LOAD_GLOBAL              3 (NULL + stat)
            104 CACHE
            106 LOAD_ATTR                4 (OSError)
            126 CACHE
            128 UNPACK_SEQUENCE          1
            132 CALL                     1
            140 CACHE
            142 RETURN_VALUE
ExceptionTable:
  4 to 42 -> 46 [0]
  46 to 78 -> 88 [1] lasti
  86 to 86 -> 88 [1] lasti

Disassembly of <code object isdir at 0x000001B2A71C70C0, file "genericpath.py", line 39>:
 39           0 RESUME                   0

 41           2 NOP

 42           4 LOAD_GLOBAL              1 (NULL + os)
             14 CACHE
             16 LOAD_ATTR                1 (NULL|self + os)
             36 CACHE
             38 CACHE
             40 CACHE
             42 STORE_FAST               1 (st)
             44 JUMP_FORWARD            24 (to 94)
        >>   46 PUSH_EXC_INFO

 43          48 LOAD_GLOBAL              4 (OSError)
             58 CACHE
             60 LOAD_GLOBAL              6 (ValueError)
             70 CACHE
             72 BUILD_TUPLE              2
             74 CHECK_EXC_MATCH
             76 POP_JUMP_IF_FALSE        4 (to 86)
             78 POP_TOP

 44          80 POP_EXCEPT
             82 LOAD_CONST               1 (False)
             84 RETURN_VALUE

 43     >>   86 RERAISE                  0
        >>   88 COPY                     3
             90 POP_EXCEPT
             92 RERAISE                  1

 45     >>   94 LOAD_GLOBAL              3 (NULL + stat)
            104 CACHE
            106 LOAD_ATTR                4 (OSError)
            126 CACHE
            128 UNPACK_SEQUENCE          1
            132 CALL                     1
            140 CACHE
            142 RETURN_VALUE
ExceptionTable:
  4 to 42 -> 46 [0]
  46 to 78 -> 88 [1] lasti
  86 to 86 -> 88 [1] lasti

Disassembly of <code object getsize at 0x000001B2A8A0EA30, file "genericpath.py", line 48>:
 48           0 RESUME                   0

 50           2 LOAD_GLOBAL              1 (NULL + os)
             12 CACHE
             14 LOAD_ATTR                1 (NULL|self + os)
             34 CACHE
             36 CACHE
             38 CACHE
             40 LOAD_ATTR                2 (stat)

Disassembly of <code object getmtime at 0x000001B2A8A0EB30, file "genericpath.py", line 53>:
 53           0 RESUME                   0

 55           2 LOAD_GLOBAL              1 (NULL + os)
             12 CACHE
             14 LOAD_ATTR                1 (NULL|self + os)
             34 CACHE
             36 CACHE
             38 CACHE
             40 LOAD_ATTR                2 (stat)

Disassembly of <code object getatime at 0x000001B2A8A0ED30, file "genericpath.py", line 58>:
 58           0 RESUME                   0

 60           2 LOAD_GLOBAL              1 (NULL + os)
             12 CACHE
             14 LOAD_ATTR                1 (NULL|self + os)
             34 CACHE
             36 CACHE
             38 CACHE
             40 LOAD_ATTR                2 (stat)

Disassembly of <code object getctime at 0x000001B2A8A0EC30, file "genericpath.py", line 63>:
 63           0 RESUME                   0

 65           2 LOAD_GLOBAL              1 (NULL + os)
             12 CACHE
             14 LOAD_ATTR                1 (NULL|self + os)
             34 CACHE
             36 CACHE
             38 CACHE
             40 LOAD_ATTR                2 (stat)

Disassembly of <code object commonprefix at 0x000001B2A8A1D030, file "genericpath.py", line 69>:
 69           0 RESUME                   0

 71           2 LOAD_FAST                0 (m)
              4 POP_JUMP_IF_TRUE         2 (to 10)
              6 LOAD_CONST               1 ('')
              8 RETURN_VALUE

 76     >>   10 LOAD_GLOBAL              1 (NULL + isinstance)
             20 CACHE
             22 LOAD_FAST                0 (m)
             24 LOAD_CONST               2 (0)
             26 BINARY_SUBSCR
             30 CACHE
             32 CACHE
             34 CACHE
             36 LOAD_GLOBAL              2 (list)
             46 CACHE
             48 LOAD_GLOBAL              4 (tuple)
             58 CACHE
             60 BUILD_TUPLE              2
             62 UNPACK_SEQUENCE          2
             66 CALL                     2
             74 CACHE
             76 POP_JUMP_IF_TRUE        39 (to 156)

 77          78 LOAD_GLOBAL              5 (NULL + tuple)
             88 CACHE
             90 LOAD_GLOBAL              7 (NULL + map)
            100 CACHE
            102 LOAD_GLOBAL              8 (os)
            112 CACHE
            114 LOAD_ATTR                5 (NULL|self + tuple)
            134 CACHE
            136 CACHE
            138 CACHE
            140 UNPACK_SEQUENCE          1
            144 CALL                     1
            152 CACHE
            154 STORE_FAST               0 (m)

 78     >>  156 LOAD_GLOBAL             13 (NULL + min)
            166 CACHE
            168 LOAD_FAST                0 (m)
            170 UNPACK_SEQUENCE          1
            174 CALL                     1
            182 CACHE
            184 STORE_FAST               1 (s1)

 79         186 LOAD_GLOBAL             15 (NULL + max)
            196 CACHE
            198 LOAD_FAST                0 (m)
            200 UNPACK_SEQUENCE          1
            204 CALL                     1
            212 CACHE
            214 STORE_FAST               2 (s2)

 80         216 LOAD_GLOBAL             17 (NULL + enumerate)
            226 CACHE
            228 LOAD_FAST                1 (s1)
            230 UNPACK_SEQUENCE          1
            234 CALL                     1
            242 CACHE
            244 GET_ITER
        >>  246 FOR_ITER                29 (to 308)
            250 CACHE
            252 STORE_FAST               3 (i)
            254 STORE_FAST               4 (c)

 81         256 LOAD_FAST                4 (c)
            258 LOAD_FAST                2 (s2)
            260 LOAD_FAST                3 (i)
            262 BINARY_SUBSCR
            266 CACHE
            268 CACHE
            270 CACHE
            272 COMPARE_OP               3 (<)
            276 CACHE
            278 POP_JUMP_IF_FALSE       12 (to 304)

 82         280 LOAD_FAST                1 (s1)
            282 LOAD_CONST               3 (None)
            284 LOAD_FAST                3 (i)
            286 BUILD_SLICE              2
            288 BINARY_SUBSCR
            292 CACHE
            294 CACHE
            296 CACHE
            298 SWAP                     2
            300 POP_TOP
            302 RETURN_VALUE

 81     >>  304 JUMP_BACKWARD           30 (to 246)

 83         306 LOAD_FAST                1 (s1)
        >>  308 RETURN_VALUE

Disassembly of <code object samestat at 0x000001B2A76AECD0, file "genericpath.py", line 87>:
 87           0 RESUME                   0

 89           2 LOAD_FAST                0 (s1)
              4 LOAD_ATTR                0 (st_ino)
             24 CACHE
             26 COMPARE_OP               2 (<)
             30 CACHE
