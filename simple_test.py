#!/usr/bin/env python3
"""
Simple test to verify the normal map algorithms work
"""

import sys
import os

# Test if we can import the basic modules
try:
    import numpy as np
    print("✓ NumPy imported successfully")
except ImportError as e:
    print(f"✗ Failed to import NumPy: {e}")
    sys.exit(1)

try:
    import cv2
    print("✓ OpenCV imported successfully")
except ImportError as e:
    print(f"✗ Failed to import OpenCV: {e}")
    sys.exit(1)

# Test basic functionality
def test_basic_functions():
    """Test basic image processing functions"""
    print("\nTesting basic functions:")
    
    # Test normalize_intensity function
    def normalize_intensity(image):
        if image.dtype == np.uint8:
            return image.astype(np.float32) / 255.0
        elif image.dtype == np.uint16:
            return image.astype(np.float32) / 65535.0
        else:
            return None
    
    # Test with 8-bit image
    test_8bit = np.array([[0, 127, 255]], dtype=np.uint8)
    result_8bit = normalize_intensity(test_8bit)
    if result_8bit is not None:
        print(f"✓ 8-bit normalization: {test_8bit} -> {result_8bit}")
    else:
        print("✗ 8-bit normalization failed")
    
    # Test with 16-bit image
    test_16bit = np.array([[0, 32767, 65535]], dtype=np.uint16)
    result_16bit = normalize_intensity(test_16bit)
    if result_16bit is not None:
        print(f"✓ 16-bit normalization: {test_16bit} -> {result_16bit}")
    else:
        print("✗ 16-bit normalization failed")

def test_gradient_computation():
    """Test gradient computation for normal maps"""
    print("\nTesting gradient computation:")
    
    # Create a simple height map (pyramid)
    size = 64
    height_map = np.zeros((size, size), dtype=np.float32)
    center = size // 2
    
    for y in range(size):
        for x in range(size):
            dist = np.sqrt((x - center)**2 + (y - center)**2)
            height_map[y, x] = max(0, 1.0 - dist / 20.0)
    
    print(f"Created test height map: {height_map.shape}")
    
    # Compute gradients
    grad_x = cv2.Sobel(height_map, cv2.CV_32F, 1, 0, ksize=3)
    grad_y = cv2.Sobel(height_map, cv2.CV_32F, 0, 1, ksize=3)
    
    print(f"✓ Computed gradients: X={grad_x.shape}, Y={grad_y.shape}")
    print(f"  Gradient X range: [{np.min(grad_x):.3f}, {np.max(grad_x):.3f}]")
    print(f"  Gradient Y range: [{np.min(grad_y):.3f}, {np.max(grad_y):.3f}]")
    
    # Create normal map
    normal_map = np.zeros((size, size, 3), dtype=np.float32)
    normal_map[:, :, 0] = -grad_x  # X component
    normal_map[:, :, 1] = -grad_y  # Y component
    normal_map[:, :, 2] = 1.0      # Z component
    
    # Normalize vectors
    for y in range(size):
        for x in range(size):
            normal = normal_map[y, x]
            length = np.linalg.norm(normal)
            if length > 0:
                normal_map[y, x] = normal / length
            else:
                normal_map[y, x] = [0.0, 0.0, 1.0]
    
    print("✓ Created normal map from gradients")
    
    # Convert to display format
    display_normal = (normal_map + 1.0) * 0.5 * 255.0
    display_normal = display_normal.astype(np.uint8)
    
    print(f"✓ Converted to display format: {display_normal.shape}")
    print(f"  Display range: [{np.min(display_normal)}, {np.max(display_normal)}]")
    
    return True

def test_photometric_stereo():
    """Test photometric stereo computation"""
    print("\nTesting photometric stereo:")
    
    # Define light directions
    light_directions = np.array([
        [-1.0, 0.0, 1.0],   # Left
        [1.0, 0.0, 1.0],    # Right
        [0.0, -1.0, 1.0],   # Top
        [0.0, 1.0, 1.0],    # Bottom
        [0.0, 0.0, 1.0],    # Front
        [0.0, 0.0, -1.0]    # Back
    ])
    
    # Normalize light directions
    for i in range(len(light_directions)):
        light_directions[i] = light_directions[i] / np.linalg.norm(light_directions[i])
    
    print(f"✓ Created {len(light_directions)} light directions")
    
    # Test solving for a known normal
    test_normal = np.array([0.5, 0.3, 0.8])
    test_normal = test_normal / np.linalg.norm(test_normal)
    
    # Generate synthetic intensities
    intensities = np.maximum(0, light_directions @ test_normal)
    
    print(f"✓ Generated synthetic intensities: {intensities}")
    
    # Solve for normal using least squares
    try:
        solved_normal = np.linalg.lstsq(light_directions, intensities, rcond=None)[0]
        solved_normal = solved_normal / np.linalg.norm(solved_normal)
        
        # Check if solution is close to original
        error = np.linalg.norm(solved_normal - test_normal)
        print(f"✓ Solved normal: {solved_normal}")
        print(f"  Original normal: {test_normal}")
        print(f"  Error: {error:.6f}")
        
        if error < 0.01:
            print("✓ Photometric stereo test passed")
            return True
        else:
            print("✗ Photometric stereo test failed (high error)")
            return False
            
    except np.linalg.LinAlgError:
        print("✗ Photometric stereo failed (singular matrix)")
        return False

def main():
    """Main test function"""
    print("Simple Normal Map Algorithm Test")
    print("=" * 40)
    
    try:
        # Test basic functions
        test_basic_functions()
        
        # Test gradient computation
        if not test_gradient_computation():
            print("✗ Gradient computation test failed")
            return False
        
        # Test photometric stereo
        if not test_photometric_stereo():
            print("✗ Photometric stereo test failed")
            return False
        
        print("\n" + "=" * 40)
        print("✓ All tests passed!")
        print("The normal map algorithms should work correctly.")
        
        return True
        
    except Exception as e:
        print(f"\n✗ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
