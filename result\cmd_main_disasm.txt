# MAIN APPLICATION CODE OBJECT
# Position: 8182550
# Filename: cmd.py
# Function: <module>
# Args: 0
# Locals: 0
# Stack size: 4
# Flags: 0

# BYTECODE DISASSEMBLY:
==================================================
  0           0 RESUME                   0

  1           2 LOAD_CONST               0 ('A generic class to build line-oriented command interpreters.\n\nInterpreters constructed with this class obey the following conventions:\n\n1. End of file on input is processed as the command \'EOF\'.\n2. A command is parsed out of each line by collecting the prefix composed\n   of characters in the identchars member.\n3. A command `foo\' is dispatched to a method \'do_foo()\'; the do_ method\n   is passed a single argument consisting of the remainder of the line.\n4. Typing an empty line repeats the last command.  (Actually, it calls the\n   method `emptyline\', which may be overridden in a subclass.)\n5. There is a predefined `help\' method.  Given an argument `topic\', it\n   calls the command `help_topic\'.  With no arguments, it lists all topics\n   with defined help_ functions, broken into up to three topics; documented\n   commands, miscellaneous help topics, and undocumented commands.\n6. The command \'?\' is a synonym for `help\'.  The command \'!\' is a synonym\n   for `shell\', if a do_shell method exists.\n7. If completion is enabled, completing commands will be done automatically,\n   and completing of commands args is done by calling complete_foo() with\n   arguments text, line, begidx, endidx.  text is string we are matching\n   against, all returned matches must begin with it.  line is the current\n   input line (lstripped), begidx and endidx are the beginning and end\n   indexes of the text being matched, which could be used to provide\n   different completion depending upon which position the argument is in.\n\nThe `default\' method may be overridden to intercept commands for which there\nis no do_ method.\n\nThe `completedefault\' method may be overridden to intercept completions for\ncommands that have no complete_ method.\n\nThe data member `self.ruler\' sets the character used to draw separator lines\nin the help messages.  If empty, no ruler line is drawn.  It defaults to "=".\n\nIf the value of `self.intro\' is nonempty when the cmdloop method is called,\nit is printed out on interpreter startup.  This value may be overridden\nvia an optional argument to the cmdloop() method.\n\nThe data members `self.doc_header\', `self.misc_header\', and\n`self.undoc_header\' set the headers used for the help function\'s\nlistings of documented functions, miscellaneous topics, and undocumented\nfunctions respectively.\n')
              4 STORE_NAME               0 (__doc__)

 45           6 LOAD_CONST               1 (0)
              8 LOAD_CONST               2 (None)
             10 IMPORT_NAME              1 (string)
             12 STORE_NAME               1 (string)
             14 LOAD_CONST               1 (0)
             16 LOAD_CONST               2 (None)
             18 IMPORT_NAME              2 (sys)
             20 STORE_NAME               2 (sys)

 47          22 LOAD_CONST               3 ('Cmd')
             24 BUILD_LIST               1
             26 STORE_NAME               3 (__all__)

 49          28 LOAD_CONST               4 ('(Cmd) ')
             30 STORE_NAME               4 (PROMPT)

 50          32 LOAD_NAME                1 (string)
             34 LOAD_ATTR                5 (NULL|self + sys)
             54 CACHE
             56 BINARY_OP                0 (+)
             60 LOAD_CONST               5 ('_')
             62 BINARY_OP                0 (+)
             66 STORE_NAME               7 (IDENTCHARS)

 52          68 PUSH_NULL
             70 LOAD_BUILD_CLASS
             72 LOAD_CONST               6 (<code object Cmd at 0x000001E77ECB6B50, file "cmd.py", line 52>)
             74 MAKE_FUNCTION            0
             76 LOAD_CONST               3 ('Cmd')
             78 UNPACK_SEQUENCE          2
             82 CALL                     2
             90 CACHE
             92 STORE_NAME               8 (Cmd)
             94 LOAD_CONST               2 (None)
             96 RETURN_VALUE

Disassembly of <code object Cmd at 0x000001E77ECB6B50, file "cmd.py", line 52>:
 52           0 RESUME                   0
              2 LOAD_NAME                0 (__name__)
              4 STORE_NAME               1 (__module__)
              6 LOAD_CONST               0 ('Cmd')
              8 STORE_NAME               2 (__qualname__)

 53          10 LOAD_CONST               1 ("A simple framework for writing line-oriented command interpreters.\n\n    These are often useful for test harnesses, administrative tools, and\n    prototypes that will later be wrapped in a more sophisticated interface.\n\n    A Cmd instance or subclass instance is a line-oriented interpreter\n    framework.  There is no good reason to instantiate Cmd itself; rather,\n    it's useful as a superclass of an interpreter class you define yourself\n    in order to inherit Cmd's methods and encapsulate action methods.\n\n    ")
             12 STORE_NAME               3 (__doc__)

 64          14 LOAD_NAME                4 (PROMPT)
             16 STORE_NAME               5 (prompt)

 65          18 LOAD_NAME                6 (IDENTCHARS)
             20 STORE_NAME               7 (identchars)

 66          22 LOAD_CONST               2 ('=')
             24 STORE_NAME               8 (ruler)

 67          26 LOAD_CONST               3 ('')
             28 STORE_NAME               9 (lastcmd)

 68          30 LOAD_CONST               4 (None)
             32 STORE_NAME              10 (intro)

 69          34 LOAD_CONST               3 ('')
             36 STORE_NAME              11 (doc_leader)

 70          38 LOAD_CONST               5 ('Documented commands (type help <topic>):')
             40 STORE_NAME              12 (doc_header)

 71          42 LOAD_CONST               6 ('Miscellaneous help topics:')
             44 STORE_NAME              13 (misc_header)

 72          46 LOAD_CONST               7 ('Undocumented commands:')
             48 STORE_NAME              14 (undoc_header)

 73          50 LOAD_CONST               8 ('*** No help on %s')
             52 STORE_NAME              15 (nohelp)

 74          54 LOAD_CONST               9 (1)
             56 STORE_NAME              16 (use_rawinput)

 76          58 LOAD_CONST              30 (('tab', None, None))
             60 LOAD_CONST              11 (<code object __init__ at 0x000001E77ED18180, file "cmd.py", line 76>)
             62 MAKE_FUNCTION            1 (defaults)
             64 STORE_NAME              17 (__init__)

 98          66 LOAD_CONST              31 ((None,))
             68 LOAD_CONST              12 (<code object cmdloop at 0x000001E77EF28090, file "cmd.py", line 98>)
             70 MAKE_FUNCTION            1 (defaults)
             72 STORE_NAME              18 (cmdloop)

150          74 LOAD_CONST              13 (<code object precmd at 0x000001E77ED109F0, file "cmd.py", line 150>)
             76 MAKE_FUNCTION            0
             78 STORE_NAME              19 (precmd)

157          80 LOAD_CONST              14 (<code object postcmd at 0x000001E77ED10AC0, file "cmd.py", line 157>)
             82 MAKE_FUNCTION            0
             84 STORE_NAME              20 (postcmd)

161          86 LOAD_CONST              15 (<code object preloop at 0x000001E77ED10B90, file "cmd.py", line 161>)
             88 MAKE_FUNCTION            0
             90 STORE_NAME              21 (preloop)

165          92 LOAD_CONST              16 (<code object postloop at 0x000001E77ED10C60, file "cmd.py", line 165>)
             94 MAKE_FUNCTION            0
             96 STORE_NAME              22 (postloop)

172          98 LOAD_CONST              17 (<code object parseline at 0x000001E77E9F6340, file "cmd.py", line 172>)
            100 MAKE_FUNCTION            0
            102 STORE_NAME              23 (parseline)

192         104 LOAD_CONST              18 (<code object onecmd at 0x000001E77EF57040, file "cmd.py", line 192>)
            106 MAKE_FUNCTION            0
            108 STORE_NAME              24 (onecmd)

219         110 LOAD_CONST              19 (<code object emptyline at 0x000001E77ECF2670, file "cmd.py", line 219>)
            112 MAKE_FUNCTION            0
            114 STORE_NAME              25 (emptyline)

229         116 LOAD_CONST              20 (<code object default at 0x000001E77ECDA430, file "cmd.py", line 229>)
            118 MAKE_FUNCTION            0
            120 STORE_NAME              26 (default)

238         122 LOAD_CONST              21 (<code object completedefault at 0x000001E77ED10ED0, file "cmd.py", line 238>)
            124 MAKE_FUNCTION            0
            126 STORE_NAME              27 (completedefault)

247         128 LOAD_CONST              22 (<code object completenames at 0x000001E77ECF2890, file "cmd.py", line 247>)
            130 MAKE_FUNCTION            0
            132 STORE_NAME              28 (completenames)

251         134 LOAD_CONST              23 (<code object complete at 0x000001E77EF240E0, file "cmd.py", line 251>)
            136 MAKE_FUNCTION            0
            138 STORE_NAME              29 (complete)

281         140 LOAD_CONST              24 (<code object get_names at 0x000001E77ECF5D40, file "cmd.py", line 281>)
            142 MAKE_FUNCTION            0
            144 STORE_NAME              30 (get_names)

286         146 LOAD_CONST              25 (<code object complete_help at 0x000001E77ECB69E0, file "cmd.py", line 286>)
            148 MAKE_FUNCTION            0
            150 STORE_NAME              31 (complete_help)

292         152 LOAD_CONST              26 (<code object do_help at 0x000001E77EF52750, file "cmd.py", line 292>)
            154 MAKE_FUNCTION            0
            156 STORE_NAME              32 (do_help)

338         158 LOAD_CONST              27 (<code object print_topics at 0x000001E77EF6D580, file "cmd.py", line 338>)
            160 MAKE_FUNCTION            0
            162 STORE_NAME              33 (print_topics)

346         164 LOAD_CONST              32 ((80,))
            166 LOAD_CONST              29 (<code object columnize at 0x000001E77E959E50, file "cmd.py", line 346>)
            168 MAKE_FUNCTION            1 (defaults)
            170 STORE_NAME              34 (columnize)
            172 LOAD_CONST               4 (None)
            174 RETURN_VALUE

Disassembly of <code object __init__ at 0x000001E77ED18180, file "cmd.py", line 76>:
 76           0 RESUME                   0

 87           2 LOAD_FAST                2 (stdin)
              4 POP_JUMP_IF_NONE         8 (to 22)

 88           6 LOAD_FAST                2 (stdin)
              8 LOAD_FAST                0 (self)
             10 STORE_ATTR               0 (stdin)
             20 JUMP_FORWARD            17 (to 56)

 90     >>   22 LOAD_GLOBAL              2 (sys)
             32 CACHE
             34 LOAD_ATTR                0 (stdin)
             54 CACHE

 91     >>   56 LOAD_FAST                3 (stdout)
             58 POP_JUMP_IF_NONE         8 (to 76)

 92          60 LOAD_FAST                3 (stdout)
             62 LOAD_FAST                0 (self)
             64 STORE_ATTR               2 (stdout)
             74 JUMP_FORWARD            17 (to 110)

 94     >>   76 LOAD_GLOBAL              2 (sys)
             86 CACHE
             88 LOAD_ATTR                2 (sys)
            108 CACHE

 95     >>  110 BUILD_LIST               0
            112 LOAD_FAST                0 (self)
            114 STORE_ATTR               3 (cmdqueue)

 96         124 LOAD_FAST                1 (completekey)
            126 LOAD_FAST                0 (self)
            128 STORE_ATTR               4 (completekey)
            138 LOAD_CONST               1 (None)
            140 RETURN_VALUE

Disassembly of <code object cmdloop at 0x000001E77EF28090, file "cmd.py", line 98>:
 98           0 RESUME                   0

105           2 LOAD_FAST                0 (self)
              4 STORE_SUBSCR
              8 CACHE
             10 CACHE
             12 CACHE
             14 CACHE
             16 CACHE
             18 CACHE
             20 CACHE
             22 CACHE
             24 CACHE
             26 UNPACK_SEQUENCE          0
             30 CALL                     0
             38 CACHE
             40 POP_TOP

106          42 LOAD_FAST                0 (self)
             44 LOAD_ATTR                1 (NULL|self + preloop)
             64 CACHE
             66 CACHE
             68 POP_JUMP_IF_FALSE      102 (to 274)

107          70 NOP

108          72 LOAD_CONST               1 (0)
             74 LOAD_CONST               2 (None)
             76 IMPORT_NAME              3 (readline)
             78 STORE_FAST               2 (readline)

109          80 LOAD_FAST                2 (readline)
             82 STORE_SUBSCR
             86 CACHE
             88 CACHE
             90 CACHE
             92 CACHE
             94 CACHE
             96 CACHE
             98 CACHE
            100 CACHE
            102 CACHE
            104 UNPACK_SEQUENCE          0
            108 CALL                     0
            116 CACHE
            118 LOAD_FAST                0 (self)
            120 STORE_ATTR               5 (old_completer)

110         130 LOAD_FAST                2 (readline)
            132 STORE_SUBSCR
            136 CACHE
            138 CACHE
            140 CACHE
            142 CACHE
            144 CACHE
            146 CACHE
            148 CACHE
            150 CACHE
            152 CACHE
            154 LOAD_FAST                0 (self)
            156 LOAD_ATTR                7 (NULL|self + readline)
            176 CACHE
            178 CACHE
            180 POP_TOP

111         182 LOAD_FAST                2 (readline)
            184 STORE_SUBSCR
            188 CACHE
            190 CACHE
            192 CACHE
            194 CACHE
            196 CACHE
            198 CACHE
            200 CACHE
            202 CACHE
            204 CACHE
            206 LOAD_FAST                0 (self)
            208 LOAD_ATTR                2 (use_rawinput)
            228 CALL                     1
            236 CACHE
            238 POP_TOP
            240 JUMP_FORWARD            16 (to 274)
        >>  242 PUSH_EXC_INFO

112         244 LOAD_GLOBAL             18 (ImportError)
            254 CACHE
            256 CHECK_EXC_MATCH
            258 POP_JUMP_IF_FALSE        3 (to 266)
            260 POP_TOP

113         262 POP_EXCEPT
            264 JUMP_FORWARD             4 (to 274)

112     >>  266 RERAISE                  0
        >>  268 COPY                     3
            270 POP_EXCEPT
            272 RERAISE                  1

114     >>  274 NOP

115         276 LOAD_FAST                1 (intro)
            278 POP_JUMP_IF_NONE         7 (to 294)

116         280 LOAD_FAST                1 (intro)
            282 LOAD_FAST                0 (self)
            284 STORE_ATTR              10 (intro)

117     >>  294 LOAD_FAST                0 (self)
            296 LOAD_ATTR               10 (old_completer)
            316 CACHE
            318 CACHE
            320 STORE_SUBSCR
            324 CACHE
            326 CACHE
            328 CACHE
            330 CACHE
            332 CACHE
            334 CACHE
            336 CACHE
            338 CACHE
            340 CACHE
            342 LOAD_GLOBAL             27 (NULL + str)
            352 CACHE
            354 LOAD_FAST                0 (self)
            356 LOAD_ATTR               10 (old_completer)
            376 CACHE
            378 CACHE
            380 LOAD_CONST               4 ('\n')
            382 BINARY_OP                0 (+)
            386 UNPACK_SEQUENCE          1
            390 CALL                     1
            398 CACHE
            400 POP_TOP

119         402 LOAD_CONST               2 (None)
            404 STORE_FAST               3 (stop)

120         406 LOAD_FAST                3 (stop)
            408 EXTENDED_ARG             1
            410 POP_JUMP_IF_TRUE       268 (to 948)

121         412 LOAD_FAST                0 (self)
            414 LOAD_ATTR               14 (complete)
            434 CACHE
            436 CACHE
            438 STORE_SUBSCR
            442 CACHE
            444 CACHE
            446 CACHE
            448 CACHE
            450 CACHE
            452 CACHE
            454 CACHE
            456 CACHE
            458 CACHE
            460 LOAD_CONST               1 (0)
            462 UNPACK_SEQUENCE          1
            466 CALL                     1
            474 CACHE
            476 STORE_FAST               4 (line)
            478 JUMP_FORWARD           167 (to 814)

124         480 LOAD_FAST                0 (self)
            482 LOAD_ATTR                1 (NULL|self + preloop)
            502 CACHE
            504 CACHE
            506 CACHE
            508 LOAD_FAST                0 (self)
            510 LOAD_ATTR               17 (NULL|self + parse_and_bind)
            530 CACHE
            532 CACHE
            534 STORE_FAST               4 (line)
            536 JUMP_FORWARD           138 (to 814)
        >>  538 PUSH_EXC_INFO

127         540 LOAD_GLOBAL             36 (EOFError)
            550 CACHE
            552 CHECK_EXC_MATCH
            554 POP_JUMP_IF_FALSE        5 (to 566)
            556 POP_TOP

128         558 LOAD_CONST               5 ('EOF')
            560 STORE_FAST               4 (line)
            562 POP_EXCEPT
            564 JUMP_FORWARD           124 (to 814)

127     >>  566 RERAISE                  0
        >>  568 COPY                     3
            570 POP_EXCEPT
            572 RERAISE                  1

130         574 LOAD_FAST                0 (self)
            576 LOAD_ATTR               11 (NULL|self + old_completer)
            596 CACHE
            598 CACHE
            600 CACHE
            602 CACHE
            604 CACHE
            606 CACHE
            608 LOAD_FAST                0 (self)
            610 LOAD_ATTR               17 (NULL|self + parse_and_bind)
            630 CACHE
            632 CACHE
            634 POP_TOP

131         636 LOAD_FAST                0 (self)
            638 LOAD_ATTR               11 (NULL|self + old_completer)
            658 CACHE
            660 CACHE
            662 CACHE
            664 CACHE
            666 CACHE
            668 CACHE
            670 UNPACK_SEQUENCE          0
            674 CALL                     0
            682 CACHE
            684 POP_TOP

132         686 LOAD_FAST                0 (self)
            688 LOAD_ATTR               20 (intro)
            708 CACHE
            710 CACHE
            712 CACHE
            714 CACHE
            716 CACHE
            718 CACHE
            720 UNPACK_SEQUENCE          0
            724 CALL                     0
            732 CACHE
            734 STORE_FAST               4 (line)

133         736 LOAD_GLOBAL             43 (NULL + len)
            746 CACHE
            748 LOAD_FAST                4 (line)
            750 UNPACK_SEQUENCE          1
            754 CALL                     1
            762 CACHE
            764 POP_JUMP_IF_TRUE         3 (to 772)

134         766 LOAD_CONST               5 ('EOF')
            768 STORE_FAST               4 (line)
            770 JUMP_FORWARD            21 (to 814)

136     >>  772 LOAD_FAST                4 (line)
            774 STORE_SUBSCR
            778 CACHE
            780 CACHE
            782 CACHE
            784 CACHE
            786 CACHE
            788 CACHE
            790 CACHE
            792 CACHE
            794 CACHE
            796 LOAD_CONST               6 ('\r\n')
            798 UNPACK_SEQUENCE          1
            802 CALL                     1
            810 CACHE
            812 STORE_FAST               4 (line)

137     >>  814 LOAD_FAST                0 (self)
            816 STORE_SUBSCR
            820 CACHE
            822 CACHE
            824 CACHE
            826 CACHE
            828 CACHE
            830 CACHE
            832 CACHE
            834 CACHE
            836 CACHE
            838 LOAD_FAST                4 (line)
            840 UNPACK_SEQUENCE          1
            844 CALL                     1
            852 CACHE
            854 STORE_FAST               4 (line)

138         856 LOAD_FAST                0 (self)
            858 STORE_SUBSCR
            862 CACHE
            864 CACHE
            866 CACHE
            868 CACHE
            870 CACHE
            872 CACHE
            874 CACHE
            876 CACHE
            878 CACHE
            880 LOAD_FAST                4 (line)
            882 UNPACK_SEQUENCE          1
            886 CALL                     1
            894 CACHE
            896 STORE_FAST               3 (stop)

139         898 LOAD_FAST                0 (self)
            900 STORE_SUBSCR
            904 CACHE
            906 CACHE
            908 CACHE
            910 CACHE
            912 CACHE
            914 CACHE
            916 CACHE
            918 CACHE
            920 CACHE
            922 LOAD_FAST                3 (stop)
            924 LOAD_FAST                4 (line)
            926 UNPACK_SEQUENCE          2
            930 CALL                     2
            938 CACHE
            940 STORE_FAST               3 (stop)

120         942 LOAD_FAST                3 (stop)
            944 EXTENDED_ARG             1
