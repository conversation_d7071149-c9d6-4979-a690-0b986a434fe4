# Code object from position 10704787
# Filename: imghdr.py
# Name: <module>
# Args: 0
# Locals: 0
# Stack size: 4
# Flags: 0

  0           0 RESUME                   0

  1           2 LOAD_CONST               0 ('Recognize image file formats based on their first few bytes.')
              4 STORE_NAME               0 (__doc__)

  3           6 LOAD_CONST               1 (0)
              8 LOAD_CONST               2 (('PathLike',))
             10 IMPORT_NAME              1 (os)
             12 IMPORT_FROM              2 (PathLike)
             14 STORE_NAME               2 (PathLike)
             16 POP_TOP

  4          18 LOAD_CONST               1 (0)
             20 LOAD_CONST               3 (None)
             22 IMPORT_NAME              3 (warnings)
             24 STORE_NAME               3 (warnings)

  6          26 LOAD_CONST               4 ('what')
             28 BUILD_LIST               1
             30 STORE_NAME               4 (__all__)

  9          32 PUSH_NULL
             34 LOAD_NAME                3 (warnings)
             36 LOAD_ATTR                5 (NULL|self + PathLike)
             56 CALL                     2
             64 CACHE
             66 POP_TOP

 16          68 LOAD_CONST              24 ((None,))
             70 LOAD_CONST               7 (<code object what at 0x000001B2A74AA5D0, file "imghdr.py", line 16>)
             72 MAKE_FUNCTION            1 (defaults)
             74 STORE_NAME               7 (what)

 40          76 BUILD_LIST               0
             78 STORE_NAME               8 (tests)

 42          80 LOAD_CONST               8 (<code object test_jpeg at 0x000001B2A76AF440, file "imghdr.py", line 42>)
             82 MAKE_FUNCTION            0
             84 STORE_NAME               9 (test_jpeg)

 49          86 LOAD_NAME                8 (tests)
             88 STORE_SUBSCR
             92 CACHE
             94 CACHE
             96 CACHE
             98 CACHE
            100 CACHE
            102 CACHE
            104 CACHE
            106 CACHE
            108 CACHE
            110 LOAD_NAME                9 (test_jpeg)
            112 UNPACK_SEQUENCE          1
            116 CALL                     1
            124 CACHE
            126 POP_TOP

 51         128 LOAD_CONST               9 (<code object test_png at 0x000001B2A8A0D730, file "imghdr.py", line 51>)
            130 MAKE_FUNCTION            0
            132 STORE_NAME              11 (test_png)

 55         134 LOAD_NAME                8 (tests)
            136 STORE_SUBSCR
            140 CACHE
            142 CACHE
            144 CACHE
            146 CACHE
            148 CACHE
            150 CACHE
            152 CACHE
            154 CACHE
            156 CACHE
            158 LOAD_NAME               11 (test_png)
            160 UNPACK_SEQUENCE          1
            164 CALL                     1
            172 CACHE
            174 POP_TOP

 57         176 LOAD_CONST              10 (<code object test_gif at 0x000001B2A8A48A80, file "imghdr.py", line 57>)
            178 MAKE_FUNCTION            0
            180 STORE_NAME              12 (test_gif)

 62         182 LOAD_NAME                8 (tests)
            184 STORE_SUBSCR
            188 CACHE
            190 CACHE
            192 CACHE
            194 CACHE
            196 CACHE
            198 CACHE
            200 CACHE
            202 CACHE
            204 CACHE
            206 LOAD_NAME               12 (test_gif)
            208 UNPACK_SEQUENCE          1
            212 CALL                     1
            220 CACHE
            222 POP_TOP

 64         224 LOAD_CONST              11 (<code object test_tiff at 0x000001B2A8A48990, file "imghdr.py", line 64>)
            226 MAKE_FUNCTION            0
            228 STORE_NAME              13 (test_tiff)

 69         230 LOAD_NAME                8 (tests)
            232 STORE_SUBSCR
            236 CACHE
            238 CACHE
            240 CACHE
            242 CACHE
            244 CACHE
            246 CACHE
            248 CACHE
            250 CACHE
            252 CACHE
            254 LOAD_NAME               13 (test_tiff)
            256 UNPACK_SEQUENCE          1
            260 CALL                     1
            268 CACHE
            270 POP_TOP

 71         272 LOAD_CONST              12 (<code object test_rgb at 0x000001B2A8A0D430, file "imghdr.py", line 71>)
            274 MAKE_FUNCTION            0
            276 STORE_NAME              14 (test_rgb)

 76         278 LOAD_NAME                8 (tests)
            280 STORE_SUBSCR
            284 CACHE
            286 CACHE
            288 CACHE
            290 CACHE
            292 CACHE
            294 CACHE
            296 CACHE
            298 CACHE
            300 CACHE
            302 LOAD_NAME               14 (test_rgb)
            304 UNPACK_SEQUENCE          1
            308 CALL                     1
            316 CACHE
            318 POP_TOP

 78         320 LOAD_CONST              13 (<code object test_pbm at 0x000001B2A71D3470, file "imghdr.py", line 78>)
            322 MAKE_FUNCTION            0
            324 STORE_NAME              15 (test_pbm)

 84         326 LOAD_NAME                8 (tests)
            328 STORE_SUBSCR
            332 CACHE
            334 CACHE
            336 CACHE
            338 CACHE
            340 CACHE
            342 CACHE
            344 CACHE
            346 CACHE
            348 CACHE
            350 LOAD_NAME               15 (test_pbm)
            352 UNPACK_SEQUENCE          1
            356 CALL                     1
            364 CACHE
            366 POP_TOP

 86         368 LOAD_CONST              14 (<code object test_pgm at 0x000001B2A71D3310, file "imghdr.py", line 86>)
            370 MAKE_FUNCTION            0
            372 STORE_NAME              16 (test_pgm)

 92         374 LOAD_NAME                8 (tests)
            376 STORE_SUBSCR
            380 CACHE
            382 CACHE
            384 CACHE
            386 CACHE
            388 CACHE
            390 CACHE
            392 CACHE
            394 CACHE
            396 CACHE
            398 LOAD_NAME               16 (test_pgm)
            400 UNPACK_SEQUENCE          1
            404 CALL                     1
            412 CACHE
            414 POP_TOP

 94         416 LOAD_CONST              15 (<code object test_ppm at 0x000001B2A71D3E10, file "imghdr.py", line 94>)
            418 MAKE_FUNCTION            0
            420 STORE_NAME              17 (test_ppm)

100         422 LOAD_NAME                8 (tests)
            424 STORE_SUBSCR
            428 CACHE
            430 CACHE
            432 CACHE
            434 CACHE
            436 CACHE
            438 CACHE
            440 CACHE
            442 CACHE
            444 CACHE
            446 LOAD_NAME               17 (test_ppm)
            448 UNPACK_SEQUENCE          1
            452 CALL                     1
            460 CACHE
            462 POP_TOP

102         464 LOAD_CONST              16 (<code object test_rast at 0x000001B2A8A0D030, file "imghdr.py", line 102>)
            466 MAKE_FUNCTION            0
            468 STORE_NAME              18 (test_rast)

107         470 LOAD_NAME                8 (tests)
            472 STORE_SUBSCR
            476 CACHE
            478 CACHE
            480 CACHE
            482 CACHE
            484 CACHE
            486 CACHE
            488 CACHE
            490 CACHE
            492 CACHE
            494 LOAD_NAME               18 (test_rast)
            496 UNPACK_SEQUENCE          1
            500 CALL                     1
            508 CACHE
            510 POP_TOP

109         512 LOAD_CONST              17 (<code object test_xbm at 0x000001B2A8A0E230, file "imghdr.py", line 109>)
            514 MAKE_FUNCTION            0
            516 STORE_NAME              19 (test_xbm)

114         518 LOAD_NAME                8 (tests)
            520 STORE_SUBSCR
            524 CACHE
            526 CACHE
            528 CACHE
            530 CACHE
            532 CACHE
            534 CACHE
            536 CACHE
            538 CACHE
            540 CACHE
            542 LOAD_NAME               19 (test_xbm)
            544 UNPACK_SEQUENCE          1
            548 CALL                     1
            556 CACHE
            558 POP_TOP

116         560 LOAD_CONST              18 (<code object test_bmp at 0x000001B2A8A0E430, file "imghdr.py", line 116>)
            562 MAKE_FUNCTION            0
            564 STORE_NAME              20 (test_bmp)

120         566 LOAD_NAME                8 (tests)
            568 STORE_SUBSCR
            572 CACHE
            574 CACHE
            576 CACHE
            578 CACHE
            580 CACHE
            582 CACHE
            584 CACHE
            586 CACHE
            588 CACHE
            590 LOAD_NAME               20 (test_bmp)
            592 UNPACK_SEQUENCE          1
            596 CALL                     1
            604 CACHE
            606 POP_TOP

122         608 LOAD_CONST              19 (<code object test_webp at 0x000001B2A8A4C030, file "imghdr.py", line 122>)
            610 MAKE_FUNCTION            0
            612 STORE_NAME              21 (test_webp)

126         614 LOAD_NAME                8 (tests)
            616 STORE_SUBSCR
            620 CACHE
            622 CACHE
            624 CACHE
            626 CACHE
            628 CACHE
            630 CACHE
            632 CACHE
            634 CACHE
            636 CACHE
            638 LOAD_NAME               21 (test_webp)
            640 UNPACK_SEQUENCE          1
            644 CALL                     1
            652 CACHE
            654 POP_TOP

128         656 LOAD_CONST              20 (<code object test_exr at 0x000001B2A8A0E630, file "imghdr.py", line 128>)
            658 MAKE_FUNCTION            0
            660 STORE_NAME              22 (test_exr)

132         662 LOAD_NAME                8 (tests)
            664 STORE_SUBSCR
            668 CACHE
            670 CACHE
            672 CACHE
            674 CACHE
            676 CACHE
            678 CACHE
            680 CACHE
            682 CACHE
            684 CACHE
            686 LOAD_NAME               22 (test_exr)
            688 UNPACK_SEQUENCE          1
            692 CALL                     1
            700 CACHE
            702 POP_TOP

138         704 LOAD_CONST              21 (<code object test at 0x000001B2A75CF4A0, file "imghdr.py", line 138>)
            706 MAKE_FUNCTION            0
            708 STORE_NAME              23 (test)

153         710 LOAD_CONST              22 (<code object testall at 0x000001B2A74A5900, file "imghdr.py", line 153>)
            712 MAKE_FUNCTION            0
            714 STORE_NAME              24 (testall)

174         716 LOAD_NAME                6 (__name__)
            718 LOAD_CONST              23 ('__main__')
            720 COMPARE_OP               2 (<)
            724 CACHE
            726 POP_JUMP_IF_FALSE       12 (to 752)

175         728 PUSH_NULL
            730 LOAD_NAME               23 (test)
            732 UNPACK_SEQUENCE          0
            736 CALL                     0
            744 CACHE
            746 POP_TOP
            748 LOAD_CONST               3 (None)
            750 RETURN_VALUE

174     >>  752 LOAD_CONST               3 (None)
            754 RETURN_VALUE

Disassembly of <code object what at 0x000001B2A74AA5D0, file "imghdr.py", line 16>:
 16           0 RESUME                   0

 17           2 LOAD_CONST               0 (None)
              4 STORE_FAST               2 (f)

 18           6 NOP

 19           8 LOAD_FAST                1 (h)
             10 POP_JUMP_IF_NOT_NONE   128 (to 268)

 20          12 LOAD_GLOBAL              1 (NULL + isinstance)
             22 CACHE
             24 LOAD_FAST                0 (file)
             26 LOAD_GLOBAL              2 (str)
             36 CACHE
             38 LOAD_GLOBAL              4 (PathLike)
             48 CACHE
             50 BUILD_TUPLE              2
             52 UNPACK_SEQUENCE          2
             56 CALL                     2
             64 CACHE
             66 POP_JUMP_IF_FALSE       38 (to 144)

 21          68 LOAD_GLOBAL              7 (NULL + open)
             78 CACHE
             80 LOAD_FAST                0 (file)
             82 LOAD_CONST               1 ('rb')
             84 UNPACK_SEQUENCE          2
             88 CALL                     2
             96 CACHE
             98 STORE_FAST               2 (f)

 22         100 LOAD_FAST                2 (f)
            102 STORE_SUBSCR
            106 CACHE
            108 CACHE
            110 CACHE
            112 CACHE
            114 CACHE
            116 CACHE
            118 CACHE
            120 CACHE
            122 CACHE
            124 LOAD_CONST               2 (32)
            126 UNPACK_SEQUENCE          1
            130 CALL                     1
            138 CACHE
            140 STORE_FAST               1 (h)
            142 JUMP_FORWARD            62 (to 268)

 24     >>  144 LOAD_FAST                0 (file)
            146 STORE_SUBSCR
            150 CACHE
            152 CACHE
            154 CACHE
            156 CACHE
            158 CACHE
            160 CACHE
            162 CACHE
            164 CACHE
            166 CACHE
            168 UNPACK_SEQUENCE          0
            172 CALL                     0
            180 CACHE
            182 STORE_FAST               3 (location)

 25         184 LOAD_FAST                0 (file)
            186 STORE_SUBSCR
            190 CACHE
            192 CACHE
            194 CACHE
            196 CACHE
            198 CACHE
            200 CACHE
            202 CACHE
            204 CACHE
            206 CACHE
            208 LOAD_CONST               2 (32)
            210 UNPACK_SEQUENCE          1
            214 CALL                     1
            222 CACHE
            224 STORE_FAST               1 (h)

 26         226 LOAD_FAST                0 (file)
            228 STORE_SUBSCR
            232 CACHE
            234 CACHE
            236 CACHE
            238 CACHE
            240 CACHE
            242 CACHE
            244 CACHE
            246 CACHE
            248 CACHE
            250 LOAD_FAST                3 (location)
            252 UNPACK_SEQUENCE          1
            256 CALL                     1
            264 CACHE
            266 POP_TOP

 27     >>  268 LOAD_GLOBAL             14 (tests)
            278 CACHE
            280 GET_ITER
        >>  282 FOR_ITER                43 (to 372)

 28         286 PUSH_NULL
            288 LOAD_FAST                4 (tf)
            290 LOAD_FAST                1 (h)
            292 LOAD_FAST                2 (f)
            294 UNPACK_SEQUENCE          2
            298 CALL                     2
            306 CACHE
            308 STORE_FAST               5 (res)

 29         310 LOAD_FAST                5 (res)
            312 POP_JUMP_IF_FALSE       27 (to 368)

 30         314 LOAD_FAST                5 (res)
            316 SWAP                     2
            318 POP_TOP

 32         320 LOAD_FAST                2 (f)
            322 POP_JUMP_IF_FALSE       21 (to 366)
            324 LOAD_FAST                2 (f)
            326 STORE_SUBSCR
            330 CACHE
            332 CACHE
            334 CACHE
            336 CACHE
            338 CACHE
            340 CACHE
            342 CACHE
            344 CACHE
            346 CACHE
            348 UNPACK_SEQUENCE          0
            352 CALL                     0
            360 CACHE
            362 POP_TOP
            364 RETURN_VALUE
        >>  366 RETURN_VALUE

 29     >>  368 JUMP_BACKWARD           44 (to 282)

 27         370 NOP

 32     >>  372 LOAD_FAST                2 (f)
            374 POP_JUMP_IF_FALSE       20 (to 416)
            376 LOAD_FAST                2 (f)
            378 STORE_SUBSCR
            382 CACHE
            384 CACHE
            386 CACHE
            388 CACHE
            390 CACHE
            392 CACHE
            394 CACHE
            396 CACHE
            398 CACHE
            400 UNPACK_SEQUENCE          0
            404 CALL                     0
            412 CACHE
            414 POP_TOP
        >>  416 JUMP_FORWARD            28 (to 474)
        >>  418 PUSH_EXC_INFO
            420 LOAD_FAST                2 (f)
            422 POP_JUMP_IF_FALSE       21 (to 466)
            424 LOAD_FAST                2 (f)
            426 STORE_SUBSCR
            430 CACHE
            432 CACHE
            434 CACHE
            436 CACHE
            438 CACHE
            440 CACHE
            442 CACHE
            444 CACHE
            446 CACHE
            448 UNPACK_SEQUENCE          0
            452 CALL                     0
            460 CACHE
            462 POP_TOP
            464 RERAISE                  0
        >>  466 RERAISE                  0
        >>  468 COPY                     3
            470 POP_EXCEPT
            472 RERAISE                  1

 33     >>  474 LOAD_CONST               0 (None)
            476 RETURN_VALUE
ExceptionTable:
  8 to 318 -> 418 [0]
  368 to 368 -> 418 [0]
  418 to 466 -> 468 [1] lasti

Disassembly of <code object test_jpeg at 0x000001B2A76AF440, file "imghdr.py", line 42>:
 42           0 RESUME                   0

 44           2 LOAD_FAST                0 (h)
              4 LOAD_CONST               1 (6)
              6 LOAD_CONST               2 (10)
              8 BUILD_SLICE              2
             10 BINARY_SUBSCR
             14 CACHE
             16 CACHE
             18 CACHE
             20 LOAD_CONST               3 ((b'JFIF', b'Exif'))
             22 CONTAINS_OP              0
             24 POP_JUMP_IF_FALSE        2 (to 30)

 45          26 LOAD_CONST               4 ('jpeg')
             28 RETURN_VALUE

 46     >>   30 LOAD_FAST                0 (h)
             32 LOAD_CONST               5 (None)
             34 LOAD_CONST               6 (4)
             36 BUILD_SLICE              2
             38 BINARY_SUBSCR
             42 CACHE
             44 CACHE
             46 CACHE
             48 LOAD_CONST               7 (b'\xff\xd8\xff\xdb')
             50 COMPARE_OP               2 (<)
             54 CACHE
             56 POP_JUMP_IF_FALSE        2 (to 62)

 47          58 LOAD_CONST               4 ('jpeg')
             60 RETURN_VALUE

 46     >>   62 LOAD_CONST               5 (None)
             64 RETURN_VALUE

Disassembly of <code object test_png at 0x000001B2A8A0D730, file "imghdr.py", line 51>:
 51           0 RESUME                   0

 52           2 LOAD_FAST                0 (h)
              4 STORE_SUBSCR
              8 CACHE
             10 CACHE
             12 CACHE
             14 CACHE
             16 CACHE
             18 CACHE
             20 CACHE
             22 CACHE
             24 CACHE
             26 LOAD_CONST               1 (b'\x89PNG\r\n\x1a\n')
             28 UNPACK_SEQUENCE          1
             32 CALL                     1
             40 CACHE
             42 POP_JUMP_IF_FALSE        2 (to 48)

 53          44 LOAD_CONST               2 ('png')
             46 RETURN_VALUE

 52     >>   48 LOAD_CONST               0 (None)
             50 RETURN_VALUE

Disassembly of <code object test_gif at 0x000001B2A8A48A80, file "imghdr.py", line 57>:
 57           0 RESUME                   0

 59           2 LOAD_FAST                0 (h)
              4 LOAD_CONST               1 (None)
              6 LOAD_CONST               2 (6)
              8 BUILD_SLICE              2
             10 BINARY_SUBSCR
             14 CACHE
             16 CACHE
             18 CACHE
             20 LOAD_CONST               3 ((b'GIF87a', b'GIF89a'))
             22 CONTAINS_OP              0
             24 POP_JUMP_IF_FALSE        2 (to 30)

 60          26 LOAD_CONST               4 ('gif')
             28 RETURN_VALUE

 59     >>   30 LOAD_CONST               1 (None)
             32 RETURN_VALUE

Disassembly of <code object test_tiff at 0x000001B2A8A48990, file "imghdr.py", line 64>:
 64           0 RESUME                   0

 66           2 LOAD_FAST                0 (h)
              4 LOAD_CONST               1 (None)
              6 LOAD_CONST               2 (2)
              8 BUILD_SLICE              2
             10 BINARY_SUBSCR
             14 CACHE
             16 CACHE
             18 CACHE
             20 LOAD_CONST               3 ((b'MM', b'II'))
             22 CONTAINS_OP              0
             24 POP_JUMP_IF_FALSE        2 (to 30)

 67          26 LOAD_CONST               4 ('tiff')
             28 RETURN_VALUE

 66     >>   30 LOAD_CONST               1 (None)
             32 RETURN_VALUE

Disassembly of <code object test_rgb at 0x000001B2A8A0D430, file "imghdr.py", line 71>:
 71           0 RESUME                   0

 73           2 LOAD_FAST                0 (h)
              4 STORE_SUBSCR
              8 CACHE
             10 CACHE
             12 CACHE
             14 CACHE
             16 CACHE
             18 CACHE
             20 CACHE
             22 CACHE
             24 CACHE
             26 LOAD_CONST               1 (b'\x01\xda')
             28 UNPACK_SEQUENCE          1
             32 CALL                     1
             40 CACHE
             42 POP_JUMP_IF_FALSE        2 (to 48)

 74          44 LOAD_CONST               2 ('rgb')
             46 RETURN_VALUE

 73     >>   48 LOAD_CONST               3 (None)
             50 RETURN_VALUE

Disassembly of <code object test_pbm at 0x000001B2A71D3470, file "imghdr.py", line 78>:
 78           0 RESUME                   0

 80           2 LOAD_GLOBAL              1 (NULL + len)
             12 CACHE
             14 LOAD_FAST                0 (h)
             16 UNPACK_SEQUENCE          1
             20 CALL                     1
             28 CACHE
             30 LOAD_CONST               1 (3)
             32 COMPARE_OP               5 (<)
             36 CACHE
             38 POP_JUMP_IF_FALSE       47 (to 134)

 81          40 LOAD_FAST                0 (h)
             42 LOAD_CONST               2 (0)
             44 BINARY_SUBSCR
             48 CACHE
             50 CACHE
             52 CACHE
             54 LOAD_GLOBAL              3 (NULL + ord)
             64 CACHE
             66 LOAD_CONST               3 (b'P')
             68 UNPACK_SEQUENCE          1
             72 CALL                     1
             80 CACHE
             82 COMPARE_OP               2 (<)
             86 CACHE
             88 POP_JUMP_IF_FALSE       24 (to 138)
             90 LOAD_FAST                0 (h)
             92 LOAD_CONST               4 (1)
             94 BINARY_SUBSCR
             98 CACHE
            100 CACHE
            102 CACHE
            104 LOAD_CONST               5 (b'14')
            106 CONTAINS_OP              0
            108 POP_JUMP_IF_FALSE       16 (to 142)
            110 LOAD_FAST                0 (h)
            112 LOAD_CONST               6 (2)
            114 BINARY_SUBSCR
            118 CACHE
            120 CACHE
            122 CACHE
            124 LOAD_CONST               7 (b' \t\n\r')
            126 CONTAINS_OP              0
            128 POP_JUMP_IF_FALSE        8 (to 146)

 82         130 LOAD_CONST               8 ('pbm')
            132 RETURN_VALUE

 80     >>  134 LOAD_CONST               9 (None)
            136 RETURN_VALUE

 81     >>  138 LOAD_CONST               9 (None)
            140 RETURN_VALUE
        >>  142 LOAD_CONST               9 (None)
            144 RETURN_VALUE
        >>  146 LOAD_CONST               9 (None)
            148 RETURN_VALUE

Disassembly of <code object test_pgm at 0x000001B2A71D3310, file "imghdr.py", line 86>:
 86           0 RESUME                   0

 88           2 LOAD_GLOBAL              1 (NULL + len)
             12 CACHE
             14 LOAD_FAST                0 (h)
             16 UNPACK_SEQUENCE          1
             20 CALL                     1
             28 CACHE
             30 LOAD_CONST               1 (3)
             32 COMPARE_OP               5 (<)
             36 CACHE
             38 POP_JUMP_IF_FALSE       47 (to 134)

 89          40 LOAD_FAST                0 (h)
             42 LOAD_CONST               2 (0)
             44 BINARY_SUBSCR
             48 CACHE
             50 CACHE
             52 CACHE
             54 LOAD_GLOBAL              3 (NULL + ord)
             64 CACHE
             66 LOAD_CONST               3 (b'P')
             68 UNPACK_SEQUENCE          1
             72 CALL                     1
             80 CACHE
             82 COMPARE_OP               2 (<)
             86 CACHE
             88 POP_JUMP_IF_FALSE       24 (to 138)
             90 LOAD_FAST                0 (h)
             92 LOAD_CONST               4 (1)
             94 BINARY_SUBSCR
             98 CACHE
            100 CACHE
            102 CACHE
            104 LOAD_CONST               5 (b'25')
            106 CONTAINS_OP              0
            108 POP_JUMP_IF_FALSE       16 (to 142)
            110 LOAD_FAST                0 (h)
            112 LOAD_CONST               6 (2)
            114 BINARY_SUBSCR
            118 CACHE
            120 CACHE
            122 CACHE
            124 LOAD_CONST               7 (b' \t\n\r')
            126 CONTAINS_OP              0
            128 POP_JUMP_IF_FALSE        8 (to 146)

 90         130 LOAD_CONST               8 ('pgm')
            132 RETURN_VALUE

 88     >>  134 LOAD_CONST               9 (None)
            136 RETURN_VALUE

 89     >>  138 LOAD_CONST               9 (None)
            140 RETURN_VALUE
        >>  142 LOAD_CONST               9 (None)
            144 RETURN_VALUE
        >>  146 LOAD_CONST               9 (None)
            148 RETURN_VALUE

Disassembly of <code object test_ppm at 0x000001B2A71D3E10, file "imghdr.py", line 94>:
 94           0 RESUME                   0

 96           2 LOAD_GLOBAL              1 (NULL + len)
             12 CACHE
             14 LOAD_FAST                0 (h)
             16 UNPACK_SEQUENCE          1
             20 CALL                     1
             28 CACHE
             30 LOAD_CONST               1 (3)
             32 COMPARE_OP               5 (<)
             36 CACHE
             38 POP_JUMP_IF_FALSE       47 (to 134)

 97          40 LOAD_FAST                0 (h)
             42 LOAD_CONST               2 (0)
             44 BINARY_SUBSCR
             48 CACHE
             50 CACHE
             52 CACHE
             54 LOAD_GLOBAL              3 (NULL + ord)
             64 CACHE
             66 LOAD_CONST               3 (b'P')
             68 UNPACK_SEQUENCE          1
             72 CALL                     1
             80 CACHE
             82 COMPARE_OP               2 (<)
             86 CACHE
             88 POP_JUMP_IF_FALSE       24 (to 138)
             90 LOAD_FAST                0 (h)
             92 LOAD_CONST               4 (1)
             94 BINARY_SUBSCR
             98 CACHE
            100 CACHE
            102 CACHE
            104 LOAD_CONST               5 (b'36')
            106 CONTAINS_OP              0
            108 POP_JUMP_IF_FALSE       16 (to 142)
            110 LOAD_FAST                0 (h)
            112 LOAD_CONST               6 (2)
            114 BINARY_SUBSCR
            118 CACHE
            120 CACHE
            122 CACHE
            124 LOAD_CONST               7 (b' \t\n\r')
            126 CONTAINS_OP              0
            128 POP_JUMP_IF_FALSE        8 (to 146)

 98         130 LOAD_CONST               8 ('ppm')
            132 RETURN_VALUE

 96     >>  134 LOAD_CONST               9 (None)
            136 RETURN_VALUE

 97     >>  138 LOAD_CONST               9 (None)
            140 RETURN_VALUE
        >>  142 LOAD_CONST               9 (None)
            144 RETURN_VALUE
        >>  146 LOAD_CONST               9 (None)
            148 RETURN_VALUE

Disassembly of <code object test_rast at 0x000001B2A8A0D030, file "imghdr.py", line 102>:
102           0 RESUME                   0

104           2 LOAD_FAST                0 (h)
              4 STORE_SUBSCR
              8 CACHE
             10 CACHE
             12 CACHE
             14 CACHE
             16 CACHE
             18 CACHE
             20 CACHE
             22 CACHE
             24 CACHE
             26 LOAD_CONST               1 (b'Y\xa6j\x95')
             28 UNPACK_SEQUENCE          1
             32 CALL                     1
             40 CACHE
             42 POP_JUMP_IF_FALSE        2 (to 48)

105          44 LOAD_CONST               2 ('rast')
             46 RETURN_VALUE

104     >>   48 LOAD_CONST               3 (None)
             50 RETURN_VALUE

Disassembly of <code object test_xbm at 0x000001B2A8A0E230, file "imghdr.py", line 109>:
109           0 RESUME                   0

111           2 LOAD_FAST                0 (h)
              4 STORE_SUBSCR
              8 CACHE
             10 CACHE
             12 CACHE
             14 CACHE
             16 CACHE
             18 CACHE
             20 CACHE
             22 CACHE
             24 CACHE
             26 LOAD_CONST               1 (b'#define ')
             28 UNPACK_SEQUENCE          1
             32 CALL                     1
             40 CACHE
             42 POP_JUMP_IF_FALSE        2 (to 48)

112          44 LOAD_CONST               2 ('xbm')
             46 RETURN_VALUE

111     >>   48 LOAD_CONST               3 (None)
             50 RETURN_VALUE

Disassembly of <code object test_bmp at 0x000001B2A8A0E430, file "imghdr.py", line 116>:
116           0 RESUME                   0

117           2 LOAD_FAST                0 (h)
              4 STORE_SUBSCR
              8 CACHE
             10 CACHE
             12 CACHE
             14 CACHE
             16 CACHE
             18 CACHE
             20 CACHE
             22 CACHE
             24 CACHE
             26 LOAD_CONST               1 (b'BM')
             28 UNPACK_SEQUENCE          1
             32 CALL                     1
             40 CACHE
             42 POP_JUMP_IF_FALSE        2 (to 48)

118          44 LOAD_CONST               2 ('bmp')
             46 RETURN_VALUE

117     >>   48 LOAD_CONST               0 (None)
             50 RETURN_VALUE

Disassembly of <code object test_webp at 0x000001B2A8A4C030, file "imghdr.py", line 122>:
122           0 RESUME                   0

123           2 LOAD_FAST                0 (h)
              4 STORE_SUBSCR
              8 CACHE
             10 CACHE
             12 CACHE
             14 CACHE
             16 CACHE
             18 CACHE
             20 CACHE
             22 CACHE
             24 CACHE
             26 LOAD_CONST               1 (b'RIFF')
             28 UNPACK_SEQUENCE          1
             32 CALL                     1
             40 CACHE
             42 POP_JUMP_IF_FALSE       16 (to 76)
             44 LOAD_FAST                0 (h)
             46 LOAD_CONST               2 (8)
             48 LOAD_CONST               3 (12)
             50 BUILD_SLICE              2
             52 BINARY_SUBSCR
             56 CACHE
             58 CACHE
             60 CACHE
             62 LOAD_CONST               4 (b'WEBP')
             64 COMPARE_OP               2 (<)
             68 CACHE
             70 POP_JUMP_IF_FALSE        4 (to 80)

124          72 LOAD_CONST               5 ('webp')
             74 RETURN_VALUE

123     >>   76 LOAD_CONST               0 (None)
             78 RETURN_VALUE
        >>   80 LOAD_CONST               0 (None)
             82 RETURN_VALUE

Disassembly of <code object test_exr at 0x000001B2A8A0E630, file "imghdr.py", line 128>:
128           0 RESUME                   0

129           2 LOAD_FAST                0 (h)
              4 STORE_SUBSCR
              8 CACHE
             10 CACHE
             12 CACHE
             14 CACHE
             16 CACHE
             18 CACHE
             20 CACHE
             22 CACHE
             24 CACHE
             26 LOAD_CONST               1 (b'v/1\x01')
             28 UNPACK_SEQUENCE          1
             32 CALL                     1
             40 CACHE
             42 POP_JUMP_IF_FALSE        2 (to 48)

130          44 LOAD_CONST               2 ('exr')
             46 RETURN_VALUE

129     >>   48 LOAD_CONST               0 (None)
             50 RETURN_VALUE

Disassembly of <code object test at 0x000001B2A75CF4A0, file "imghdr.py", line 138>:
138           0 RESUME                   0

139           2 LOAD_CONST               1 (0)
              4 LOAD_CONST               0 (None)
              6 IMPORT_NAME              0 (sys)
              8 STORE_FAST               0 (sys)

140          10 LOAD_CONST               1 (0)
             12 STORE_FAST               1 (recursive)

141          14 LOAD_FAST                0 (sys)
             16 LOAD_ATTR                1 (NULL|self + sys)
             36 CACHE
             38 CACHE
             40 CACHE
             42 POP_JUMP_IF_FALSE       29 (to 102)
             44 LOAD_FAST                0 (sys)
             46 LOAD_ATTR                1 (NULL|self + sys)
             66 CACHE
             68 LOAD_CONST               3 ('-r')
             70 COMPARE_OP               2 (<)
             74 CACHE
             76 POP_JUMP_IF_FALSE       12 (to 102)

142          78 LOAD_FAST                0 (sys)
             80 LOAD_ATTR                1 (NULL|self + sys)
            100 STORE_FAST               1 (recursive)

144     >>  102 NOP

145         104 LOAD_FAST                0 (sys)
            106 LOAD_ATTR                1 (NULL|self + sys)
            126 CACHE
            128 CACHE
            130 CACHE
            132 POP_JUMP_IF_FALSE       32 (to 198)

146         134 LOAD_GLOBAL              5 (NULL + testall)
            144 CACHE
            146 LOAD_FAST                0 (sys)
            148 LOAD_ATTR                1 (NULL|self + sys)
            168 CACHE
            170 CACHE
            172 CACHE
            174 LOAD_FAST                1 (recursive)
            176 LOAD_CONST               2 (1)
            178 UNPACK_SEQUENCE          3
            182 CALL                     3
            190 CACHE
            192 POP_TOP
            194 LOAD_CONST               0 (None)
            196 RETURN_VALUE

148     >>  198 LOAD_GLOBAL              5 (NULL + testall)
            208 CACHE
            210 LOAD_CONST               5 ('.')
            212 BUILD_LIST               1
            214 LOAD_FAST                1 (recursive)
            216 LOAD_CONST               2 (1)
            218 UNPACK_SEQUENCE          3
            222 CALL                     3
            230 CACHE
            232 POP_TOP
            234 LOAD_CONST               0 (None)
            236 RETURN_VALUE
        >>  238 PUSH_EXC_INFO

149         240 LOAD_GLOBAL              6 (KeyboardInterrupt)
            250 CACHE
            252 CHECK_EXC_MATCH
            254 POP_JUMP_IF_FALSE       51 (to 358)
            256 POP_TOP

150         258 LOAD_FAST                0 (sys)
            260 LOAD_ATTR                4 (testall)
            280 CACHE
            282 CACHE
            284 CACHE
            286 CACHE
            288 CACHE
            290 CACHE
            292 LOAD_CONST               6 ('\n[Interrupted]\n')
            294 UNPACK_SEQUENCE          1
            298 CALL                     1
            306 CACHE
            308 POP_TOP

151         310 LOAD_FAST                0 (sys)
            312 STORE_SUBSCR
            316 CACHE
            318 CACHE
            320 CACHE
            322 CACHE
            324 CACHE
            326 CACHE
            328 CACHE
            330 CACHE
            332 CACHE
            334 LOAD_CONST               2 (1)
            336 UNPACK_SEQUENCE          1
            340 CALL                     1
            348 CACHE
            350 POP_TOP
            352 POP_EXCEPT
            354 LOAD_CONST               0 (None)
            356 RETURN_VALUE

149     >>  358 RERAISE                  0
        >>  360 COPY                     3
            362 POP_EXCEPT
            364 RERAISE                  1
ExceptionTable:
  104 to 192 -> 238 [0]
  198 to 232 -> 238 [0]
  238 to 350 -> 360 [1] lasti
  358 to 358 -> 360 [1] lasti

Disassembly of <code object testall at 0x000001B2A74A5900, file "imghdr.py", line 153>:
153           0 RESUME                   0

154           2 LOAD_CONST               1 (0)
              4 LOAD_CONST               0 (None)
              6 IMPORT_NAME              0 (sys)
              8 STORE_FAST               3 (sys)

155          10 LOAD_CONST               1 (0)
             12 LOAD_CONST               0 (None)
             14 IMPORT_NAME              1 (os)
             16 STORE_FAST               4 (os)

156          18 LOAD_FAST                0 (list)
             20 GET_ITER
        >>   22 EXTENDED_ARG             1
             24 FOR_ITER               276 (to 580)

157          28 LOAD_FAST                4 (os)
             30 LOAD_ATTR                2 (os)
             50 CACHE
             52 CACHE
             54 CACHE
             56 CACHE
             58 CACHE
             60 CACHE
             62 LOAD_FAST                5 (filename)
             64 UNPACK_SEQUENCE          1
             68 CALL                     1
             76 CACHE
             78 POP_JUMP_IF_FALSE      142 (to 364)

158          80 LOAD_GLOBAL              9 (NULL + print)
             90 CACHE
             92 LOAD_FAST                5 (filename)
             94 LOAD_CONST               2 ('/:')
             96 BINARY_OP                0 (+)
            100 LOAD_CONST               3 (' ')
            102 KW_NAMES                 4 (('end',))
            104 UNPACK_SEQUENCE          2
            108 CALL                     2
            116 CACHE
            118 POP_TOP

159         120 LOAD_FAST                1 (recursive)
            122 POP_JUMP_IF_TRUE         2 (to 128)
            124 LOAD_FAST                2 (toplevel)
            126 POP_JUMP_IF_FALSE      102 (to 332)

160     >>  128 LOAD_GLOBAL              9 (NULL + print)
            138 CACHE
            140 LOAD_CONST               5 ('recursing down:')
            142 UNPACK_SEQUENCE          1
            146 CALL                     1
            154 CACHE
            156 POP_TOP

161         158 LOAD_CONST               1 (0)
            160 LOAD_CONST               0 (None)
            162 IMPORT_NAME              5 (glob)
            164 STORE_FAST               6 (glob)

162         166 LOAD_FAST                6 (glob)
            168 STORE_SUBSCR
            172 CACHE
            174 CACHE
            176 CACHE
            178 CACHE
            180 CACHE
            182 CACHE
            184 CACHE
            186 CACHE
            188 CACHE
            190 LOAD_FAST                4 (os)
            192 LOAD_ATTR                2 (os)
            212 CACHE
            214 CACHE
            216 CACHE
            218 CACHE
            220 CACHE
            222 CACHE
            224 LOAD_FAST                6 (glob)
            226 STORE_SUBSCR
            230 CACHE
            232 CACHE
            234 CACHE
            236 CACHE
            238 CACHE
            240 CACHE
            242 CACHE
            244 CACHE
            246 CACHE
            248 LOAD_FAST                5 (filename)
            250 UNPACK_SEQUENCE          1
            254 CALL                     1
            262 CACHE
            264 LOAD_CONST               6 ('*')
            266 UNPACK_SEQUENCE          2
            270 CALL                     2
            278 CACHE
            280 UNPACK_SEQUENCE          1
            284 CALL                     1
            292 CACHE
            294 STORE_FAST               7 (names)

163         296 LOAD_GLOBAL             17 (NULL + testall)
            306 CACHE
            308 LOAD_FAST                7 (names)
            310 LOAD_FAST                1 (recursive)
            312 LOAD_CONST               1 (0)
            314 UNPACK_SEQUENCE          3
            318 CALL                     3
            326 CACHE
            328 POP_TOP
            330 JUMP_BACKWARD          155 (to 22)

165     >>  332 LOAD_GLOBAL              9 (NULL + print)
            342 CACHE
            344 LOAD_CONST               7 ('*** directory (use -r) ***')
            346 UNPACK_SEQUENCE          1
            350 CALL                     1
            358 CACHE
            360 POP_TOP
            362 JUMP_BACKWARD          171 (to 22)

167     >>  364 LOAD_GLOBAL              9 (NULL + print)
            374 CACHE
            376 LOAD_FAST                5 (filename)
            378 LOAD_CONST               8 (':')
            380 BINARY_OP                0 (+)
            384 LOAD_CONST               3 (' ')
            386 KW_NAMES                 4 (('end',))
            388 UNPACK_SEQUENCE          2
            392 CALL                     2
            400 CACHE
            402 POP_TOP

168         404 LOAD_FAST                3 (sys)
            406 LOAD_ATTR                9 (NULL|self + print)
            426 CACHE
            428 CACHE
            430 CACHE
            432 CACHE
            434 CACHE
            436 CACHE
            438 UNPACK_SEQUENCE          0
            442 CALL                     0
            450 CACHE
            452 POP_TOP

169         454 NOP

170         456 LOAD_GLOBAL              9 (NULL + print)
            466 CACHE
            468 LOAD_GLOBAL             23 (NULL + what)
            478 CACHE
            480 LOAD_FAST                5 (filename)
            482 UNPACK_SEQUENCE          1
            486 CALL                     1
            494 CACHE
            496 UNPACK_SEQUENCE          1
            500 CALL                     1
            508 CACHE
            510 POP_TOP
            512 JUMP_BACKWARD          246 (to 22)
        >>  514 PUSH_EXC_INFO

171         516 LOAD_GLOBAL             24 (OSError)
            526 CACHE
            528 CHECK_EXC_MATCH
            530 POP_JUMP_IF_FALSE       19 (to 570)
            532 POP_TOP

172         534 LOAD_GLOBAL              9 (NULL + print)
            544 CACHE
            546 LOAD_CONST               9 ('*** not found ***')
            548 UNPACK_SEQUENCE          1
            552 CALL                     1
            560 CACHE
            562 POP_TOP
            564 POP_EXCEPT
            566 EXTENDED_ARG             1
            568 JUMP_BACKWARD          274 (to 22)

171     >>  570 RERAISE                  0
        >>  572 COPY                     3
            574 POP_EXCEPT
            576 RERAISE                  1

156         578 LOAD_CONST               0 (None)
        >>  580 RETURN_VALUE
ExceptionTable:
  456 to 510 -> 514 [1]
  514 to 562 -> 572 [2] lasti
  570 to 570 -> 572 [2] lasti


# Constants:
# 0: 'Recognize image file formats based on their first few bytes.'
# 1: 0
# 2: tuple
# 3: None
# 4: 'what'
# 5: tuple
# 6: tuple
# 7: <code object what>
# 8: <code object test_jpeg>
# 9: <code object test_png>
# 10: <code object test_gif>
# 11: <code object test_tiff>
# 12: <code object test_rgb>
# 13: <code object test_pbm>
# 14: <code object test_pgm>
# 15: <code object test_ppm>
# 16: <code object test_rast>
# 17: <code object test_xbm>
# 18: <code object test_bmp>
# 19: <code object test_webp>
# 20: <code object test_exr>
# 21: <code object test>
# 22: <code object testall>
# 23: '__main__'
# 24: tuple


# Names:
# 0: '__doc__'
# 1: 'os'
# 2: 'PathLike'
# 3: 'warnings'
# 4: '__all__'
# 5: '_deprecated'
# 6: '__name__'
# 7: 'what'
# 8: 'tests'
# 9: 'test_jpeg'
# 10: 'append'
# 11: 'test_png'
# 12: 'test_gif'
# 13: 'test_tiff'
# 14: 'test_rgb'
# 15: 'test_pbm'
# 16: 'test_pgm'
# 17: 'test_ppm'
# 18: 'test_rast'
# 19: 'test_xbm'
# 20: 'test_bmp'
# 21: 'test_webp'
# 22: 'test_exr'
# 23: 'test'
# 24: 'testall'


# Variable names:
