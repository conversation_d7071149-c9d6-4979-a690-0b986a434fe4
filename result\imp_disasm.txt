# Code object from position 10712595
# Filename: imp.py
# Name: <module>
# Args: 0
# Locals: 0
# Stack size: 6
# Flags: 0

  0           0 RESUME                   0

  1           2 LOAD_CONST               0 ("This module provides the components needed to build your own __import__\nfunction.  Undocumented functions are obsolete.\n\nIn most cases it is preferred you consider using the importlib module's\nfunctionality over this module.\n\n")
              4 STORE_NAME               0 (__doc__)

  9           6 LOAD_CONST               1 (0)
              8 LOAD_CONST               2 (('lock_held', 'acquire_lock', 'release_lock', 'get_frozen_object', 'is_frozen_package', 'init_frozen', 'is_builtin', 'is_frozen', '_fix_co_filename', '_frozen_module_names'))
             10 IMPORT_NAME              1 (_imp)
             12 IMPORT_FROM              2 (lock_held)
             14 STORE_NAME               2 (lock_held)
             16 IMPORT_FROM              3 (acquire_lock)
             18 STORE_NAME               3 (acquire_lock)
             20 IMPORT_FROM              4 (release_lock)
             22 STORE_NAME               4 (release_lock)
             24 IMPORT_FROM              5 (get_frozen_object)
             26 STORE_NAME               5 (get_frozen_object)
             28 IMPORT_FROM              6 (is_frozen_package)
             30 STORE_NAME               6 (is_frozen_package)
             32 IMPORT_FROM              7 (init_frozen)
             34 STORE_NAME               7 (init_frozen)
             36 IMPORT_FROM              8 (is_builtin)
             38 STORE_NAME               8 (is_builtin)
             40 IMPORT_FROM              9 (is_frozen)
             42 STORE_NAME               9 (is_frozen)
             44 IMPORT_FROM             10 (_fix_co_filename)
             46 STORE_NAME              10 (_fix_co_filename)
             48 IMPORT_FROM             11 (_frozen_module_names)
             50 STORE_NAME              11 (_frozen_module_names)
             52 POP_TOP

 13          54 NOP

 14          56 LOAD_CONST               1 (0)
             58 LOAD_CONST               3 (('create_dynamic',))
             60 IMPORT_NAME              1 (_imp)
             62 IMPORT_FROM             12 (create_dynamic)
             64 STORE_NAME              12 (create_dynamic)
             66 POP_TOP
             68 JUMP_FORWARD            13 (to 96)
        >>   70 PUSH_EXC_INFO

 15          72 LOAD_NAME               13 (ImportError)
             74 CHECK_EXC_MATCH
             76 POP_JUMP_IF_FALSE        5 (to 88)
             78 POP_TOP

 17          80 LOAD_CONST               4 (None)
             82 STORE_NAME              12 (create_dynamic)
             84 POP_EXCEPT
             86 JUMP_FORWARD             4 (to 96)

 15     >>   88 RERAISE                  0
        >>   90 COPY                     3
             92 POP_EXCEPT
             94 RERAISE                  1

 19     >>   96 LOAD_CONST               1 (0)
             98 LOAD_CONST               5 (('_ERR_MSG', '_exec', '_load', '_builtin_from_name'))
            100 IMPORT_NAME             14 (importlib._bootstrap)
            102 IMPORT_FROM             15 (_ERR_MSG)
            104 STORE_NAME              15 (_ERR_MSG)
            106 IMPORT_FROM             16 (_exec)
            108 STORE_NAME              16 (_exec)
            110 IMPORT_FROM             17 (_load)
            112 STORE_NAME              17 (_load)
            114 IMPORT_FROM             18 (_builtin_from_name)
            116 STORE_NAME              18 (_builtin_from_name)
            118 POP_TOP

 20         120 LOAD_CONST               1 (0)
            122 LOAD_CONST               6 (('SourcelessFileLoader',))
            124 IMPORT_NAME             19 (importlib._bootstrap_external)
            126 IMPORT_FROM             20 (SourcelessFileLoader)
            128 STORE_NAME              20 (SourcelessFileLoader)
            130 POP_TOP

 22         132 LOAD_CONST               1 (0)
            134 LOAD_CONST               7 (('machinery',))
            136 IMPORT_NAME             21 (importlib)
            138 IMPORT_FROM             22 (machinery)
            140 STORE_NAME              22 (machinery)
            142 POP_TOP

 23         144 LOAD_CONST               1 (0)
            146 LOAD_CONST               8 (('util',))
            148 IMPORT_NAME             21 (importlib)
            150 IMPORT_FROM             23 (util)
            152 STORE_NAME              23 (util)
            154 POP_TOP

 24         156 LOAD_CONST               1 (0)
            158 LOAD_CONST               4 (None)
            160 IMPORT_NAME             21 (importlib)
            162 STORE_NAME              21 (importlib)

 25         164 LOAD_CONST               1 (0)
            166 LOAD_CONST               4 (None)
            168 IMPORT_NAME             24 (os)
            170 STORE_NAME              24 (os)

 26         172 LOAD_CONST               1 (0)
            174 LOAD_CONST               4 (None)
            176 IMPORT_NAME             25 (sys)
            178 STORE_NAME              25 (sys)

 27         180 LOAD_CONST               1 (0)
            182 LOAD_CONST               4 (None)
            184 IMPORT_NAME             26 (tokenize)
            186 STORE_NAME              26 (tokenize)

 28         188 LOAD_CONST               1 (0)
            190 LOAD_CONST               4 (None)
            192 IMPORT_NAME             27 (types)
            194 STORE_NAME              27 (types)

 29         196 LOAD_CONST               1 (0)
            198 LOAD_CONST               4 (None)
            200 IMPORT_NAME             28 (warnings)
            202 STORE_NAME              28 (warnings)

 31         204 PUSH_NULL
            206 LOAD_NAME               28 (warnings)
            208 LOAD_ATTR               29 (NULL|self + importlib._bootstrap)
            228 CACHE
            230 CALL                     3
            238 CACHE
            240 POP_TOP

 37         242 LOAD_CONST               1 (0)
            244 STORE_NAME              31 (SEARCH_ERROR)

 38         246 LOAD_CONST              12 (1)
            248 STORE_NAME              32 (PY_SOURCE)

 39         250 LOAD_CONST              10 (2)
            252 STORE_NAME              33 (PY_COMPILED)

 40         254 LOAD_CONST              13 (3)
            256 STORE_NAME              34 (C_EXTENSION)

 41         258 LOAD_CONST              14 (4)
            260 STORE_NAME              35 (PY_RESOURCE)

 42         262 LOAD_CONST              15 (5)
            264 STORE_NAME              36 (PKG_DIRECTORY)

 43         266 LOAD_CONST              16 (6)
            268 STORE_NAME              37 (C_BUILTIN)

 44         270 LOAD_CONST              17 (7)
            272 STORE_NAME              38 (PY_FROZEN)

 45         274 LOAD_CONST              18 (8)
            276 STORE_NAME              39 (PY_CODERESOURCE)

 46         278 LOAD_CONST              19 (9)
            280 STORE_NAME              40 (IMP_HOOK)

 49         282 LOAD_CONST              20 (<code object new_module at 0x000001B2A8A48D50, file "imp.py", line 49>)
            284 MAKE_FUNCTION            0
            286 STORE_NAME              41 (new_module)

 60         288 LOAD_CONST              21 (<code object get_magic at 0x000001B2A768BE50, file "imp.py", line 60>)
            290 MAKE_FUNCTION            0
            292 STORE_NAME              42 (get_magic)

 68         294 LOAD_CONST              22 (<code object get_tag at 0x000001B2A8A48E40, file "imp.py", line 68>)
            296 MAKE_FUNCTION            0
            298 STORE_NAME              43 (get_tag)

 73         300 LOAD_CONST              42 ((None,))
            302 LOAD_CONST              23 (<code object cache_from_source at 0x000001B2A728E2B0, file "imp.py", line 73>)
            304 MAKE_FUNCTION            1 (defaults)
            306 STORE_NAME              44 (cache_from_source)