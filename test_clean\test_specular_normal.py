#!/usr/bin/env python3
"""
Test script for the advanced specular normal map algorithm
"""

import cv2
import numpy as np
import os
import sys
import time
from typing import List, Tuple, Optional

# Import the functions from our main module
from reconstructed_main import (
    create_specular_normal_map,
    generate_hemisphere_light_positions,
    normalize_intensity
)


def create_synthetic_specular_test_data():
    """
    Create synthetic test data with both diffuse and specular reflections
    """
    print("Creating synthetic specular test data...")
    
    # Create test directory
    test_dir = "specular_test_images"
    os.makedirs(test_dir, exist_ok=True)
    
    # Image parameters
    height, width = 256, 256
    center_x, center_y = width // 2, height // 2
    
    # Create a synthetic 3D object (sphere with varying roughness)
    sphere_radius = 80
    
    # Material properties
    diffuse_albedo = 0.7
    specular_albedo = 0.3
    specular_exponent = 20.0
    
    # Light setup (6 lights)
    light_positions = [
        (-1.0, 0.0, 1.0),   # Left
        (1.0, 0.0, 1.0),    # Right
        (0.0, -1.0, 1.0),   # Top
        (0.0, 1.0, 1.0),    # Bottom
        (0.0, 0.0, 1.0),    # Front
        (0.0, 0.0, -1.0)    # Back
    ]
    
    # Normalize light directions
    light_dirs = []
    for pos in light_positions:
        light_dir = np.array(pos)
        light_dir = light_dir / np.linalg.norm(light_dir)
        light_dirs.append(light_dir)
    
    # View direction (camera looking down)
    view_dir = np.array([0.0, 0.0, 1.0])
    
    directional_paths = []
    
    for i, light_dir in enumerate(light_dirs):
        print(f"Generating image {i+1}/6 with light direction {light_dir}")
        
        # Create image for this light direction
        lit_image = np.zeros((height, width), dtype=np.float32)
        
        for y in range(height):
            for x in range(width):
                # Calculate if point is on sphere
                dx = x - center_x
                dy = y - center_y
                dist_sq = dx*dx + dy*dy
                
                if dist_sq <= sphere_radius*sphere_radius:
                    # Calculate surface normal for sphere
                    dz = np.sqrt(sphere_radius*sphere_radius - dist_sq)
                    surface_normal = np.array([dx, dy, dz], dtype=np.float32)
                    surface_normal = surface_normal / np.linalg.norm(surface_normal)
                    
                    # Diffuse component: ρ_d * (n · l)
                    dot_nl = np.dot(surface_normal, light_dir)
                    diffuse_intensity = diffuse_albedo * max(0, dot_nl)
                    
                    # Specular component: ρ_s * (r · v)^α
                    reflection_dir = 2.0 * dot_nl * surface_normal - light_dir
                    dot_rv = np.dot(reflection_dir, view_dir)
                    specular_intensity = specular_albedo * np.power(max(0, dot_rv), specular_exponent)
                    
                    # Add some surface roughness variation
                    roughness_factor = 1.0 + 0.2 * np.sin(dx * 0.1) * np.cos(dy * 0.1)
                    specular_intensity *= roughness_factor
                    
                    # Total intensity
                    total_intensity = diffuse_intensity + specular_intensity
                    
                    # Add some noise
                    noise = np.random.normal(0, 0.02)
                    total_intensity += noise
                    
                    lit_image[y, x] = np.clip(total_intensity, 0.0, 1.0)
        
        # Convert to 8-bit and save
        lit_image_8bit = (lit_image * 255).astype(np.uint8)
        
        # Save directional image
        dir_path = os.path.join(test_dir, f"specular_directional_{i+1}.png")
        cv2.imwrite(dir_path, lit_image_8bit)
        directional_paths.append(dir_path)
        print(f"  Saved: {dir_path}")
    
    return directional_paths, light_positions


def test_specular_normal_map_basic():
    """Test basic specular normal map generation"""
    print("\n" + "="*60)
    print("Testing Basic Specular Normal Map Generation")
    print("="*60)
    
    # Create synthetic test data
    directional_paths, light_positions = create_synthetic_specular_test_data()
    
    # Test with default parameters
    output_path = "specular_test_images/specular_normal_basic.png"
    
    print(f"\nTesting with {len(directional_paths)} directional images...")
    
    success = create_specular_normal_map(
        directional_images=directional_paths,
        output_path=output_path
    )
    
    if success:
        print(f"✓ Basic specular normal map created: {output_path}")
        
        # Validate outputs
        if os.path.exists(output_path):
            normal_img = cv2.imread(output_path, cv2.IMREAD_UNCHANGED)
            print(f"  Normal map shape: {normal_img.shape}, dtype: {normal_img.dtype}")
            print(f"  Value range: [{np.min(normal_img)}, {np.max(normal_img)}]")
        
        # Check for albedo maps
        base_name = os.path.splitext(output_path)[0]
        diffuse_path = f"{base_name}_diffuse_albedo.png"
        specular_path = f"{base_name}_specular_albedo.png"
        
        if os.path.exists(diffuse_path):
            diffuse_img = cv2.imread(diffuse_path, cv2.IMREAD_GRAYSCALE)
            print(f"  Diffuse albedo range: [{np.min(diffuse_img)}, {np.max(diffuse_img)}]")
        
        if os.path.exists(specular_path):
            specular_img = cv2.imread(specular_path, cv2.IMREAD_GRAYSCALE)
            print(f"  Specular albedo range: [{np.min(specular_img)}, {np.max(specular_img)}]")
        
        return True
    else:
        print("✗ Basic specular normal map generation failed")
        return False


def test_specular_normal_map_advanced():
    """Test advanced specular normal map with custom parameters"""
    print("\n" + "="*60)
    print("Testing Advanced Specular Normal Map Generation")
    print("="*60)
    
    # Use existing test data
    directional_paths = []
    for i in range(1, 7):
        path = f"specular_test_images/specular_directional_{i}.png"
        if os.path.exists(path):
            directional_paths.append(path)
    
    if len(directional_paths) < 4:
        print("✗ Not enough test images found")
        return False
    
    # Test with custom parameters
    output_path = "specular_test_images/specular_normal_advanced.png"
    
    # Custom light positions
    custom_light_positions = [
        (-0.707, 0.0, 0.707),   # 45° left
        (0.707, 0.0, 0.707),    # 45° right
        (0.0, -0.707, 0.707),   # 45° top
        (0.0, 0.707, 0.707),    # 45° bottom
        (0.0, 0.0, 1.0),        # Front
        (0.0, 0.0, -1.0)        # Back
    ]
    
    # Custom view direction (slightly angled)
    view_direction = (0.1, 0.1, 0.98)
    
    # Lower roughness threshold for more sensitive specular detection
    roughness_threshold = 0.05
    
    print(f"Testing with custom parameters:")
    print(f"  Light positions: {len(custom_light_positions)} custom positions")
    print(f"  View direction: {view_direction}")
    print(f"  Roughness threshold: {roughness_threshold}")
    
    success = create_specular_normal_map(
        directional_images=directional_paths,
        output_path=output_path,
        light_positions=custom_light_positions,
        view_direction=view_direction,
        roughness_threshold=roughness_threshold
    )
    
    if success:
        print(f"✓ Advanced specular normal map created: {output_path}")
        return True
    else:
        print("✗ Advanced specular normal map generation failed")
        return False


def compare_with_standard_photometric_stereo():
    """Compare specular-aware method with standard photometric stereo"""
    print("\n" + "="*60)
    print("Comparing Specular-Aware vs Standard Photometric Stereo")
    print("="*60)
    
    # Use existing test data
    directional_paths = []
    for i in range(1, 7):
        path = f"specular_test_images/specular_directional_{i}.png"
        if os.path.exists(path):
            directional_paths.append(path)
    
    if len(directional_paths) < 6:
        print("✗ Not enough test images found")
        return False
    
    # Import standard method for comparison
    from reconstructed_main import create_object_normal_map
    
    # Generate standard normal map
    standard_output = "specular_test_images/standard_normal_comparison.png"
    print("Generating standard photometric stereo normal map...")
    
    success_standard = create_object_normal_map(directional_paths, standard_output)
    
    # Generate specular-aware normal map
    specular_output = "specular_test_images/specular_normal_comparison.png"
    print("Generating specular-aware normal map...")
    
    success_specular = create_specular_normal_map(directional_paths, specular_output)
    
    if success_standard and success_specular:
        print("✓ Both methods completed successfully")
        
        # Load and compare results
        standard_img = cv2.imread(standard_output, cv2.IMREAD_UNCHANGED)
        specular_img = cv2.imread(specular_output, cv2.IMREAD_UNCHANGED)
        
        if standard_img is not None and specular_img is not None:
            # Calculate difference
            diff = cv2.absdiff(standard_img, specular_img)
            mean_diff = np.mean(diff)
            max_diff = np.max(diff)
            
            print(f"  Standard normal map range: [{np.min(standard_img)}, {np.max(standard_img)}]")
            print(f"  Specular normal map range: [{np.min(specular_img)}, {np.max(specular_img)}]")
            print(f"  Mean difference: {mean_diff:.2f}")
            print(f"  Max difference: {max_diff}")
            
            # Save difference map
            diff_output = "specular_test_images/normal_map_difference.png"
            cv2.imwrite(diff_output, diff)
            print(f"  Difference map saved: {diff_output}")
            
            return True
        else:
            print("✗ Failed to load comparison images")
            return False
    else:
        print("✗ One or both methods failed")
        return False


def main():
    """Main test function"""
    print("Specular Normal Map Algorithm Test Suite")
    print("=" * 60)
    
    try:
        # Test basic functionality
        if not test_specular_normal_map_basic():
            print("\n✗ Basic test failed")
            return False
        
        # Test advanced functionality
        if not test_specular_normal_map_advanced():
            print("\n✗ Advanced test failed")
            return False
        
        # Compare with standard method
        if not compare_with_standard_photometric_stereo():
            print("\n✗ Comparison test failed")
            return False
        
        print("\n" + "="*60)
        print("✓ All specular normal map tests completed successfully!")
        print("Check the 'specular_test_images' directory for output files.")
        print("\nGenerated files:")
        print("- specular_normal_basic.png (basic specular normal map)")
        print("- specular_normal_basic_diffuse_albedo.png (diffuse component)")
        print("- specular_normal_basic_specular_albedo.png (specular component)")
        print("- specular_normal_advanced.png (advanced with custom parameters)")
        print("- standard_normal_comparison.png (standard photometric stereo)")
        print("- specular_normal_comparison.png (specular-aware method)")
        print("- normal_map_difference.png (difference visualization)")
        
        return True
        
    except Exception as e:
        print(f"\nTest failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
