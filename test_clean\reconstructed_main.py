#!/usr/bin/env python3
"""
ImgProc - Image Processing CLI Tool
Reconstructed from reverse engineering imgproc.exe

This tool provides various image processing functions including:
- Image alignment
- Normal map creation (object and tangent)
- Specular map creation
"""

import argparse
import cv2
import numpy as np
import os
import sys
import time
from typing import List, Tuple, Optional


def print_elapsed_time(func):
    """Print the elapsed time of the decorated function"""
    def wrapper(*args, **kwargs):
        start_time = time.time()
        print(f"imgproc: Started - {func.__name__}")
        result = func(*args, **kwargs)
        elapsed = time.time() - start_time
        hours = int(elapsed // 3600)
        minutes = int((elapsed % 3600) // 60)
        seconds = elapsed % 60
        print(f"imgproc: Complete - elapsed time: {hours:d}:{minutes:02d}:{seconds:0.2f}")
        return result
    return wrapper


def load_directional_images(directional_images: List[str]) -> List[np.ndarray]:
    """
    Load directional images for normal map creation

    Args:
        directional_images: List of image file paths

    Returns:
        List of loaded images or None if any image fails to load
    """
    images = []
    for i, image_path in enumerate(directional_images):
        if not os.path.exists(image_path):
            print(f"Image not found: {image_path}")
            return None

        image = cv2.imread(image_path, cv2.IMREAD_UNCHANGED)
        if image is None:
            print(f"Failed to load image: {image_path}")
            return None

        # Validate image dimensions consistency
        if i > 0 and image.shape != images[0].shape:
            print(f"Image {i+1} dimensions {image.shape} don't match first image {images[0].shape}")
            return None

        print(f"Image for direction {i+1}: {image_path} (shape: {image.shape})")
        images.append(image)

    return images


def normalize_intensity(image: np.ndarray) -> np.ndarray:
    """
    Normalize image intensity to [0, 1] range

    Args:
        image: Input image (8-bit or 16-bit)

    Returns:
        Normalized image as float32 or None if unsupported format
    """
    if image.dtype == np.uint8:
        normalized = image.astype(np.float32) / 255.0
        print(f"Normalized 8-bit image: min={np.min(normalized):.3f}, max={np.max(normalized):.3f}")
        return normalized
    elif image.dtype == np.uint16:
        normalized = image.astype(np.float32) / 65535.0
        print(f"Normalized 16-bit image: min={np.min(normalized):.3f}, max={np.max(normalized):.3f}")
        return normalized
    else:
        print(f"Image is not 8bit or 16bit (dtype: {image.dtype})")
        return None


def edge_preprocess_image(image: np.ndarray, edge_threshold: float = 0.1) -> np.ndarray:
    """
    Preprocess image with edge enhancement for better normal map quality

    Args:
        image: Input normalized image
        edge_threshold: Threshold for edge detection

    Returns:
        Edge-enhanced image
    """
    # Apply Canny edge detection
    edges = cv2.Canny((image * 255).astype(np.uint8), 50, 150)
    edges_normalized = edges.astype(np.float32) / 255.0

    # Combine original image with edge information
    enhanced = image + edges_normalized * edge_threshold
    enhanced = np.clip(enhanced, 0.0, 1.0)

    return enhanced


def validate_normal_map(normal_map: np.ndarray) -> bool:
    """
    Validate normal map for common issues

    Args:
        normal_map: Normal map to validate

    Returns:
        True if valid, False otherwise
    """
    if normal_map is None:
        print("Normal map is None")
        return False

    if len(normal_map.shape) != 3 or normal_map.shape[2] != 3:
        print(f"Invalid normal map shape: {normal_map.shape}")
        return False

    # Check value ranges
    min_val, max_val = np.min(normal_map), np.max(normal_map)
    if min_val < -1.1 or max_val > 1.1:
        print(f"Normal map values out of range: [{min_val:.3f}, {max_val:.3f}]")
        return False

    # Check for NaN or infinite values
    if np.any(np.isnan(normal_map)) or np.any(np.isinf(normal_map)):
        print("Normal map contains NaN or infinite values")
        return False

    print("Normal map validation passed")
    return True


@print_elapsed_time
def align_image(image1_path: str, image2_path: str, output_path: str,
                crop_fraction: float = 0.1, auto_crop_face: bool = False,
                face_mask_path: Optional[str] = None) -> bool:
    """
    Align two images using various alignment methods

    Args:
        image1_path: Path to the first image (base image)
        image2_path: Path to the second image (image to align)
        output_path: Path to save the aligned image
        crop_fraction: Crop fraction for alignment
        auto_crop_face: Auto detect and crop face for alignment
        face_mask_path: Path to mask image for face detection

    Returns:
        bool: True if successful, False otherwise
    """
    # Load images
    base_image = cv2.imread(image1_path, cv2.IMREAD_UNCHANGED)
    image_to_align = cv2.imread(image2_path, cv2.IMREAD_UNCHANGED)

    if base_image is None or image_to_align is None:
        print("Failed to load images")
        return False

    print(f"image min: {np.min(image_to_align)}, max: {np.max(image_to_align)}")

    # Perform alignment (placeholder - actual implementation would be more complex)
    aligned_image = image_to_align  # Simplified

    # Save aligned image
    cv2.imwrite(output_path, aligned_image)
    print(f"Image aligned and saved to {output_path}")

    return True


@print_elapsed_time
def align_image_set(image_dir: str, image_prefix: str, output_dir: str,
                   base_image_index: int = 0) -> bool:
    """
    Align a set of images with the same prefix

    Args:
        image_dir: Directory containing images
        image_prefix: Prefix for images to be aligned
        output_dir: Output directory
        base_image_index: Index of the base image

    Returns:
        bool: True if successful, False otherwise
    """
    # Get list of images with prefix
    image_files = [f for f in os.listdir(image_dir) if f.startswith(image_prefix)]
    image_files.sort()

    if not image_files:
        print(f"No images found with prefix: {image_prefix}")
        return False

    base_image_path = os.path.join(image_dir, image_files[base_image_index])
    print(f"Copied base image to {output_dir}")

    for i, image_file in enumerate(image_files):
        if i == base_image_index:
            continue

        image_path = os.path.join(image_dir, image_file)
        output_path = os.path.join(output_dir, f"aligned_{image_file}")

        # Align image
        align_image(base_image_path, image_path, output_path)
        print(f"Aligned and saved {image_file}")

    return True


@print_elapsed_time
def align_image_set_threaded(image_dir: str, image_prefix: str, output_dir: str,
                           base_image_index: int = 0) -> bool:
    """Threaded version of align_image_set"""
    # This would implement threading for faster processing
    return align_image_set(image_dir, image_prefix, output_dir, base_image_index)


@print_elapsed_time
def create_object_normal_map(directional_images: List[str], output_path: str) -> bool:
    """
    Create object normal map from directional images using photometric stereo

    This function implements photometric stereo to generate normal maps from
    6 directional lighting images. The algorithm assumes the following light directions:
    - Image 1: Light from left (-1, 0, 1)
    - Image 2: Light from right (1, 0, 1)
    - Image 3: Light from top (0, -1, 1)
    - Image 4: Light from bottom (0, 1, 1)
    - Image 5: Light from front (0, 0, 1)
    - Image 6: Light from back (0, 0, -1)

    Args:
        directional_images: Paths to six directional images
        output_path: Path to save the normal map

    Returns:
        bool: True if successful, False otherwise
    """
    if len(directional_images) != 6:
        print("Six image paths are required for create_object_normal_map function")
        return False

    # Load directional images
    images = load_directional_images(directional_images)
    if images is None:
        return False

    # Define light directions for 6-directional setup
    light_directions = np.array([
        [-1.0, 0.0, 1.0],   # Left
        [1.0, 0.0, 1.0],    # Right
        [0.0, -1.0, 1.0],   # Top
        [0.0, 1.0, 1.0],    # Bottom
        [0.0, 0.0, 1.0],    # Front
        [0.0, 0.0, -1.0]    # Back
    ])

    # Normalize light directions
    for i in range(len(light_directions)):
        light_directions[i] = light_directions[i] / np.linalg.norm(light_directions[i])

    print("Computing object normal map using photometric stereo...")

    # Get image dimensions
    height, width = images[0].shape[:2]

    # Convert images to grayscale and normalize
    normalized_images = []
    for i, img in enumerate(images):
        print(f"Processing directional image {i+1}/6...")

        # Convert to grayscale if needed
        if len(img.shape) == 3:
            gray_img = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
        else:
            gray_img = img.copy()

        # Normalize intensity
        normalized_img = normalize_intensity(gray_img)
        if normalized_img is None:
            return False

        normalized_images.append(normalized_img)

    # Create normal map using photometric stereo
    object_normal_map = photometric_stereo_6_lights(normalized_images, light_directions)

    # Convert normal map to 8-bit BGR format for OpenCV
    normal_map_bgr = convert_normal_to_bgr(object_normal_map)

    # Save normal map
    cv2.imwrite(output_path, normal_map_bgr)
    print(f"object_normal_map: {output_path}")

    return True


def photometric_stereo_6_lights(images: List[np.ndarray], light_directions: np.ndarray) -> np.ndarray:
    """
    Perform photometric stereo with 6 directional lights

    Args:
        images: List of 6 normalized grayscale images
        light_directions: 6x3 array of light direction vectors

    Returns:
        Normal map as float32 array with values in [-1, 1]
    """
    height, width = images[0].shape
    normal_map = np.zeros((height, width, 3), dtype=np.float32)

    # Process each pixel
    for y in range(height):
        if y % 50 == 0:  # Progress indicator
            print(f"Processing row {y}/{height}")

        for x in range(width):
            # Get intensities for this pixel from all 6 images
            intensities = np.array([img[y, x] for img in images])

            # Skip if any intensity is too low (shadow/noise)
            if np.any(intensities < 0.01):
                normal_map[y, x] = [0.0, 0.0, 1.0]  # Default upward normal
                continue

            # Solve for surface normal using least squares
            # L * n = I, where L is light matrix, n is normal, I is intensities
            try:
                # Use pseudo-inverse for overdetermined system
                normal = np.linalg.lstsq(light_directions, intensities, rcond=None)[0]

                # Normalize the normal vector
                norm_length = np.linalg.norm(normal)
                if norm_length > 0:
                    normal = normal / norm_length
                else:
                    normal = np.array([0.0, 0.0, 1.0])

                # Ensure Z component is positive (facing outward)
                if normal[2] < 0:
                    normal = -normal

                normal_map[y, x] = normal

            except np.linalg.LinAlgError:
                # If singular matrix, use default normal
                normal_map[y, x] = [0.0, 0.0, 1.0]

    return normal_map


def convert_normal_to_bgr(normal_map: np.ndarray) -> np.ndarray:
    """
    Convert normal map from [-1,1] range to BGR [0,255] format

    Args:
        normal_map: Normal map with values in [-1, 1]

    Returns:
        BGR image with values in [0, 255]
    """
    # Convert from [-1, 1] to [0, 1]
    normalized = (normal_map + 1.0) * 0.5

    # Convert to [0, 255] and ensure proper data type
    bgr_map = (normalized * 255.0).astype(np.uint8)

    # OpenCV uses BGR format, but normal maps are typically stored as RGB
    # So we need to swap R and B channels: (X, Y, Z) -> (Z, Y, X) in BGR
    bgr_map = bgr_map[:, :, [2, 1, 0]]

    return bgr_map


def generate_hemisphere_light_positions(num_lights: int) -> List[Tuple[float, float, float]]:
    """
    Generate light positions on a hemisphere for specular normal computation

    Args:
        num_lights: Number of light positions to generate

    Returns:
        List of 3D light position tuples
    """
    positions = []

    if num_lights == 4:
        # Standard 4-light setup: cardinal directions + zenith
        positions = [
            (-1.0, 0.0, 1.0),   # Left
            (1.0, 0.0, 1.0),    # Right
            (0.0, -1.0, 1.0),   # Top
            (0.0, 1.0, 1.0),    # Bottom
        ]
    elif num_lights == 6:
        # 6-light setup: cardinal directions + front/back
        positions = [
            (-1.0, 0.0, 1.0),   # Left
            (1.0, 0.0, 1.0),    # Right
            (0.0, -1.0, 1.0),   # Top
            (0.0, 1.0, 1.0),    # Bottom
            (0.0, 0.0, 1.0),    # Front
            (0.0, 0.0, -1.0),   # Back
        ]
    else:
        # Generate uniform hemisphere sampling
        for i in range(num_lights):
            # Spherical coordinates: theta (azimuth), phi (elevation)
            theta = 2.0 * np.pi * i / num_lights
            phi = np.pi / 4.0  # 45 degrees elevation

            x = np.cos(phi) * np.cos(theta)
            y = np.cos(phi) * np.sin(theta)
            z = np.sin(phi)

            positions.append((x, y, z))

    # Normalize all positions
    normalized_positions = []
    for pos in positions:
        pos_array = np.array(pos)
        normalized = pos_array / np.linalg.norm(pos_array)
        normalized_positions.append(tuple(normalized))

    return normalized_positions


def compute_specular_photometric_stereo(images: List[np.ndarray],
                                      light_positions: List[Tuple[float, float, float]],
                                      view_direction: Tuple[float, float, float],
                                      roughness_threshold: float) -> Tuple[np.ndarray, np.ndarray, np.ndarray]:
    """
    Compute specular-aware photometric stereo following Ma et al. 2007 method

    This implementation separates diffuse and specular reflections to compute
    more accurate surface normals that account for both reflection types.

    Args:
        images: List of normalized grayscale images
        light_positions: List of 3D light position tuples
        view_direction: Camera/view direction vector
        roughness_threshold: Threshold for diffuse/specular separation

    Returns:
        Tuple of (normal_map, diffuse_albedo, specular_albedo)
    """
    height, width = images[0].shape
    num_lights = len(images)

    # Convert inputs to numpy arrays
    light_dirs = np.array(light_positions)  # Shape: (num_lights, 3)
    view_dir = np.array(view_direction)
    view_dir = view_dir / np.linalg.norm(view_dir)

    # Initialize output arrays
    normal_map = np.zeros((height, width, 3), dtype=np.float32)
    diffuse_albedo = np.zeros((height, width), dtype=np.float32)
    specular_albedo = np.zeros((height, width), dtype=np.float32)

    print("Computing specular-aware photometric stereo...")

    for y in range(height):
        if y % 50 == 0:
            print(f"Processing row {y}/{height}")

        for x in range(width):
            # Get intensities for this pixel
            intensities = np.array([img[y, x] for img in images])

            # Skip dark pixels
            if np.max(intensities) < 0.01:
                normal_map[y, x] = [0.0, 0.0, 1.0]
                continue

            # Separate diffuse and specular components
            normal, diffuse_rho, specular_rho = solve_specular_photometric_stereo(
                intensities, light_dirs, view_dir, roughness_threshold
            )

            normal_map[y, x] = normal
            diffuse_albedo[y, x] = diffuse_rho
            specular_albedo[y, x] = specular_rho

    return normal_map, diffuse_albedo, specular_albedo


def solve_specular_photometric_stereo(intensities: np.ndarray,
                                    light_dirs: np.ndarray,
                                    view_dir: np.ndarray,
                                    roughness_threshold: float) -> Tuple[np.ndarray, float, float]:
    """
    Solve for surface normal considering both diffuse and specular reflections

    Based on the dichromatic reflection model:
    I = ρ_d * (n · l) + ρ_s * (r · v)^α

    Where:
    - I: observed intensity
    - ρ_d: diffuse albedo
    - ρ_s: specular albedo
    - n: surface normal
    - l: light direction
    - r: reflection direction
    - v: view direction
    - α: specular exponent (related to surface roughness)

    Args:
        intensities: Observed intensities for all lights
        light_dirs: Light direction vectors
        view_dir: View direction vector
        roughness_threshold: Threshold for diffuse/specular separation

    Returns:
        Tuple of (normal_vector, diffuse_albedo, specular_albedo)
    """
    num_lights = len(intensities)

    # Initial estimate using standard photometric stereo (diffuse assumption)
    try:
        # Solve L * (ρ_d * n) = I for diffuse case
        diffuse_solution = np.linalg.lstsq(light_dirs, intensities, rcond=None)[0]

        # Extract normal and diffuse albedo
        diffuse_rho = np.linalg.norm(diffuse_solution)
        if diffuse_rho > 0:
            normal_estimate = diffuse_solution / diffuse_rho
        else:
            normal_estimate = np.array([0.0, 0.0, 1.0])
            diffuse_rho = 0.0

    except np.linalg.LinAlgError:
        normal_estimate = np.array([0.0, 0.0, 1.0])
        diffuse_rho = 0.0

    # Ensure normal points outward
    if normal_estimate[2] < 0:
        normal_estimate = -normal_estimate

    # Compute specular component
    specular_rho = 0.0

    # Calculate reflection directions for each light
    reflection_dirs = []
    for light_dir in light_dirs:
        # r = 2(n·l)n - l
        dot_nl = np.dot(normal_estimate, light_dir)
        reflection_dir = 2.0 * dot_nl * normal_estimate - light_dir
        reflection_dirs.append(reflection_dir)

    reflection_dirs = np.array(reflection_dirs)

    # Compute specular intensities (r·v)^α
    specular_terms = []
    alpha = 10.0  # Specular exponent (can be made adaptive)

    for reflection_dir in reflection_dirs:
        dot_rv = np.dot(reflection_dir, view_dir)
        if dot_rv > 0:
            specular_term = np.power(dot_rv, alpha)
        else:
            specular_term = 0.0
        specular_terms.append(specular_term)

    specular_terms = np.array(specular_terms)

    # Estimate specular albedo if there are significant specular highlights
    if np.max(specular_terms) > roughness_threshold:
        # Compute diffuse component
        diffuse_intensities = diffuse_rho * np.maximum(0, np.dot(light_dirs, normal_estimate))

        # Residual should be specular
        residual_intensities = intensities - diffuse_intensities

        # Fit specular component
        if np.sum(specular_terms) > 0:
            specular_rho = np.sum(residual_intensities * specular_terms) / np.sum(specular_terms * specular_terms)
            specular_rho = max(0.0, specular_rho)  # Ensure non-negative

        # Refine normal estimate considering specular component
        if specular_rho > 0.01:  # Significant specular component
            normal_estimate = refine_normal_with_specular(
                intensities, light_dirs, view_dir, normal_estimate,
                diffuse_rho, specular_rho, alpha
            )

    # Final normalization
    normal_estimate = normal_estimate / np.linalg.norm(normal_estimate)

    return normal_estimate, diffuse_rho, specular_rho


def refine_normal_with_specular(intensities: np.ndarray,
                               light_dirs: np.ndarray,
                               view_dir: np.ndarray,
                               initial_normal: np.ndarray,
                               diffuse_rho: float,
                               specular_rho: float,
                               alpha: float,
                               max_iterations: int = 5) -> np.ndarray:
    """
    Refine normal estimate using iterative optimization considering specular reflections

    Args:
        intensities: Observed intensities
        light_dirs: Light directions
        view_dir: View direction
        initial_normal: Initial normal estimate
        diffuse_rho: Diffuse albedo
        specular_rho: Specular albedo
        alpha: Specular exponent
        max_iterations: Maximum optimization iterations

    Returns:
        Refined normal vector
    """
    normal = initial_normal.copy()

    for iteration in range(max_iterations):
        # Compute predicted intensities
        predicted_intensities = []

        for light_dir in light_dirs:
            # Diffuse component: ρ_d * (n · l)
            diffuse_term = diffuse_rho * max(0, np.dot(normal, light_dir))

            # Specular component: ρ_s * (r · v)^α
            dot_nl = np.dot(normal, light_dir)
            reflection_dir = 2.0 * dot_nl * normal - light_dir
            dot_rv = np.dot(reflection_dir, view_dir)
            specular_term = specular_rho * np.power(max(0, dot_rv), alpha)

            predicted_intensity = diffuse_term + specular_term
            predicted_intensities.append(predicted_intensity)

        predicted_intensities = np.array(predicted_intensities)

        # Compute error
        error = intensities - predicted_intensities
        error_magnitude = np.linalg.norm(error)

        if error_magnitude < 0.001:  # Convergence threshold
            break

        # Simple gradient descent update (simplified)
        # In practice, this would use proper Jacobian computation
        gradient = np.zeros(3)
        epsilon = 0.001

        for i in range(3):
            normal_plus = normal.copy()
            normal_plus[i] += epsilon
            normal_plus = normal_plus / np.linalg.norm(normal_plus)

            predicted_plus = []
            for light_dir in light_dirs:
                diffuse_term = diffuse_rho * max(0, np.dot(normal_plus, light_dir))
                dot_nl = np.dot(normal_plus, light_dir)
                reflection_dir = 2.0 * dot_nl * normal_plus - light_dir
                dot_rv = np.dot(reflection_dir, view_dir)
                specular_term = specular_rho * np.power(max(0, dot_rv), alpha)
                predicted_plus.append(diffuse_term + specular_term)

            predicted_plus = np.array(predicted_plus)
            error_plus = np.linalg.norm(intensities - predicted_plus)

            gradient[i] = (error_plus - error_magnitude) / epsilon

        # Update normal
        learning_rate = 0.1
        normal = normal - learning_rate * gradient
        normal = normal / np.linalg.norm(normal)

        # Ensure normal points outward
        if normal[2] < 0:
            normal = -normal

    return normal


@print_elapsed_time
def create_tangent_normal_map(image1_path: str, output_path: str,
                            blur_size: int = 5, kernel_size: int = 3,
                            sigma: float = 1.0, remove_blue: bool = False) -> bool:
    """
    Create tangent normal map from height map using gradient-based method

    This function converts a height map (grayscale image) into a tangent space normal map
    by computing gradients and applying various filtering options.

    Args:
        image1_path: Path to input height map image (16-bit preferred)
        output_path: Path to save the tangent normal map
        blur_size: Blur size for pre-processing the height map
        kernel_size: Kernel size for gradient computation (Sobel filter)
        sigma: Sigma value for Gaussian filtering
        remove_blue: Remove blue channel from tangent normal map

    Returns:
        bool: True if successful, False otherwise
    """
    # Load image
    image = cv2.imread(image1_path, cv2.IMREAD_UNCHANGED)
    if image is None:
        print(f"Image not found: {image1_path}")
        return False

    print(f"Input image shape: {image.shape}, dtype: {image.dtype}")

    # Convert to grayscale if needed
    if len(image.shape) == 3:
        height_map = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
    else:
        height_map = image.copy()

    # Validate bit depth (prefer 16-bit but accept 8-bit)
    if image.dtype == np.uint16:
        print("Processing 16-bit height map")
        height_map = height_map.astype(np.float32) / 65535.0
    elif image.dtype == np.uint8:
        print("Processing 8-bit height map")
        height_map = height_map.astype(np.float32) / 255.0
    else:
        print("Image is not 8bit or 16bit")
        return False

    # Create height map for normal generation
    processed_height_map = create_height_map(height_map, blur_size, sigma)

    # Generate tangent normal map from height map
    tangent_normal_map = height_map_to_normal_map(processed_height_map, kernel_size)

    # Apply post-processing
    if remove_blue:
        print("Removing blue channel from tangent normal map")
        tangent_normal_map[:, :, 2] = 0.5  # Set Z to neutral (0.5 in [0,1] range)

    # Convert to 8-bit BGR for output
    normal_map_bgr = (tangent_normal_map * 255.0).astype(np.uint8)

    # Save tangent normal map
    cv2.imwrite(output_path, normal_map_bgr)
    print(f"Tangent normal map saved to: {output_path}")

    return True


def create_height_map(input_image: np.ndarray, blur_size: int, sigma: float) -> np.ndarray:
    """
    Create and preprocess height map for normal map generation

    Args:
        input_image: Input grayscale image normalized to [0, 1]
        blur_size: Size of blur kernel
        sigma: Gaussian blur sigma value

    Returns:
        Processed height map
    """
    height_map = input_image.copy()

    # Apply Gaussian blur to smooth the height map
    if blur_size > 1:
        # Ensure kernel size is odd
        kernel_size = blur_size if blur_size % 2 == 1 else blur_size + 1
        height_map = cv2.GaussianBlur(height_map, (kernel_size, kernel_size), sigma)
        print(f"Applied Gaussian blur: kernel={kernel_size}, sigma={sigma}")

    return height_map


def height_map_to_normal_map(height_map: np.ndarray, kernel_size: int = 3) -> np.ndarray:
    """
    Convert height map to normal map using gradient computation

    Args:
        height_map: Input height map normalized to [0, 1]
        kernel_size: Size of Sobel kernel for gradient computation

    Returns:
        Normal map with values in [0, 1] range (RGB format)
    """
    print("Computing gradients for normal map generation...")

    # Compute gradients using Sobel operators
    # Note: OpenCV Sobel uses different kernel sizes, we'll use the standard 3x3
    grad_x = cv2.Sobel(height_map, cv2.CV_32F, 1, 0, ksize=kernel_size)
    grad_y = cv2.Sobel(height_map, cv2.CV_32F, 0, 1, ksize=kernel_size)

    # Scale gradients (adjust strength of normal map)
    scale_factor = 1.0  # Can be adjusted for stronger/weaker normals
    grad_x *= scale_factor
    grad_y *= scale_factor

    # Create normal vectors
    # In tangent space: X = -grad_x, Y = -grad_y, Z = 1
    height, width = height_map.shape
    normal_map = np.zeros((height, width, 3), dtype=np.float32)

    # X component (red channel): negative X gradient
    normal_map[:, :, 0] = -grad_x

    # Y component (green channel): negative Y gradient
    normal_map[:, :, 1] = -grad_y

    # Z component (blue channel): always positive (pointing up)
    normal_map[:, :, 2] = 1.0

    # Normalize each normal vector
    print("Normalizing normal vectors...")
    for y in range(height):
        for x in range(width):
            normal = normal_map[y, x]
            length = np.linalg.norm(normal)
            if length > 0:
                normal_map[y, x] = normal / length
            else:
                normal_map[y, x] = [0.0, 0.0, 1.0]  # Default upward normal

    # Convert from [-1, 1] to [0, 1] range for storage
    normal_map = (normal_map + 1.0) * 0.5

    # Ensure Z component is always > 0.5 (pointing outward)
    normal_map[:, :, 2] = np.maximum(normal_map[:, :, 2], 0.5)

    return normal_map


@print_elapsed_time
def create_specular_normal_map(directional_images: List[str], output_path: str,
                              light_positions: Optional[List[Tuple[float, float, float]]] = None,
                              view_direction: Tuple[float, float, float] = (0.0, 0.0, 1.0),
                              roughness_threshold: float = 0.1) -> bool:
    """
    Create specular normal map using photometric stereo with specular reflection analysis

    Based on "Rapid Acquisition of Specular and Diffuse Normal Maps from Polarized Spherical Gradient Illumination"
    and "Real-time Acquisition and Rendering of Dynamic 3D Geometry and Reflectance"

    This method separates diffuse and specular components to compute accurate surface normals
    that account for both diffuse and specular reflections.

    Args:
        directional_images: List of paths to directional lighting images (minimum 4, recommended 6+)
        output_path: Path to save the specular normal map
        light_positions: Optional list of 3D light positions. If None, uses default hemisphere sampling
        view_direction: Camera/view direction vector (default: looking down Z-axis)
        roughness_threshold: Threshold for separating diffuse vs specular reflections

    Returns:
        bool: True if successful, False otherwise
    """
    if len(directional_images) < 4:
        print("At least 4 directional images are required for specular normal map computation")
        return False

    # Load and validate images
    images = load_directional_images(directional_images)
    if images is None:
        return False

    # Set up light positions if not provided
    if light_positions is None:
        light_positions = generate_hemisphere_light_positions(len(directional_images))

    print(f"Computing specular normal map using {len(directional_images)} directional images...")

    # Convert images to normalized grayscale
    normalized_images = []
    for i, img in enumerate(images):
        if len(img.shape) == 3:
            gray_img = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
        else:
            gray_img = img.copy()

        normalized_img = normalize_intensity(gray_img)
        if normalized_img is None:
            return False
        normalized_images.append(normalized_img)

    # Perform specular-aware photometric stereo
    specular_normal_map, diffuse_albedo, specular_albedo = compute_specular_photometric_stereo(
        normalized_images, light_positions, view_direction, roughness_threshold
    )

    # Convert to BGR format and save
    normal_map_bgr = convert_normal_to_bgr(specular_normal_map)
    cv2.imwrite(output_path, normal_map_bgr)

    # Also save albedo maps
    base_name = os.path.splitext(output_path)[0]
    cv2.imwrite(f"{base_name}_diffuse_albedo.png", (diffuse_albedo * 255).astype(np.uint8))
    cv2.imwrite(f"{base_name}_specular_albedo.png", (specular_albedo * 255).astype(np.uint8))

    print(f"Specular normal map saved to: {output_path}")
    print(f"Diffuse albedo saved to: {base_name}_diffuse_albedo.png")
    print(f"Specular albedo saved to: {base_name}_specular_albedo.png")

    return True


@print_elapsed_time
def create_specular_map(image1_path: str, image2_path: str, output_path: str) -> bool:
    """
    Create simple specular map from two images (legacy method)

    Args:
        image1_path: Path to the first image
        image2_path: Path to the second image
        output_path: Path to save the specular map

    Returns:
        bool: True if successful, False otherwise
    """
    # Load images
    image1 = cv2.imread(image1_path, cv2.IMREAD_UNCHANGED)
    image2 = cv2.imread(image2_path, cv2.IMREAD_UNCHANGED)

    if image1 is None or image2 is None:
        print("Failed to load images")
        return False

    if image1.shape != image2.shape:
        print("Images must be of the same dimensions to calculate the specular map.")
        return False

    # Create specular map using difference
    specular_map = cv2.absdiff(image1, image2)

    # Save specular map
    cv2.imwrite(output_path, specular_map)
    print(f"Simple specular map saved to: {output_path}")

    return True

def main():
    """Main function - ImgProc CLI Tool"""
    parser = argparse.ArgumentParser(description='ImgProc CLI Tool')

    # Add function argument
    parser.add_argument('--function',
                        choices=['align_image', 'align_image_set', 'align_image_set_threaded',
                                'create_object_normal_map', 'create_tangent_normal_map',
                                'create_specular_map', 'create_specular_normal_map'],
                        required=True,
                        help='Function to execute')

    # Image arguments
    parser.add_argument('--image1', help='Path to the first image')
    parser.add_argument('--image2', help='Path to the second image')
    parser.add_argument('--image_dir', help='Directory containing images (required for align_image_set)')
    parser.add_argument('--image_prefix', help='Prefix for images to be aligned (required for align_image_set)')
    parser.add_argument('--directional_images', nargs=6, help='Paths to six images (required for create_object_normal_map)')

    # Output argument
    parser.add_argument('--output', required=True, help='Path to save the output (directory or file depending on the function)')

    # Optional arguments
    parser.add_argument('--base_image_index', type=int, default=0, help='Index of the base image (required for align_image_set)')
    parser.add_argument('--crop_fraction', type=float, default=0.1, help='Crop fraction for alignment')
    parser.add_argument('--auto_crop_face', action='store_true', help='Auto detect and crop face for alignment')
    parser.add_argument('--face_mask_path', help='Path to mask image for face detection')

    # Tangent normal map specific arguments
    parser.add_argument('--blur_size', type=int, default=5, help='Blur size for tangent normal map')
    parser.add_argument('--kernel_size', type=int, default=3, help='Kernel size for tangent normal filtering')
    parser.add_argument('--sigma', type=float, default=1.0, help='Sigma value for tangent normal filtering')
    parser.add_argument('--remove_blue', action='store_true', help='Remove blue channel from tangent normal map')

    # Specular normal map specific arguments
    parser.add_argument('--view_direction', nargs=3, type=float, default=[0.0, 0.0, 1.0],
                        help='Camera/view direction vector (x y z)')
    parser.add_argument('--roughness_threshold', type=float, default=0.1,
                        help='Threshold for separating diffuse vs specular reflections')
    parser.add_argument('--light_positions', nargs='+', type=str,
                        help='Custom light positions as "x,y,z" strings (optional)')

    args = parser.parse_args()

    # Validate arguments based on function
    if args.function == 'align_image':
        if not args.image1 or not args.image2:
            print("--image1 --image2 --output are required for align_image function")
            return False
        return align_image(args.image1, args.image2, args.output,
                          args.crop_fraction, args.auto_crop_face, args.face_mask_path)

    elif args.function in ['align_image_set', 'align_image_set_threaded']:
        if not args.image_dir or not args.image_prefix:
            print("--image_dir and --image_prefix are required for align_image_set function")
            return False

        if args.function == 'align_image_set_threaded':
            return align_image_set_threaded(args.image_dir, args.image_prefix, args.output, args.base_image_index)
        else:
            return align_image_set(args.image_dir, args.image_prefix, args.output, args.base_image_index)

    elif args.function == 'create_object_normal_map':
        if not args.directional_images or len(args.directional_images) != 6:
            print("Six image paths are required for create_object_normal_map function")
            return False
        return create_object_normal_map(args.directional_images, args.output)

    elif args.function == 'create_tangent_normal_map':
        if not args.image1:
            print("--image1 is required for create_tangent_normal_map function")
            return False
        return create_tangent_normal_map(args.image1, args.output,
                                       args.blur_size, args.kernel_size, args.sigma, args.remove_blue)

    elif args.function == 'create_specular_map':
        if not args.image1 or not args.image2:
            print("Both --image1 and --image2 are required for create_specular_map function")
            return False
        return create_specular_map(args.image1, args.image2, args.output)

    elif args.function == 'create_specular_normal_map':
        if not args.directional_images or len(args.directional_images) < 4:
            print("At least 4 directional images are required for create_specular_normal_map function")
            return False

        # Parse custom light positions if provided
        light_positions = None
        if args.light_positions:
            try:
                light_positions = []
                for pos_str in args.light_positions:
                    x, y, z = map(float, pos_str.split(','))
                    light_positions.append((x, y, z))
            except ValueError:
                print("Invalid light position format. Use 'x,y,z' format for each position")
                return False

        return create_specular_normal_map(
            args.directional_images,
            args.output,
            light_positions,
            tuple(args.view_direction),
            args.roughness_threshold
        )

    else:
        parser.print_help()
        return False


if __name__ == '__main__':
    # success = main()
    # if not success:
    #     sys.exit(1)
    create_object_normal_map(
        directional_images=[
        r"Z:\MoCap-LA\TEST DATA\Gus_base\Photos\A\A2\A2_9.tif",
        r"Z:\MoCap-LA\TEST DATA\Gus_base\Photos\A\A2\A2_10.tif",
        r"Z:\MoCap-LA\TEST DATA\Gus_base\Photos\A\A2\A2_11.tif",
        r"Z:\MoCap-LA\TEST DATA\Gus_base\Photos\A\A2\A2_12.tif",
        r"Z:\MoCap-LA\TEST DATA\Gus_base\Photos\A\A2\A2_13.tif",
        r"Z:\MoCap-LA\TEST DATA\Gus_base\Photos\A\A2\A2_14.tif"
    ],
        output_path=r"Z:\MoCap-LA\TEST DATA\Gus_base\Photos\Maps\SpecularNormalMap\object_normal_output.png"
    )


# USAGE EXAMPLES (from extracted strings):
# ./imgproc --function align_image --image1 path/to/image1.tif --image2 path/to/image2.tif --output path/to/output.tif
# ./imgproc --function align_image_set --image_dir path/to/images --image_prefix TX-01 --output path/to/output
# ./imgproc --function create_object_normal_map --directional_images path/to/image1.tif path/to/image2.tif path/to/image3.tif path/to/image4.tif path/to/image5.tif path/to/image6.tif --output path/to/output.tif
# ./imgproc --function create_tangent_normal_map --image1 path/to/image1.tif --output path/to/output.tif
# ./imgproc --function create_specular_map --image1 path/to/image1.tif --image2 path/to/image2.tif --output path/to/output.tif


# REVERSE ENGINEERING NOTES:
# This script was reconstructed by reverse engineering imgproc.exe
# Key functions identified:
# - align_image: Align two images using various methods (AKAZE, ECC, optical flow)
# - align_image_set: Align multiple images with same prefix
# - align_image_set_threaded: Threaded version for better performance
# - create_object_normal_map: Create normal maps from 6 directional images
# - create_tangent_normal_map: Create tangent normal maps with filtering options
# - create_specular_map: Create specular maps from two images
#
# The original application appears to use OpenCV for image processing
# and includes face detection capabilities for alignment.
