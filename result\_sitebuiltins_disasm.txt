# Code object from position 7731438
# Filename: _sitebuiltins.py
# Name: <module>
# Args: 0
# Locals: 0

  0           0 RESUME                   0

  1           2 LOAD_CONST               0 ('\nThe objects used by the site module to add custom builtins.\n')
              4 STORE_NAME               0 (__doc__)

 11           6 LOAD_CONST               1 (0)
              8 LOAD_CONST               2 (None)
             10 IMPORT_NAME              1 (sys)
             12 STORE_NAME               1 (sys)

 13          14 PUSH_NULL
             16 LOAD_BUILD_CLASS
             18 LOAD_CONST               3 (<code object Quitter at 0x000001A2D0632010, file "_sitebuiltins.py", line 13>)
             20 MAKE_FUNCTION            0
             22 LOAD_CONST               4 ('Quitter')
             24 LOAD_NAME                2 (object)
             26 UNPACK_SEQUENCE          3
             30 CALL                     3
             38 CACHE
             40 STORE_NAME               3 (Quitter)

 29          42 PUSH_NULL
             44 LOAD_BUILD_CLASS
             46 LOAD_CONST               5 (<code object _Printer at 0x000001A2D06321F0, file "_sitebuiltins.py", line 29>)
             48 MAKE_FUNCTION            0
             50 LOAD_CONST               6 ('_Printer')
             52 LOAD_NAME                2 (object)
             54 UNPACK_SEQUENCE          3
             58 CALL                     3
             66 CACHE
             68 STORE_NAME               4 (_Printer)

 88          70 PUSH_NULL
             72 LOAD_BUILD_CLASS
             74 LOAD_CONST               7 (<code object _Helper at 0x000001A2D05D7AD0, file "_sitebuiltins.py", line 88>)
             76 MAKE_FUNCTION            0
             78 LOAD_CONST               8 ('_Helper')
             80 LOAD_NAME                2 (object)
             82 UNPACK_SEQUENCE          3
             86 CALL                     3
             94 CACHE
             96 STORE_NAME               5 (_Helper)
             98 LOAD_CONST               2 (None)
            100 RETURN_VALUE

Disassembly of <code object Quitter at 0x000001A2D0632010, file "_sitebuiltins.py", line 13>:
 13           0 RESUME                   0
              2 LOAD_NAME                0 (__name__)
              4 STORE_NAME               1 (__module__)
              6 LOAD_CONST               0 ('Quitter')
              8 STORE_NAME               2 (__qualname__)

 14          10 LOAD_CONST               1 (<code object __init__ at 0x000001A2D05CAB50, file "_sitebuiltins.py", line 14>)
             12 MAKE_FUNCTION            0
             14 STORE_NAME               3 (__init__)

 17          16 LOAD_CONST               2 (<code object __repr__ at 0x000001A2D06316B0, file "_sitebuiltins.py", line 17>)
             18 MAKE_FUNCTION            0
             20 STORE_NAME               4 (__repr__)

 19          22 LOAD_CONST               5 ((None,))
             24 LOAD_CONST               4 (<code object __call__ at 0x000001A2D0628FA0, file "_sitebuiltins.py", line 19>)
             26 MAKE_FUNCTION            1 (defaults)
             28 STORE_NAME               5 (__call__)
             30 LOAD_CONST               3 (None)
             32 RETURN_VALUE

Disassembly of <code object __init__ at 0x000001A2D05CAB50, file "_sitebuiltins.py", line 14>:
 14           0 RESUME                   0

 15           2 LOAD_FAST                1 (name)
              4 LOAD_FAST                0 (self)
              6 STORE_ATTR               0 (name)

 16          16 LOAD_FAST                2 (eof)
             18 LOAD_FAST                0 (self)
             20 STORE_ATTR               1 (eof)
             30 LOAD_CONST               0 (None)
             32 RETURN_VALUE

Disassembly of <code object __repr__ at 0x000001A2D06316B0, file "_sitebuiltins.py", line 17>:
 17           0 RESUME                   0
              2 LOAD_CONST               1 ('Use ')

 18           4 LOAD_FAST                0 (self)
              6 LOAD_ATTR                0 (name)
             26 CACHE
             28 CACHE
             30 CACHE
             32 FORMAT_VALUE             1 (str)
             34 LOAD_CONST               3 (' to exit')
             36 BUILD_STRING             5
             38 RETURN_VALUE

Disassembly of <code object __call__ at 0x000001A2D0628FA0, file "_sitebuiltins.py", line 19>:
 19           0 RESUME                   0

 22           2 NOP

 23           4 LOAD_GLOBAL              0 (sys)
             14 CACHE
             16 LOAD_ATTR                1 (NULL|self + sys)
             36 CACHE
             38 CACHE
             40 CACHE
             42 CACHE
             44 CACHE
             46 CACHE
             48 UNPACK_SEQUENCE          0
             52 CALL                     0
             60 CACHE
             62 POP_TOP
             64 JUMP_FORWARD             7 (to 80)
        >>   66 PUSH_EXC_INFO

 24          68 POP_TOP

 25          70 POP_EXCEPT
             72 JUMP_FORWARD             3 (to 80)
        >>   74 COPY                     3
             76 POP_EXCEPT
             78 RERAISE                  1

 26     >>   80 LOAD_GLOBAL              7 (NULL + SystemExit)
             90 CACHE
             92 LOAD_FAST                1 (code)
             94 UNPACK_SEQUENCE          1
             98 CALL                     1
            106 CACHE
            108 RAISE_VARARGS            1
ExceptionTable:
  4 to 62 -> 66 [0]
  66 to 68 -> 74 [1] lasti

Disassembly of <code object _Printer at 0x000001A2D06321F0, file "_sitebuiltins.py", line 29>:
 29           0 RESUME                   0
              2 LOAD_NAME                0 (__name__)
              4 STORE_NAME               1 (__module__)
              6 LOAD_CONST               0 ('_Printer')
              8 STORE_NAME               2 (__qualname__)

 30          10 LOAD_CONST               1 ('interactive prompt objects for printing the license text, a list of\n    contributors and the copyright notice.')
             12 STORE_NAME               3 (__doc__)

 33          14 LOAD_CONST               2 (23)
             16 STORE_NAME               4 (MAXLINES)

 35          18 LOAD_CONST               9 (((), ()))
             20 LOAD_CONST               4 (<code object __init__ at 0x000001A2D0629460, file "_sitebuiltins.py", line 35>)
             22 MAKE_FUNCTION            1 (defaults)
             24 STORE_NAME               5 (__init__)

 44          26 LOAD_CONST               5 (<code object __setup at 0x000001A2CDE8B900, file "_sitebuiltins.py", line 44>)
             28 MAKE_FUNCTION            0
             30 STORE_NAME               6 (_Printer__setup)

 60          32 LOAD_CONST               6 (<code object __repr__ at 0x000001A2D0152EB0, file "_sitebuiltins.py", line 60>)
             34 MAKE_FUNCTION            0
             36 STORE_NAME               7 (__repr__)

 67          38 LOAD_CONST               7 (<code object __call__ at 0x000001A2D01911A0, file "_sitebuiltins.py", line 67>)
             40 MAKE_FUNCTION            0
             42 STORE_NAME               8 (__call__)
             44 LOAD_CONST               8 (None)
             46 RETURN_VALUE

Disassembly of <code object __init__ at 0x000001A2D0629460, file "_sitebuiltins.py", line 35>:
              0 MAKE_CELL                3 (files)
              2 MAKE_CELL                5 (os)

 35           4 RESUME                   0

 36           6 LOAD_CONST               1 (0)
              8 LOAD_CONST               0 (None)
             10 IMPORT_NAME              0 (os)
             12 STORE_DEREF              5 (os)

 37          14 LOAD_FAST                1 (name)
             16 LOAD_FAST                0 (self)
             18 STORE_ATTR               1 (_Printer__name)

 38          28 LOAD_FAST                2 (data)
             30 LOAD_FAST                0 (self)
             32 STORE_ATTR               2 (_Printer__data)

 39          42 LOAD_CONST               0 (None)
             44 LOAD_FAST                0 (self)
             46 STORE_ATTR               3 (_Printer__lines)

 40          56 LOAD_CLOSURE             3 (files)
             58 LOAD_CLOSURE             5 (os)
             60 BUILD_TUPLE              2
             62 LOAD_CONST               2 (<code object <listcomp> at 0x000001A2D0641570, file "_sitebuiltins.py", line 40>)
             64 MAKE_FUNCTION            8 (closure)

 41          66 LOAD_FAST                4 (dirs)

 40          68 GET_ITER
             70 UNPACK_SEQUENCE          0
             74 CALL                     0
             82 CACHE
             84 LOAD_FAST                0 (self)
             86 STORE_ATTR               4 (_Printer__filenames)
             96 LOAD_CONST               0 (None)
             98 RETURN_VALUE

Disassembly of <code object <listcomp> at 0x000001A2D0641570, file "_sitebuiltins.py", line 40>:
              0 COPY_FREE_VARS           2

 40           2 RESUME                   0
              4 BUILD_LIST               0
              6 LOAD_FAST                0 (.0)
        >>    8 FOR_ITER                34 (to 80)

 42          12 LOAD_DEREF               3 (files)

 40          14 GET_ITER
        >>   16 FOR_ITER                29 (to 78)

 40          20 LOAD_DEREF               4 (os)
             22 LOAD_ATTR                0 (path)
             42 CACHE
             44 CACHE
             46 CACHE
             48 CACHE
             50 CACHE
             52 CACHE
             54 LOAD_FAST                1 (dir)
             56 LOAD_FAST                2 (filename)
             58 UNPACK_SEQUENCE          2
             62 CALL                     2
             70 CACHE
             72 LIST_APPEND              3
             74 JUMP_BACKWARD           30 (to 16)
             76 JUMP_BACKWARD           35 (to 8)
        >>   78 RETURN_VALUE

Disassembly of <code object __setup at 0x000001A2CDE8B900, file "_sitebuiltins.py", line 44>:
 44           0 RESUME                   0

 45           2 LOAD_FAST                0 (self)
              4 LOAD_ATTR                0 (_Printer__lines)

 48          24 LOAD_FAST                0 (self)
             26 LOAD_ATTR                1 (NULL|self + _Printer__lines)
             46 CACHE
             48 CACHE
             50 CACHE
             52 CACHE
             54 CACHE
             56 LOAD_FAST                2 (filename)
             58 LOAD_CONST               1 ('utf-8')
             60 KW_NAMES                 2 (('encoding',))
             62 UNPACK_SEQUENCE          2
             66 CALL                     2
             74 CACHE
             76 BEFORE_WITH
             78 STORE_FAST               3 (fp)

 51          80 LOAD_FAST                3 (fp)
             82 STORE_SUBSCR
             86 CACHE
             88 CACHE
             90 CACHE
             92 CACHE
             94 CACHE
             96 CACHE
             98 CACHE
            100 CACHE
            102 CACHE
            104 UNPACK_SEQUENCE          0
            108 CALL                     0
            116 CACHE
            118 STORE_FAST               1 (data)

 50         120 LOAD_CONST               0 (None)
            122 LOAD_CONST               0 (None)
            124 LOAD_CONST               0 (None)
            126 UNPACK_SEQUENCE          2
            130 CALL                     2
            138 CACHE
            140 POP_TOP
            142 JUMP_FORWARD            11 (to 166)
        >>  144 PUSH_EXC_INFO
            146 WITH_EXCEPT_START
            148 POP_JUMP_IF_TRUE         4 (to 158)
            150 RERAISE                  2
        >>  152 COPY                     3
            154 POP_EXCEPT
            156 RERAISE                  1
        >>  158 POP_TOP
            160 POP_EXCEPT
            162 POP_TOP
            164 POP_TOP

 52     >>  166 POP_TOP
            168 JUMP_FORWARD            16 (to 202)
        >>  170 PUSH_EXC_INFO

 53         172 LOAD_GLOBAL              8 (OSError)
            182 CACHE
            184 CHECK_EXC_MATCH
            186 POP_JUMP_IF_FALSE        3 (to 194)
            188 POP_TOP

 54         190 POP_EXCEPT
            192 JUMP_BACKWARD           78 (to 38)

 53     >>  194 RERAISE                  0
        >>  196 COPY                     3
            198 POP_EXCEPT
            200 RERAISE                  1

 55     >>  202 LOAD_FAST                1 (data)
            204 POP_JUMP_IF_TRUE         7 (to 220)

 56         206 LOAD_FAST                0 (self)
            208 LOAD_ATTR                5 (NULL|self + open)
            228 CACHE
            230 CACHE
            232 CACHE
            234 CACHE
            236 CACHE
            238 CACHE
            240 CACHE
            242 CACHE
            244 LOAD_CONST               3 ('\n')
            246 UNPACK_SEQUENCE          1
            250 CALL                     1
            258 CACHE
            260 LOAD_FAST                0 (self)
            262 STORE_ATTR               0 (_Printer__lines)

 58         272 LOAD_GLOBAL             15 (NULL + len)
            282 CACHE
            284 LOAD_FAST                0 (self)
            286 LOAD_ATTR                0 (_Printer__lines)
            306 CACHE
            308 CACHE
            310 LOAD_FAST                0 (self)
            312 STORE_ATTR               8 (_Printer__linecnt)
            322 LOAD_CONST               0 (None)
            324 RETURN_VALUE
ExceptionTable:
  44 to 76 -> 170 [1]
  78 to 118 -> 144 [2] lasti
  120 to 142 -> 170 [1]
  144 to 150 -> 152 [4] lasti
  152 to 156 -> 170 [1]
  158 to 158 -> 152 [4] lasti
  160 to 164 -> 170 [1]
  170 to 188 -> 196 [2] lasti
  194 to 194 -> 196 [2] lasti

Disassembly of <code object __repr__ at 0x000001A2D0152EB0, file "_sitebuiltins.py", line 60>:
 60           0 RESUME                   0

 61           2 LOAD_FAST                0 (self)
              4 STORE_SUBSCR
              8 CACHE
             10 CACHE
             12 CACHE
             14 CACHE
             16 CACHE
             18 CACHE
             20 CACHE
             22 CACHE
             24 CACHE
             26 UNPACK_SEQUENCE          0
             30 CALL                     0
             38 CACHE
             40 POP_TOP

 62          42 LOAD_GLOBAL              3 (NULL + len)
             52 CACHE
             54 LOAD_FAST                0 (self)
             56 LOAD_ATTR                2 (len)
             76 CACHE
             78 CACHE
             80 LOAD_FAST                0 (self)
             82 LOAD_ATTR                3 (NULL|self + len)
            102 STORE_SUBSCR
            106 CACHE
            108 CACHE
            110 CACHE
            112 CACHE
            114 CACHE
            116 CACHE
            118 CACHE
            120 CACHE
            122 CACHE
            124 LOAD_FAST                0 (self)
            126 LOAD_ATTR                2 (len)
            146 CACHE
            148 CACHE
            150 RETURN_VALUE

 65         152 LOAD_CONST               2 ('Type %s() to see the full %s text')
            154 LOAD_FAST                0 (self)
            156 LOAD_ATTR                5 (NULL|self + _Printer__lines)
            176 CACHE
            178 RETURN_VALUE

Disassembly of <code object __call__ at 0x000001A2D01911A0, file "_sitebuiltins.py", line 67>:
 67           0 RESUME                   0

 68           2 LOAD_FAST                0 (self)
              4 STORE_SUBSCR
              8 CACHE
             10 CACHE
             12 CACHE
             14 CACHE
             16 CACHE
             18 CACHE
             20 CACHE
             22 CACHE
             24 CACHE
             26 UNPACK_SEQUENCE          0
             30 CALL                     0
             38 CACHE
             40 POP_TOP

 69          42 LOAD_CONST               1 ('Hit Return for more, or q (and Return) to quit: ')
             44 STORE_FAST               1 (prompt)

 70          46 LOAD_CONST               2 (0)
             48 STORE_FAST               2 (lineno)

 71          50 NOP

 72     >>   52 NOP

 73          54 LOAD_GLOBAL              3 (NULL + range)
             64 CACHE
             66 LOAD_FAST                2 (lineno)
             68 LOAD_FAST                2 (lineno)
             70 LOAD_FAST                0 (self)
             72 LOAD_ATTR                2 (range)
             92 CACHE
             94 CACHE
             96 CACHE
             98 CACHE
            100 GET_ITER
        >>  102 FOR_ITER                28 (to 162)

 74         106 LOAD_GLOBAL              7 (NULL + print)
            116 CACHE
            118 LOAD_FAST                0 (self)
            120 LOAD_ATTR                4 (MAXLINES)
            140 CACHE
            142 UNPACK_SEQUENCE          1
            146 CALL                     1
            154 CACHE
            156 POP_TOP
            158 JUMP_BACKWARD           29 (to 102)

 73         160 NOP

 78     >>  162 LOAD_FAST                2 (lineno)
            164 LOAD_FAST                0 (self)
            166 LOAD_ATTR                2 (range)

 80         186 LOAD_FAST                4 (key)
            188 POP_JUMP_IF_NOT_NONE    23 (to 236)

 81         190 LOAD_GLOBAL             11 (NULL + input)
            200 CACHE
            202 LOAD_FAST                1 (prompt)
            204 UNPACK_SEQUENCE          1
            208 CALL                     1
            216 CACHE
            218 STORE_FAST               4 (key)

 82         220 LOAD_FAST                4 (key)
            222 LOAD_CONST               4 (('', 'q'))
            224 CONTAINS_OP              1
            226 POP_JUMP_IF_FALSE        2 (to 232)

 83         228 LOAD_CONST               0 (None)
            230 STORE_FAST               4 (key)

 80     >>  232 LOAD_FAST                4 (key)
