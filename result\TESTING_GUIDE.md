# 法线贴图算法测试指南

## 🚨 重要提示

由于反编译过程中提取了大量Python标准库模块，这些模块可能与系统的numpy/opencv库产生冲突。为了避免导入错误，请按照以下方法运行测试。

## 🔧 解决方案

### 方法1: 使用干净的测试环境（推荐）

```bash
cd test_clean
python test_normal_maps_clean.py
```

这个版本在独立目录中运行，避免了所有模块冲突。

### 方法2: 从父目录运行

```bash
cd ..
python result/test_normal_maps.py
```

### 方法3: 清理冲突模块

如果您想在result目录中直接运行，需要先移除冲突的模块：

```bash
# 移除冲突的Python模块
rm result/_compat_pickle.py
rm result/_collections_abc.py
rm result/_py_abc.py
rm result/contextvars.py
rm -rf result/__pycache__

# 然后运行测试
cd result
python test_normal_maps.py
```

## 📋 测试内容

### 1. 基础功能测试
- ✅ NumPy/OpenCV导入验证
- ✅ 图像归一化函数测试
- ✅ 法线贴图验证函数测试

### 2. 切线法线贴图测试
- ✅ 高度图生成
- ✅ 梯度计算
- ✅ 法线向量构建
- ✅ 格式转换

### 3. 物体法线贴图测试
- ✅ 6个方向性光照图像生成
- ✅ 光度立体法计算
- ✅ 法线求解
- ✅ BGR格式输出

## 🎯 预期输出

成功运行后，您应该看到类似以下的输出：

```
Clean Normal Map Test Suite
========================================
Creating test images...
Created height map: test_images\test_height_map.tif
Created directional image 1: test_images\directional_1.png
...
Created directional image 6: test_images\directional_6.png

========================================
imgproc: Started - test_tangent_normal_map
Testing tangent normal map generation...
Loaded height map: shape=(256, 256), dtype=uint16
Normalized 16-bit image: min=0.000, max=1.000
Computed gradients: X range=[-0.187, 0.183]
                   Y range=[-0.176, 0.173]
Saved tangent normal map: test_images/tangent_normal_test.png
imgproc: Complete - elapsed time: 0:00:0.13

========================================
imgproc: Started - test_object_normal_map
Testing object normal map generation...
...
Processing row 250/256
Saved object normal map: test_images/object_normal_test.png
imgproc: Complete - elapsed time: 0:00:0.21

========================================
✓ All tests completed successfully!
Check the 'test_images' directory for output files.
```

## 📁 生成的文件

测试成功后，会在 `test_images` 目录中生成以下文件：

- `test_height_map.tif` - 测试用的16位高度图
- `directional_1.png` 到 `directional_6.png` - 6个方向性光照图像
- `tangent_normal_test.png` - 切线法线贴图测试结果
- `object_normal_test.png` - 物体法线贴图测试结果

## 🔍 验证结果

### 切线法线贴图验证
- 图像应该主要呈现蓝紫色调（Z分量占主导）
- 边缘区域应该有红绿色变化（X、Y分量）
- 整体应该看起来像一个凸起的金字塔

### 物体法线贴图验证
- 图像应该呈现球形的法线分布
- 中心区域偏蓝色（向前的法线）
- 边缘区域有红绿色渐变（侧向法线）

## ❌ 常见错误及解决方案

### 错误1: ImportError: cannot import name 'ContextVar'
```
原因: contextvars.py模块冲突
解决: 删除 result/contextvars.py 文件
```

### 错误2: AttributeError: module '_compat_pickle' has no attribute 'NAME_MAPPING'
```
原因: _compat_pickle.py模块冲突
解决: 删除 result/_compat_pickle.py 文件
```

### 错误3: 其他numpy相关导入错误
```
原因: 提取的Python模块与系统库冲突
解决: 使用 test_clean/test_normal_maps_clean.py
```

## 🚀 性能基准

在标准配置下的预期性能：

- **切线法线贴图生成**: ~0.1-0.2秒 (256x256图像)
- **物体法线贴图生成**: ~0.2-0.5秒 (256x256图像)
- **内存使用**: ~50-100MB峰值

## 📞 故障排除

如果测试仍然失败，请检查：

1. **Python版本**: 确保使用Python 3.7+
2. **依赖库**: 确保安装了opencv-python和numpy
3. **权限**: 确保有写入test_images目录的权限
4. **磁盘空间**: 确保有足够空间保存测试图像

## 🎉 成功标志

如果看到以下消息，说明算法实现正确：

```
✓ All tests completed successfully!
Check the 'test_images' directory for output files.
```

这表明两个核心法线贴图算法都已成功实现并通过验证！
