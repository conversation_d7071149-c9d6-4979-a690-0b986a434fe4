# Code object from position 7764702
# Filename: _threading_local.py
# Name: <module>
# Args: 0
# Locals: 0

  0           0 RESUME                   0

  1           2 LOAD_CONST               0 ("Thread-local objects.\n\n(Note that this module provides a Python version of the threading.local\n class.  Depending on the version of Python you're using, there may be a\n faster one available.  You should always import the `local` class from\n `threading`.)\n\nThread-local objects support the management of thread-local data.\nIf you have data that you want to be local to a thread, simply create\na thread-local object and use its attributes:\n\n  >>> mydata = local()\n  >>> mydata.number = 42\n  >>> mydata.number\n  42\n\nYou can also access the local-object's dictionary:\n\n  >>> mydata.__dict__\n  {'number': 42}\n  >>> mydata.__dict__.setdefault('widgets', [])\n  []\n  >>> mydata.widgets\n  []\n\nWhat's important about thread-local objects is that their data are\nlocal to a thread. If we access the data in a different thread:\n\n  >>> log = []\n  >>> def f():\n  ...     items = sorted(mydata.__dict__.items())\n  ...     log.append(items)\n  ...     mydata.number = 11\n  ...     log.append(mydata.number)\n\n  >>> import threading\n  >>> thread = threading.Thread(target=f)\n  >>> thread.start()\n  >>> thread.join()\n  >>> log\n  [[], 11]\n\nwe get different data.  Furthermore, changes made in the other thread\ndon't affect data seen in this thread:\n\n  >>> mydata.number\n  42\n\nOf course, values you get from a local object, including a __dict__\nattribute, are for whatever thread was current at the time the\nattribute was read.  For that reason, you generally don't want to save\nthese values across threads, as they apply only to the thread they\ncame from.\n\nYou can create custom local objects by subclassing the local class:\n\n  >>> class MyLocal(local):\n  ...     number = 2\n  ...     def __init__(self, /, **kw):\n  ...         self.__dict__.update(kw)\n  ...     def squared(self):\n  ...         return self.number ** 2\n\nThis can be useful to support default values, methods and\ninitialization.  Note that if you define an __init__ method, it will be\ncalled each time the local object is used in a separate thread.  This\nis necessary to initialize each thread's dictionary.\n\nNow if we create a local object:\n\n  >>> mydata = MyLocal(color='red')\n\nNow we have a default number:\n\n  >>> mydata.number\n  2\n\nan initial color:\n\n  >>> mydata.color\n  'red'\n  >>> del mydata.color\n\nAnd a method that operates on the data:\n\n  >>> mydata.squared()\n  4\n\nAs before, we can access the data in a separate thread:\n\n  >>> log = []\n  >>> thread = threading.Thread(target=f)\n  >>> thread.start()\n  >>> thread.join()\n  >>> log\n  [[('color', 'red')], 11]\n\nwithout affecting this thread's data:\n\n  >>> mydata.number\n  2\n  >>> mydata.color\n  Traceback (most recent call last):\n  ...\n  AttributeError: 'MyLocal' object has no attribute 'color'\n\nNote that subclasses can define slots, but they are not thread\nlocal. They are shared across threads:\n\n  >>> class MyLocal(local):\n  ...     __slots__ = 'number'\n\n  >>> mydata = MyLocal()\n  >>> mydata.number = 42\n  >>> mydata.color = 'red'\n\nSo, the separate thread:\n\n  >>> thread = threading.Thread(target=f)\n  >>> thread.start()\n  >>> thread.join()\n\naffects what we see:\n\n  >>> mydata.number\n  11\n\n>>> del mydata\n")
              4 STORE_NAME               0 (__doc__)

131           6 LOAD_CONST               1 (0)
              8 LOAD_CONST               2 (('ref',))
             10 IMPORT_NAME              1 (weakref)
             12 IMPORT_FROM              2 (ref)
             14 STORE_NAME               2 (ref)
             16 POP_TOP

132          18 LOAD_CONST               1 (0)
             20 LOAD_CONST               3 (('contextmanager',))
             22 IMPORT_NAME              3 (contextlib)
             24 IMPORT_FROM              4 (contextmanager)
             26 STORE_NAME               4 (contextmanager)
             28 POP_TOP

134          30 LOAD_CONST               4 ('local')
             32 BUILD_LIST               1
             34 STORE_NAME               5 (__all__)

146          36 PUSH_NULL
             38 LOAD_BUILD_CLASS
             40 LOAD_CONST               5 (<code object _localimpl at 0x000001A2D0632790, file "_threading_local.py", line 146>)
             42 MAKE_FUNCTION            0
             44 LOAD_CONST               6 ('_localimpl')
             46 UNPACK_SEQUENCE          2
             50 CALL                     2
             58 CACHE
             60 STORE_NAME               6 (_localimpl)

190          62 LOAD_NAME                4 (contextmanager)

191          64 LOAD_CONST               7 (<code object _patch at 0x000001A2D03B67F0, file "_threading_local.py", line 190>)
             66 MAKE_FUNCTION            0

190          68 UNPACK_SEQUENCE          0
             72 CALL                     0
             80 CACHE

191          82 STORE_NAME               7 (_patch)

204          84 PUSH_NULL
             86 LOAD_BUILD_CLASS
             88 LOAD_CONST               8 (<code object local at 0x000001A2D0632970, file "_threading_local.py", line 204>)
             90 MAKE_FUNCTION            0
             92 LOAD_CONST               4 ('local')
             94 UNPACK_SEQUENCE          2
             98 CALL                     2
            106 CACHE
            108 STORE_NAME               8 (local)

242         110 LOAD_CONST               1 (0)
            112 LOAD_CONST               9 (('current_thread', 'RLock'))
            114 IMPORT_NAME              9 (threading)
            116 IMPORT_FROM             10 (current_thread)
            118 STORE_NAME              10 (current_thread)
            120 IMPORT_FROM             11 (RLock)
            122 STORE_NAME              11 (RLock)
            124 POP_TOP
            126 LOAD_CONST              10 (None)
            128 RETURN_VALUE

Disassembly of <code object _localimpl at 0x000001A2D0632790, file "_threading_local.py", line 146>:
146           0 RESUME                   0
              2 LOAD_NAME                0 (__name__)
              4 STORE_NAME               1 (__module__)
              6 LOAD_CONST               0 ('_localimpl')
              8 STORE_NAME               2 (__qualname__)

147          10 LOAD_CONST               1 ('A class managing thread-local dicts')
             12 STORE_NAME               3 (__doc__)

148          14 LOAD_CONST               2 (('key', 'dicts', 'localargs', 'locallock', '__weakref__'))
             16 STORE_NAME               4 (__slots__)

150          18 LOAD_CONST               3 (<code object __init__ at 0x000001A2D06A58F0, file "_threading_local.py", line 150>)
             20 MAKE_FUNCTION            0
             22 STORE_NAME               5 (__init__)

158          24 LOAD_CONST               4 (<code object get_dict at 0x000001A2D06A5FB0, file "_threading_local.py", line 158>)
             26 MAKE_FUNCTION            0
             28 STORE_NAME               6 (get_dict)

164          30 LOAD_CONST               5 (<code object create_dict at 0x000001A2D010F430, file "_threading_local.py", line 164>)
             32 MAKE_FUNCTION            0
             34 STORE_NAME               7 (create_dict)
             36 LOAD_CONST               6 (None)
             38 RETURN_VALUE

Disassembly of <code object __init__ at 0x000001A2D06A58F0, file "_threading_local.py", line 150>:
150           0 RESUME                   0

154           2 LOAD_CONST               1 ('_threading_local._localimpl.')
              4 LOAD_GLOBAL              1 (NULL + str)
             14 CACHE
             16 LOAD_GLOBAL              3 (NULL + id)
             26 CACHE
             28 LOAD_FAST                0 (self)
             30 UNPACK_SEQUENCE          1
             34 CALL                     1
             42 CACHE
             44 UNPACK_SEQUENCE          1
             48 CALL                     1
             56 CACHE
             58 BINARY_OP                0 (+)
             62 LOAD_FAST                0 (self)
             64 STORE_ATTR               2 (key)

156          74 BUILD_MAP                0
             76 LOAD_FAST                0 (self)
             78 STORE_ATTR               3 (dicts)
             88 LOAD_CONST               0 (None)
             90 RETURN_VALUE

Disassembly of <code object get_dict at 0x000001A2D06A5FB0, file "_threading_local.py", line 158>:
158           0 RESUME                   0

161           2 LOAD_GLOBAL              1 (NULL + current_thread)
             12 CACHE
             14 UNPACK_SEQUENCE          0
             18 CALL                     0
             26 CACHE
             28 STORE_FAST               1 (thread)

162          30 LOAD_FAST                0 (self)
             32 LOAD_ATTR                1 (NULL|self + current_thread)
             52 CACHE
             54 LOAD_FAST                1 (thread)
             56 UNPACK_SEQUENCE          1
             60 CALL                     1
             68 CACHE
             70 BINARY_SUBSCR
             74 CACHE
             76 CACHE
             78 CACHE
             80 LOAD_CONST               1 (1)
             82 BINARY_SUBSCR
             86 CACHE
             88 CACHE
             90 CACHE
             92 RETURN_VALUE

Disassembly of <code object create_dict at 0x000001A2D010F430, file "_threading_local.py", line 164>:
              0 MAKE_CELL                7 (wrlocal)
              2 MAKE_CELL                8 (wrthread)

164           4 RESUME                   0

166           6 BUILD_MAP                0
              8 STORE_FAST               1 (localdict)

167          10 LOAD_FAST                0 (self)
             12 LOAD_ATTR                0 (key)
             32 CACHE
             34 CACHE
             36 UNPACK_SEQUENCE          0
             40 CALL                     0
             48 CACHE
             50 STORE_FAST               3 (thread)

169          52 LOAD_GLOBAL              5 (NULL + id)
             62 CACHE
             64 LOAD_FAST                3 (thread)
             66 UNPACK_SEQUENCE          1
             70 CALL                     1
             78 CACHE
             80 STORE_FAST               4 (idt)

170          82 LOAD_FAST                2 (key)
             84 BUILD_TUPLE              1
             86 LOAD_CLOSURE             8 (wrthread)
             88 BUILD_TUPLE              1
             90 LOAD_CONST               1 (<code object local_deleted at 0x000001A2D06C4930, file "_threading_local.py", line 170>)
             92 MAKE_FUNCTION            9 (defaults, closure)
             94 STORE_FAST               5 (local_deleted)

175          96 LOAD_FAST                4 (idt)
             98 BUILD_TUPLE              1
            100 LOAD_CLOSURE             7 (wrlocal)
            102 BUILD_TUPLE              1
            104 LOAD_CONST               2 (<code object thread_deleted at 0x000001A2D06A60D0, file "_threading_local.py", line 175>)
            106 MAKE_FUNCTION            9 (defaults, closure)
            108 STORE_FAST               6 (thread_deleted)

183         110 LOAD_GLOBAL              7 (NULL + ref)
            120 CACHE
            122 LOAD_FAST                0 (self)
            124 LOAD_FAST                5 (local_deleted)
            126 UNPACK_SEQUENCE          2
            130 CALL                     2
            138 CACHE
            140 STORE_DEREF              7 (wrlocal)

184         142 LOAD_GLOBAL              7 (NULL + ref)
            152 CACHE
            154 LOAD_FAST                3 (thread)
            156 LOAD_FAST                6 (thread_deleted)
            158 UNPACK_SEQUENCE          2
            162 CALL                     2
            170 CACHE
            172 STORE_DEREF              8 (wrthread)

185         174 LOAD_DEREF               7 (wrlocal)
            176 LOAD_FAST                3 (thread)
            178 LOAD_ATTR                4 (id)
            198 BUILD_TUPLE              2
            200 LOAD_FAST                0 (self)
            202 LOAD_ATTR                5 (NULL|self + id)

Disassembly of <code object local_deleted at 0x000001A2D06C4930, file "_threading_local.py", line 170>:
              0 COPY_FREE_VARS           1

170           2 RESUME                   0

172           4 PUSH_NULL
              6 LOAD_DEREF               3 (wrthread)
              8 UNPACK_SEQUENCE          0
             12 CALL                     0
             20 CACHE
             22 STORE_FAST               2 (thread)

173          24 LOAD_FAST                2 (thread)
             26 POP_JUMP_IF_NONE        10 (to 48)

174          28 LOAD_FAST                2 (thread)
             30 LOAD_ATTR                0 (__dict__)
             50 RETURN_VALUE

Disassembly of <code object thread_deleted at 0x000001A2D06A60D0, file "_threading_local.py", line 175>:
              0 COPY_FREE_VARS           1

175           2 RESUME                   0

180           4 PUSH_NULL
              6 LOAD_DEREF               4 (wrlocal)
              8 UNPACK_SEQUENCE          0
             12 CALL                     0
             20 CACHE
             22 STORE_FAST               2 (local)

181          24 LOAD_FAST                2 (local)
             26 POP_JUMP_IF_NONE        28 (to 84)

182          28 LOAD_FAST                2 (local)
             30 LOAD_ATTR                0 (dicts)
             50 CACHE
             52 CACHE
             54 CACHE
             56 CACHE
             58 CACHE
             60 CACHE
             62 LOAD_FAST                1 (idt)
             64 UNPACK_SEQUENCE          1
             68 CALL                     1
             76 CACHE
             78 STORE_FAST               3 (dct)
             80 LOAD_CONST               0 (None)
             82 RETURN_VALUE

181     >>   84 LOAD_CONST               0 (None)
             86 RETURN_VALUE

Disassembly of <code object _patch at 0x000001A2D03B67F0, file "_threading_local.py", line 190>:
190           0 RETURN_GENERATOR
              2 POP_TOP
              4 RESUME                   0

192           6 LOAD_GLOBAL              0 (object)
             16 CACHE
             18 STORE_SUBSCR
             22 CACHE
             24 CACHE
             26 CACHE
             28 CACHE
             30 CACHE
             32 CACHE
             34 CACHE
             36 CACHE
             38 CACHE
             40 LOAD_FAST                0 (self)
             42 LOAD_CONST               1 ('_local__impl')
             44 UNPACK_SEQUENCE          2
             48 CALL                     2
             56 CACHE
             58 STORE_FAST               1 (impl)

193          60 NOP

194          62 LOAD_FAST                1 (impl)
             64 STORE_SUBSCR
             68 CACHE
             70 CACHE
             72 CACHE
             74 CACHE
             76 CACHE
             78 CACHE
             80 CACHE
             82 CACHE
             84 CACHE
             86 UNPACK_SEQUENCE          0
             90 CALL                     0
             98 CACHE
            100 STORE_FAST               2 (dct)
            102 JUMP_FORWARD            59 (to 222)
        >>  104 PUSH_EXC_INFO

195         106 LOAD_GLOBAL              6 (KeyError)
            116 CACHE
            118 CHECK_EXC_MATCH
            120 POP_JUMP_IF_FALSE       46 (to 214)
            122 POP_TOP

196         124 LOAD_FAST                1 (impl)
            126 STORE_SUBSCR
            130 CACHE
            132 CACHE
            134 CACHE
            136 CACHE
            138 CACHE
            140 CACHE
            142 CACHE
            144 CACHE
            146 CACHE
            148 UNPACK_SEQUENCE          0
            152 CALL                     0
            160 CACHE
            162 STORE_FAST               2 (dct)

197         164 LOAD_FAST                1 (impl)
            166 LOAD_ATTR                5 (NULL|self + get_dict)
            186 LOAD_FAST                0 (self)
            188 LOAD_ATTR                6 (KeyError)
            208 POP_TOP
            210 POP_EXCEPT
            212 JUMP_FORWARD             4 (to 222)

195     >>  214 RERAISE                  0
        >>  216 COPY                     3
            218 POP_EXCEPT
            220 RERAISE                  1

199     >>  222 LOAD_FAST                1 (impl)
            224 LOAD_ATTR                7 (NULL|self + KeyError)
            244 CACHE
            246 CACHE
            248 CACHE
            250 STORE_SUBSCR
            254 CACHE
            256 CACHE
            258 CACHE
            260 CACHE
            262 CACHE
            264 CACHE
            266 CACHE
            268 CACHE
            270 CACHE
            272 LOAD_FAST                0 (self)
            274 LOAD_CONST               2 ('__dict__')
            276 LOAD_FAST                2 (dct)
            278 UNPACK_SEQUENCE          3
            282 CALL                     3
            290 CACHE
            292 POP_TOP

201         294 LOAD_CONST               0 (None)
            296 LOAD_FAST                0 (self)
            298 RESUME                   1
            300 POP_TOP

199         302 LOAD_CONST               0 (None)
            304 LOAD_CONST               0 (None)
            306 LOAD_CONST               0 (None)
            308 UNPACK_SEQUENCE          2
            312 CALL                     2
            320 CACHE
            322 POP_TOP
            324 LOAD_CONST               0 (None)
            326 RETURN_VALUE
        >>  328 PUSH_EXC_INFO
            330 WITH_EXCEPT_START
            332 POP_JUMP_IF_TRUE         4 (to 342)
            334 RERAISE                  2
        >>  336 COPY                     3
            338 POP_EXCEPT
            340 RERAISE                  1
        >>  342 POP_TOP
            344 POP_EXCEPT
            346 POP_TOP
            348 POP_TOP
            350 LOAD_CONST               0 (None)
            352 RETURN_VALUE
ExceptionTable:
  62 to 100 -> 104 [0]
  104 to 208 -> 216 [1] lasti
  214 to 214 -> 216 [1] lasti
  236 to 300 -> 328 [1] lasti
  328 to 334 -> 336 [3] lasti
  342 to 342 -> 336 [3] lasti

Disassembly of <code object local at 0x000001A2D0632970, file "_threading_local.py", line 204>:
204           0 RESUME                   0
              2 LOAD_NAME                0 (__name__)
              4 STORE_NAME               1 (__module__)
              6 LOAD_CONST               0 ('local')
              8 STORE_NAME               2 (__qualname__)

205          10 LOAD_CONST               1 (('_local__impl', '__dict__'))
             12 STORE_NAME               3 (__slots__)

207          14 LOAD_CONST               2 (<code object __new__ at 0x000001A2D0648E30, file "_threading_local.py", line 207>)
             16 MAKE_FUNCTION            0
             18 STORE_NAME               4 (__new__)

221          20 LOAD_CONST               3 (<code object __getattribute__ at 0x000001A2D064CD50, file "_threading_local.py", line 221>)
             22 MAKE_FUNCTION            0
             24 STORE_NAME               5 (__getattribute__)

225          26 LOAD_CONST               4 (<code object __setattr__ at 0x000001A2D06C8CB0, file "_threading_local.py", line 225>)
             28 MAKE_FUNCTION            0
             30 STORE_NAME               6 (__setattr__)

233          32 LOAD_CONST               5 (<code object __delattr__ at 0x000001A2D06C8E40, file "_threading_local.py", line 233>)
             34 MAKE_FUNCTION            0
             36 STORE_NAME               7 (__delattr__)
             38 LOAD_CONST               6 (None)
             40 RETURN_VALUE

Disassembly of <code object __new__ at 0x000001A2D0648E30, file "_threading_local.py", line 207>:
207           0 RESUME                   0

208           2 LOAD_FAST                1 (args)
              4 POP_JUMP_IF_TRUE         2 (to 10)
              6 LOAD_FAST                2 (kw)
              8 POP_JUMP_IF_FALSE       34 (to 78)
        >>   10 LOAD_FAST                0 (cls)
             12 LOAD_ATTR                0 (__init__)
             32 CACHE
             34 LOAD_ATTR                0 (__init__)
             54 CACHE
             56 CACHE
             58 CACHE
             60 LOAD_CONST               1 ('Initialization arguments are not supported')
             62 UNPACK_SEQUENCE          1
             66 CALL                     1
             74 CACHE
             76 RAISE_VARARGS            1

210     >>   78 LOAD_GLOBAL              2 (object)
             88 CACHE
             90 STORE_SUBSCR
             94 CACHE
             96 CACHE
             98 CACHE
            100 CACHE
            102 CACHE
            104 CACHE
            106 CACHE
            108 CACHE
            110 CACHE
            112 LOAD_FAST                0 (cls)
            114 UNPACK_SEQUENCE          1
            118 CALL                     1
            126 CACHE
            128 STORE_FAST               3 (self)

211         130 LOAD_GLOBAL              9 (NULL + _localimpl)
            140 CACHE
            142 UNPACK_SEQUENCE          0
            146 CALL                     0
            154 CACHE
            156 STORE_FAST               4 (impl)

212         158 LOAD_FAST                1 (args)
            160 LOAD_FAST                2 (kw)
            162 BUILD_TUPLE              2
            164 LOAD_FAST                4 (impl)
            166 STORE_ATTR               5 (localargs)

213         176 LOAD_GLOBAL             13 (NULL + RLock)
            186 CACHE
            188 UNPACK_SEQUENCE          0
            192 CALL                     0
            200 CACHE
            202 LOAD_FAST                4 (impl)
            204 STORE_ATTR               7 (locallock)

214         214 LOAD_GLOBAL              2 (object)
            224 CACHE
            226 STORE_SUBSCR
            230 CACHE
            232 CACHE
            234 CACHE
            236 CACHE
            238 CACHE
            240 CACHE
            242 CACHE
            244 CACHE
            246 CACHE
            248 LOAD_FAST                3 (self)
            250 LOAD_CONST               2 ('_local__impl')
            252 LOAD_FAST                4 (impl)
            254 UNPACK_SEQUENCE          3
            258 CALL                     3
            266 CACHE
            268 POP_TOP

218         270 LOAD_FAST                4 (impl)
            272 STORE_SUBSCR
            276 CACHE
            278 CACHE
            280 CACHE
            282 CACHE
            284 CACHE
            286 CACHE
            288 CACHE
            290 CACHE
            292 CACHE
            294 UNPACK_SEQUENCE          0
            298 CALL                     0
            306 CACHE
            308 POP_TOP

219         310 LOAD_FAST                3 (self)
            312 RETURN_VALUE

Disassembly of <code object __getattribute__ at 0x000001A2D064CD50, file "_threading_local.py", line 221>:
221           0 RESUME                   0

222           2 LOAD_GLOBAL              1 (NULL + _patch)
             12 CACHE
             14 LOAD_FAST                0 (self)
             16 UNPACK_SEQUENCE          1
             20 CALL                     1
             28 CACHE
             30 BEFORE_WITH
             32 POP_TOP

223          34 LOAD_GLOBAL              2 (object)
             44 CACHE
             46 STORE_SUBSCR
             50 CACHE
             52 CACHE
             54 CACHE
             56 CACHE
             58 CACHE
             60 CACHE
             62 CACHE
             64 CACHE
             66 CACHE
             68 LOAD_FAST                0 (self)
             70 LOAD_FAST                1 (name)
             72 UNPACK_SEQUENCE          2
             76 CALL                     2
             84 CACHE

222          86 SWAP                     2
             88 LOAD_CONST               0 (None)
             90 LOAD_CONST               0 (None)
             92 LOAD_CONST               0 (None)
             94 UNPACK_SEQUENCE          2
             98 CALL                     2
            106 CACHE
            108 POP_TOP
            110 RETURN_VALUE
        >>  112 PUSH_EXC_INFO
            114 WITH_EXCEPT_START
            116 POP_JUMP_IF_TRUE         4 (to 126)
            118 RERAISE                  2
        >>  120 COPY                     3
            122 POP_EXCEPT
            124 RERAISE                  1
        >>  126 POP_TOP
            128 POP_EXCEPT
            130 POP_TOP
            132 POP_TOP
            134 LOAD_CONST               0 (None)
            136 RETURN_VALUE
ExceptionTable:
  32 to 84 -> 112 [1] lasti
  112 to 118 -> 120 [3] lasti
  126 to 126 -> 120 [3] lasti

Disassembly of <code object __setattr__ at 0x000001A2D06C8CB0, file "_threading_local.py", line 225>:
225           0 RESUME                   0

226           2 LOAD_FAST                1 (name)
              4 LOAD_CONST               1 ('__dict__')
              6 COMPARE_OP               2 (<)
             10 CACHE
             12 POP_JUMP_IF_FALSE       28 (to 70)

227          14 LOAD_GLOBAL              1 (NULL + AttributeError)
             24 CACHE

228          26 LOAD_CONST               2 ("%r object attribute '__dict__' is read-only")

229          28 LOAD_FAST                0 (self)
             30 LOAD_ATTR                1 (NULL|self + AttributeError)

228          50 BINARY_OP                6 (%)

227          54 UNPACK_SEQUENCE          1
             58 CALL                     1
             66 CACHE
             68 RAISE_VARARGS            1

230     >>   70 LOAD_GLOBAL              7 (NULL + _patch)
             80 CACHE
             82 LOAD_FAST                0 (self)
             84 UNPACK_SEQUENCE          1
             88 CALL                     1
             96 CACHE
             98 BEFORE_WITH
            100 POP_TOP

231         102 LOAD_GLOBAL              8 (object)
            112 CACHE
            114 STORE_SUBSCR
            118 CACHE
            120 CACHE
            122 CACHE
            124 CACHE
            126 CACHE
            128 CACHE
            130 CACHE
            132 CACHE
            134 CACHE
            136 LOAD_FAST                0 (self)
            138 LOAD_FAST                1 (name)
            140 LOAD_FAST                2 (value)
            142 UNPACK_SEQUENCE          3
            146 CALL                     3
            154 CACHE

230         156 SWAP                     2
            158 LOAD_CONST               0 (None)
            160 LOAD_CONST               0 (None)
            162 LOAD_CONST               0 (None)
            164 UNPACK_SEQUENCE          2
            168 CALL                     2
            176 CACHE
            178 POP_TOP
            180 RETURN_VALUE
        >>  182 PUSH_EXC_INFO
            184 WITH_EXCEPT_START
            186 POP_JUMP_IF_TRUE         4 (to 196)
            188 RERAISE                  2
        >>  190 COPY                     3
            192 POP_EXCEPT
            194 RERAISE                  1
        >>  196 POP_TOP
            198 POP_EXCEPT
            200 POP_TOP
            202 POP_TOP
            204 LOAD_CONST               0 (None)
            206 RETURN_VALUE
ExceptionTable:
  100 to 154 -> 182 [1] lasti
  182 to 188 -> 190 [3] lasti
  196 to 196 -> 190 [3] lasti

Disassembly of <code object __delattr__ at 0x000001A2D06C8E40, file "_threading_local.py", line 233>:
233           0 RESUME                   0

234           2 LOAD_FAST                1 (name)
              4 LOAD_CONST               1 ('__dict__')
              6 COMPARE_OP               2 (<)
             10 CACHE
             12 POP_JUMP_IF_FALSE       28 (to 70)

235          14 LOAD_GLOBAL              1 (NULL + AttributeError)
             24 CACHE

236          26 LOAD_CONST               2 ("%r object attribute '__dict__' is read-only")

237          28 LOAD_FAST                0 (self)
             30 LOAD_ATTR                1 (NULL|self + AttributeError)

236          50 BINARY_OP                6 (%)

235          54 UNPACK_SEQUENCE          1
             58 CALL                     1
             66 CACHE
             68 RAISE_VARARGS            1

238     >>   70 LOAD_GLOBAL              7 (NULL + _patch)
             80 CACHE
             82 LOAD_FAST                0 (self)
             84 UNPACK_SEQUENCE          1
             88 CALL                     1
             96 CACHE
             98 BEFORE_WITH
            100 POP_TOP

239         102 LOAD_GLOBAL              8 (object)
            112 CACHE
            114 STORE_SUBSCR
            118 CACHE
            120 CACHE
            122 CACHE
            124 CACHE
            126 CACHE
            128 CACHE
            130 CACHE
            132 CACHE
            134 CACHE
            136 LOAD_FAST                0 (self)
            138 LOAD_FAST                1 (name)
            140 UNPACK_SEQUENCE          2
            144 CALL                     2
            152 CACHE

238         154 SWAP                     2
            156 LOAD_CONST               0 (None)
            158 LOAD_CONST               0 (None)
            160 LOAD_CONST               0 (None)
            162 UNPACK_SEQUENCE          2
            166 CALL                     2
            174 CACHE
            176 POP_TOP
            178 RETURN_VALUE
        >>  180 PUSH_EXC_INFO
            182 WITH_EXCEPT_START
            184 POP_JUMP_IF_TRUE         4 (to 194)
            186 RERAISE                  2
        >>  188 COPY                     3
            190 POP_EXCEPT
            192 RERAISE                  1
        >>  194 POP_TOP
            196 POP_EXCEPT
            198 POP_TOP
            200 POP_TOP
            202 LOAD_CONST               0 (None)
            204 RETURN_VALUE
ExceptionTable:
  100 to 152 -> 180 [1] lasti
  180 to 186 -> 188 [3] lasti
  194 to 194 -> 188 [3] lasti


# Constants:
# 0: str
# 1: 0
# 2: tuple
# 3: tuple
# 4: 'local'
# 5: code
# 6: '_localimpl'
# 7: code
# 8: code
# 9: tuple
# 10: None


# Names:
# 0: '__doc__'
# 1: 'weakref'
# 2: 'ref'
# 3: 'contextlib'
# 4: 'contextmanager'
# 5: '__all__'
# 6: '_localimpl'
# 7: '_patch'
# 8: 'local'
# 9: 'threading'
# 10: 'current_thread'
# 11: 'RLock'


# Variable names:
