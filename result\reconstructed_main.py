#!/usr/bin/env python3
"""
ImgProc - Image Processing CLI Tool
Reconstructed from reverse engineering imgproc.exe

This tool provides various image processing functions including:
- Image alignment
- Normal map creation (object and tangent)
- Specular map creation
"""

import argparse
import cv2
import numpy as np
import os
import sys
import time
from typing import List, Tuple, Optional


def print_elapsed_time(func):
    """Print the elapsed time of the decorated function"""
    def wrapper(*args, **kwargs):
        start_time = time.time()
        print(f"imgproc: Started - {func.__name__}")
        result = func(*args, **kwargs)
        elapsed = time.time() - start_time
        hours = int(elapsed // 3600)
        minutes = int((elapsed % 3600) // 60)
        seconds = elapsed % 60
        print(f"imgproc: Complete - elapsed time: {hours:d}:{minutes:02d}:{seconds:0.2f}")
        return result
    return wrapper


def load_directional_images(directional_images: List[str]) -> List[np.ndarray]:
    """Load directional images for normal map creation"""
    images = []
    for i, image_path in enumerate(directional_images):
        if not os.path.exists(image_path):
            print(f"Image not found: {image_path}")
            return None

        image = cv2.imread(image_path, cv2.IMREAD_UNCHANGED)
        if image is None:
            print(f"Failed to load image: {image_path}")
            return None

        print(f"Image for direction {i+1}: {image_path}")
        images.append(image)

    return images


def normalize_intensity(image: np.ndarray) -> np.ndarray:
    """Normalize image intensity"""
    if image.dtype == np.uint8:
        return image.astype(np.float32) / 255.0
    elif image.dtype == np.uint16:
        return image.astype(np.float32) / 65535.0
    else:
        print("Image is not 8bit or 16bit")
        return None


@print_elapsed_time
def align_image(image1_path: str, image2_path: str, output_path: str,
                crop_fraction: float = 0.1, auto_crop_face: bool = False,
                face_mask_path: Optional[str] = None) -> bool:
    """
    Align two images using various alignment methods

    Args:
        image1_path: Path to the first image (base image)
        image2_path: Path to the second image (image to align)
        output_path: Path to save the aligned image
        crop_fraction: Crop fraction for alignment
        auto_crop_face: Auto detect and crop face for alignment
        face_mask_path: Path to mask image for face detection

    Returns:
        bool: True if successful, False otherwise
    """
    # Load images
    base_image = cv2.imread(image1_path, cv2.IMREAD_UNCHANGED)
    image_to_align = cv2.imread(image2_path, cv2.IMREAD_UNCHANGED)

    if base_image is None or image_to_align is None:
        print("Failed to load images")
        return False

    print(f"image min: {np.min(image_to_align)}, max: {np.max(image_to_align)}")

    # Perform alignment (placeholder - actual implementation would be more complex)
    aligned_image = image_to_align  # Simplified

    # Save aligned image
    cv2.imwrite(output_path, aligned_image)
    print(f"Image aligned and saved to {output_path}")

    return True


@print_elapsed_time
def align_image_set(image_dir: str, image_prefix: str, output_dir: str,
                   base_image_index: int = 0) -> bool:
    """
    Align a set of images with the same prefix

    Args:
        image_dir: Directory containing images
        image_prefix: Prefix for images to be aligned
        output_dir: Output directory
        base_image_index: Index of the base image

    Returns:
        bool: True if successful, False otherwise
    """
    # Get list of images with prefix
    image_files = [f for f in os.listdir(image_dir) if f.startswith(image_prefix)]
    image_files.sort()

    if not image_files:
        print(f"No images found with prefix: {image_prefix}")
        return False

    base_image_path = os.path.join(image_dir, image_files[base_image_index])
    print(f"Copied base image to {output_dir}")

    for i, image_file in enumerate(image_files):
        if i == base_image_index:
            continue

        image_path = os.path.join(image_dir, image_file)
        output_path = os.path.join(output_dir, f"aligned_{image_file}")

        # Align image
        align_image(base_image_path, image_path, output_path)
        print(f"Aligned and saved {image_file}")

    return True


@print_elapsed_time
def align_image_set_threaded(image_dir: str, image_prefix: str, output_dir: str,
                           base_image_index: int = 0) -> bool:
    """Threaded version of align_image_set"""
    # This would implement threading for faster processing
    return align_image_set(image_dir, image_prefix, output_dir, base_image_index)


@print_elapsed_time
def create_object_normal_map(directional_images: List[str], output_path: str) -> bool:
    """
    Create object normal map from directional images

    Args:
        directional_images: Paths to six directional images
        output_path: Path to save the normal map

    Returns:
        bool: True if successful, False otherwise
    """
    if len(directional_images) != 6:
        print("Six image paths are required for create_object_normal_map function")
        return False

    # Load directional images
    images = load_directional_images(directional_images)
    if images is None:
        return False

    # Create normal map (placeholder - actual implementation would be more complex)
    height, width = images[0].shape[:2]
    object_normal_map = np.zeros((height, width, 3), dtype=np.uint8)

    # Save normal map
    cv2.imwrite(output_path, object_normal_map)
    print(f"object_normal_map: {output_path}")

    return True


@print_elapsed_time
def create_tangent_normal_map(image1_path: str, output_path: str,
                            blur_size: int = 5, kernel_size: int = 3,
                            sigma: float = 1.0, remove_blue: bool = False) -> bool:
    """
    Create tangent normal map from input image

    Args:
        image1_path: Path to input image
        output_path: Path to save the tangent normal map
        blur_size: Blur size for tangent normal map
        kernel_size: Kernel size for tangent normal filtering
        sigma: Sigma value for tangent normal filtering
        remove_blue: Remove blue channel from tangent normal map

    Returns:
        bool: True if successful, False otherwise
    """
    # Load image
    image = cv2.imread(image1_path, cv2.IMREAD_UNCHANGED)
    if image is None:
        print(f"Image not found: {image1_path}")
        return False

    if image.dtype != np.uint16:
        print("Input image must be a 16-bit image.")
        return False

    # Create tangent normal map (placeholder)
    tangent_normal_map = image.copy()

    if remove_blue and len(image.shape) == 3:
        tangent_normal_map[:, :, 0] = 0  # Remove blue channel

    # Save tangent normal map
    cv2.imwrite(output_path, tangent_normal_map)

    return True


@print_elapsed_time
def create_specular_map(image1_path: str, image2_path: str, output_path: str) -> bool:
    """
    Create specular map from two images

    Args:
        image1_path: Path to the first image
        image2_path: Path to the second image
        output_path: Path to save the specular map

    Returns:
        bool: True if successful, False otherwise
    """
    # Load images
    image1 = cv2.imread(image1_path, cv2.IMREAD_UNCHANGED)
    image2 = cv2.imread(image2_path, cv2.IMREAD_UNCHANGED)

    if image1 is None or image2 is None:
        print("Failed to load images")
        return False

    if image1.shape != image2.shape:
        print("Images must be of the same dimensions to calculate the specular map.")
        return False

    # Create specular map (placeholder)
    specular_map = cv2.absdiff(image1, image2)

    # Save specular map
    cv2.imwrite(output_path, specular_map)

    return True

def main():
    """Main function - ImgProc CLI Tool"""
    parser = argparse.ArgumentParser(description='ImgProc CLI Tool')

    # Add function argument
    parser.add_argument('--function',
                        choices=['align_image', 'align_image_set', 'align_image_set_threaded',
                                'create_object_normal_map', 'create_tangent_normal_map', 'create_specular_map'],
                        required=True,
                        help='Function to execute')

    # Image arguments
    parser.add_argument('--image1', help='Path to the first image')
    parser.add_argument('--image2', help='Path to the second image')
    parser.add_argument('--image_dir', help='Directory containing images (required for align_image_set)')
    parser.add_argument('--image_prefix', help='Prefix for images to be aligned (required for align_image_set)')
    parser.add_argument('--directional_images', nargs=6, help='Paths to six images (required for create_object_normal_map)')

    # Output argument
    parser.add_argument('--output', required=True, help='Path to save the output (directory or file depending on the function)')

    # Optional arguments
    parser.add_argument('--base_image_index', type=int, default=0, help='Index of the base image (required for align_image_set)')
    parser.add_argument('--crop_fraction', type=float, default=0.1, help='Crop fraction for alignment')
    parser.add_argument('--auto_crop_face', action='store_true', help='Auto detect and crop face for alignment')
    parser.add_argument('--face_mask_path', help='Path to mask image for face detection')

    # Tangent normal map specific arguments
    parser.add_argument('--blur_size', type=int, default=5, help='Blur size for tangent normal map')
    parser.add_argument('--kernel_size', type=int, default=3, help='Kernel size for tangent normal filtering')
    parser.add_argument('--sigma', type=float, default=1.0, help='Sigma value for tangent normal filtering')
    parser.add_argument('--remove_blue', action='store_true', help='Remove blue channel from tangent normal map')

    args = parser.parse_args()

    # Validate arguments based on function
    if args.function == 'align_image':
        if not args.image1 or not args.image2:
            print("--image1 --image2 --output are required for align_image function")
            return False
        return align_image(args.image1, args.image2, args.output,
                          args.crop_fraction, args.auto_crop_face, args.face_mask_path)

    elif args.function in ['align_image_set', 'align_image_set_threaded']:
        if not args.image_dir or not args.image_prefix:
            print("--image_dir and --image_prefix are required for align_image_set function")
            return False

        if args.function == 'align_image_set_threaded':
            return align_image_set_threaded(args.image_dir, args.image_prefix, args.output, args.base_image_index)
        else:
            return align_image_set(args.image_dir, args.image_prefix, args.output, args.base_image_index)

    elif args.function == 'create_object_normal_map':
        if not args.directional_images or len(args.directional_images) != 6:
            print("Six image paths are required for create_object_normal_map function")
            return False
        return create_object_normal_map(args.directional_images, args.output)

    elif args.function == 'create_tangent_normal_map':
        if not args.image1:
            print("--image1 is required for create_tangent_normal_map function")
            return False
        return create_tangent_normal_map(args.image1, args.output,
                                       args.blur_size, args.kernel_size, args.sigma, args.remove_blue)

    elif args.function == 'create_specular_map':
        if not args.image1 or not args.image2:
            print("Both --image1 and --image2 are required for create_specular_map function")
            return False
        return create_specular_map(args.image1, args.image2, args.output)

    else:
        parser.print_help()
        return False


if __name__ == '__main__':
    success = main()
    if not success:
        sys.exit(1)


# USAGE EXAMPLES (from extracted strings):
# ./imgproc --function align_image --image1 path/to/image1.tif --image2 path/to/image2.tif --output path/to/output.tif
# ./imgproc --function align_image_set --image_dir path/to/images --image_prefix TX-01 --output path/to/output
# ./imgproc --function create_object_normal_map --directional_images path/to/image1.tif path/to/image2.tif path/to/image3.tif path/to/image4.tif path/to/image5.tif path/to/image6.tif --output path/to/output.tif
# ./imgproc --function create_tangent_normal_map --image1 path/to/image1.tif --output path/to/output.tif
# ./imgproc --function create_specular_map --image1 path/to/image1.tif --image2 path/to/image2.tif --output path/to/output.tif


# REVERSE ENGINEERING NOTES:
# This script was reconstructed by reverse engineering imgproc.exe
# Key functions identified:
# - align_image: Align two images using various methods (AKAZE, ECC, optical flow)
# - align_image_set: Align multiple images with same prefix
# - align_image_set_threaded: Threaded version for better performance
# - create_object_normal_map: Create normal maps from 6 directional images
# - create_tangent_normal_map: Create tangent normal maps with filtering options
# - create_specular_map: Create specular maps from two images
#
# The original application appears to use OpenCV for image processing
# and includes face detection capabilities for alignment.
