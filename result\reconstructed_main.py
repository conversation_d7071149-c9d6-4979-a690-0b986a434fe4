#!/usr/bin/env python3
"""
ImgProc - Image Processing CLI Tool
Reconstructed from reverse engineering imgproc.exe

This tool provides various image processing functions including:
- Image alignment
- Normal map creation (object and tangent)
- Specular map creation
"""

import argparse
import cv2
import numpy as np
import os
import sys
import time
from typing import List, Tuple, Optional


def print_elapsed_time(func):
    """Print the elapsed time of the decorated function"""
    def wrapper(*args, **kwargs):
        start_time = time.time()
        print(f"imgproc: Started - {func.__name__}")
        result = func(*args, **kwargs)
        elapsed = time.time() - start_time
        hours = int(elapsed // 3600)
        minutes = int((elapsed % 3600) // 60)
        seconds = elapsed % 60
        print(f"imgproc: Complete - elapsed time: {hours:d}:{minutes:02d}:{seconds:0.2f}")
        return result
    return wrapper


def load_directional_images(directional_images: List[str]) -> List[np.ndarray]:
    """
    Load directional images for normal map creation

    Args:
        directional_images: List of image file paths

    Returns:
        List of loaded images or None if any image fails to load
    """
    images = []
    for i, image_path in enumerate(directional_images):
        if not os.path.exists(image_path):
            print(f"Image not found: {image_path}")
            return None

        image = cv2.imread(image_path, cv2.IMREAD_UNCHANGED)
        if image is None:
            print(f"Failed to load image: {image_path}")
            return None

        # Validate image dimensions consistency
        if i > 0 and image.shape != images[0].shape:
            print(f"Image {i+1} dimensions {image.shape} don't match first image {images[0].shape}")
            return None

        print(f"Image for direction {i+1}: {image_path} (shape: {image.shape})")
        images.append(image)

    return images


def normalize_intensity(image: np.ndarray) -> np.ndarray:
    """
    Normalize image intensity to [0, 1] range

    Args:
        image: Input image (8-bit or 16-bit)

    Returns:
        Normalized image as float32 or None if unsupported format
    """
    if image.dtype == np.uint8:
        normalized = image.astype(np.float32) / 255.0
        print(f"Normalized 8-bit image: min={np.min(normalized):.3f}, max={np.max(normalized):.3f}")
        return normalized
    elif image.dtype == np.uint16:
        normalized = image.astype(np.float32) / 65535.0
        print(f"Normalized 16-bit image: min={np.min(normalized):.3f}, max={np.max(normalized):.3f}")
        return normalized
    else:
        print(f"Image is not 8bit or 16bit (dtype: {image.dtype})")
        return None


def edge_preprocess_image(image: np.ndarray, edge_threshold: float = 0.1) -> np.ndarray:
    """
    Preprocess image with edge enhancement for better normal map quality

    Args:
        image: Input normalized image
        edge_threshold: Threshold for edge detection

    Returns:
        Edge-enhanced image
    """
    # Apply Canny edge detection
    edges = cv2.Canny((image * 255).astype(np.uint8), 50, 150)
    edges_normalized = edges.astype(np.float32) / 255.0

    # Combine original image with edge information
    enhanced = image + edges_normalized * edge_threshold
    enhanced = np.clip(enhanced, 0.0, 1.0)

    return enhanced


def validate_normal_map(normal_map: np.ndarray) -> bool:
    """
    Validate normal map for common issues

    Args:
        normal_map: Normal map to validate

    Returns:
        True if valid, False otherwise
    """
    if normal_map is None:
        print("Normal map is None")
        return False

    if len(normal_map.shape) != 3 or normal_map.shape[2] != 3:
        print(f"Invalid normal map shape: {normal_map.shape}")
        return False

    # Check value ranges
    min_val, max_val = np.min(normal_map), np.max(normal_map)
    if min_val < -1.1 or max_val > 1.1:
        print(f"Normal map values out of range: [{min_val:.3f}, {max_val:.3f}]")
        return False

    # Check for NaN or infinite values
    if np.any(np.isnan(normal_map)) or np.any(np.isinf(normal_map)):
        print("Normal map contains NaN or infinite values")
        return False

    print("Normal map validation passed")
    return True


@print_elapsed_time
def align_image(image1_path: str, image2_path: str, output_path: str,
                crop_fraction: float = 0.1, auto_crop_face: bool = False,
                face_mask_path: Optional[str] = None) -> bool:
    """
    Align two images using various alignment methods

    Args:
        image1_path: Path to the first image (base image)
        image2_path: Path to the second image (image to align)
        output_path: Path to save the aligned image
        crop_fraction: Crop fraction for alignment
        auto_crop_face: Auto detect and crop face for alignment
        face_mask_path: Path to mask image for face detection

    Returns:
        bool: True if successful, False otherwise
    """
    # Load images
    base_image = cv2.imread(image1_path, cv2.IMREAD_UNCHANGED)
    image_to_align = cv2.imread(image2_path, cv2.IMREAD_UNCHANGED)

    if base_image is None or image_to_align is None:
        print("Failed to load images")
        return False

    print(f"image min: {np.min(image_to_align)}, max: {np.max(image_to_align)}")

    # Perform alignment (placeholder - actual implementation would be more complex)
    aligned_image = image_to_align  # Simplified

    # Save aligned image
    cv2.imwrite(output_path, aligned_image)
    print(f"Image aligned and saved to {output_path}")

    return True


@print_elapsed_time
def align_image_set(image_dir: str, image_prefix: str, output_dir: str,
                   base_image_index: int = 0) -> bool:
    """
    Align a set of images with the same prefix

    Args:
        image_dir: Directory containing images
        image_prefix: Prefix for images to be aligned
        output_dir: Output directory
        base_image_index: Index of the base image

    Returns:
        bool: True if successful, False otherwise
    """
    # Get list of images with prefix
    image_files = [f for f in os.listdir(image_dir) if f.startswith(image_prefix)]
    image_files.sort()

    if not image_files:
        print(f"No images found with prefix: {image_prefix}")
        return False

    base_image_path = os.path.join(image_dir, image_files[base_image_index])
    print(f"Copied base image to {output_dir}")

    for i, image_file in enumerate(image_files):
        if i == base_image_index:
            continue

        image_path = os.path.join(image_dir, image_file)
        output_path = os.path.join(output_dir, f"aligned_{image_file}")

        # Align image
        align_image(base_image_path, image_path, output_path)
        print(f"Aligned and saved {image_file}")

    return True


@print_elapsed_time
def align_image_set_threaded(image_dir: str, image_prefix: str, output_dir: str,
                           base_image_index: int = 0) -> bool:
    """Threaded version of align_image_set"""
    # This would implement threading for faster processing
    return align_image_set(image_dir, image_prefix, output_dir, base_image_index)


@print_elapsed_time
def create_object_normal_map(directional_images: List[str], output_path: str) -> bool:
    """
    Create object normal map from directional images using photometric stereo

    This function implements photometric stereo to generate normal maps from
    6 directional lighting images. The algorithm assumes the following light directions:
    - Image 1: Light from left (-1, 0, 1)
    - Image 2: Light from right (1, 0, 1)
    - Image 3: Light from top (0, -1, 1)
    - Image 4: Light from bottom (0, 1, 1)
    - Image 5: Light from front (0, 0, 1)
    - Image 6: Light from back (0, 0, -1)

    Args:
        directional_images: Paths to six directional images
        output_path: Path to save the normal map

    Returns:
        bool: True if successful, False otherwise
    """
    if len(directional_images) != 6:
        print("Six image paths are required for create_object_normal_map function")
        return False

    # Load directional images
    images = load_directional_images(directional_images)
    if images is None:
        return False

    # Define light directions for 6-directional setup
    light_directions = np.array([
        [-1.0, 0.0, 1.0],   # Left
        [1.0, 0.0, 1.0],    # Right
        [0.0, -1.0, 1.0],   # Top
        [0.0, 1.0, 1.0],    # Bottom
        [0.0, 0.0, 1.0],    # Front
        [0.0, 0.0, -1.0]    # Back
    ])

    # Normalize light directions
    for i in range(len(light_directions)):
        light_directions[i] = light_directions[i] / np.linalg.norm(light_directions[i])

    print("Computing object normal map using photometric stereo...")

    # Get image dimensions
    height, width = images[0].shape[:2]

    # Convert images to grayscale and normalize
    normalized_images = []
    for i, img in enumerate(images):
        print(f"Processing directional image {i+1}/6...")

        # Convert to grayscale if needed
        if len(img.shape) == 3:
            gray_img = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
        else:
            gray_img = img.copy()

        # Normalize intensity
        normalized_img = normalize_intensity(gray_img)
        if normalized_img is None:
            return False

        normalized_images.append(normalized_img)

    # Create normal map using photometric stereo
    object_normal_map = photometric_stereo_6_lights(normalized_images, light_directions)

    # Convert normal map to 8-bit BGR format for OpenCV
    normal_map_bgr = convert_normal_to_bgr(object_normal_map)

    # Save normal map
    cv2.imwrite(output_path, normal_map_bgr)
    print(f"object_normal_map: {output_path}")

    return True


def photometric_stereo_6_lights(images: List[np.ndarray], light_directions: np.ndarray) -> np.ndarray:
    """
    Perform photometric stereo with 6 directional lights

    Args:
        images: List of 6 normalized grayscale images
        light_directions: 6x3 array of light direction vectors

    Returns:
        Normal map as float32 array with values in [-1, 1]
    """
    height, width = images[0].shape
    normal_map = np.zeros((height, width, 3), dtype=np.float32)

    # Process each pixel
    for y in range(height):
        if y % 50 == 0:  # Progress indicator
            print(f"Processing row {y}/{height}")

        for x in range(width):
            # Get intensities for this pixel from all 6 images
            intensities = np.array([img[y, x] for img in images])

            # Skip if any intensity is too low (shadow/noise)
            if np.any(intensities < 0.01):
                normal_map[y, x] = [0.0, 0.0, 1.0]  # Default upward normal
                continue

            # Solve for surface normal using least squares
            # L * n = I, where L is light matrix, n is normal, I is intensities
            try:
                # Use pseudo-inverse for overdetermined system
                normal = np.linalg.lstsq(light_directions, intensities, rcond=None)[0]

                # Normalize the normal vector
                norm_length = np.linalg.norm(normal)
                if norm_length > 0:
                    normal = normal / norm_length
                else:
                    normal = np.array([0.0, 0.0, 1.0])

                # Ensure Z component is positive (facing outward)
                if normal[2] < 0:
                    normal = -normal

                normal_map[y, x] = normal

            except np.linalg.LinAlgError:
                # If singular matrix, use default normal
                normal_map[y, x] = [0.0, 0.0, 1.0]

    return normal_map


def convert_normal_to_bgr(normal_map: np.ndarray) -> np.ndarray:
    """
    Convert normal map from [-1,1] range to BGR [0,255] format

    Args:
        normal_map: Normal map with values in [-1, 1]

    Returns:
        BGR image with values in [0, 255]
    """
    # Convert from [-1, 1] to [0, 1]
    normalized = (normal_map + 1.0) * 0.5

    # Convert to [0, 255] and ensure proper data type
    bgr_map = (normalized * 255.0).astype(np.uint8)

    # OpenCV uses BGR format, but normal maps are typically stored as RGB
    # So we need to swap R and B channels: (X, Y, Z) -> (Z, Y, X) in BGR
    bgr_map = bgr_map[:, :, [2, 1, 0]]

    return bgr_map


@print_elapsed_time
def create_tangent_normal_map(image1_path: str, output_path: str,
                            blur_size: int = 5, kernel_size: int = 3,
                            sigma: float = 1.0, remove_blue: bool = False) -> bool:
    """
    Create tangent normal map from height map using gradient-based method

    This function converts a height map (grayscale image) into a tangent space normal map
    by computing gradients and applying various filtering options.

    Args:
        image1_path: Path to input height map image (16-bit preferred)
        output_path: Path to save the tangent normal map
        blur_size: Blur size for pre-processing the height map
        kernel_size: Kernel size for gradient computation (Sobel filter)
        sigma: Sigma value for Gaussian filtering
        remove_blue: Remove blue channel from tangent normal map

    Returns:
        bool: True if successful, False otherwise
    """
    # Load image
    image = cv2.imread(image1_path, cv2.IMREAD_UNCHANGED)
    if image is None:
        print(f"Image not found: {image1_path}")
        return False

    print(f"Input image shape: {image.shape}, dtype: {image.dtype}")

    # Convert to grayscale if needed
    if len(image.shape) == 3:
        height_map = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
    else:
        height_map = image.copy()

    # Validate bit depth (prefer 16-bit but accept 8-bit)
    if image.dtype == np.uint16:
        print("Processing 16-bit height map")
        height_map = height_map.astype(np.float32) / 65535.0
    elif image.dtype == np.uint8:
        print("Processing 8-bit height map")
        height_map = height_map.astype(np.float32) / 255.0
    else:
        print("Image is not 8bit or 16bit")
        return False

    # Create height map for normal generation
    processed_height_map = create_height_map(height_map, blur_size, sigma)

    # Generate tangent normal map from height map
    tangent_normal_map = height_map_to_normal_map(processed_height_map, kernel_size)

    # Apply post-processing
    if remove_blue:
        print("Removing blue channel from tangent normal map")
        tangent_normal_map[:, :, 2] = 0.5  # Set Z to neutral (0.5 in [0,1] range)

    # Convert to 8-bit BGR for output
    normal_map_bgr = (tangent_normal_map * 255.0).astype(np.uint8)

    # Save tangent normal map
    cv2.imwrite(output_path, normal_map_bgr)
    print(f"Tangent normal map saved to: {output_path}")

    return True


def create_height_map(input_image: np.ndarray, blur_size: int, sigma: float) -> np.ndarray:
    """
    Create and preprocess height map for normal map generation

    Args:
        input_image: Input grayscale image normalized to [0, 1]
        blur_size: Size of blur kernel
        sigma: Gaussian blur sigma value

    Returns:
        Processed height map
    """
    height_map = input_image.copy()

    # Apply Gaussian blur to smooth the height map
    if blur_size > 1:
        # Ensure kernel size is odd
        kernel_size = blur_size if blur_size % 2 == 1 else blur_size + 1
        height_map = cv2.GaussianBlur(height_map, (kernel_size, kernel_size), sigma)
        print(f"Applied Gaussian blur: kernel={kernel_size}, sigma={sigma}")

    return height_map


def height_map_to_normal_map(height_map: np.ndarray, kernel_size: int = 3) -> np.ndarray:
    """
    Convert height map to normal map using gradient computation

    Args:
        height_map: Input height map normalized to [0, 1]
        kernel_size: Size of Sobel kernel for gradient computation

    Returns:
        Normal map with values in [0, 1] range (RGB format)
    """
    print("Computing gradients for normal map generation...")

    # Compute gradients using Sobel operators
    # Note: OpenCV Sobel uses different kernel sizes, we'll use the standard 3x3
    grad_x = cv2.Sobel(height_map, cv2.CV_32F, 1, 0, ksize=kernel_size)
    grad_y = cv2.Sobel(height_map, cv2.CV_32F, 0, 1, ksize=kernel_size)

    # Scale gradients (adjust strength of normal map)
    scale_factor = 1.0  # Can be adjusted for stronger/weaker normals
    grad_x *= scale_factor
    grad_y *= scale_factor

    # Create normal vectors
    # In tangent space: X = -grad_x, Y = -grad_y, Z = 1
    height, width = height_map.shape
    normal_map = np.zeros((height, width, 3), dtype=np.float32)

    # X component (red channel): negative X gradient
    normal_map[:, :, 0] = -grad_x

    # Y component (green channel): negative Y gradient
    normal_map[:, :, 1] = -grad_y

    # Z component (blue channel): always positive (pointing up)
    normal_map[:, :, 2] = 1.0

    # Normalize each normal vector
    print("Normalizing normal vectors...")
    for y in range(height):
        for x in range(width):
            normal = normal_map[y, x]
            length = np.linalg.norm(normal)
            if length > 0:
                normal_map[y, x] = normal / length
            else:
                normal_map[y, x] = [0.0, 0.0, 1.0]  # Default upward normal

    # Convert from [-1, 1] to [0, 1] range for storage
    normal_map = (normal_map + 1.0) * 0.5

    # Ensure Z component is always > 0.5 (pointing outward)
    normal_map[:, :, 2] = np.maximum(normal_map[:, :, 2], 0.5)

    return normal_map


@print_elapsed_time
def create_specular_map(image1_path: str, image2_path: str, output_path: str) -> bool:
    """
    Create specular map from two images

    Args:
        image1_path: Path to the first image
        image2_path: Path to the second image
        output_path: Path to save the specular map

    Returns:
        bool: True if successful, False otherwise
    """
    # Load images
    image1 = cv2.imread(image1_path, cv2.IMREAD_UNCHANGED)
    image2 = cv2.imread(image2_path, cv2.IMREAD_UNCHANGED)

    if image1 is None or image2 is None:
        print("Failed to load images")
        return False

    if image1.shape != image2.shape:
        print("Images must be of the same dimensions to calculate the specular map.")
        return False

    # Create specular map (placeholder)
    specular_map = cv2.absdiff(image1, image2)

    # Save specular map
    cv2.imwrite(output_path, specular_map)

    return True

def main():
    """Main function - ImgProc CLI Tool"""
    parser = argparse.ArgumentParser(description='ImgProc CLI Tool')

    # Add function argument
    parser.add_argument('--function',
                        choices=['align_image', 'align_image_set', 'align_image_set_threaded',
                                'create_object_normal_map', 'create_tangent_normal_map', 'create_specular_map'],
                        required=True,
                        help='Function to execute')

    # Image arguments
    parser.add_argument('--image1', help='Path to the first image')
    parser.add_argument('--image2', help='Path to the second image')
    parser.add_argument('--image_dir', help='Directory containing images (required for align_image_set)')
    parser.add_argument('--image_prefix', help='Prefix for images to be aligned (required for align_image_set)')
    parser.add_argument('--directional_images', nargs=6, help='Paths to six images (required for create_object_normal_map)')

    # Output argument
    parser.add_argument('--output', required=True, help='Path to save the output (directory or file depending on the function)')

    # Optional arguments
    parser.add_argument('--base_image_index', type=int, default=0, help='Index of the base image (required for align_image_set)')
    parser.add_argument('--crop_fraction', type=float, default=0.1, help='Crop fraction for alignment')
    parser.add_argument('--auto_crop_face', action='store_true', help='Auto detect and crop face for alignment')
    parser.add_argument('--face_mask_path', help='Path to mask image for face detection')

    # Tangent normal map specific arguments
    parser.add_argument('--blur_size', type=int, default=5, help='Blur size for tangent normal map')
    parser.add_argument('--kernel_size', type=int, default=3, help='Kernel size for tangent normal filtering')
    parser.add_argument('--sigma', type=float, default=1.0, help='Sigma value for tangent normal filtering')
    parser.add_argument('--remove_blue', action='store_true', help='Remove blue channel from tangent normal map')

    args = parser.parse_args()

    # Validate arguments based on function
    if args.function == 'align_image':
        if not args.image1 or not args.image2:
            print("--image1 --image2 --output are required for align_image function")
            return False
        return align_image(args.image1, args.image2, args.output,
                          args.crop_fraction, args.auto_crop_face, args.face_mask_path)

    elif args.function in ['align_image_set', 'align_image_set_threaded']:
        if not args.image_dir or not args.image_prefix:
            print("--image_dir and --image_prefix are required for align_image_set function")
            return False

        if args.function == 'align_image_set_threaded':
            return align_image_set_threaded(args.image_dir, args.image_prefix, args.output, args.base_image_index)
        else:
            return align_image_set(args.image_dir, args.image_prefix, args.output, args.base_image_index)

    elif args.function == 'create_object_normal_map':
        if not args.directional_images or len(args.directional_images) != 6:
            print("Six image paths are required for create_object_normal_map function")
            return False
        return create_object_normal_map(args.directional_images, args.output)

    elif args.function == 'create_tangent_normal_map':
        if not args.image1:
            print("--image1 is required for create_tangent_normal_map function")
            return False
        return create_tangent_normal_map(args.image1, args.output,
                                       args.blur_size, args.kernel_size, args.sigma, args.remove_blue)

    elif args.function == 'create_specular_map':
        if not args.image1 or not args.image2:
            print("Both --image1 and --image2 are required for create_specular_map function")
            return False
        return create_specular_map(args.image1, args.image2, args.output)

    else:
        parser.print_help()
        return False


if __name__ == '__main__':
    success = main()
    if not success:
        sys.exit(1)


# USAGE EXAMPLES (from extracted strings):
# ./imgproc --function align_image --image1 path/to/image1.tif --image2 path/to/image2.tif --output path/to/output.tif
# ./imgproc --function align_image_set --image_dir path/to/images --image_prefix TX-01 --output path/to/output
# ./imgproc --function create_object_normal_map --directional_images path/to/image1.tif path/to/image2.tif path/to/image3.tif path/to/image4.tif path/to/image5.tif path/to/image6.tif --output path/to/output.tif
# ./imgproc --function create_tangent_normal_map --image1 path/to/image1.tif --output path/to/output.tif
# ./imgproc --function create_specular_map --image1 path/to/image1.tif --image2 path/to/image2.tif --output path/to/output.tif


# REVERSE ENGINEERING NOTES:
# This script was reconstructed by reverse engineering imgproc.exe
# Key functions identified:
# - align_image: Align two images using various methods (AKAZE, ECC, optical flow)
# - align_image_set: Align multiple images with same prefix
# - align_image_set_threaded: Threaded version for better performance
# - create_object_normal_map: Create normal maps from 6 directional images
# - create_tangent_normal_map: Create tangent normal maps with filtering options
# - create_specular_map: Create specular maps from two images
#
# The original application appears to use OpenCV for image processing
# and includes face detection capabilities for alignment.
