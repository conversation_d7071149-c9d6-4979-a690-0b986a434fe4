# Code object from position 8545902
# Filename: contextvars.py
# Name: <module>
# Args: 0
# Locals: 0
# Stack size: 2
# Flags: 0

  0           0 RESUME                   0

  1           2 LOAD_CONST               0 (0)
              4 LOAD_CONST               1 (('Context', 'ContextVar', 'Token', 'copy_context'))
              6 IMPORT_NAME              0 (_contextvars)
              8 IMPORT_FROM              1 (Context)
             10 STORE_NAME               1 (Context)
             12 IMPORT_FROM              2 (ContextVar)
             14 STORE_NAME               2 (ContextVar)
             16 IMPORT_FROM              3 (Token)
             18 STORE_NAME               3 (Token)
             20 IMPORT_FROM              4 (copy_context)
             22 STORE_NAME               4 (copy_context)
             24 POP_TOP

  4          26 LOAD_CONST               1 (('Context', 'ContextVar', 'Token', 'copy_context'))
             28 STORE_NAME               5 (__all__)
             30 LOAD_CONST               2 (None)
             32 RETURN_VALUE


# Constants:
# 0: 0
# 1: tuple
# 2: None


# Names:
# 0: '_contextvars'
# 1: 'Context'
# 2: 'ContextVar'
# 3: 'Token'
# 4: 'copy_context'
# 5: '__all__'


# Variable names:
