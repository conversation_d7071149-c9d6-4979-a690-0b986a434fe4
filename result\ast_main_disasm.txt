# MAIN APPLICATION CODE OBJECT
# Position: 7909935
# Filename: ast.py
# Function: <module>
# Args: 0
# Locals: 0
# Stack size: 17
# Flags: 0

# BYTECODE DISASSEMBLY:
==================================================
   0           0 RESUME                   0

   1           2 LOAD_CONST               0 ('\n    ast\n    ~~~\n\n    The `ast` module helps Python applications to process trees of the Python\n    abstract syntax grammar.  The abstract syntax itself might change with\n    each Python release; this module helps to find out programmatically what\n    the current grammar looks like and allows modifications of it.\n\n    An abstract syntax tree can be generated by passing `ast.PyCF_ONLY_AST` as\n    a flag to the `compile()` builtin function or by using the `parse()`\n    function from this module.  The result will be a tree of objects whose\n    classes all inherit from `ast.AST`.\n\n    A modified abstract syntax tree can be compiled into a Python code object\n    using the built-in `compile()` function.\n\n    Additionally various helper functions are provided that make working with\n    the trees simpler.  The main intention of the helper functions and this\n    module in general is to provide an easy to use interface for libraries\n    that work tightly with the python syntax (template engines for example).\n\n\n    :copyright: Copyright 2008 by Armin Ronacher.\n    :license: Python License.\n')
               4 STORE_NAME               0 (__doc__)

  27           6 LOAD_CONST               1 (0)
               8 LOAD_CONST               2 (None)
              10 IMPORT_NAME              1 (sys)
              12 STORE_NAME               1 (sys)

  28          14 LOAD_CONST               1 (0)
              16 LOAD_CONST               3 (('*',))
              18 IMPORT_NAME              2 (_ast)
              20 LOAD_CONST               0 ('\n    ast\n    ~~~\n\n    The `ast` module helps Python applications to process trees of the Python\n    abstract syntax grammar.  The abstract syntax itself might change with\n    each Python release; this module helps to find out programmatically what\n    the current grammar looks like and allows modifications of it.\n\n    An abstract syntax tree can be generated by passing `ast.PyCF_ONLY_AST` as\n    a flag to the `compile()` builtin function or by using the `parse()`\n    function from this module.  The result will be a tree of objects whose\n    classes all inherit from `ast.AST`.\n\n    A modified abstract syntax tree can be compiled into a Python code object\n    using the built-in `compile()` function.\n\n    Additionally various helper functions are provided that make working with\n    the trees simpler.  The main intention of the helper functions and this\n    module in general is to provide an easy to use interface for libraries\n    that work tightly with the python syntax (template engines for example).\n\n\n    :copyright: Copyright 2008 by Armin Ronacher.\n    :license: Python License.\n')

  29          22 LOAD_CONST               1 (0)
              24 LOAD_CONST               4 (('contextmanager', 'nullcontext'))
              26 IMPORT_NAME              3 (contextlib)
              28 IMPORT_FROM              4 (contextmanager)
              30 STORE_NAME               4 (contextmanager)
              32 IMPORT_FROM              5 (nullcontext)
              34 STORE_NAME               5 (nullcontext)
              36 POP_TOP

  30          38 LOAD_CONST               1 (0)
              40 LOAD_CONST               5 (('IntEnum', 'auto', '_simple_enum'))
              42 IMPORT_NAME              6 (enum)
              44 IMPORT_FROM              7 (IntEnum)
              46 STORE_NAME               7 (IntEnum)
              48 IMPORT_FROM              8 (auto)
              50 STORE_NAME               8 (auto)
              52 IMPORT_FROM              9 (_simple_enum)
              54 STORE_NAME               9 (_simple_enum)
              56 POP_TOP

  33          58 LOAD_CONST              76 (('<unknown>', 'exec'))

  34          60 LOAD_CONST               8 (False)
              62 LOAD_CONST               2 (None)

  33          64 LOAD_CONST               9 (('type_comments', 'feature_version'))
              66 BUILD_CONST_KEY_MAP      2
              68 LOAD_CONST              10 (<code object parse at 0x000001E77ECB4310, file "ast.py", line 33>)
              70 MAKE_FUNCTION            3 (defaults, kwdefaults)
              72 STORE_NAME              10 (parse)

  54          74 LOAD_CONST              11 (<code object literal_eval at 0x000001E77E73F4B0, file "ast.py", line 54>)
              76 MAKE_FUNCTION            0
              78 STORE_NAME              11 (literal_eval)

 113          80 LOAD_CONST              77 ((True, False))
              82 LOAD_CONST               2 (None)
              84 LOAD_CONST              13 (('indent',))
              86 BUILD_CONST_KEY_MAP      1
              88 LOAD_CONST              14 (<code object dump at 0x000001E77E6DF5D0, file "ast.py", line 113>)
              90 MAKE_FUNCTION            3 (defaults, kwdefaults)
              92 STORE_NAME              12 (dump)

 180          94 LOAD_CONST              15 (<code object copy_location at 0x000001E77EC71930, file "ast.py", line 180>)
              96 MAKE_FUNCTION            0
              98 STORE_NAME              13 (copy_location)

 197         100 LOAD_CONST              16 (<code object fix_missing_locations at 0x000001E77EC90E40, file "ast.py", line 197>)
             102 MAKE_FUNCTION            0
             104 STORE_NAME              14 (fix_missing_locations)

 232         106 LOAD_CONST              78 ((1,))
             108 LOAD_CONST              18 (<code object increment_lineno at 0x000001E77E72AD30, file "ast.py", line 232>)
             110 MAKE_FUNCTION            1 (defaults)
             112 STORE_NAME              15 (increment_lineno)

 255         114 LOAD_CONST              19 (<code object iter_fields at 0x000001E77ECBC030, file "ast.py", line 255>)
             116 MAKE_FUNCTION            0
             118 STORE_NAME              16 (iter_fields)

 267         120 LOAD_CONST              20 (<code object iter_child_nodes at 0x000001E77EC71AC0, file "ast.py", line 267>)
             122 MAKE_FUNCTION            0
             124 STORE_NAME              17 (iter_child_nodes)

 281         126 LOAD_CONST              79 ((True,))
             128 LOAD_CONST              21 (<code object get_docstring at 0x000001E77EF1BD00, file "ast.py", line 281>)
             130 MAKE_FUNCTION            1 (defaults)
             132 STORE_NAME              18 (get_docstring)

 307         134 LOAD_CONST              22 (<code object _splitlines_no_ff at 0x000001E77C48BCA0, file "ast.py", line 307>)
             136 MAKE_FUNCTION            0
             138 STORE_NAME              19 (_splitlines_no_ff)

 332         140 LOAD_CONST              23 (<code object _pad_whitespace at 0x000001E77EC7B430, file "ast.py", line 332>)
             142 MAKE_FUNCTION            0
             144 STORE_NAME              20 (_pad_whitespace)

 343         146 LOAD_CONST               8 (False)
             148 LOAD_CONST              24 (('padded',))
             150 BUILD_CONST_KEY_MAP      1
             152 LOAD_CONST              25 (<code object get_source_segment at 0x000001E77E8AE3C0, file "ast.py", line 343>)
             154 MAKE_FUNCTION            2 (kwdefaults)
             156 STORE_NAME              21 (get_source_segment)

 380         158 LOAD_CONST              26 (<code object walk at 0x000001E77ECB4A40, file "ast.py", line 380>)
             160 MAKE_FUNCTION            0
             162 STORE_NAME              22 (walk)

 394         164 PUSH_NULL
             166 LOAD_BUILD_CLASS
             168 LOAD_CONST              27 (<code object NodeVisitor at 0x000001E77EC916B0, file "ast.py", line 394>)
             170 MAKE_FUNCTION            0
             172 LOAD_CONST              28 ('NodeVisitor')
             174 LOAD_NAME               23 (object)
             176 UNPACK_SEQUENCE          3
             180 CALL                     3
             188 CACHE
             190 STORE_NAME              24 (NodeVisitor)

 452         192 PUSH_NULL
             194 LOAD_BUILD_CLASS
             196 LOAD_CONST              29 (<code object NodeTransformer at 0x000001E77EC5E5D0, file "ast.py", line 452>)
             198 MAKE_FUNCTION            0
             200 LOAD_CONST              30 ('NodeTransformer')
             202 LOAD_NAME               24 (NodeVisitor)
             204 UNPACK_SEQUENCE          3
             208 CALL                     3
             216 CACHE
             218 STORE_NAME              25 (NodeTransformer)

 512         220 PUSH_NULL
             222 LOAD_NAME               26 (hasattr)
             224 LOAD_NAME               27 (Constant)
             226 LOAD_CONST              31 ('n')
             228 UNPACK_SEQUENCE          2
             232 CALL                     2
             240 CACHE
             242 POP_JUMP_IF_TRUE        40 (to 324)

 516         244 LOAD_CONST              32 (<code object _getter at 0x000001E77EC22730, file "ast.py", line 516>)
             246 MAKE_FUNCTION            0
             248 STORE_NAME              28 (_getter)

 520         250 LOAD_CONST              33 (<code object _setter at 0x000001E77EC5E790, file "ast.py", line 520>)
             252 MAKE_FUNCTION            0
             254 STORE_NAME              29 (_setter)

 523         256 PUSH_NULL
             258 LOAD_NAME               30 (property)
             260 LOAD_NAME               28 (_getter)
             262 LOAD_NAME               29 (_setter)
             264 UNPACK_SEQUENCE          2
             268 CALL                     2
             276 CACHE
             278 LOAD_NAME               27 (Constant)
             280 STORE_ATTR              31 (n)

 524         290 PUSH_NULL
             292 LOAD_NAME               30 (property)
             294 LOAD_NAME               28 (_getter)
             296 LOAD_NAME               29 (_setter)
             298 UNPACK_SEQUENCE          2
             302 CALL                     2
             310 CACHE
             312 LOAD_NAME               27 (Constant)
             314 STORE_ATTR              32 (s)

 526     >>  324 PUSH_NULL
             326 LOAD_BUILD_CLASS
             328 LOAD_CONST              34 (<code object _ABC at 0x000001E77EC5EF70, file "ast.py", line 526>)
             330 MAKE_FUNCTION            0
             332 LOAD_CONST              35 ('_ABC')
             334 LOAD_NAME               33 (type)
             336 UNPACK_SEQUENCE          3
             340 CALL                     3
             348 CACHE
             350 STORE_NAME              34 (_ABC)

 546         352 LOAD_CONST              36 (<code object _new at 0x000001E77ECC01F0, file "ast.py", line 546>)
             354 MAKE_FUNCTION            0
             356 STORE_NAME              35 (_new)

 558         358 PUSH_NULL
             360 LOAD_BUILD_CLASS
             362 LOAD_CONST              37 (<code object Num at 0x000001E77EC5F2F0, file "ast.py", line 558>)
             364 MAKE_FUNCTION            0
             366 LOAD_CONST              38 ('Num')
             368 LOAD_NAME               27 (Constant)
             370 LOAD_NAME               34 (_ABC)
             372 KW_NAMES                39 (('metaclass',))
             374 UNPACK_SEQUENCE          4
             378 CALL                     4
             386 CACHE
             388 STORE_NAME              36 (Num)

 562         390 PUSH_NULL
             392 LOAD_BUILD_CLASS
             394 LOAD_CONST              40 (<code object Str at 0x000001E77EC5F3D0, file "ast.py", line 562>)
             396 MAKE_FUNCTION            0
             398 LOAD_CONST              41 ('Str')
             400 LOAD_NAME               27 (Constant)
             402 LOAD_NAME               34 (_ABC)
             404 KW_NAMES                39 (('metaclass',))
             406 UNPACK_SEQUENCE          4
             410 CALL                     4
             418 CACHE
             420 STORE_NAME              37 (Str)

 566         422 PUSH_NULL
             424 LOAD_BUILD_CLASS
             426 LOAD_CONST              42 (<code object Bytes at 0x000001E77EC5F4B0, file "ast.py", line 566>)
             428 MAKE_FUNCTION            0
             430 LOAD_CONST              43 ('Bytes')
             432 LOAD_NAME               27 (Constant)
             434 LOAD_NAME               34 (_ABC)
             436 KW_NAMES                39 (('metaclass',))
             438 UNPACK_SEQUENCE          4
             442 CALL                     4
             450 CACHE
             452 STORE_NAME              38 (Bytes)

 570         454 PUSH_NULL
             456 LOAD_BUILD_CLASS
             458 LOAD_CONST              44 (<code object NameConstant at 0x000001E77EC5F590, file "ast.py", line 570>)
             460 MAKE_FUNCTION            0
             462 LOAD_CONST              45 ('NameConstant')
             464 LOAD_NAME               27 (Constant)
             466 LOAD_NAME               34 (_ABC)
             468 KW_NAMES                39 (('metaclass',))
             470 UNPACK_SEQUENCE          4
             474 CALL                     4
             482 CACHE
             484 STORE_NAME              39 (NameConstant)

 573         486 PUSH_NULL
             488 LOAD_BUILD_CLASS
             490 LOAD_CONST              46 (<code object Ellipsis at 0x000001E77EC5F670, file "ast.py", line 573>)
             492 MAKE_FUNCTION            0
             494 LOAD_CONST              47 ('Ellipsis')
             496 LOAD_NAME               27 (Constant)
             498 LOAD_NAME               34 (_ABC)
             500 KW_NAMES                39 (('metaclass',))
             502 UNPACK_SEQUENCE          4
             506 CALL                     4
             514 CACHE
             516 STORE_NAME              40 (Ellipsis)

 582         518 LOAD_NAME               36 (Num)
             520 LOAD_NAME               41 (int)
             522 LOAD_NAME               42 (float)
             524 LOAD_NAME               43 (complex)
             526 BUILD_TUPLE              3

 583         528 LOAD_NAME               37 (Str)
             530 LOAD_NAME               44 (str)
             532 BUILD_TUPLE              1

 584         534 LOAD_NAME               38 (Bytes)
             536 LOAD_NAME               45 (bytes)
             538 BUILD_TUPLE              1

 585         540 LOAD_NAME               39 (NameConstant)
             542 PUSH_NULL
             544 LOAD_NAME               33 (type)
             546 LOAD_CONST               2 (None)
             548 UNPACK_SEQUENCE          1
             552 CALL                     1
             560 CACHE
             562 LOAD_NAME               46 (bool)
             564 BUILD_TUPLE              2

 586         566 LOAD_NAME               40 (Ellipsis)
             568 PUSH_NULL
             570 LOAD_NAME               33 (type)
             572 LOAD_CONST              48 (Ellipsis)
             574 UNPACK_SEQUENCE          1
             578 CALL                     1
             586 CACHE
             588 BUILD_TUPLE              1

 581         590 BUILD_MAP                5
             592 STORE_NAME              47 (_const_types)

 589         594 LOAD_NAME               36 (Num)
             596 LOAD_NAME               46 (bool)
             598 BUILD_TUPLE              1

 588         600 BUILD_MAP                1
             602 STORE_NAME              48 (_const_types_not)

 593         604 LOAD_NAME               46 (bool)
             606 LOAD_CONST              45 ('NameConstant')

 594         608 PUSH_NULL
             610 LOAD_NAME               33 (type)
             612 LOAD_CONST               2 (None)
             614 UNPACK_SEQUENCE          1
             618 CALL                     1
             626 CACHE
             628 LOAD_CONST              45 ('NameConstant')

 595         630 LOAD_NAME               41 (int)
             632 LOAD_CONST              38 ('Num')

 596         634 LOAD_NAME               42 (float)
             636 LOAD_CONST              38 ('Num')

 597         638 LOAD_NAME               43 (complex)
             640 LOAD_CONST              38 ('Num')

 598         642 LOAD_NAME               44 (str)
             644 LOAD_CONST              41 ('Str')

 599         646 LOAD_NAME               45 (bytes)
             648 LOAD_CONST              43 ('Bytes')

 600         650 PUSH_NULL
             652 LOAD_NAME               33 (type)
             654 LOAD_CONST              48 (Ellipsis)
             656 UNPACK_SEQUENCE          1
             660 CALL                     1
             668 CACHE
             670 LOAD_CONST              47 ('Ellipsis')

 592         672 BUILD_MAP                8
             674 STORE_NAME              49 (_const_node_type_names)

 603         676 PUSH_NULL
             678 LOAD_BUILD_CLASS
             680 LOAD_CONST              49 (<code object slice at 0x000001E77EC5F750, file "ast.py", line 603>)
             682 MAKE_FUNCTION            0
             684 LOAD_CONST              50 ('slice')
             686 LOAD_NAME               50 (AST)
             688 UNPACK_SEQUENCE          3
             692 CALL                     3
             700 CACHE
             702 STORE_NAME              51 (slice)

 606         704 PUSH_NULL
             706 LOAD_BUILD_CLASS
             708 LOAD_CONST              51 (<code object Index at 0x000001E77EC5F830, file "ast.py", line 606>)
             710 MAKE_FUNCTION            0
             712 LOAD_CONST              52 ('Index')
             714 LOAD_NAME               51 (slice)
             716 UNPACK_SEQUENCE          3
             720 CALL                     3
             728 CACHE
             730 STORE_NAME              52 (Index)

 611         732 PUSH_NULL
             734 LOAD_BUILD_CLASS
             736 LOAD_CONST              53 (<code object ExtSlice at 0x000001E77EC5F910, file "ast.py", line 611>)
             738 MAKE_FUNCTION            0
             740 LOAD_CONST              54 ('ExtSlice')
             742 LOAD_NAME               51 (slice)
             744 UNPACK_SEQUENCE          3
             748 CALL                     3
             756 CACHE
             758 STORE_NAME              53 (ExtSlice)

 617         760 PUSH_NULL
             762 LOAD_NAME               26 (hasattr)
             764 LOAD_NAME               54 (Tuple)
             766 LOAD_CONST              55 ('dims')
             768 UNPACK_SEQUENCE          2
             772 CALL                     2
             780 CACHE
             782 POP_JUMP_IF_TRUE        23 (to 830)

 621         784 LOAD_CONST              56 (<code object _dims_getter at 0x000001E77EC231C0, file "ast.py", line 621>)
             786 MAKE_FUNCTION            0
             788 STORE_NAME              55 (_dims_getter)

 625         790 LOAD_CONST              57 (<code object _dims_setter at 0x000001E77EC5F9F0, file "ast.py", line 625>)
             792 MAKE_FUNCTION            0
             794 STORE_NAME              56 (_dims_setter)

 628         796 PUSH_NULL
             798 LOAD_NAME               30 (property)
             800 LOAD_NAME               55 (_dims_getter)
             802 LOAD_NAME               56 (_dims_setter)
             804 UNPACK_SEQUENCE          2
             808 CALL                     2
             816 CACHE
             818 LOAD_NAME               54 (Tuple)
             820 STORE_ATTR              57 (dims)

 630     >>  830 PUSH_NULL
             832 LOAD_BUILD_CLASS
             834 LOAD_CONST              58 (<code object Suite at 0x000001E77EC5FAD0, file "ast.py", line 630>)
             836 MAKE_FUNCTION            0
             838 LOAD_CONST              59 ('Suite')
             840 LOAD_NAME               58 (mod)
             842 UNPACK_SEQUENCE          3
             846 CALL                     3
             854 CACHE
             856 STORE_NAME              59 (Suite)

 633         858 PUSH_NULL
             860 LOAD_BUILD_CLASS
             862 LOAD_CONST              60 (<code object AugLoad at 0x000001E77EC5FBB0, file "ast.py", line 633>)
             864 MAKE_FUNCTION            0
             866 LOAD_CONST              61 ('AugLoad')
             868 LOAD_NAME               60 (expr_context)
             870 UNPACK_SEQUENCE          3
             874 CALL                     3
             882 CACHE
             884 STORE_NAME              61 (AugLoad)

 636         886 PUSH_NULL
             888 LOAD_BUILD_CLASS
             890 LOAD_CONST              62 (<code object AugStore at 0x000001E77EC5FC90, file "ast.py", line 636>)
             892 MAKE_FUNCTION            0
             894 LOAD_CONST              63 ('AugStore')
             896 LOAD_NAME               60 (expr_context)
             898 UNPACK_SEQUENCE          3
             902 CALL                     3
             910 CACHE
             912 STORE_NAME              62 (AugStore)

 639         914 PUSH_NULL
             916 LOAD_BUILD_CLASS
             918 LOAD_CONST              64 (<code object Param at 0x000001E77EC5FD70, file "ast.py", line 639>)
             920 MAKE_FUNCTION            0
             922 LOAD_CONST              65 ('Param')
             924 LOAD_NAME               60 (expr_context)
             926 UNPACK_SEQUENCE          3
             930 CALL                     3
             938 CACHE
             940 STORE_NAME              63 (Param)

 645         942 LOAD_CONST              66 ('1e')
             944 PUSH_NULL
             946 LOAD_NAME               64 (repr)
             948 LOAD_NAME                1 (sys)
             950 LOAD_ATTR               65 (NULL|self + s)
             970 LOAD_CONST              17 (1)
             972 BINARY_OP                0 (+)
             976 UNPACK_SEQUENCE          1
             980 CALL                     1
             988 CACHE
             990 BINARY_OP                0 (+)
             994 STORE_NAME              67 (_INFSTR)

 647         996 PUSH_NULL
             998 LOAD_NAME                9 (_simple_enum)
            1000 LOAD_NAME                7 (IntEnum)
            1002 UNPACK_SEQUENCE          1
            1006 CALL                     1
            1014 CACHE

 648        1016 PUSH_NULL
            1018 LOAD_BUILD_CLASS
            1020 LOAD_CONST              67 (<code object _Precedence at 0x000001E77E9EB760, file "ast.py", line 647>)
            1022 MAKE_FUNCTION            0
            1024 LOAD_CONST              68 ('_Precedence')
            1026 UNPACK_SEQUENCE          2
            1030 CALL                     2
            1038 CACHE

 647        1040 UNPACK_SEQUENCE          0
            1044 CALL                     0
            1052 CACHE

 648        1054 STORE_NAME              68 (_Precedence)

 679        1056 LOAD_CONST              69 (("'", '"'))
            1058 STORE_NAME              69 (_SINGLE_QUOTES)

 680        1060 LOAD_CONST              70 (('"""', "'''"))
            1062 STORE_NAME              70 (_MULTI_QUOTES)

 681        1064 BUILD_LIST               0
            1066 LOAD_NAME               69 (_SINGLE_QUOTES)
            1068 LIST_EXTEND              1
            1070 LOAD_NAME               70 (_MULTI_QUOTES)
            1072 LIST_EXTEND              1
            1074 LOAD_ATTR                0 (__doc__)
            1094 CALL                     3
            1102 CACHE
            1104 STORE_NAME              72 (_Unparser)

1722        1106 LOAD_CONST              73 (<code object unparse at 0x000001E77ECF0140, file "ast.py", line 1722>)
            1108 MAKE_FUNCTION            0
            1110 STORE_NAME              73 (unparse)

1727        1112 LOAD_CONST              74 (<code object main at 0x000001E77E968A60, file "ast.py", line 1727>)
            1114 MAKE_FUNCTION            0
            1116 STORE_NAME              74 (main)

1751        1118 LOAD_NAME               75 (__name__)
            1120 LOAD_CONST              75 ('__main__')
            1122 COMPARE_OP               2 (<)
            1126 CACHE
            1128 POP_JUMP_IF_FALSE       12 (to 1154)

1752        1130 PUSH_NULL
            1132 LOAD_NAME               74 (main)
            1134 UNPACK_SEQUENCE          0
            1138 CALL                     0
            1146 CACHE
            1148 POP_TOP
            1150 LOAD_CONST               2 (None)
            1152 RETURN_VALUE

1751     >> 1154 LOAD_CONST               2 (None)
            1156 RETURN_VALUE

Disassembly of <code object parse at 0x000001E77ECB4310, file "ast.py", line 33>:
 33           0 RESUME                   0

 40           2 LOAD_GLOBAL              0 (PyCF_ONLY_AST)
             12 CACHE
             14 STORE_FAST               5 (flags)

 41          16 LOAD_FAST                3 (type_comments)
             18 POP_JUMP_IF_FALSE       10 (to 40)

 42          20 LOAD_FAST                5 (flags)
             22 LOAD_GLOBAL              2 (PyCF_TYPE_COMMENTS)
             32 CACHE
             34 BINARY_OP               20 (|=)
             38 STORE_FAST               5 (flags)

 43     >>   40 LOAD_GLOBAL              5 (NULL + isinstance)
             50 CACHE
             52 LOAD_FAST                4 (feature_version)
             54 LOAD_GLOBAL              6 (tuple)
             64 CACHE
             66 UNPACK_SEQUENCE          2
             70 CALL                     2
             78 CACHE
             80 POP_JUMP_IF_FALSE       16 (to 114)

 44          82 LOAD_FAST                4 (feature_version)
             84 UNPACK_SEQUENCE          2
             88 STORE_FAST               6 (major)
             90 STORE_FAST               7 (minor)

 45          92 LOAD_FAST                6 (major)
             94 LOAD_CONST               1 (3)
             96 COMPARE_OP               2 (<)
            100 CACHE
            102 POP_JUMP_IF_TRUE         2 (to 108)
            104 LOAD_ASSERTION_ERROR
            106 RAISE_VARARGS            1

 46     >>  108 LOAD_FAST                7 (minor)
            110 STORE_FAST               4 (feature_version)
            112 JUMP_FORWARD             4 (to 122)

 47     >>  114 LOAD_FAST                4 (feature_version)
            116 POP_JUMP_IF_NOT_NONE     2 (to 122)

 48         118 LOAD_CONST               3 (-1)
            120 STORE_FAST               4 (feature_version)

 50     >>  122 LOAD_GLOBAL              9 (NULL + compile)
            132 CACHE
            134 LOAD_FAST                0 (source)
            136 LOAD_FAST                1 (filename)
            138 LOAD_FAST                2 (mode)
            140 LOAD_FAST                5 (flags)

 51         142 LOAD_FAST                4 (feature_version)

 50         144 KW_NAMES                 4 (('_feature_version',))
            146 UNPACK_SEQUENCE          5
            150 CALL                     5
            158 CACHE
            160 RETURN_VALUE

Disassembly of <code object literal_eval at 0x000001E77E73F4B0, file "ast.py", line 54>:
              0 MAKE_CELL                1 (_convert)
              2 MAKE_CELL                2 (_convert_num)
              4 MAKE_CELL                3 (_convert_signed_num)
              6 MAKE_CELL                4 (_raise_malformed_node)

 54           8 RESUME                   0

 63          10 LOAD_GLOBAL              1 (NULL + isinstance)
             20 CACHE
             22 LOAD_FAST                0 (node_or_string)
             24 LOAD_GLOBAL              2 (str)
             34 CACHE
             36 UNPACK_SEQUENCE          2
             40 CALL                     2
             48 CACHE
             50 POP_JUMP_IF_FALSE       36 (to 124)

 64          52 LOAD_GLOBAL              5 (NULL + parse)
             62 CACHE
             64 LOAD_FAST                0 (node_or_string)
             66 STORE_SUBSCR
             70 CACHE
             72 CACHE
             74 CACHE
             76 CACHE
             78 CACHE
             80 CACHE
             82 CACHE
             84 CACHE
             86 CACHE
             88 LOAD_CONST               1 (' \t')
             90 UNPACK_SEQUENCE          1
             94 CALL                     1
            102 CACHE
            104 LOAD_CONST               2 ('eval')
            106 KW_NAMES                 3 (('mode',))
            108 UNPACK_SEQUENCE          2
            112 CALL                     2
            120 CACHE
            122 STORE_FAST               0 (node_or_string)

 65     >>  124 LOAD_GLOBAL              1 (NULL + isinstance)
            134 CACHE
            136 LOAD_FAST                0 (node_or_string)
            138 LOAD_GLOBAL              8 (Expression)
            148 CACHE
            150 UNPACK_SEQUENCE          2
            154 CALL                     2
            162 CACHE
            164 POP_JUMP_IF_FALSE        7 (to 180)

 66         166 LOAD_FAST                0 (node_or_string)
            168 LOAD_ATTR                5 (NULL|self + parse)
            188 BUILD_TUPLE              1
            190 LOAD_CONST               5 (<code object _convert_num at 0x000001E77ECB4480, file "ast.py", line 72>)
            192 MAKE_FUNCTION            8 (closure)
            194 STORE_DEREF              2 (_convert_num)

 76         196 LOAD_CLOSURE             2 (_convert_num)
            198 BUILD_TUPLE              1
            200 LOAD_CONST               6 (<code object _convert_signed_num at 0x000001E77E77F2D0, file "ast.py", line 76>)
            202 MAKE_FUNCTION            8 (closure)
            204 STORE_DEREF              3 (_convert_signed_num)

 84         206 LOAD_CLOSURE             1 (_convert)
            208 LOAD_CLOSURE             2 (_convert_num)
            210 LOAD_CLOSURE             3 (_convert_signed_num)
            212 LOAD_CLOSURE             4 (_raise_malformed_node)
            214 BUILD_TUPLE              4
            216 LOAD_CONST               7 (<code object _convert at 0x000001E77E8AB150, file "ast.py", line 84>)
            218 MAKE_FUNCTION            8 (closure)
            220 STORE_DEREF              1 (_convert)

110         222 PUSH_NULL
            224 LOAD_DEREF               1 (_convert)
            226 LOAD_FAST                0 (node_or_string)
            228 UNPACK_SEQUENCE          1
            232 CALL                     1
            240 CACHE
            242 RETURN_VALUE

Disassembly of <code object _raise_malformed_node at 0x000001E77EC2EE80, file "ast.py", line 67>:
 67           0 RESUME                   0

 68           2 LOAD_CONST               1 ('malformed node or string')
              4 STORE_FAST               1 (msg)

 69           6 LOAD_GLOBAL              1 (NULL + getattr)
             16 CACHE
             18 LOAD_FAST                0 (node)
             20 LOAD_CONST               2 ('lineno')
             22 LOAD_CONST               0 (None)
             24 UNPACK_SEQUENCE          3
             28 CALL                     3
             36 CACHE
             38 COPY                     1
             40 STORE_FAST               2 (lno)
             42 POP_JUMP_IF_FALSE        8 (to 60)

 70          44 LOAD_FAST                1 (msg)
             46 LOAD_CONST               3 (' on line ')
             48 LOAD_FAST                2 (lno)
             50 FORMAT_VALUE             0
             52 BUILD_STRING             2
             54 BINARY_OP               13 (+=)
             58 STORE_FAST               1 (msg)

 71     >>   60 LOAD_GLOBAL              3 (NULL + ValueError)
             70 CACHE
             72 LOAD_FAST                1 (msg)
             74 LOAD_CONST               4 (': ')
             76 LOAD_FAST                0 (node)
             78 FORMAT_VALUE             2 (repr)
             80 BUILD_STRING             2
             82 BINARY_OP                0 (+)
             86 UNPACK_SEQUENCE          1
             90 CALL                     1
             98 CACHE
            100 RAISE_VARARGS            1

Disassembly of <code object _convert_num at 0x000001E77ECB4480, file "ast.py", line 72>:
              0 COPY_FREE_VARS           1

 72           2 RESUME                   0

 73           4 LOAD_GLOBAL              1 (NULL + isinstance)
             14 CACHE
             16 LOAD_FAST                0 (node)
             18 LOAD_GLOBAL              2 (Constant)
             28 CACHE
             30 UNPACK_SEQUENCE          2
             34 CALL                     2
             42 CACHE
             44 POP_JUMP_IF_FALSE       40 (to 126)
             46 LOAD_GLOBAL              5 (NULL + type)
             56 CACHE
             58 LOAD_FAST                0 (node)
             60 LOAD_ATTR                3 (NULL|self + Constant)
             80 CACHE
             82 CACHE
             84 LOAD_GLOBAL              8 (int)
             94 CACHE
             96 LOAD_GLOBAL             10 (float)
            106 CACHE
            108 LOAD_GLOBAL             12 (complex)
            118 CACHE
            120 BUILD_TUPLE              3
            122 CONTAINS_OP              1
            124 POP_JUMP_IF_FALSE       11 (to 148)

 74     >>  126 PUSH_NULL
            128 LOAD_DEREF               1 (_raise_malformed_node)
            130 LOAD_FAST                0 (node)
            132 UNPACK_SEQUENCE          1
            136 CALL                     1
            144 CACHE
            146 POP_TOP

 75     >>  148 LOAD_FAST                0 (node)
            150 LOAD_ATTR                3 (NULL|self + Constant)

Disassembly of <code object _convert_signed_num at 0x000001E77E77F2D0, file "ast.py", line 76>:
              0 COPY_FREE_VARS           1

 76           2 RESUME                   0

 77           4 LOAD_GLOBAL              1 (NULL + isinstance)
             14 CACHE
             16 LOAD_FAST                0 (node)
             18 LOAD_GLOBAL              2 (UnaryOp)
             28 CACHE
             30 UNPACK_SEQUENCE          2
             34 CALL                     2
             42 CACHE
             44 POP_JUMP_IF_FALSE       81 (to 208)
             46 LOAD_GLOBAL              1 (NULL + isinstance)
             56 CACHE
             58 LOAD_FAST                0 (node)
             60 LOAD_ATTR                2 (UnaryOp)
             80 CACHE
             82 LOAD_GLOBAL              8 (USub)
             92 CACHE
             94 BUILD_TUPLE              2
             96 UNPACK_SEQUENCE          2
            100 CALL                     2
            108 CACHE
            110 POP_JUMP_IF_FALSE       48 (to 208)

 78         112 PUSH_NULL
            114 LOAD_DEREF               2 (_convert_num)
            116 LOAD_FAST                0 (node)
            118 LOAD_ATTR                5 (NULL|self + op)
            138 CACHE
            140 CACHE
            142 STORE_FAST               1 (operand)

 79         144 LOAD_GLOBAL              1 (NULL + isinstance)
            154 CACHE
            156 LOAD_FAST                0 (node)
            158 LOAD_ATTR                2 (UnaryOp)
            178 CACHE
            180 UNPACK_SEQUENCE          2
            184 CALL                     2
            192 CACHE
            194 POP_JUMP_IF_FALSE        3 (to 202)

 80         196 LOAD_FAST                1 (operand)
            198 BINARY_OP                0 (+)

 82     >>  202 LOAD_FAST                1 (operand)
            204 UNARY_NEGATIVE
            206 RETURN_VALUE

 83     >>  208 PUSH_NULL
            210 LOAD_DEREF               2 (_convert_num)
            212 LOAD_FAST                0 (node)
            214 UNPACK_SEQUENCE          1
            218 CALL                     1
            226 CACHE
            228 RETURN_VALUE

Disassembly of <code object _convert at 0x000001E77E8AB150, file "ast.py", line 84>:
              0 COPY_FREE_VARS           4

 84           2 RESUME                   0

 85           4 LOAD_GLOBAL              1 (NULL + isinstance)
             14 CACHE
             16 LOAD_FAST                0 (node)
             18 LOAD_GLOBAL              2 (Constant)
             28 CACHE
             30 UNPACK_SEQUENCE          2
             34 CALL                     2
             42 CACHE
             44 POP_JUMP_IF_FALSE        7 (to 60)

 86          46 LOAD_FAST                0 (node)
             48 LOAD_ATTR                2 (Constant)
             68 CACHE
             70 CACHE
             72 LOAD_FAST                0 (node)
             74 LOAD_GLOBAL              6 (Tuple)
             84 CACHE
             86 UNPACK_SEQUENCE          2
             90 CALL                     2
             98 CACHE
            100 POP_JUMP_IF_FALSE       34 (to 170)

 88         102 LOAD_GLOBAL              9 (NULL + tuple)
            112 CACHE
            114 LOAD_GLOBAL             11 (NULL + map)
            124 CACHE
            126 LOAD_DEREF               3 (_convert)
            128 LOAD_FAST                0 (node)
            130 LOAD_ATTR                6 (Tuple)
            150 CACHE
            152 CACHE
            154 UNPACK_SEQUENCE          1
            158 CALL                     1
            166 CACHE
            168 RETURN_VALUE

 89     >>  170 LOAD_GLOBAL              1 (NULL + isinstance)
            180 CACHE
            182 LOAD_FAST                0 (node)
            184 LOAD_GLOBAL             14 (List)
            194 CACHE
            196 UNPACK_SEQUENCE          2
            200 CALL                     2
            208 CACHE
            210 POP_JUMP_IF_FALSE       34 (to 280)

 90         212 LOAD_GLOBAL             17 (NULL + list)
            222 CACHE
            224 LOAD_GLOBAL             11 (NULL + map)
            234 CACHE
            236 LOAD_DEREF               3 (_convert)
            238 LOAD_FAST                0 (node)
            240 LOAD_ATTR                6 (Tuple)
            260 CACHE
            262 CACHE
            264 UNPACK_SEQUENCE          1
            268 CALL                     1
            276 CACHE
            278 RETURN_VALUE

 91     >>  280 LOAD_GLOBAL              1 (NULL + isinstance)
            290 CACHE
            292 LOAD_FAST                0 (node)
            294 LOAD_GLOBAL             18 (Set)
            304 CACHE
            306 UNPACK_SEQUENCE          2
            310 CALL                     2
            318 CACHE
            320 POP_JUMP_IF_FALSE       34 (to 390)

 92         322 LOAD_GLOBAL             21 (NULL + set)
            332 CACHE
            334 LOAD_GLOBAL             11 (NULL + map)
            344 CACHE
            346 LOAD_DEREF               3 (_convert)
            348 LOAD_FAST                0 (node)
            350 LOAD_ATTR                6 (Tuple)
            370 CACHE
            372 CACHE
            374 UNPACK_SEQUENCE          1
            378 CALL                     1
            386 CACHE
            388 RETURN_VALUE

 93     >>  390 LOAD_GLOBAL              1 (NULL + isinstance)
            400 CACHE
            402 LOAD_FAST                0 (node)
            404 LOAD_GLOBAL             22 (Call)
            414 CACHE
            416 UNPACK_SEQUENCE          2
            420 CALL                     2
            428 CACHE
            430 POP_JUMP_IF_FALSE       82 (to 596)
            432 LOAD_GLOBAL              1 (NULL + isinstance)
            442 CACHE
            444 LOAD_FAST                0 (node)
            446 LOAD_ATTR               12 (elts)
            466 CACHE
            468 UNPACK_SEQUENCE          2
            472 CALL                     2
            480 CACHE
            482 POP_JUMP_IF_FALSE       56 (to 596)

 94         484 LOAD_FAST                0 (node)
            486 LOAD_ATTR               12 (elts)
            506 LOAD_CONST               1 ('set')
            508 COMPARE_OP               2 (<)
            512 CACHE
            514 POP_JUMP_IF_FALSE       40 (to 596)
            516 LOAD_FAST                0 (node)
            518 LOAD_ATTR               15 (NULL|self + List)
            538 CACHE
            540 SWAP                     2
            542 COPY                     2
            544 COMPARE_OP               2 (<)
            548 CACHE
            550 POP_JUMP_IF_FALSE        6 (to 564)
            552 BUILD_LIST               0
            554 COMPARE_OP               2 (<)
            558 CACHE
            560 POP_JUMP_IF_FALSE       17 (to 596)
            562 JUMP_FORWARD             2 (to 568)
        >>  564 POP_TOP
            566 JUMP_FORWARD            14 (to 596)

 95     >>  568 LOAD_GLOBAL             21 (NULL + set)
            578 CACHE
            580 UNPACK_SEQUENCE          0
            584 CALL                     0
            592 CACHE
            594 RETURN_VALUE

 96     >>  596 LOAD_GLOBAL              1 (NULL + isinstance)
            606 CACHE
            608 LOAD_FAST                0 (node)
            610 LOAD_GLOBAL             34 (Dict)
            620 CACHE
            622 UNPACK_SEQUENCE          2
            626 CALL                     2
            634 CACHE
            636 POP_JUMP_IF_FALSE      120 (to 878)

 97         638 LOAD_GLOBAL             37 (NULL + len)
            648 CACHE
            650 LOAD_FAST                0 (node)
            652 LOAD_ATTR               19 (NULL|self + Set)
            672 CACHE
            674 CACHE
            676 LOAD_GLOBAL             37 (NULL + len)
            686 CACHE
            688 LOAD_FAST                0 (node)
            690 LOAD_ATTR               20 (set)
            710 CACHE
            712 CACHE
            714 COMPARE_OP               3 (<)
            718 CACHE
            720 POP_JUMP_IF_FALSE       11 (to 744)

 98         722 PUSH_NULL
            724 LOAD_DEREF               6 (_raise_malformed_node)
            726 LOAD_FAST                0 (node)
            728 UNPACK_SEQUENCE          1
            732 CALL                     1
            740 CACHE
            742 POP_TOP

 99     >>  744 LOAD_GLOBAL             43 (NULL + dict)
            754 CACHE
            756 LOAD_GLOBAL             45 (NULL + zip)
            766 CACHE
            768 LOAD_GLOBAL             11 (NULL + map)
            778 CACHE
            780 LOAD_DEREF               3 (_convert)
            782 LOAD_FAST                0 (node)
            784 LOAD_ATTR               19 (NULL|self + Set)
            804 CACHE
            806 CACHE

100         808 LOAD_GLOBAL             11 (NULL + map)
            818 CACHE
            820 LOAD_DEREF               3 (_convert)
            822 LOAD_FAST                0 (node)
            824 LOAD_ATTR               20 (set)
            844 CACHE
            846 CACHE

 99         848 UNPACK_SEQUENCE          2
            852 CALL                     2
            860 CACHE
            862 UNPACK_SEQUENCE          1
            866 CALL                     1
            874 CACHE
            876 RETURN_VALUE

101     >>  878 LOAD_GLOBAL              1 (NULL + isinstance)
            888 CACHE
            890 LOAD_FAST                0 (node)
            892 LOAD_GLOBAL             46 (BinOp)
            902 CACHE
            904 UNPACK_SEQUENCE          2
            908 CALL                     2
            916 CACHE
            918 POP_JUMP_IF_FALSE      150 (to 1220)
            920 LOAD_GLOBAL              1 (NULL + isinstance)
            930 CACHE
            932 LOAD_FAST                0 (node)
            934 LOAD_ATTR               24 (func)
            954 CACHE
            956 LOAD_GLOBAL             52 (Sub)
            966 CACHE
            968 BUILD_TUPLE              2
            970 UNPACK_SEQUENCE          2
            974 CALL                     2
            982 CACHE
            984 POP_JUMP_IF_FALSE      117 (to 1220)

102         986 PUSH_NULL
            988 LOAD_DEREF               5 (_convert_signed_num)
            990 LOAD_FAST                0 (node)
            992 LOAD_ATTR               27 (NULL|self + Name)
           1012 CACHE
           1014 CACHE
           1016 STORE_FAST               1 (left)

103        1018 PUSH_NULL
           1020 LOAD_DEREF               4 (_convert_num)
           1022 LOAD_FAST                0 (node)
           1024 LOAD_ATTR               28 (id)
           1044 CACHE
           1046 CACHE
           1048 STORE_FAST               2 (right)

104        1050 LOAD_GLOBAL              1 (NULL + isinstance)
           1060 CACHE
           1062 LOAD_FAST                1 (left)
           1064 LOAD_GLOBAL             58 (int)
           1074 CACHE
           1076 LOAD_GLOBAL             60 (float)
           1086 CACHE
           1088 BUILD_TUPLE              2
           1090 UNPACK_SEQUENCE          2
           1094 CALL                     2
           1102 CACHE
           1104 POP_JUMP_IF_FALSE       57 (to 1220)
           1106 LOAD_GLOBAL              1 (NULL + isinstance)
           1116 CACHE
           1118 LOAD_FAST                2 (right)
           1120 LOAD_GLOBAL             62 (complex)
           1130 CACHE
           1132 UNPACK_SEQUENCE          2
           1136 CALL                     2
           1144 CACHE
           1146 POP_JUMP_IF_FALSE       36 (to 1220)

105        1148 LOAD_GLOBAL              1 (NULL + isinstance)
           1158 CACHE
           1160 LOAD_FAST                0 (node)
           1162 LOAD_ATTR               24 (func)
           1182 CACHE
           1184 UNPACK_SEQUENCE          2
           1188 CALL                     2
           1196 CACHE
           1198 POP_JUMP_IF_FALSE        5 (to 1210)

106        1200 LOAD_FAST                1 (left)
           1202 LOAD_FAST                2 (right)
           1204 BINARY_OP                0 (+)
           1208 RETURN_VALUE

108     >> 1210 LOAD_FAST                1 (left)
           1212 LOAD_FAST                2 (right)
           1214 BINARY_OP               10 (-)
           1218 RETURN_VALUE

109     >> 1220 PUSH_NULL
           1222 LOAD_DEREF               5 (_convert_signed_num)
           1224 LOAD_FAST                0 (node)
           1226 UNPACK_SEQUENCE          1
           1230 CALL                     1
           1238 CACHE
           1240 RETURN_VALUE

Disassembly of <code object dump at 0x000001E77E6DF5D0, file "ast.py", line 113>:
              0 MAKE_CELL                1 (annotate_fields)
              2 MAKE_CELL                2 (include_attributes)
              4 MAKE_CELL                3 (indent)
              6 MAKE_CELL                4 (_format)

113           8 RESUME                   0

125          10 LOAD_CONST               6 ((0,))
             12 LOAD_CLOSURE             4 (_format)
             14 LOAD_CLOSURE             1 (annotate_fields)
             16 LOAD_CLOSURE             2 (include_attributes)
             18 LOAD_CLOSURE             3 (indent)
             20 BUILD_TUPLE              4
             22 LOAD_CONST               2 (<code object _format at 0x000001E77E9F4240, file "ast.py", line 125>)
             24 MAKE_FUNCTION            9 (defaults, closure)
             26 STORE_DEREF              4 (_format)

173          28 LOAD_GLOBAL              1 (NULL + isinstance)
             38 CACHE
             40 LOAD_FAST                0 (node)
             42 LOAD_GLOBAL              2 (AST)
             52 CACHE
             54 UNPACK_SEQUENCE          2
             58 CALL                     2
             66 CACHE
             68 POP_JUMP_IF_TRUE        28 (to 126)

174          70 LOAD_GLOBAL              5 (NULL + TypeError)
             80 CACHE
             82 LOAD_CONST               3 ('expected AST, got %r')
             84 LOAD_FAST                0 (node)
             86 LOAD_ATTR                3 (NULL|self + AST)
            106 BINARY_OP                6 (%)
            110 UNPACK_SEQUENCE          1
            114 CALL                     1
            122 CACHE
            124 RAISE_VARARGS            1

175     >>  126 LOAD_DEREF               3 (indent)
            128 POP_JUMP_IF_NONE        26 (to 182)
            130 LOAD_GLOBAL              1 (NULL + isinstance)
            140 CACHE
            142 LOAD_DEREF               3 (indent)
            144 LOAD_GLOBAL             10 (str)
            154 CACHE
            156 UNPACK_SEQUENCE          2
            160 CALL                     2
            168 CACHE
            170 POP_JUMP_IF_TRUE         5 (to 182)

176         172 LOAD_CONST               5 (' ')
            174 LOAD_DEREF               3 (indent)
            176 BINARY_OP                5 (*)
            180 STORE_DEREF              3 (indent)

177     >>  182 PUSH_NULL
            184 LOAD_DEREF               4 (_format)
            186 LOAD_FAST                0 (node)
            188 UNPACK_SEQUENCE          1
            192 CALL                     1
            200 CACHE
            202 LOAD_CONST               1 (0)
            204 BINARY_SUBSCR
            208 CACHE
            210 CACHE
            212 CACHE
            214 RETURN_VALUE

Disassembly of <code object _format at 0x000001E77E9F4240, file "ast.py", line 125>:
              0 COPY_FREE_VARS           4
              2 MAKE_CELL                1 (level)

125           4 RESUME                   0

126           6 LOAD_DEREF              14 (indent)
              8 POP_JUMP_IF_NONE        22 (to 54)

127          10 LOAD_DEREF               1 (level)
             12 LOAD_CONST               1 (1)
             14 BINARY_OP               13 (+=)
             18 STORE_DEREF              1 (level)

128          20 LOAD_CONST               2 ('\n')
             22 LOAD_DEREF              14 (indent)
             24 LOAD_DEREF               1 (level)
             26 BINARY_OP                5 (*)
             30 BINARY_OP                0 (+)
             34 STORE_FAST               2 (prefix)

129          36 LOAD_CONST               3 (',\n')
             38 LOAD_DEREF              14 (indent)
             40 LOAD_DEREF               1 (level)
             42 BINARY_OP                5 (*)
             46 BINARY_OP                0 (+)
             50 STORE_FAST               3 (sep)
             52 JUMP_FORWARD             4 (to 62)

131     >>   54 LOAD_CONST               4 ('')
             56 STORE_FAST               2 (prefix)

132          58 LOAD_CONST               5 (', ')
             60 STORE_FAST               3 (sep)

133     >>   62 LOAD_GLOBAL              1 (NULL + isinstance)
             72 CACHE
             74 LOAD_FAST                0 (node)
             76 LOAD_GLOBAL              2 (AST)
             86 CACHE
             88 UNPACK_SEQUENCE          2
             92 CALL                     2
            100 CACHE
            102 EXTENDED_ARG             1
            104 POP_JUMP_IF_FALSE      378 (to 862)

134         106 LOAD_GLOBAL              5 (NULL + type)
            116 CACHE
            118 LOAD_FAST                0 (node)
            120 UNPACK_SEQUENCE          1
            124 CALL                     1
            132 CACHE
            134 STORE_FAST               4 (cls)

135         136 BUILD_LIST               0
            138 STORE_FAST               5 (args)

136         140 LOAD_CONST               6 (True)
            142 STORE_FAST               6 (allsimple)

137         144 LOAD_DEREF              12 (annotate_fields)
            146 STORE_FAST               7 (keywords)

138         148 LOAD_FAST                0 (node)
            150 LOAD_ATTR                3 (NULL|self + AST)
            170 CACHE
            172 CACHE
            174 CACHE
            176 CACHE
            178 CACHE
            180 LOAD_FAST                0 (node)
            182 LOAD_FAST                8 (name)
            184 UNPACK_SEQUENCE          2
            188 CALL                     2
            196 CACHE
            198 STORE_FAST               9 (value)
            200 JUMP_FORWARD            18 (to 238)
        >>  202 PUSH_EXC_INFO

141         204 LOAD_GLOBAL             10 (AttributeError)
            214 CACHE
            216 CHECK_EXC_MATCH
            218 POP_JUMP_IF_FALSE        5 (to 230)
            220 POP_TOP

142         222 LOAD_CONST               6 (True)
            224 STORE_FAST               7 (keywords)

143         226 POP_EXCEPT
            228 JUMP_BACKWARD           34 (to 162)

141     >>  230 RERAISE                  0
        >>  232 COPY                     3
            234 POP_EXCEPT
            236 RERAISE                  1

144     >>  238 LOAD_FAST                9 (value)
            240 POP_JUMP_IF_NOT_NONE    20 (to 282)
            242 LOAD_GLOBAL              9 (NULL + getattr)
            252 CACHE
            254 LOAD_FAST                4 (cls)
            256 LOAD_FAST                8 (name)
            258 LOAD_CONST               7 (Ellipsis)
            260 UNPACK_SEQUENCE          3
            264 CALL                     3
            272 CACHE
            274 POP_JUMP_IF_NOT_NONE     3 (to 282)

145         276 LOAD_CONST               6 (True)
            278 STORE_FAST               7 (keywords)

146         280 JUMP_BACKWARD           60 (to 162)

147     >>  282 PUSH_NULL
            284 LOAD_DEREF              11 (_format)
            286 LOAD_FAST                9 (value)
            288 LOAD_DEREF               1 (level)
            290 UNPACK_SEQUENCE          2
            294 CALL                     2
            302 CACHE
            304 UNPACK_SEQUENCE          2
            308 STORE_FAST               9 (value)
            310 STORE_FAST              10 (simple)

148         312 LOAD_FAST                6 (allsimple)
            314 LOAD_GLOBAL              1 (NULL + isinstance)

150         324 LOAD_FAST                5 (args)
            326 STORE_SUBSCR
            330 CACHE
            332 CACHE
            334 CACHE
            336 CACHE
            338 CACHE
            340 CACHE
            342 CACHE
            344 CACHE
            346 CACHE
            348 LOAD_FAST                8 (name)
            350 FORMAT_VALUE             1 (str)
            352 LOAD_CONST               8 ('=')
            354 LOAD_FAST                9 (value)
            356 FORMAT_VALUE             1 (str)
            358 BUILD_STRING             3
            360 UNPACK_SEQUENCE          1
            364 CALL                     1
            372 CACHE
            374 POP_TOP
            376 JUMP_BACKWARD          108 (to 162)

152         378 LOAD_FAST                5 (args)
            380 STORE_SUBSCR
            384 CACHE
            386 CACHE
            388 CACHE
            390 CACHE
            392 CACHE
            394 CACHE
            396 CACHE
            398 CACHE
            400 CACHE
            402 LOAD_FAST                9 (value)
            404 UNPACK_SEQUENCE          1
            408 CALL                     1
            416 CACHE
            418 POP_TOP
            420 JUMP_BACKWARD          130 (to 162)

153         422 LOAD_DEREF              13 (include_attributes)
            424 POP_JUMP_IF_FALSE      116 (to 658)
            426 LOAD_FAST                0 (node)
            428 LOAD_ATTR                7 (NULL|self + _fields)
            448 CACHE
            450 CACHE
            452 GET_ITER
        >>  454 FOR_ITER               101 (to 660)

155         458 NOP

156         460 LOAD_GLOBAL              9 (NULL + getattr)
            470 CACHE
            472 LOAD_FAST                0 (node)
            474 LOAD_FAST                8 (name)
            476 UNPACK_SEQUENCE          2
            480 CALL                     2
            488 CACHE
            490 STORE_FAST               9 (value)
            492 JUMP_FORWARD            16 (to 526)
        >>  494 PUSH_EXC_INFO

157         496 LOAD_GLOBAL             10 (AttributeError)
            506 CACHE
            508 CHECK_EXC_MATCH
            510 POP_JUMP_IF_FALSE        3 (to 518)
            512 POP_TOP

158         514 POP_EXCEPT
            516 JUMP_BACKWARD           32 (to 454)

157     >>  518 RERAISE                  0
        >>  520 COPY                     3
            522 POP_EXCEPT
            524 RERAISE                  1

159     >>  526 LOAD_FAST                9 (value)
            528 POP_JUMP_IF_NOT_NONE    18 (to 566)
            530 LOAD_GLOBAL              9 (NULL + getattr)
            540 CACHE
            542 LOAD_FAST                4 (cls)
            544 LOAD_FAST                8 (name)
            546 LOAD_CONST               7 (Ellipsis)
            548 UNPACK_SEQUENCE          3
            552 CALL                     3
            560 CACHE
            562 POP_JUMP_IF_NOT_NONE     1 (to 566)

160         564 JUMP_BACKWARD           56 (to 454)

161     >>  566 PUSH_NULL
            568 LOAD_DEREF              11 (_format)
            570 LOAD_FAST                9 (value)
            572 LOAD_DEREF               1 (level)
            574 UNPACK_SEQUENCE          2
            578 CALL                     2
            586 CACHE
            588 UNPACK_SEQUENCE          2
            592 STORE_FAST               9 (value)
            594 STORE_FAST              10 (simple)

162         596 LOAD_FAST                6 (allsimple)
            598 LOAD_GLOBAL              1 (NULL + isinstance)
            608 CACHE
            610 CACHE
            612 CACHE
            614 CACHE
            616 CACHE
            618 CACHE
            620 CACHE
            622 CACHE
            624 CACHE
            626 CACHE
            628 LOAD_FAST                8 (name)
            630 FORMAT_VALUE             1 (str)
            632 LOAD_CONST               8 ('=')
            634 LOAD_FAST                9 (value)
            636 FORMAT_VALUE             1 (str)
            638 BUILD_STRING             3
            640 UNPACK_SEQUENCE          1
            644 CALL                     1
            652 CACHE
            654 POP_TOP
            656 JUMP_BACKWARD          102 (to 454)

164     >>  658 LOAD_FAST                6 (allsimple)
        >>  660 POP_JUMP_IF_FALSE       59 (to 780)
            662 LOAD_GLOBAL             17 (NULL + len)
            672 CACHE
            674 LOAD_FAST                5 (args)
            676 UNPACK_SEQUENCE          1
            680 CALL                     1
            688 CACHE
            690 LOAD_CONST               9 (3)
            692 COMPARE_OP               1 (<)
            696 CACHE
            698 POP_JUMP_IF_FALSE       40 (to 780)

165         700 LOAD_FAST                0 (node)
            702 LOAD_ATTR                9 (NULL|self + getattr)
            722 FORMAT_VALUE             1 (str)
            724 LOAD_CONST              10 ('(')
            726 LOAD_CONST               5 (', ')
            728 STORE_SUBSCR
            732 CACHE
            734 CACHE
            736 CACHE
            738 CACHE
            740 CACHE
            742 CACHE
            744 CACHE
            746 CACHE
            748 CACHE
            750 LOAD_FAST                5 (args)
            752 UNPACK_SEQUENCE          1
            756 CALL                     1
            764 CACHE
            766 FORMAT_VALUE             1 (str)
            768 LOAD_CONST              11 (')')
            770 BUILD_STRING             4
            772 LOAD_FAST                5 (args)
            774 UNARY_NOT
            776 BUILD_TUPLE              2
            778 RETURN_VALUE

166     >>  780 LOAD_FAST                0 (node)
            782 LOAD_ATTR                9 (NULL|self + getattr)
            802 FORMAT_VALUE             1 (str)
            804 LOAD_CONST              10 ('(')
            806 LOAD_FAST                2 (prefix)
            808 FORMAT_VALUE             1 (str)
            810 LOAD_FAST                3 (sep)
            812 STORE_SUBSCR
            816 CACHE
            818 CACHE
            820 CACHE
            822 CACHE
            824 CACHE
            826 CACHE
            828 CACHE
            830 CACHE
            832 CACHE
            834 LOAD_FAST                5 (args)
            836 UNPACK_SEQUENCE          1
            840 CALL                     1
            848 CACHE
            850 FORMAT_VALUE             1 (str)
            852 LOAD_CONST              11 (')')
            854 BUILD_STRING             5
            856 LOAD_CONST              12 (False)
            858 BUILD_TUPLE              2
            860 RETURN_VALUE

167     >>  862 LOAD_GLOBAL              1 (NULL + isinstance)
            872 CACHE
            874 LOAD_FAST                0 (node)
            876 LOAD_GLOBAL             24 (list)
            886 CACHE
            888 UNPACK_SEQUENCE          2
            892 CALL                     2
            900 CACHE
            902 POP_JUMP_IF_FALSE       46 (to 996)

168         904 LOAD_FAST                0 (node)
            906 POP_JUMP_IF_TRUE         2 (to 912)

169         908 LOAD_CONST              13 (('[]', True))
            910 RETURN_VALUE

168     >>  912 LOAD_CONST              14 ('[')

170         914 LOAD_FAST                2 (prefix)
            916 FORMAT_VALUE             1 (str)
            918 LOAD_FAST                3 (sep)
            920 STORE_SUBSCR
            924 CACHE
            926 CACHE
            928 CACHE
            930 CACHE
            932 CACHE
            934 CACHE
            936 CACHE
            938 CACHE
            940 CACHE
            942 LOAD_CLOSURE            11 (_format)
            944 LOAD_CLOSURE             1 (level)
            946 BUILD_TUPLE              2
            948 LOAD_CONST              15 (<code object <genexpr> at 0x000001E77EC7A230, file "ast.py", line 170>)
            950 MAKE_FUNCTION            8 (closure)
            952 LOAD_FAST                0 (node)
            954 GET_ITER
            956 UNPACK_SEQUENCE          0
            960 CALL                     0
            968 CACHE
            970 UNPACK_SEQUENCE          1
            974 CALL                     1
            982 CACHE
            984 FORMAT_VALUE             1 (str)
            986 LOAD_CONST              16 (']')
            988 BUILD_STRING             4
            990 LOAD_CONST              12 (False)
            992 BUILD_TUPLE              2
            994 RETURN_VALUE

171     >>  996 LOAD_GLOBAL             27 (NULL + repr)
           1006 CACHE
           1008 LOAD_FAST                0 (node)
           1010 UNPACK_SEQUENCE          1
           1014 CALL                     1
           1022 CACHE
           1024 LOAD_CONST               6 (True)
           1026 BUILD_TUPLE              2
           1028 RETURN_VALUE
ExceptionTable:
  168 to 198 -> 202 [1]
  202 to 224 -> 232 [2] lasti
  230 to 230 -> 232 [2] lasti
  460 to 490 -> 494 [1]
  494 to 512 -> 520 [2] lasti
  518 to 518 -> 520 [2] lasti

Disassembly of <code object <genexpr> at 0x000001E77EC7A230, file "ast.py", line 170>:
              0 COPY_FREE_VARS           2

170           2 RETURN_GENERATOR
              4 POP_TOP
              6 RESUME                   0
              8 LOAD_FAST                0 (.0)
        >>   10 FOR_ITER                22 (to 58)
             14 PUSH_NULL
             16 LOAD_DEREF               2 (_format)
             18 LOAD_FAST                1 (x)
             20 LOAD_DEREF               3 (level)
             22 UNPACK_SEQUENCE          2
             26 CALL                     2
             34 CACHE
             36 LOAD_CONST               0 (0)
             38 BINARY_SUBSCR
             42 CACHE
             44 CACHE
             46 CACHE
             48 LOAD_FAST                0 (.0)
             50 RESUME                   1
             52 POP_TOP
             54 JUMP_BACKWARD           23 (to 10)
             56 LOAD_CONST               1 (None)
        >>   58 RETURN_VALUE

Disassembly of <code object copy_location at 0x000001E77EC71930, file "ast.py", line 180>:
180           0 RESUME                   0

185           2 LOAD_CONST               1 (('lineno', 'col_offset', 'end_lineno', 'end_col_offset'))
              4 GET_ITER
        >>    6 FOR_ITER                93 (to 196)

186          10 LOAD_FAST                2 (attr)
             12 LOAD_FAST                1 (old_node)
             14 LOAD_ATTR                0 (_attributes)
             34 CACHE
             36 CACHE
             38 CACHE
             40 CACHE
             42 CONTAINS_OP              0
             44 POP_JUMP_IF_FALSE       73 (to 192)

187          46 LOAD_GLOBAL              3 (NULL + getattr)
             56 CACHE
             58 LOAD_FAST                1 (old_node)
             60 LOAD_FAST                2 (attr)
             62 LOAD_CONST               2 (None)
             64 UNPACK_SEQUENCE          3
             68 CALL                     3
             76 CACHE
             78 STORE_FAST               3 (value)

190          80 LOAD_FAST                3 (value)
             82 POP_JUMP_IF_NOT_NONE    37 (to 158)

191          84 LOAD_GLOBAL              5 (NULL + hasattr)
             94 CACHE
             96 LOAD_FAST                1 (old_node)
             98 LOAD_FAST                2 (attr)
            100 UNPACK_SEQUENCE          2
            104 CALL                     2
            112 CACHE

190         114 POP_JUMP_IF_FALSE       38 (to 192)

191         116 LOAD_FAST                2 (attr)
            118 STORE_SUBSCR
            122 CACHE
            124 CACHE
            126 CACHE
            128 CACHE
            130 CACHE
            132 CACHE
            134 CACHE
            136 CACHE
            138 CACHE
            140 LOAD_CONST               3 ('end_')
            142 UNPACK_SEQUENCE          1
            146 CALL                     1
            154 CACHE

190         156 POP_JUMP_IF_FALSE       17 (to 192)

193     >>  158 LOAD_GLOBAL              9 (NULL + setattr)
            168 CACHE
            170 LOAD_FAST                0 (new_node)
            172 LOAD_FAST                2 (attr)
            174 LOAD_FAST                3 (value)
            176 UNPACK_SEQUENCE          3
            180 CALL                     3
            188 CACHE
            190 POP_TOP
        >>  192 JUMP_BACKWARD           94 (to 6)

194         194 LOAD_FAST                0 (new_node)
        >>  196 RETURN_VALUE

Disassembly of <code object fix_missing_locations at 0x000001E77EC90E40, file "ast.py", line 197>:
              0 MAKE_CELL                1 (_fix)

197           2 RESUME                   0

205           4 LOAD_CLOSURE             1 (_fix)
              6 BUILD_TUPLE              1
              8 LOAD_CONST               1 (<code object _fix at 0x000001E77E9F0760, file "ast.py", line 205>)
             10 MAKE_FUNCTION            8 (closure)
             12 STORE_DEREF              1 (_fix)

228          14 PUSH_NULL
             16 LOAD_DEREF               1 (_fix)
             18 LOAD_FAST                0 (node)
             20 LOAD_CONST               2 (1)
             22 LOAD_CONST               3 (0)
             24 LOAD_CONST               2 (1)
             26 LOAD_CONST               3 (0)
             28 UNPACK_SEQUENCE          5
             32 CALL                     5
             40 CACHE
             42 POP_TOP

229          44 LOAD_FAST                0 (node)
             46 RETURN_VALUE

Disassembly of <code object _fix at 0x000001E77E9F0760, file "ast.py", line 205>:
              0 COPY_FREE_VARS           1

205           2 RESUME                   0

206           4 LOAD_CONST               1 ('lineno')
              6 LOAD_FAST                0 (node)
              8 LOAD_ATTR                0 (_attributes)
             28 CACHE
             30 CACHE
             32 CACHE
             34 LOAD_FAST                0 (node)
             36 LOAD_CONST               1 ('lineno')
             38 UNPACK_SEQUENCE          2
             42 CALL                     2
             50 CACHE
             52 POP_JUMP_IF_TRUE         8 (to 70)

208          54 LOAD_FAST                1 (lineno)
             56 LOAD_FAST                0 (node)
             58 STORE_ATTR               2 (lineno)
             68 JUMP_FORWARD             7 (to 84)

210     >>   70 LOAD_FAST                0 (node)
             72 LOAD_ATTR                2 (hasattr)
             92 CACHE
             94 CACHE
             96 CACHE
             98 CONTAINS_OP              0
            100 POP_JUMP_IF_FALSE       32 (to 166)

212         102 LOAD_GLOBAL              7 (NULL + getattr)
            112 CACHE
            114 LOAD_FAST                0 (node)
            116 LOAD_CONST               2 ('end_lineno')
            118 LOAD_CONST               0 (None)
            120 UNPACK_SEQUENCE          3
            124 CALL                     3
            132 CACHE
            134 POP_JUMP_IF_NOT_NONE     8 (to 152)

213         136 LOAD_FAST                3 (end_lineno)
            138 LOAD_FAST                0 (node)
            140 STORE_ATTR               4 (end_lineno)
            150 JUMP_FORWARD             7 (to 166)

215     >>  152 LOAD_FAST                0 (node)
            154 LOAD_ATTR                4 (lineno)
            174 CACHE
            176 CACHE
            178 CACHE
            180 CONTAINS_OP              0
            182 POP_JUMP_IF_FALSE       31 (to 246)

217         184 LOAD_GLOBAL              3 (NULL + hasattr)
            194 CACHE
            196 LOAD_FAST                0 (node)
            198 LOAD_CONST               3 ('col_offset')
            200 UNPACK_SEQUENCE          2
            204 CALL                     2
            212 CACHE
            214 POP_JUMP_IF_TRUE         8 (to 232)

218         216 LOAD_FAST                2 (col_offset)
            218 LOAD_FAST                0 (node)
            220 STORE_ATTR               5 (col_offset)
            230 JUMP_FORWARD             7 (to 246)

220     >>  232 LOAD_FAST                0 (node)
            234 LOAD_ATTR                5 (NULL|self + lineno)
            254 CACHE
            256 CACHE
            258 CACHE
            260 CONTAINS_OP              0
            262 POP_JUMP_IF_FALSE       32 (to 328)

222         264 LOAD_GLOBAL              7 (NULL + getattr)
            274 CACHE
            276 LOAD_FAST                0 (node)
            278 LOAD_CONST               4 ('end_col_offset')
            280 LOAD_CONST               0 (None)
            282 UNPACK_SEQUENCE          3
            286 CALL                     3
            294 CACHE
            296 POP_JUMP_IF_NOT_NONE     8 (to 314)

223         298 LOAD_FAST                4 (end_col_offset)
            300 LOAD_FAST                0 (node)
            302 STORE_ATTR               6 (end_col_offset)
            312 JUMP_FORWARD             7 (to 328)

225     >>  314 LOAD_FAST                0 (node)
            316 LOAD_ATTR                6 (getattr)
            336 CACHE
            338 CACHE
            340 LOAD_FAST                0 (node)
            342 UNPACK_SEQUENCE          1
            346 CALL                     1
            354 CACHE
            356 GET_ITER
        >>  358 FOR_ITER                17 (to 396)

227         362 PUSH_NULL
            364 LOAD_DEREF               6 (_fix)
            366 LOAD_FAST                5 (child)
            368 LOAD_FAST                1 (lineno)
            370 LOAD_FAST                2 (col_offset)
            372 LOAD_FAST                3 (end_lineno)
            374 LOAD_FAST                4 (end_col_offset)
            376 UNPACK_SEQUENCE          5
            380 CALL                     5
            388 CACHE
            390 POP_TOP
            392 JUMP_BACKWARD           18 (to 358)

226         394 LOAD_CONST               0 (None)
        >>  396 RETURN_VALUE

Disassembly of <code object increment_lineno at 0x000001E77E72AD30, file "ast.py", line 232>:
232           0 RESUME                   0

238           2 LOAD_GLOBAL              1 (NULL + walk)
             12 CACHE
             14 LOAD_FAST                0 (node)
             16 UNPACK_SEQUENCE          1
             20 CALL                     1
             28 CACHE
             30 GET_ITER
        >>   32 FOR_ITER               121 (to 278)

241          36 LOAD_GLOBAL              3 (NULL + isinstance)
             46 CACHE
             48 LOAD_FAST                2 (child)
             50 LOAD_GLOBAL              4 (TypeIgnore)
             60 CACHE
             62 UNPACK_SEQUENCE          2
             66 CALL                     2
             74 CACHE
             76 POP_JUMP_IF_FALSE       26 (to 130)

242          78 LOAD_GLOBAL              7 (NULL + getattr)
             88 CACHE
             90 LOAD_FAST                2 (child)
             92 LOAD_CONST               1 ('lineno')
             94 LOAD_CONST               2 (0)
             96 UNPACK_SEQUENCE          3
            100 CALL                     3
            108 CACHE
            110 LOAD_FAST                1 (n)
            112 BINARY_OP                0 (+)
            116 LOAD_FAST                2 (child)
            118 STORE_ATTR               4 (lineno)

243         128 JUMP_BACKWARD           49 (to 32)

245     >>  130 LOAD_CONST               1 ('lineno')
            132 LOAD_FAST                2 (child)
            134 LOAD_ATTR                5 (NULL|self + TypeIgnore)
            154 CACHE
            156 CACHE
            158 CACHE
            160 LOAD_FAST                2 (child)
            162 LOAD_CONST               1 ('lineno')
            164 LOAD_CONST               2 (0)
            166 UNPACK_SEQUENCE          3
            170 CALL                     3
            178 CACHE
            180 LOAD_FAST                1 (n)
            182 BINARY_OP                0 (+)
            186 LOAD_FAST                2 (child)
            188 STORE_ATTR               4 (lineno)

248         198 LOAD_CONST               3 ('end_lineno')
            200 LOAD_FAST                2 (child)
            202 LOAD_ATTR                5 (NULL|self + TypeIgnore)
            222 CACHE
            224 CACHE
            226 CACHE
            228 LOAD_FAST                2 (child)
            230 LOAD_CONST               3 ('end_lineno')
            232 LOAD_CONST               2 (0)
            234 UNPACK_SEQUENCE          3
            238 CALL                     3
            246 CACHE
            248 COPY                     1
            250 STORE_FAST               3 (end_lineno)
            252 POP_JUMP_IF_NONE        10 (to 274)

251         254 LOAD_FAST                3 (end_lineno)
            256 LOAD_FAST                1 (n)
            258 BINARY_OP                0 (+)
            262 LOAD_FAST                2 (child)
            264 STORE_ATTR               6 (end_lineno)
        >>  274 JUMP_BACKWARD          122 (to 32)

252         276 LOAD_FAST                0 (node)
        >>  278 RETURN_VALUE

Disassembly of <code object iter_fields at 0x000001E77ECBC030, file "ast.py", line 255>:
255           0 RETURN_GENERATOR
              2 POP_TOP
              4 RESUME                   0

260           6 LOAD_FAST                0 (node)
              8 LOAD_ATTR                0 (_fields)
             28 LOAD_GLOBAL              3 (NULL + getattr)
             38 CACHE
             40 LOAD_FAST                0 (node)
             42 LOAD_FAST                1 (field)
             44 UNPACK_SEQUENCE          2
             48 CALL                     2
             56 CACHE
             58 BUILD_TUPLE              2
             60 LOAD_FAST                0 (node)
             62 RESUME                   1
             64 POP_TOP
             66 JUMP_BACKWARD           24 (to 20)
        >>   68 PUSH_EXC_INFO

263          70 LOAD_GLOBAL              4 (AttributeError)
             80 CACHE
             82 CHECK_EXC_MATCH
             84 POP_JUMP_IF_FALSE        3 (to 92)
             86 POP_TOP

264          88 POP_EXCEPT
             90 JUMP_BACKWARD           36 (to 20)

263     >>   92 RERAISE                  0
        >>   94 COPY                     3
             96 POP_EXCEPT
             98 RERAISE                  1

260         100 LOAD_CONST               1 (None)
            102 RETURN_VALUE
ExceptionTable:
  26 to 64 -> 68 [1]
  68 to 86 -> 94 [2] lasti
  92 to 92 -> 94 [2] lasti

Disassembly of <code object iter_child_nodes at 0x000001E77EC71AC0, file "ast.py", line 267>:
267           0 RETURN_GENERATOR
              2 POP_TOP
              4 RESUME                   0

272           6 LOAD_GLOBAL              1 (NULL + iter_fields)
             16 CACHE
             18 LOAD_FAST                0 (node)
             20 UNPACK_SEQUENCE          1
             24 CALL                     1
             32 CACHE
             34 GET_ITER
        >>   36 FOR_ITER                82 (to 204)
             40 CACHE
             42 STORE_FAST               1 (name)
             44 STORE_FAST               2 (field)

273          46 LOAD_GLOBAL              3 (NULL + isinstance)
             56 CACHE
             58 LOAD_FAST                2 (field)
             60 LOAD_GLOBAL              4 (AST)
             70 CACHE
             72 UNPACK_SEQUENCE          2
             76 CALL                     2
             84 CACHE
             86 POP_JUMP_IF_FALSE        5 (to 98)

274          88 LOAD_FAST                2 (field)
             90 LOAD_FAST                0 (node)
             92 RESUME                   1
             94 POP_TOP
             96 JUMP_BACKWARD           31 (to 36)

275     >>   98 LOAD_GLOBAL              3 (NULL + isinstance)
            108 CACHE
            110 LOAD_FAST                2 (field)
            112 LOAD_GLOBAL              6 (list)
            122 CACHE
            124 UNPACK_SEQUENCE          2
            128 CALL                     2
            136 CACHE
            138 POP_JUMP_IF_FALSE       30 (to 200)

276         140 LOAD_FAST                2 (field)
            142 GET_ITER
        >>  144 FOR_ITER                27 (to 202)

277         148 LOAD_GLOBAL              3 (NULL + isinstance)
            158 CACHE
            160 LOAD_FAST                3 (item)
            162 LOAD_GLOBAL              4 (AST)
            172 CACHE
            174 UNPACK_SEQUENCE          2
            178 CALL                     2
            186 CACHE
            188 POP_JUMP_IF_FALSE        4 (to 198)

278         190 LOAD_FAST                3 (item)
            192 LOAD_FAST                0 (node)
            194 RESUME                   1
            196 POP_TOP
        >>  198 JUMP_BACKWARD           28 (to 144)
        >>  200 JUMP_BACKWARD           83 (to 36)

272     >>  202 LOAD_CONST               1 (None)
        >>  204 RETURN_VALUE

Disassembly of <code object get_docstring at 0x000001E77EF1BD00, file "ast.py", line 281>:
281           0 RESUME                   0

290           2 LOAD_GLOBAL              1 (NULL + isinstance)
             12 CACHE
             14 LOAD_FAST                0 (node)
             16 LOAD_GLOBAL              2 (AsyncFunctionDef)
             26 CACHE
             28 LOAD_GLOBAL              4 (FunctionDef)
             38 CACHE
             40 LOAD_GLOBAL              6 (ClassDef)
             50 CACHE
             52 LOAD_GLOBAL              8 (Module)
             62 CACHE
             64 BUILD_TUPLE              4
             66 UNPACK_SEQUENCE          2
             70 CALL                     2
             78 CACHE
             80 POP_JUMP_IF_TRUE        28 (to 138)

291          82 LOAD_GLOBAL             11 (NULL + TypeError)
             92 CACHE
             94 LOAD_CONST               1 ("%r can't have docstrings")
             96 LOAD_FAST                0 (node)
             98 LOAD_ATTR                6 (ClassDef)
            118 BINARY_OP                6 (%)
            122 UNPACK_SEQUENCE          1
            126 CALL                     1
            134 CACHE
            136 RAISE_VARARGS            1

292     >>  138 LOAD_FAST                0 (node)
            140 LOAD_ATTR                8 (Module)
            160 CACHE
            162 CACHE
            164 LOAD_FAST                0 (node)
            166 LOAD_ATTR                8 (Module)
            186 CACHE
            188 LOAD_GLOBAL             18 (Expr)
            198 CACHE
            200 UNPACK_SEQUENCE          2
            204 CALL                     2
            212 CACHE
            214 POP_JUMP_IF_TRUE         2 (to 220)

293         216 LOAD_CONST               3 (None)
            218 RETURN_VALUE

294     >>  220 LOAD_FAST                0 (node)
            222 LOAD_ATTR                8 (Module)
            242 CACHE
            244 LOAD_ATTR               10 (TypeError)
            264 CACHE
            266 CACHE
            268 LOAD_FAST                0 (node)
            270 LOAD_GLOBAL             22 (Str)
            280 CACHE
            282 UNPACK_SEQUENCE          2
            286 CALL                     2
            294 CACHE
            296 POP_JUMP_IF_FALSE        8 (to 314)

296         298 LOAD_FAST                0 (node)
            300 LOAD_ATTR               12 (__class__)
            320 CACHE
            322 CACHE
            324 CACHE
            326 LOAD_FAST                0 (node)
            328 LOAD_GLOBAL             26 (Constant)
            338 CACHE
            340 UNPACK_SEQUENCE          2
            344 CALL                     2
            352 CACHE
            354 POP_JUMP_IF_FALSE       34 (to 424)
            356 LOAD_GLOBAL              1 (NULL + isinstance)
            366 CACHE
            368 LOAD_FAST                0 (node)
            370 LOAD_ATTR               10 (TypeError)
            390 CACHE
            392 UNPACK_SEQUENCE          2
            396 CALL                     2
            404 CACHE
            406 POP_JUMP_IF_FALSE        8 (to 424)

298         408 LOAD_FAST                0 (node)
            410 LOAD_ATTR               10 (TypeError)
            430 POP_JUMP_IF_FALSE       25 (to 482)

302         432 LOAD_CONST               2 (0)
            434 LOAD_CONST               3 (None)
            436 IMPORT_NAME             15 (inspect)
            438 STORE_FAST               3 (inspect)

303         440 LOAD_FAST                3 (inspect)
            442 STORE_SUBSCR
            446 CACHE
            448 CACHE
            450 CACHE
            452 CACHE
            454 CACHE
            456 CACHE
            458 CACHE
            460 CACHE
            462 CACHE
            464 LOAD_FAST                2 (text)
            466 UNPACK_SEQUENCE          1
            470 CALL                     1
            478 CACHE
            480 STORE_FAST               2 (text)

304     >>  482 LOAD_FAST                2 (text)
            484 RETURN_VALUE

Disassembly of <code object _splitlines_no_ff at 0x000001E77C48BCA0, file "ast.py", line 307>:
307           0 RESUME                   0

312           2 LOAD_CONST               1 (0)
              4 STORE_FAST               1 (idx)

313           6 BUILD_LIST               0
              8 STORE_FAST               2 (lines)

314          10 LOAD_CONST               2 ('')
             12 STORE_FAST               3 (next_line)

315          14 LOAD_FAST                1 (idx)
             16 LOAD_GLOBAL              1 (NULL + len)
             26 CACHE
             28 LOAD_FAST                0 (source)
             30 UNPACK_SEQUENCE          1
             34 CALL                     1
             42 CACHE
             44 COMPARE_OP               0 (<)
             48 CACHE
             50 POP_JUMP_IF_FALSE      111 (to 274)

316          52 LOAD_FAST                0 (source)
             54 LOAD_FAST                1 (idx)
             56 BINARY_SUBSCR
             60 CACHE
             62 CACHE
             64 CACHE
             66 STORE_FAST               4 (c)

317          68 LOAD_FAST                3 (next_line)
             70 LOAD_FAST                4 (c)
             72 BINARY_OP               13 (+=)
             76 STORE_FAST               3 (next_line)

318          78 LOAD_FAST                1 (idx)
             80 LOAD_CONST               3 (1)
             82 BINARY_OP               13 (+=)
             86 STORE_FAST               1 (idx)

320          88 LOAD_FAST                4 (c)
             90 LOAD_CONST               4 ('\r')
             92 COMPARE_OP               2 (<)
             96 CACHE
             98 POP_JUMP_IF_FALSE       41 (to 182)
            100 LOAD_FAST                1 (idx)
            102 LOAD_GLOBAL              1 (NULL + len)
            112 CACHE
            114 LOAD_FAST                0 (source)
            116 UNPACK_SEQUENCE          1
            120 CALL                     1
            128 CACHE
            130 COMPARE_OP               0 (<)
            134 CACHE
            136 POP_JUMP_IF_FALSE       22 (to 182)
            138 LOAD_FAST                0 (source)
            140 LOAD_FAST                1 (idx)
            142 BINARY_SUBSCR
            146 CACHE
            148 CACHE
            150 CACHE
            152 LOAD_CONST               5 ('\n')
            154 COMPARE_OP               2 (<)
            158 CACHE
            160 POP_JUMP_IF_FALSE       10 (to 182)

321         162 LOAD_FAST                3 (next_line)
            164 LOAD_CONST               5 ('\n')
            166 BINARY_OP               13 (+=)
            170 STORE_FAST               3 (next_line)

322         172 LOAD_FAST                1 (idx)
            174 LOAD_CONST               3 (1)
            176 BINARY_OP               13 (+=)
            180 STORE_FAST               1 (idx)

323     >>  182 LOAD_FAST                4 (c)
            184 LOAD_CONST               6 ('\r\n')
            186 CONTAINS_OP              0
            188 POP_JUMP_IF_FALSE       23 (to 236)

324         190 LOAD_FAST                2 (lines)
            192 STORE_SUBSCR
            196 CACHE
            198 CACHE
            200 CACHE
            202 CACHE
            204 CACHE
            206 CACHE
            208 CACHE
            210 CACHE
            212 CACHE
            214 LOAD_FAST                3 (next_line)
            216 UNPACK_SEQUENCE          1
            220 CALL                     1
            228 CACHE
            230 POP_TOP

325         232 LOAD_CONST               2 ('')
            234 STORE_FAST               3 (next_line)

315     >>  236 LOAD_FAST                1 (idx)
            238 LOAD_GLOBAL              1 (NULL + len)
            248 CACHE
            250 LOAD_FAST                0 (source)
            252 UNPACK_SEQUENCE          1
            256 CALL                     1
            264 CACHE
            266 COMPARE_OP               0 (<)
            270 CACHE
