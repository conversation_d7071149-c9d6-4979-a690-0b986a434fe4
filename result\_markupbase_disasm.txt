# Code object from position 7325322
# Filename: _markupbase.py
# Name: <module>
# Args: 0
# Locals: 0

  0           0 RESUME                   0

  1           2 LOAD_CONST               0 ('Shared support for scanning document type declarations in HTML and XHTML.\n\nThis module is used as a foundation for the html.parser module.  It has no\ndocumented public API and should not be used directly.\n\n')
              4 STORE_NAME               0 (__doc__)

  8           6 LOAD_CONST               1 (0)
              8 LOAD_CONST               2 (None)
             10 IMPORT_NAME              1 (re)
             12 STORE_NAME               1 (re)

 10          14 PUSH_NULL
             16 LOAD_NAME                1 (re)
             18 LOAD_ATTR                2 (re)
             38 CACHE
             40 CACHE
             42 CACHE
             44 LOAD_ATTR                3 (NULL|self + re)
             64 CACHE
             66 CACHE
             68 CACHE
             70 LOAD_CONST               4 ('(\\\'[^\\\']*\\\'|"[^"]*")\\s*')
             72 UNPACK_SEQUENCE          1
             76 CALL                     1
             84 CACHE
             86 LOAD_ATTR                3 (NULL|self + re)
            106 CACHE
            108 CACHE
            110 CACHE
            112 LOAD_CONST               5 ('--\\s*>')
            114 UNPACK_SEQUENCE          1
            118 CALL                     1
            126 CACHE
            128 STORE_NAME               6 (_commentclose)

 13         130 PUSH_NULL
            132 LOAD_NAME                1 (re)
            134 LOAD_ATTR                2 (re)
            154 CACHE
            156 CACHE
            158 CACHE
            160 STORE_NAME               7 (_markedsectionclose)

 18         162 PUSH_NULL
            164 LOAD_NAME                1 (re)
            166 LOAD_ATTR                2 (re)
            186 CACHE
            188 CACHE
            190 CACHE
            192 STORE_NAME               8 (_msmarkedsectionclose)

 20         194 DELETE_NAME              1 (re)

 23         196 PUSH_NULL
            198 LOAD_BUILD_CLASS
            200 LOAD_CONST               8 (<code object ParserBase at 0x000001A2D019E9C0, file "_markupbase.py", line 23>)
            202 MAKE_FUNCTION            0
            204 LOAD_CONST               9 ('ParserBase')
            206 UNPACK_SEQUENCE          2
            210 CALL                     2
            218 CACHE
            220 STORE_NAME               9 (ParserBase)
            222 LOAD_CONST               2 (None)
            224 RETURN_VALUE

Disassembly of <code object ParserBase at 0x000001A2D019E9C0, file "_markupbase.py", line 23>:
 23           0 RESUME                   0
              2 LOAD_NAME                0 (__name__)
              4 STORE_NAME               1 (__module__)
              6 LOAD_CONST               0 ('ParserBase')
              8 STORE_NAME               2 (__qualname__)

 24          10 LOAD_CONST               1 ('Parser base class which provides some common support methods used\n    by the SGML/HTML and XHTML parsers.')
             12 STORE_NAME               3 (__doc__)

 27          14 LOAD_CONST               2 (<code object __init__ at 0x000001A2D059B130, file "_markupbase.py", line 27>)
             16 MAKE_FUNCTION            0
             18 STORE_NAME               4 (__init__)

 32          20 LOAD_CONST               3 (<code object reset at 0x000001A2D01155C0, file "_markupbase.py", line 32>)
             22 MAKE_FUNCTION            0
             24 STORE_NAME               5 (reset)

 36          26 LOAD_CONST               4 (<code object getpos at 0x000001A2D0566B10, file "_markupbase.py", line 36>)
             28 MAKE_FUNCTION            0
             30 STORE_NAME               6 (getpos)

 44          32 LOAD_CONST               5 (<code object updatepos at 0x000001A2D01ACC00, file "_markupbase.py", line 44>)
             34 MAKE_FUNCTION            0
             36 STORE_NAME               7 (updatepos)

 57          38 LOAD_CONST               6 ('')
             40 STORE_NAME               8 (_decl_otherchars)

 60          42 LOAD_CONST               7 (<code object parse_declaration at 0x000001A2D02C66E0, file "_markupbase.py", line 60>)
             44 MAKE_FUNCTION            0
             46 STORE_NAME               9 (parse_declaration)

141          48 LOAD_CONST              19 ((1,))
             50 LOAD_CONST               9 (<code object parse_marked_section at 0x000001A2D02C6BA0, file "_markupbase.py", line 141>)
             52 MAKE_FUNCTION            1 (defaults)
             54 STORE_NAME              10 (parse_marked_section)

165          56 LOAD_CONST              19 ((1,))
             58 LOAD_CONST              10 (<code object parse_comment at 0x000001A2D0190600, file "_markupbase.py", line 165>)
             60 MAKE_FUNCTION            1 (defaults)
             62 STORE_NAME              11 (parse_comment)

179          64 LOAD_CONST              11 (<code object _parse_doctype_subset at 0x000001A2CDE9D140, file "_markupbase.py", line 179>)
             66 MAKE_FUNCTION            0
             68 STORE_NAME              12 (_parse_doctype_subset)

249          70 LOAD_CONST              12 (<code object _parse_doctype_element at 0x000001A2D01110B0, file "_markupbase.py", line 249>)
             72 MAKE_FUNCTION            0
             74 STORE_NAME              13 (_parse_doctype_element)

260          76 LOAD_CONST              13 (<code object _parse_doctype_attlist at 0x000001A2CDE705A0, file "_markupbase.py", line 260>)
             78 MAKE_FUNCTION            0
             80 STORE_NAME              14 (_parse_doctype_attlist)

317          82 LOAD_CONST              14 (<code object _parse_doctype_notation at 0x000001A2D0190BD0, file "_markupbase.py", line 317>)
             84 MAKE_FUNCTION            0
             86 STORE_NAME              15 (_parse_doctype_notation)

340          88 LOAD_CONST              15 (<code object _parse_doctype_entity at 0x000001A2CDE24140, file "_markupbase.py", line 340>)
             90 MAKE_FUNCTION            0
             92 STORE_NAME              16 (_parse_doctype_entity)

376          94 LOAD_CONST              16 (<code object _scan_name at 0x000001A2D03B9040, file "_markupbase.py", line 376>)
             96 MAKE_FUNCTION            0
             98 STORE_NAME              17 (_scan_name)

395         100 LOAD_CONST              17 (<code object unknown_decl at 0x000001A2D05C23F0, file "_markupbase.py", line 395>)
            102 MAKE_FUNCTION            0
            104 STORE_NAME              18 (unknown_decl)
            106 LOAD_CONST              18 (None)
            108 RETURN_VALUE

Disassembly of <code object __init__ at 0x000001A2D059B130, file "_markupbase.py", line 27>:
 27           0 RESUME                   0

 28           2 LOAD_FAST                0 (self)
              4 LOAD_ATTR                0 (__class__)
             24 CACHE
             26 IS_OP                    0
             28 POP_JUMP_IF_FALSE       15 (to 60)

 29          30 LOAD_GLOBAL              5 (NULL + RuntimeError)
             40 CACHE

 30          42 LOAD_CONST               1 ('_markupbase.ParserBase must be subclassed')

 29          44 UNPACK_SEQUENCE          1
             48 CALL                     1
             56 CACHE
             58 RAISE_VARARGS            1

 28     >>   60 LOAD_CONST               0 (None)
             62 RETURN_VALUE

Disassembly of <code object reset at 0x000001A2D01155C0, file "_markupbase.py", line 32>:
 32           0 RESUME                   0

 33           2 LOAD_CONST               1 (1)
              4 LOAD_FAST                0 (self)
              6 STORE_ATTR               0 (lineno)

 34          16 LOAD_CONST               2 (0)
             18 LOAD_FAST                0 (self)
             20 STORE_ATTR               1 (offset)
             30 LOAD_CONST               0 (None)
             32 RETURN_VALUE

Disassembly of <code object getpos at 0x000001A2D0566B10, file "_markupbase.py", line 36>:
 36           0 RESUME                   0

 38           2 LOAD_FAST                0 (self)
              4 LOAD_ATTR                0 (lineno)
             24 CACHE
             26 BUILD_TUPLE              2
             28 RETURN_VALUE

Disassembly of <code object updatepos at 0x000001A2D01ACC00, file "_markupbase.py", line 44>:
 44           0 RESUME                   0

 45           2 LOAD_FAST                1 (i)
              4 LOAD_FAST                2 (j)
              6 COMPARE_OP               5 (<)
             10 CACHE
             12 POP_JUMP_IF_FALSE        2 (to 18)

 46          14 LOAD_FAST                2 (j)
             16 RETURN_VALUE

 47     >>   18 LOAD_FAST                0 (self)
             20 LOAD_ATTR                0 (rawdata)
             40 CACHE
             42 CACHE
             44 CACHE
             46 CACHE
             48 CACHE
             50 CACHE
             52 CACHE
             54 CACHE
             56 LOAD_CONST               1 ('\n')
             58 LOAD_FAST                1 (i)
             60 LOAD_FAST                2 (j)
             62 UNPACK_SEQUENCE          3
             66 CALL                     3
             74 CACHE
             76 STORE_FAST               4 (nlines)

 49          78 LOAD_FAST                4 (nlines)
             80 POP_JUMP_IF_FALSE       52 (to 186)

 50          82 LOAD_FAST                0 (self)
             84 LOAD_ATTR                2 (count)
            104 CACHE
            106 CACHE
            108 CACHE
            110 CACHE

 51         112 LOAD_FAST                3 (rawdata)
            114 STORE_SUBSCR
            118 CACHE
            120 CACHE
            122 CACHE
            124 CACHE
            126 CACHE
            128 CACHE
            130 CACHE
            132 CACHE
            134 CACHE
            136 LOAD_CONST               1 ('\n')
            138 LOAD_FAST                1 (i)
            140 LOAD_FAST                2 (j)
            142 UNPACK_SEQUENCE          3
            146 CALL                     3
            154 CACHE
            156 STORE_FAST               5 (pos)

 52         158 LOAD_FAST                2 (j)
            160 LOAD_FAST                5 (pos)
            162 LOAD_CONST               2 (1)
            164 BINARY_OP                0 (+)
            168 BINARY_OP               10 (-)
            172 LOAD_FAST                0 (self)
            174 STORE_ATTR               4 (offset)
            184 JUMP_FORWARD            18 (to 222)

 54     >>  186 LOAD_FAST                0 (self)
            188 LOAD_ATTR                4 (lineno)
            208 CACHE
            210 LOAD_FAST                0 (self)
            212 STORE_ATTR               4 (offset)

 55     >>  222 LOAD_FAST                2 (j)
            224 RETURN_VALUE

Disassembly of <code object parse_declaration at 0x000001A2D02C66E0, file "_markupbase.py", line 60>:
 60           0 RESUME                   0

 71           2 LOAD_FAST                0 (self)
              4 LOAD_ATTR                0 (rawdata)
             24 STORE_FAST               3 (j)

 73          26 LOAD_FAST                2 (rawdata)
             28 LOAD_FAST                1 (i)
             30 LOAD_FAST                3 (j)
             32 BUILD_SLICE              2
             34 BINARY_SUBSCR
             38 CACHE
             40 CACHE
             42 CACHE
             44 LOAD_CONST               2 ('<!')
             46 COMPARE_OP               2 (<)
             50 CACHE
             52 POP_JUMP_IF_TRUE        10 (to 74)
             54 LOAD_ASSERTION_ERROR
             56 LOAD_CONST               3 ('unexpected call to parse_declaration')
             58 UNPACK_SEQUENCE          0
             62 CALL                     0
             70 CACHE
             72 RAISE_VARARGS            1

 74     >>   74 LOAD_FAST                2 (rawdata)
             76 LOAD_FAST                3 (j)
             78 LOAD_FAST                3 (j)
             80 LOAD_CONST               4 (1)
             82 BINARY_OP                0 (+)
             86 BUILD_SLICE              2
             88 BINARY_SUBSCR
             92 CACHE
             94 CACHE
             96 CACHE
             98 LOAD_CONST               5 ('>')
            100 COMPARE_OP               2 (<)
            104 CACHE
            106 POP_JUMP_IF_FALSE        5 (to 118)

 76         108 LOAD_FAST                3 (j)
            110 LOAD_CONST               4 (1)
            112 BINARY_OP                0 (+)
            116 RETURN_VALUE

 77     >>  118 LOAD_FAST                2 (rawdata)
            120 LOAD_FAST                3 (j)
            122 LOAD_FAST                3 (j)
            124 LOAD_CONST               4 (1)
            126 BINARY_OP                0 (+)
            130 BUILD_SLICE              2
            132 BINARY_SUBSCR
            136 CACHE
            138 CACHE
            140 CACHE
            142 LOAD_CONST               6 (('-', ''))
            144 CONTAINS_OP              0
            146 POP_JUMP_IF_FALSE        2 (to 152)

 80         148 LOAD_CONST               7 (-1)
            150 RETURN_VALUE

 82     >>  152 LOAD_GLOBAL              3 (NULL + len)
            162 CACHE
            164 LOAD_FAST                2 (rawdata)
            166 UNPACK_SEQUENCE          1
            170 CALL                     1
            178 CACHE
            180 STORE_FAST               4 (n)

 83         182 LOAD_FAST                2 (rawdata)
            184 LOAD_FAST                3 (j)
            186 LOAD_FAST                3 (j)
            188 LOAD_CONST               1 (2)
            190 BINARY_OP                0 (+)
            194 BUILD_SLICE              2
            196 BINARY_SUBSCR
            200 CACHE
            202 CACHE
            204 CACHE
            206 LOAD_CONST               8 ('--')
            208 COMPARE_OP               2 (<)
            212 CACHE
            214 POP_JUMP_IF_FALSE       21 (to 258)

 85         216 LOAD_FAST                0 (self)
            218 STORE_SUBSCR
            222 CACHE
            224 CACHE
            226 CACHE
            228 CACHE
            230 CACHE
            232 CACHE
            234 CACHE
            236 CACHE
            238 CACHE
            240 LOAD_FAST                1 (i)
            242 UNPACK_SEQUENCE          1
            246 CALL                     1
            254 CACHE
            256 RETURN_VALUE

 86     >>  258 LOAD_FAST                2 (rawdata)
            260 LOAD_FAST                3 (j)
            262 BINARY_SUBSCR
            266 CACHE
            268 CACHE
            270 CACHE
            272 LOAD_CONST               9 ('[')
            274 COMPARE_OP               2 (<)
            278 CACHE
            280 POP_JUMP_IF_FALSE       21 (to 324)

 91         282 LOAD_FAST                0 (self)
            284 STORE_SUBSCR
            288 CACHE
            290 CACHE
            292 CACHE
            294 CACHE
            296 CACHE
            298 CACHE
            300 CACHE
            302 CACHE
            304 CACHE
            306 LOAD_FAST                1 (i)
            308 UNPACK_SEQUENCE          1
            312 CALL                     1
            320 CACHE
            322 RETURN_VALUE

 93     >>  324 LOAD_FAST                0 (self)
            326 STORE_SUBSCR
            330 CACHE
            332 CACHE
            334 CACHE
            336 CACHE
            338 CACHE
            340 CACHE
            342 CACHE
            344 CACHE
            346 CACHE
            348 LOAD_FAST                3 (j)
            350 LOAD_FAST                1 (i)
            352 UNPACK_SEQUENCE          2
            356 CALL                     2
            364 CACHE
            366 UNPACK_SEQUENCE          2
            370 STORE_FAST               5 (decltype)
            372 STORE_FAST               3 (j)

 94         374 LOAD_FAST                3 (j)
            376 LOAD_CONST              10 (0)
            378 COMPARE_OP               0 (<)
            382 CACHE
            384 POP_JUMP_IF_FALSE        2 (to 390)

 95         386 LOAD_FAST                3 (j)
            388 RETURN_VALUE

 96     >>  390 LOAD_FAST                5 (decltype)
            392 LOAD_CONST              11 ('doctype')
            394 COMPARE_OP               2 (<)
            398 CACHE
            400 POP_JUMP_IF_FALSE        7 (to 416)

 97         402 LOAD_CONST              12 ('')
            404 LOAD_FAST                0 (self)
            406 STORE_ATTR               5 (_decl_otherchars)

 98     >>  416 LOAD_FAST                3 (j)
            418 LOAD_FAST                4 (n)
            420 COMPARE_OP               0 (<)
            424 CACHE
            426 EXTENDED_ARG             1
            428 POP_JUMP_IF_FALSE      285 (to 1000)

 99         430 LOAD_FAST                2 (rawdata)
            432 LOAD_FAST                3 (j)
            434 BINARY_SUBSCR
            438 CACHE
            440 CACHE
            442 CACHE
            444 STORE_FAST               6 (c)

100         446 LOAD_FAST                6 (c)
            448 LOAD_CONST               5 ('>')
            450 COMPARE_OP               2 (<)
            454 CACHE
            456 POP_JUMP_IF_FALSE       67 (to 592)

102         458 LOAD_FAST                2 (rawdata)
            460 LOAD_FAST                1 (i)
            462 LOAD_CONST               1 (2)
            464 BINARY_OP                0 (+)
            468 LOAD_FAST                3 (j)
            470 BUILD_SLICE              2
            472 BINARY_SUBSCR
            476 CACHE
            478 CACHE
            480 CACHE
            482 STORE_FAST               7 (data)

103         484 LOAD_FAST                5 (decltype)
            486 LOAD_CONST              11 ('doctype')
            488 COMPARE_OP               2 (<)
            492 CACHE
            494 POP_JUMP_IF_FALSE       22 (to 540)

104         496 LOAD_FAST                0 (self)
            498 STORE_SUBSCR
            502 CACHE
            504 CACHE
            506 CACHE
            508 CACHE
            510 CACHE
            512 CACHE
            514 CACHE
            516 CACHE
            518 CACHE
            520 LOAD_FAST                7 (data)
            522 UNPACK_SEQUENCE          1
            526 CALL                     1
            534 CACHE
            536 POP_TOP
            538 JUMP_FORWARD            21 (to 582)

110     >>  540 LOAD_FAST                0 (self)
            542 STORE_SUBSCR
            546 CACHE
            548 CACHE
            550 CACHE
            552 CACHE
            554 CACHE
            556 CACHE
            558 CACHE
            560 CACHE
            562 CACHE
            564 LOAD_FAST                7 (data)
            566 UNPACK_SEQUENCE          1
            570 CALL                     1
            578 CACHE
            580 POP_TOP

111     >>  582 LOAD_FAST                3 (j)
            584 LOAD_CONST               4 (1)
            586 BINARY_OP                0 (+)
            590 RETURN_VALUE

112     >>  592 LOAD_FAST                6 (c)
            594 LOAD_CONST              13 ('"\'')
            596 CONTAINS_OP              0
            598 POP_JUMP_IF_FALSE       41 (to 682)

113         600 LOAD_GLOBAL             17 (NULL + _declstringlit_match)
            610 CACHE
            612 LOAD_FAST                2 (rawdata)
            614 LOAD_FAST                3 (j)
            616 UNPACK_SEQUENCE          2
            620 CALL                     2
            628 CACHE
            630 STORE_FAST               8 (m)

114         632 LOAD_FAST                8 (m)
            634 POP_JUMP_IF_TRUE         2 (to 640)

115         636 LOAD_CONST               7 (-1)
            638 RETURN_VALUE

116     >>  640 LOAD_FAST                8 (m)
            642 STORE_SUBSCR
            646 CACHE
            648 CACHE
            650 CACHE
            652 CACHE
            654 CACHE
            656 CACHE
            658 CACHE
            660 CACHE
            662 CACHE
            664 UNPACK_SEQUENCE          0
            668 CALL                     0
            676 CACHE
            678 STORE_FAST               3 (j)
            680 JUMP_FORWARD           144 (to 970)

117     >>  682 LOAD_FAST                6 (c)
            684 LOAD_CONST              14 ('abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ')
            686 CONTAINS_OP              0
            688 POP_JUMP_IF_FALSE       26 (to 742)

118         690 LOAD_FAST                0 (self)
            692 STORE_SUBSCR
            696 CACHE
            698 CACHE
            700 CACHE
            702 CACHE
            704 CACHE
            706 CACHE
            708 CACHE
            710 CACHE
            712 CACHE
            714 LOAD_FAST                3 (j)
            716 LOAD_FAST                1 (i)
            718 UNPACK_SEQUENCE          2
            722 CALL                     2
            730 CACHE
            732 UNPACK_SEQUENCE          2
            736 STORE_FAST               9 (name)
            738 STORE_FAST               3 (j)
            740 JUMP_FORWARD           114 (to 970)

119     >>  742 LOAD_FAST                6 (c)
            744 LOAD_FAST                0 (self)
            746 LOAD_ATTR                5 (NULL|self + parse_comment)
            766 CACHE
            768 STORE_FAST               3 (j)
            770 JUMP_FORWARD            99 (to 970)

121         772 LOAD_FAST                6 (c)
            774 LOAD_CONST               9 ('[')
            776 COMPARE_OP               2 (<)
            780 CACHE
            782 POP_JUMP_IF_FALSE       69 (to 922)

123         784 LOAD_FAST                5 (decltype)
            786 LOAD_CONST              11 ('doctype')
            788 COMPARE_OP               2 (<)
            792 CACHE
            794 POP_JUMP_IF_FALSE       26 (to 848)

124         796 LOAD_FAST                0 (self)
            798 STORE_SUBSCR
            802 CACHE
            804 CACHE
            806 CACHE
            808 CACHE
            810 CACHE
            812 CACHE
            814 CACHE
            816 CACHE
            818 CACHE
            820 LOAD_FAST                3 (j)
            822 LOAD_CONST               4 (1)
            824 BINARY_OP                0 (+)
            828 LOAD_FAST                1 (i)
            830 UNPACK_SEQUENCE          2
            834 CALL                     2
            842 CACHE
            844 STORE_FAST               3 (j)
            846 JUMP_FORWARD            61 (to 970)

125     >>  848 LOAD_FAST                5 (decltype)
            850 LOAD_CONST              15 (frozenset({'attlist', 'link', 'linktype', 'element'}))
            852 CONTAINS_OP              0
            854 POP_JUMP_IF_FALSE       18 (to 892)

130         856 LOAD_GLOBAL             23 (NULL + AssertionError)
            866 CACHE
            868 LOAD_CONST              16 ("unsupported '[' char in %s declaration")
            870 LOAD_FAST                5 (decltype)
            872 BINARY_OP                6 (%)
            876 UNPACK_SEQUENCE          1
            880 CALL                     1
            888 CACHE
            890 RAISE_VARARGS            1

132     >>  892 LOAD_GLOBAL             23 (NULL + AssertionError)
            902 CACHE
            904 LOAD_CONST              17 ("unexpected '[' char in declaration")
            906 UNPACK_SEQUENCE          1
            910 CALL                     1
            918 CACHE
            920 RAISE_VARARGS            1

134     >>  922 LOAD_GLOBAL             23 (NULL + AssertionError)
            932 CACHE
            934 LOAD_CONST              18 ('unexpected %r char in declaration')
            936 LOAD_FAST                2 (rawdata)
            938 LOAD_FAST                3 (j)
            940 BINARY_SUBSCR
            944 CACHE
            946 CACHE
            948 CACHE
            950 BINARY_OP                6 (%)
            954 UNPACK_SEQUENCE          1
            958 CALL                     1
            966 CACHE
            968 RAISE_VARARGS            1

135     >>  970 LOAD_FAST                3 (j)
            972 LOAD_CONST              10 (0)
            974 COMPARE_OP               0 (<)
            978 CACHE
            980 POP_JUMP_IF_FALSE        2 (to 986)

136         982 LOAD_FAST                3 (j)
            984 RETURN_VALUE

 98     >>  986 LOAD_FAST                3 (j)
            988 LOAD_FAST                4 (n)
            990 COMPARE_OP               0 (<)
            994 CACHE
            996 EXTENDED_ARG             1
