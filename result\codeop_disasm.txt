# Code object from position 8262193
# Filename: codeop.py
# Name: <module>
# Args: 0
# Locals: 0
# Stack size: 4
# Flags: 0

  0           0 RESUME                   0

  1           2 LOAD_CONST               0 ("Utilities to compile possibly incomplete Python source code.\n\nThis module provides two interfaces, broadly similar to the builtin\nfunction compile(), which take program text, a filename and a 'mode'\nand:\n\n- Return code object if the command is complete and valid\n- Return None if the command is incomplete\n- Raise SyntaxError, ValueError or OverflowError if the command is a\n  syntax error (OverflowError and ValueError can be produced by\n  malformed literals).\n\nThe two interfaces are:\n\ncompile_command(source, filename, symbol):\n\n    Compiles a single command in the manner described above.\n\nCommandCompiler():\n\n    Instances of this class have __call__ methods identical in\n    signature to compile_command; the difference is that if the\n    instance compiles program text containing a __future__ statement,\n    the instance 'remembers' and compiles all subsequent program texts\n    with the statement in force.\n\nThe module also provides another class:\n\nCompile():\n\n    Instances of this class act like the built-in function compile,\n    but with 'memory' in the sense described above.\n")
              4 STORE_NAME               0 (__doc__)

 35           6 LOAD_CONST               1 (0)
              8 LOAD_CONST               2 (None)
             10 IMPORT_NAME              1 (__future__)
             12 STORE_NAME               1 (__future__)

 36          14 LOAD_CONST               1 (0)
             16 LOAD_CONST               2 (None)
             18 IMPORT_NAME              2 (warnings)
             20 STORE_NAME               2 (warnings)

 38          22 LOAD_CONST               3 (<code object <listcomp> at 0x000001B2A7672D30, file "codeop.py", line 38>)
             24 MAKE_FUNCTION            0

 39          26 LOAD_NAME                1 (__future__)
             28 LOAD_ATTR                3 (NULL|self + __future__)
             48 CACHE
             50 CACHE
             52 CACHE
             54 STORE_NAME               4 (_features)

 41          56 BUILD_LIST               0
             58 LOAD_CONST               4 (('compile_command', 'Compile', 'CommandCompiler'))
             60 LIST_EXTEND              1
             62 STORE_NAME               5 (__all__)

 46          64 LOAD_CONST               5 (512)
             66 STORE_NAME               6 (PyCF_DONT_IMPLY_DEDENT)

 47          68 LOAD_CONST               6 (16384)
             70 STORE_NAME               7 (PyCF_ALLOW_INCOMPLETE_INPUT)

 49          72 LOAD_CONST               7 (<code object _maybe_compile at 0x000001B2A74FAEF0, file "codeop.py", line 49>)
             74 MAKE_FUNCTION            0
             76 STORE_NAME               8 (_maybe_compile)

 75          78 LOAD_CONST               8 (<code object _is_syntax_error at 0x000001B2A725EFB0, file "codeop.py", line 75>)
             80 MAKE_FUNCTION            0
             82 STORE_NAME               9 (_is_syntax_error)

 84          84 LOAD_CONST              18 ((True,))
             86 LOAD_CONST              10 (<code object _compile at 0x000001B2A76D9B30, file "codeop.py", line 84>)
             88 MAKE_FUNCTION            1 (defaults)
             90 STORE_NAME              10 (_compile)

 91          92 LOAD_CONST              19 (('<input>', 'single'))
             94 LOAD_CONST              13 (<code object compile_command at 0x000001B2A767C030, file "codeop.py", line 91>)
             96 MAKE_FUNCTION            1 (defaults)
             98 STORE_NAME              11 (compile_command)

112         100 PUSH_NULL
            102 LOAD_BUILD_CLASS
            104 LOAD_CONST              14 (<code object Compile at 0x000001B2A768B2F0, file "codeop.py", line 112>)
            106 MAKE_FUNCTION            0
            108 LOAD_CONST              15 ('Compile')
            110 UNPACK_SEQUENCE          2
            114 CALL                     2
            122 CACHE
            124 STORE_NAME              12 (Compile)

131         126 PUSH_NULL
            128 LOAD_BUILD_CLASS
            130 LOAD_CONST              16 (<code object CommandCompiler at 0x000001B2A76890D0, file "codeop.py", line 131>)
            132 MAKE_FUNCTION            0
            134 LOAD_CONST              17 ('CommandCompiler')
            136 UNPACK_SEQUENCE          2
            140 CALL                     2
            148 CACHE
            150 STORE_NAME              13 (CommandCompiler)
            152 LOAD_CONST               2 (None)
            154 RETURN_VALUE

Disassembly of <code object <listcomp> at 0x000001B2A7672D30, file "codeop.py", line 38>:
 38           0 RESUME                   0
              2 BUILD_LIST               0
              4 LOAD_FAST                0 (.0)
        >>    6 FOR_ITER                23 (to 56)

 38          10 LOAD_GLOBAL              1 (NULL + getattr)
             20 CACHE
             22 LOAD_GLOBAL              2 (__future__)
             32 CACHE
             34 LOAD_FAST                1 (fname)
             36 UNPACK_SEQUENCE          2
             40 CALL                     2
             48 CACHE
             50 LIST_APPEND              2
             52 JUMP_BACKWARD           24 (to 6)
             54 RETURN_VALUE

Disassembly of <code object _maybe_compile at 0x000001B2A74FAEF0, file "codeop.py", line 49>:
 49           0 RESUME                   0

 51           2 LOAD_FAST                1 (source)
              4 STORE_SUBSCR
              8 CACHE
             10 CACHE
             12 CACHE
             14 CACHE
             16 CACHE
             18 CACHE
             20 CACHE
             22 CACHE
             24 CACHE
             26 LOAD_CONST               1 ('\n')
             28 UNPACK_SEQUENCE          1
             32 CALL                     1
             40 CACHE
             42 GET_ITER
        >>   44 FOR_ITER                38 (to 124)

 52          48 LOAD_FAST                4 (line)
             50 STORE_SUBSCR
             54 CACHE
             56 CACHE
             58 CACHE
             60 CACHE
             62 CACHE
             64 CACHE
             66 CACHE
             68 CACHE
             70 CACHE
             72 UNPACK_SEQUENCE          0
             76 CALL                     0
             84 CACHE
             86 STORE_FAST               4 (line)

 53          88 LOAD_FAST                4 (line)
             90 POP_JUMP_IF_FALSE       14 (to 120)
             92 LOAD_FAST                4 (line)
             94 LOAD_CONST               2 (0)
             96 BINARY_SUBSCR
            100 CACHE
            102 CACHE
            104 CACHE
            106 LOAD_CONST               3 ('#')
            108 COMPARE_OP               3 (<)
            112 CACHE
            114 POP_JUMP_IF_FALSE        2 (to 120)

 54         116 POP_TOP
            118 JUMP_FORWARD             9 (to 138)
        >>  120 JUMP_BACKWARD           39 (to 44)

 56         122 LOAD_FAST                3 (symbol)
        >>  124 LOAD_CONST               4 ('eval')
            126 COMPARE_OP               3 (<)
            130 CACHE
            132 POP_JUMP_IF_FALSE        2 (to 138)

 57         134 LOAD_CONST               5 ('pass')
            136 STORE_FAST               1 (source)

 60     >>  138 LOAD_GLOBAL              5 (NULL + warnings)
            148 CACHE
            150 LOAD_ATTR                3 (NULL|self + strip)
            170 CACHE
            172 CACHE
            174 BEFORE_WITH
            176 POP_TOP

 61         178 LOAD_GLOBAL              5 (NULL + warnings)
            188 CACHE
            190 LOAD_ATTR                4 (warnings)
            210 CACHE
            212 CACHE
            214 LOAD_GLOBAL             12 (DeprecationWarning)
            224 CACHE
            226 BUILD_TUPLE              2
            228 UNPACK_SEQUENCE          2
            232 CALL                     2
            240 CACHE
            242 POP_TOP

 62         244 NOP

 63         246 PUSH_NULL
            248 LOAD_FAST                0 (compiler)
            250 LOAD_FAST                1 (source)
            252 LOAD_FAST                2 (filename)
            254 LOAD_FAST                3 (symbol)
            256 UNPACK_SEQUENCE          3
            260 CALL                     3
            268 CACHE
            270 POP_TOP
            272 JUMP_FORWARD           105 (to 484)
        >>  274 PUSH_EXC_INFO

 64         276 LOAD_GLOBAL             14 (SyntaxError)
            286 CACHE
            288 CHECK_EXC_MATCH
            290 POP_JUMP_IF_FALSE       92 (to 476)
            292 POP_TOP

 65         294 NOP

 66         296 PUSH_NULL
            298 LOAD_FAST                0 (compiler)
            300 LOAD_FAST                1 (source)
            302 LOAD_CONST               1 ('\n')
            304 BINARY_OP                0 (+)
            308 LOAD_FAST                2 (filename)
            310 LOAD_FAST                3 (symbol)
            312 UNPACK_SEQUENCE          3
            316 CALL                     3
            324 CACHE
            326 POP_TOP

 67         328 POP_EXCEPT

 60         330 LOAD_CONST               0 (None)
            332 LOAD_CONST               0 (None)
            334 LOAD_CONST               0 (None)
            336 UNPACK_SEQUENCE          2
            340 CALL                     2
            348 CACHE
            350 POP_TOP
            352 LOAD_CONST               0 (None)
            354 RETURN_VALUE
        >>  356 PUSH_EXC_INFO

 68         358 LOAD_GLOBAL             14 (SyntaxError)
            368 CACHE
            370 CHECK_EXC_MATCH
            372 POP_JUMP_IF_FALSE       45 (to 464)
            374 STORE_FAST               5 (e)

 69         376 LOAD_CONST               7 ('incomplete input')
            378 LOAD_GLOBAL             17 (NULL + str)
            388 CACHE
            390 LOAD_FAST                5 (e)
            392 UNPACK_SEQUENCE          1
            396 CALL                     1
            404 CACHE
            406 CONTAINS_OP              0
            408 POP_JUMP_IF_FALSE       18 (to 446)

 70         410 POP_EXCEPT
            412 LOAD_CONST               0 (None)
            414 STORE_FAST               5 (e)
            416 DELETE_FAST              5 (e)
            418 POP_EXCEPT

 60         420 LOAD_CONST               0 (None)
            422 LOAD_CONST               0 (None)
            424 LOAD_CONST               0 (None)
            426 UNPACK_SEQUENCE          2
            430 CALL                     2
            438 CACHE
            440 POP_TOP
            442 LOAD_CONST               0 (None)
            444 RETURN_VALUE

 69     >>  446 POP_EXCEPT
            448 LOAD_CONST               0 (None)
            450 STORE_FAST               5 (e)
            452 DELETE_FAST              5 (e)
            454 JUMP_FORWARD             8 (to 472)
        >>  456 LOAD_CONST               0 (None)
            458 STORE_FAST               5 (e)
            460 DELETE_FAST              5 (e)
            462 RERAISE                  1

 68     >>  464 RERAISE                  0
        >>  466 COPY                     3
            468 POP_EXCEPT
            470 RERAISE                  1

 69     >>  472 POP_EXCEPT
            474 JUMP_FORWARD             4 (to 484)

 64     >>  476 RERAISE                  0
        >>  478 COPY                     3
            480 POP_EXCEPT
            482 RERAISE                  1

 60     >>  484 LOAD_CONST               0 (None)
            486 LOAD_CONST               0 (None)
            488 LOAD_CONST               0 (None)
            490 UNPACK_SEQUENCE          2
            494 CALL                     2
            502 CACHE
            504 POP_TOP
            506 JUMP_FORWARD            11 (to 530)
        >>  508 PUSH_EXC_INFO
            510 WITH_EXCEPT_START
            512 POP_JUMP_IF_TRUE         4 (to 522)
            514 RERAISE                  2
        >>  516 COPY                     3
            518 POP_EXCEPT
            520 RERAISE                  1
        >>  522 POP_TOP
            524 POP_EXCEPT
            526 POP_TOP
            528 POP_TOP

 73     >>  530 PUSH_NULL
            532 LOAD_FAST                0 (compiler)
            534 LOAD_FAST                1 (source)
            536 LOAD_FAST                2 (filename)
            538 LOAD_FAST                3 (symbol)
            540 LOAD_CONST               8 (False)
            542 KW_NAMES                 9 (('incomplete_input',))
            544 UNPACK_SEQUENCE          4
            548 CALL                     4
            556 CACHE
            558 RETURN_VALUE
ExceptionTable:
  176 to 242 -> 508 [1] lasti
  246 to 270 -> 274 [1]
  272 to 272 -> 508 [1] lasti
  274 to 292 -> 478 [2] lasti
  296 to 326 -> 356 [2]
  328 to 328 -> 508 [1] lasti
  356 to 374 -> 466 [3] lasti
  376 to 408 -> 456 [3] lasti
  410 to 416 -> 478 [2] lasti
  418 to 418 -> 508 [1] lasti
  446 to 454 -> 478 [2] lasti
  456 to 464 -> 466 [3] lasti
  466 to 470 -> 478 [2] lasti
  472 to 474 -> 508 [1] lasti
  476 to 476 -> 478 [2] lasti
  478 to 482 -> 508 [1] lasti
  508 to 514 -> 516 [3] lasti
  522 to 522 -> 516 [3] lasti

Disassembly of <code object _is_syntax_error at 0x000001B2A725EFB0, file "codeop.py", line 75>:
 75           0 RESUME                   0

 76           2 LOAD_GLOBAL              1 (NULL + repr)
             12 CACHE
             14 LOAD_FAST                0 (err1)
             16 UNPACK_SEQUENCE          1
             20 CALL                     1
             28 CACHE
             30 STORE_FAST               2 (rep1)

 77          32 LOAD_GLOBAL              1 (NULL + repr)
             42 CACHE
             44 LOAD_FAST                1 (err2)
             46 UNPACK_SEQUENCE          1
             50 CALL                     1
             58 CACHE
             60 STORE_FAST               3 (rep2)

 78          62 LOAD_CONST               1 ('was never closed')
             64 LOAD_FAST                2 (rep1)
             66 CONTAINS_OP              0
             68 POP_JUMP_IF_FALSE        6 (to 82)
             70 LOAD_CONST               1 ('was never closed')
             72 LOAD_FAST                3 (rep2)
             74 CONTAINS_OP              0
             76 POP_JUMP_IF_FALSE        2 (to 82)

 79          78 LOAD_CONST               2 (False)
             80 RETURN_VALUE

 80     >>   82 LOAD_FAST                2 (rep1)
             84 LOAD_FAST                3 (rep2)
             86 COMPARE_OP               2 (<)
             90 CACHE
             92 POP_JUMP_IF_FALSE        2 (to 98)

 81          94 LOAD_CONST               3 (True)
             96 RETURN_VALUE

 82     >>   98 LOAD_CONST               2 (False)
            100 RETURN_VALUE

Disassembly of <code object _compile at 0x000001B2A76D9B30, file "codeop.py", line 84>:
 84           0 RESUME                   0

 85           2 LOAD_CONST               1 (0)
              4 STORE_FAST               4 (flags)

 86           6 LOAD_FAST                3 (incomplete_input)
              8 POP_JUMP_IF_FALSE       20 (to 50)

 87          10 LOAD_FAST                4 (flags)
             12 LOAD_GLOBAL              0 (PyCF_ALLOW_INCOMPLETE_INPUT)
             22 CACHE
             24 BINARY_OP               20 (|=)
             28 STORE_FAST               4 (flags)

 88          30 LOAD_FAST                4 (flags)
             32 LOAD_GLOBAL              2 (PyCF_DONT_IMPLY_DEDENT)
             42 CACHE
             44 BINARY_OP               20 (|=)
             48 STORE_FAST               4 (flags)

 89     >>   50 LOAD_GLOBAL              5 (NULL + compile)
             60 CACHE
             62 LOAD_FAST                0 (source)
             64 LOAD_FAST                1 (filename)
             66 LOAD_FAST                2 (symbol)
             68 LOAD_FAST                4 (flags)
             70 UNPACK_SEQUENCE          4
             74 CALL                     4
             82 CACHE
             84 RETURN_VALUE

Disassembly of <code object compile_command at 0x000001B2A767C030, file "codeop.py", line 91>:
 91           0 RESUME                   0

110           2 LOAD_GLOBAL              1 (NULL + _maybe_compile)
             12 CACHE
             14 LOAD_GLOBAL              2 (_compile)
             24 CACHE
             26 LOAD_FAST                0 (source)
             28 LOAD_FAST                1 (filename)
             30 LOAD_FAST                2 (symbol)
             32 UNPACK_SEQUENCE          4
             36 CALL                     4
             44 CACHE
             46 RETURN_VALUE

Disassembly of <code object Compile at 0x000001B2A768B2F0, file "codeop.py", line 112>:
112           0 RESUME                   0
              2 LOAD_NAME                0 (__name__)
              4 STORE_NAME               1 (__module__)
              6 LOAD_CONST               0 ('Compile')
              8 STORE_NAME               2 (__qualname__)

113          10 LOAD_CONST               1 ('Instances of this class behave much like the built-in compile\n    function, but if one is used to compile text containing a future\n    statement, it "remembers" and compiles all subsequent program texts\n    with the statement in force.')
             12 STORE_NAME               3 (__doc__)

117          14 LOAD_CONST               2 (<code object __init__ at 0x000001B2A767D4D0, file "codeop.py", line 117>)
             16 MAKE_FUNCTION            0
             18 STORE_NAME               4 (__init__)

120          20 LOAD_CONST               3 (<code object __call__ at 0x000001B2A722DFB0, file "codeop.py", line 120>)
             22 MAKE_FUNCTION            0
             24 STORE_NAME               5 (__call__)
             26 LOAD_CONST               4 (None)
             28 RETURN_VALUE

Disassembly of <code object __init__ at 0x000001B2A767D4D0, file "codeop.py", line 117>:
117           0 RESUME                   0

118           2 LOAD_GLOBAL              0 (PyCF_DONT_IMPLY_DEDENT)
             12 CACHE
             14 LOAD_GLOBAL              2 (PyCF_ALLOW_INCOMPLETE_INPUT)
             24 CACHE
             26 BINARY_OP                7 (|)
             30 LOAD_FAST                0 (self)
             32 STORE_ATTR               2 (flags)
             42 LOAD_CONST               0 (None)
             44 RETURN_VALUE

Disassembly of <code object __call__ at 0x000001B2A722DFB0, file "codeop.py", line 120>:
120           0 RESUME                   0

121           2 LOAD_FAST                0 (self)
              4 LOAD_ATTR                0 (flags)
             24 CACHE
             26 CACHE
             28 CACHE
             30 CACHE
             32 CACHE
             34 CACHE
             36 CACHE
             38 CACHE
             40 LOAD_CONST               1 ('incomplete_input')
             42 LOAD_CONST               2 (True)
             44 UNPACK_SEQUENCE          2
             48 CALL                     2
             56 CACHE
             58 LOAD_CONST               3 (False)
             60 IS_OP                    0
             62 POP_JUMP_IF_FALSE       22 (to 108)

123          64 LOAD_FAST                5 (flags)
             66 LOAD_GLOBAL              4 (PyCF_DONT_IMPLY_DEDENT)
             76 CACHE
             78 UNARY_INVERT
             80 BINARY_OP               14 (&=)
             84 STORE_FAST               5 (flags)

124          86 LOAD_FAST                5 (flags)
             88 LOAD_GLOBAL              6 (PyCF_ALLOW_INCOMPLETE_INPUT)
             98 CACHE
            100 UNARY_INVERT
            102 BINARY_OP               14 (&=)
            106 STORE_FAST               5 (flags)

125     >>  108 LOAD_GLOBAL              9 (NULL + compile)
            118 CACHE
            120 LOAD_FAST                1 (source)
            122 LOAD_FAST                2 (filename)
            124 LOAD_FAST                3 (symbol)
            126 LOAD_FAST                5 (flags)
            128 LOAD_CONST               2 (True)
            130 UNPACK_SEQUENCE          5
            134 CALL                     5
            142 CACHE
            144 STORE_FAST               6 (codeob)

126         146 LOAD_GLOBAL             10 (_features)
            156 CACHE
            158 GET_ITER
        >>  160 FOR_ITER                38 (to 240)

127         164 LOAD_FAST                6 (codeob)
            166 LOAD_ATTR                6 (PyCF_ALLOW_INCOMPLETE_INPUT)
            186 CACHE
            188 BINARY_OP                1 (&)
            192 POP_JUMP_IF_FALSE       21 (to 236)

128         194 LOAD_FAST                0 (self)
            196 COPY                     1
            198 LOAD_ATTR                0 (flags)
            218 CACHE
            220 BINARY_OP               20 (|=)
            224 SWAP                     2
            226 STORE_ATTR               0 (flags)
        >>  236 JUMP_BACKWARD           39 (to 160)

129         238 LOAD_FAST                6 (codeob)
        >>  240 RETURN_VALUE

Disassembly of <code object CommandCompiler at 0x000001B2A76890D0, file "codeop.py", line 131>:
131           0 RESUME                   0
              2 LOAD_NAME                0 (__name__)
              4 STORE_NAME               1 (__module__)
              6 LOAD_CONST               0 ('CommandCompiler')
              8 STORE_NAME               2 (__qualname__)

132          10 LOAD_CONST               1 ("Instances of this class have __call__ methods identical in\n    signature to compile_command; the difference is that if the\n    instance compiles program text containing a __future__ statement,\n    the instance 'remembers' and compiles all subsequent program texts\n    with the statement in force.")
             12 STORE_NAME               3 (__doc__)

138          14 LOAD_CONST               2 (<code object __init__ at 0x000001B2A767D5C0, file "codeop.py", line 138>)
             16 MAKE_FUNCTION            0
             18 STORE_NAME               4 (__init__)

141          20 LOAD_CONST               7 (('<input>', 'single'))
             22 LOAD_CONST               5 (<code object __call__ at 0x000001B2A767D7A0, file "codeop.py", line 141>)
             24 MAKE_FUNCTION            1 (defaults)
             26 STORE_NAME               5 (__call__)
             28 LOAD_CONST               6 (None)
             30 RETURN_VALUE

Disassembly of <code object __init__ at 0x000001B2A767D5C0, file "codeop.py", line 138>:
138           0 RESUME                   0

139           2 LOAD_GLOBAL              1 (NULL + Compile)
             12 CACHE
             14 UNPACK_SEQUENCE          0
             18 CALL                     0
             26 CACHE
             28 LOAD_FAST                0 (self)
             30 STORE_ATTR               1 (compiler)
             40 LOAD_CONST               0 (None)
             42 RETURN_VALUE

Disassembly of <code object __call__ at 0x000001B2A767D7A0, file "codeop.py", line 141>:
141           0 RESUME                   0

160           2 LOAD_GLOBAL              1 (NULL + _maybe_compile)
             12 CACHE
             14 LOAD_FAST                0 (self)
             16 LOAD_ATTR                1 (NULL|self + _maybe_compile)
             36 CALL                     4
             44 CACHE
             46 RETURN_VALUE


# Constants:
# 0: <string length 1089>
# 1: 0
# 2: None
# 3: <code object <listcomp>>
# 4: tuple
# 5: 512
# 6: 16384
# 7: <code object _maybe_compile>
# 8: <code object _is_syntax_error>
# 9: True
# 10: <code object _compile>
# 11: '<input>'
# 12: 'single'
# 13: <code object compile_command>
# 14: <code object Compile>
# 15: 'Compile'
# 16: <code object CommandCompiler>
# 17: 'CommandCompiler'
# 18: tuple
# 19: tuple


# Names:
# 0: '__doc__'
# 1: '__future__'
# 2: 'warnings'
# 3: 'all_feature_names'
# 4: '_features'
# 5: '__all__'
# 6: 'PyCF_DONT_IMPLY_DEDENT'
# 7: 'PyCF_ALLOW_INCOMPLETE_INPUT'
# 8: '_maybe_compile'
# 9: '_is_syntax_error'
# 10: '_compile'
# 11: 'compile_command'
# 12: 'Compile'
# 13: 'CommandCompiler'


# Variable names:
