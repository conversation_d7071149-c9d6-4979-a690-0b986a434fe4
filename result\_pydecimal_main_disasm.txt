# MAIN APPLICATION CODE OBJECT
# Position: 7367106
# Filename: _pydecimal.py
# Function: <module>
# Args: 0
# Locals: 0
# Stack size: 10
# Flags: 0

# BYTECODE DISASSEMBLY:
==================================================
   0           0 RESUME                   0

  16           2 LOAD_CONST               0 ('\nThis is an implementation of decimal floating point arithmetic based on\nthe General Decimal Arithmetic Specification:\n\n    http://speleotrove.com/decimal/decarith.html\n\nand IEEE standard 854-1987:\n\n    http://en.wikipedia.org/wiki/IEEE_854-1987\n\nDecimal floating point has finite precision with arbitrarily large bounds.\n\nThe purpose of this module is to support arithmetic using familiar\n"schoolhouse" rules and to avoid some of the tricky representation\nissues associated with binary floating point.  The package is especially\nuseful for financial applications or for contexts where users have\nexpectations that are at odds with binary floating point (for instance,\nin binary floating point, 1.00 % 0.1 gives 0.09999999999999995 instead\nof 0.0; Decimal(\'1.00\') % Decimal(\'0.1\') returns the expected\nDecimal(\'0.00\')).\n\nHere are some examples of using the decimal module:\n\n>>> from decimal import *\n>>> setcontext(ExtendedContext)\n>>> Decimal(0)\nDecimal(\'0\')\n>>> Decimal(\'1\')\nDecimal(\'1\')\n>>> Decimal(\'-.0123\')\nDecimal(\'-0.0123\')\n>>> Decimal(123456)\nDecimal(\'123456\')\n>>> Decimal(\'123.45e12345678\')\nDecimal(\'1.2345E+12345680\')\n>>> Decimal(\'1.33\') + Decimal(\'1.27\')\nDecimal(\'2.60\')\n>>> Decimal(\'12.34\') + Decimal(\'3.87\') - Decimal(\'18.41\')\nDecimal(\'-2.20\')\n>>> dig = Decimal(1)\n>>> print(dig / Decimal(3))\n0.333333333\n>>> getcontext().prec = 18\n>>> print(dig / Decimal(3))\n0.333333333333333333\n>>> print(dig.sqrt())\n1\n>>> print(Decimal(3).sqrt())\n1.73205080756887729\n>>> print(Decimal(3) ** 123)\n4.85192780976896427E+58\n>>> inf = Decimal(1) / Decimal(0)\n>>> print(inf)\nInfinity\n>>> neginf = Decimal(-1) / Decimal(0)\n>>> print(neginf)\n-Infinity\n>>> print(neginf + inf)\nNaN\n>>> print(neginf * inf)\n-Infinity\n>>> print(dig / 0)\nInfinity\n>>> getcontext().traps[DivisionByZero] = 1\n>>> print(dig / 0)\nTraceback (most recent call last):\n  ...\n  ...\n  ...\ndecimal.DivisionByZero: x / 0\n>>> c = Context()\n>>> c.traps[InvalidOperation] = 0\n>>> print(c.flags[InvalidOperation])\n0\n>>> c.divide(Decimal(0), Decimal(0))\nDecimal(\'NaN\')\n>>> c.traps[InvalidOperation] = 1\n>>> print(c.flags[InvalidOperation])\n1\n>>> c.flags[InvalidOperation] = 0\n>>> print(c.flags[InvalidOperation])\n0\n>>> print(c.divide(Decimal(0), Decimal(0)))\nTraceback (most recent call last):\n  ...\n  ...\n  ...\ndecimal.InvalidOperation: 0 / 0\n>>> print(c.flags[InvalidOperation])\n1\n>>> c.flags[InvalidOperation] = 0\n>>> c.traps[InvalidOperation] = 0\n>>> print(c.divide(Decimal(0), Decimal(0)))\nNaN\n>>> print(c.flags[InvalidOperation])\n1\n>>>\n')
               4 STORE_NAME               0 (__doc__)

 115           6 BUILD_LIST               0
               8 LOAD_CONST               1 (('Decimal', 'Context', 'DecimalTuple', 'DefaultContext', 'BasicContext', 'ExtendedContext', 'DecimalException', 'Clamped', 'InvalidOperation', 'DivisionByZero', 'Inexact', 'Rounded', 'Subnormal', 'Overflow', 'Underflow', 'FloatOperation', 'DivisionImpossible', 'InvalidContext', 'ConversionSyntax', 'DivisionUndefined', 'ROUND_DOWN', 'ROUND_HALF_UP', 'ROUND_HALF_EVEN', 'ROUND_CEILING', 'ROUND_FLOOR', 'ROUND_UP', 'ROUND_HALF_DOWN', 'ROUND_05UP', 'setcontext', 'getcontext', 'localcontext', 'MAX_PREC', 'MAX_EMAX', 'MIN_EMIN', 'MIN_ETINY', 'HAVE_THREADS', 'HAVE_CONTEXTVAR'))
              10 LIST_EXTEND              1
              12 STORE_NAME               1 (__all__)

 150          14 LOAD_NAME                2 (__name__)
              16 STORE_NAME               3 (__xname__)

 151          18 LOAD_CONST               2 ('decimal')
              20 STORE_NAME               2 (__name__)

 152          22 LOAD_CONST               3 ('1.70')
              24 STORE_NAME               4 (__version__)

 154          26 LOAD_CONST               4 ('2.4.2')
              28 STORE_NAME               5 (__libmpdec_version__)

 156          30 LOAD_CONST               5 (0)
              32 LOAD_CONST               6 (None)
              34 IMPORT_NAME              6 (math)
              36 STORE_NAME               7 (_math)

 157          38 LOAD_CONST               5 (0)
              40 LOAD_CONST               6 (None)
              42 IMPORT_NAME              8 (numbers)
              44 STORE_NAME               9 (_numbers)

 158          46 LOAD_CONST               5 (0)
              48 LOAD_CONST               6 (None)
              50 IMPORT_NAME             10 (sys)
              52 STORE_NAME              10 (sys)

 160          54 NOP

 161          56 LOAD_CONST               5 (0)
              58 LOAD_CONST               7 (('namedtuple',))
              60 IMPORT_NAME             11 (collections)
              62 IMPORT_FROM             12 (namedtuple)
              64 STORE_NAME              13 (_namedtuple)
              66 POP_TOP

 162          68 PUSH_NULL
              70 LOAD_NAME               13 (_namedtuple)
              72 LOAD_CONST               8 ('DecimalTuple')
              74 LOAD_CONST               9 ('sign digits exponent')
              76 UNPACK_SEQUENCE          2
              80 CALL                     2
              88 CACHE
              90 STORE_NAME              14 (DecimalTuple)
              92 JUMP_FORWARD            14 (to 122)
         >>   94 PUSH_EXC_INFO

 163          96 LOAD_NAME               15 (ImportError)
              98 CHECK_EXC_MATCH
             100 POP_JUMP_IF_FALSE        6 (to 114)
             102 POP_TOP

 164         104 LOAD_CONST              10 (<code object <lambda> at 0x000001E77EBC6660, file "_pydecimal.py", line 164>)
             106 MAKE_FUNCTION            0
             108 STORE_NAME              14 (DecimalTuple)
             110 POP_EXCEPT
             112 JUMP_FORWARD             4 (to 122)

 163     >>  114 RERAISE                  0
         >>  116 COPY                     3
             118 POP_EXCEPT
             120 RERAISE                  1

 167     >>  122 LOAD_CONST              11 ('ROUND_DOWN')
             124 STORE_NAME              16 (ROUND_DOWN)

 168         126 LOAD_CONST              12 ('ROUND_HALF_UP')
             128 STORE_NAME              17 (ROUND_HALF_UP)

 169         130 LOAD_CONST              13 ('ROUND_HALF_EVEN')
             132 STORE_NAME              18 (ROUND_HALF_EVEN)

 170         134 LOAD_CONST              14 ('ROUND_CEILING')
             136 STORE_NAME              19 (ROUND_CEILING)

 171         138 LOAD_CONST              15 ('ROUND_FLOOR')
             140 STORE_NAME              20 (ROUND_FLOOR)

 172         142 LOAD_CONST              16 ('ROUND_UP')
             144 STORE_NAME              21 (ROUND_UP)

 173         146 LOAD_CONST              17 ('ROUND_HALF_DOWN')
             148 STORE_NAME              22 (ROUND_HALF_DOWN)

 174         150 LOAD_CONST              18 ('ROUND_05UP')
             152 STORE_NAME              23 (ROUND_05UP)

 177         154 LOAD_CONST              19 (True)
             156 STORE_NAME              24 (HAVE_THREADS)

 178         158 LOAD_CONST              19 (True)
             160 STORE_NAME              25 (HAVE_CONTEXTVAR)

 179         162 LOAD_NAME               10 (sys)
             164 LOAD_ATTR               26 (_namedtuple)

 180         184 LOAD_CONST              21 (999999999999999999)
             186 STORE_NAME              27 (MAX_PREC)

 181         188 LOAD_CONST              21 (999999999999999999)
             190 STORE_NAME              28 (MAX_EMAX)

 182         192 LOAD_CONST              22 (-999999999999999999)
             194 STORE_NAME              29 (MIN_EMIN)
             196 JUMP_FORWARD             6 (to 210)

 184         198 LOAD_CONST              23 (425000000)
             200 STORE_NAME              27 (MAX_PREC)

 185         202 LOAD_CONST              23 (425000000)
             204 STORE_NAME              28 (MAX_EMAX)

 186         206 LOAD_CONST              24 (-425000000)
             208 STORE_NAME              29 (MIN_EMIN)

 188     >>  210 LOAD_NAME               29 (MIN_EMIN)
             212 LOAD_NAME               27 (MAX_PREC)
             214 LOAD_CONST              25 (1)
             216 BINARY_OP               10 (-)
             220 BINARY_OP               10 (-)
             224 STORE_NAME              30 (MIN_ETINY)

 192         226 PUSH_NULL
             228 LOAD_BUILD_CLASS
             230 LOAD_CONST              26 (<code object DecimalException at 0x000001E77EBD9610, file "_pydecimal.py", line 192>)
             232 MAKE_FUNCTION            0
             234 LOAD_CONST              27 ('DecimalException')
             236 LOAD_NAME               31 (ArithmeticError)
             238 UNPACK_SEQUENCE          3
             242 CALL                     3
             250 CACHE
             252 STORE_NAME              32 (DecimalException)

 215         254 PUSH_NULL
             256 LOAD_BUILD_CLASS
             258 LOAD_CONST              28 (<code object Clamped at 0x000001E77EBD97D0, file "_pydecimal.py", line 215>)
             260 MAKE_FUNCTION            0
             262 LOAD_CONST              29 ('Clamped')
             264 LOAD_NAME               32 (DecimalException)
             266 UNPACK_SEQUENCE          3
             270 CALL                     3
             278 CACHE
             280 STORE_NAME              33 (Clamped)

 227         282 PUSH_NULL
             284 LOAD_BUILD_CLASS
             286 LOAD_CONST              30 (<code object InvalidOperation at 0x000001E77EBD98B0, file "_pydecimal.py", line 227>)
             288 MAKE_FUNCTION            0
             290 LOAD_CONST              31 ('InvalidOperation')
             292 LOAD_NAME               32 (DecimalException)
             294 UNPACK_SEQUENCE          3
             298 CALL                     3
             306 CACHE
             308 STORE_NAME              34 (InvalidOperation)

 256         310 PUSH_NULL
             312 LOAD_BUILD_CLASS
             314 LOAD_CONST              32 (<code object ConversionSyntax at 0x000001E77EBD9990, file "_pydecimal.py", line 256>)
             316 MAKE_FUNCTION            0
             318 LOAD_CONST              33 ('ConversionSyntax')
             320 LOAD_NAME               34 (InvalidOperation)
             322 UNPACK_SEQUENCE          3
             326 CALL                     3
             334 CACHE
             336 STORE_NAME              35 (ConversionSyntax)

 266         338 PUSH_NULL
             340 LOAD_BUILD_CLASS
             342 LOAD_CONST              34 (<code object DivisionByZero at 0x000001E77EBD9B50, file "_pydecimal.py", line 266>)
             344 MAKE_FUNCTION            0
             346 LOAD_CONST              35 ('DivisionByZero')
             348 LOAD_NAME               32 (DecimalException)
             350 LOAD_NAME               36 (ZeroDivisionError)
             352 UNPACK_SEQUENCE          4
             356 CALL                     4
             364 CACHE
             366 STORE_NAME              37 (DivisionByZero)

 282         368 PUSH_NULL
             370 LOAD_BUILD_CLASS
             372 LOAD_CONST              36 (<code object DivisionImpossible at 0x000001E77EBD9C30, file "_pydecimal.py", line 282>)
             374 MAKE_FUNCTION            0
             376 LOAD_CONST              37 ('DivisionImpossible')
             378 LOAD_NAME               34 (InvalidOperation)
             380 UNPACK_SEQUENCE          3
             384 CALL                     3
             392 CACHE
             394 STORE_NAME              38 (DivisionImpossible)

 293         396 PUSH_NULL
             398 LOAD_BUILD_CLASS
             400 LOAD_CONST              38 (<code object DivisionUndefined at 0x000001E77EBD9D10, file "_pydecimal.py", line 293>)
             402 MAKE_FUNCTION            0
             404 LOAD_CONST              39 ('DivisionUndefined')
             406 LOAD_NAME               34 (InvalidOperation)
             408 LOAD_NAME               36 (ZeroDivisionError)
             410 UNPACK_SEQUENCE          4
             414 CALL                     4
             422 CACHE
             424 STORE_NAME              39 (DivisionUndefined)

 304         426 PUSH_NULL
             428 LOAD_BUILD_CLASS
             430 LOAD_CONST              40 (<code object Inexact at 0x000001E77EBD9DF0, file "_pydecimal.py", line 304>)
             432 MAKE_FUNCTION            0
             434 LOAD_CONST              41 ('Inexact')
             436 LOAD_NAME               32 (DecimalException)
             438 UNPACK_SEQUENCE          3
             442 CALL                     3
             450 CACHE
             452 STORE_NAME              40 (Inexact)

 316         454 PUSH_NULL
             456 LOAD_BUILD_CLASS
             458 LOAD_CONST              42 (<code object InvalidContext at 0x000001E77EBD9ED0, file "_pydecimal.py", line 316>)
             460 MAKE_FUNCTION            0
             462 LOAD_CONST              43 ('InvalidContext')
             464 LOAD_NAME               34 (InvalidOperation)
             466 UNPACK_SEQUENCE          3
             470 CALL                     3
             478 CACHE
             480 STORE_NAME              41 (InvalidContext)

 330         482 PUSH_NULL
             484 LOAD_BUILD_CLASS
             486 LOAD_CONST              44 (<code object Rounded at 0x000001E77EBD9FB0, file "_pydecimal.py", line 330>)
             488 MAKE_FUNCTION            0
             490 LOAD_CONST              45 ('Rounded')
             492 LOAD_NAME               32 (DecimalException)
             494 UNPACK_SEQUENCE          3
             498 CALL                     3
             506 CACHE
             508 STORE_NAME              42 (Rounded)

 342         510 PUSH_NULL
             512 LOAD_BUILD_CLASS
             514 LOAD_CONST              46 (<code object Subnormal at 0x000001E77EBDA090, file "_pydecimal.py", line 342>)
             516 MAKE_FUNCTION            0
             518 LOAD_CONST              47 ('Subnormal')
             520 LOAD_NAME               32 (DecimalException)
             522 UNPACK_SEQUENCE          3
             526 CALL                     3
             534 CACHE
             536 STORE_NAME              43 (Subnormal)

 353         538 PUSH_NULL
             540 LOAD_BUILD_CLASS
             542 LOAD_CONST              48 (<code object Overflow at 0x000001E77EBDA170, file "_pydecimal.py", line 353>)
             544 MAKE_FUNCTION            0
             546 LOAD_CONST              49 ('Overflow')
             548 LOAD_NAME               40 (Inexact)
             550 LOAD_NAME               42 (Rounded)
             552 UNPACK_SEQUENCE          4
             556 CALL                     4
             564 CACHE
             566 STORE_NAME              44 (Overflow)

 391         568 PUSH_NULL
             570 LOAD_BUILD_CLASS
             572 LOAD_CONST              50 (<code object Underflow at 0x000001E77EBDA250, file "_pydecimal.py", line 391>)
             574 MAKE_FUNCTION            0
             576 LOAD_CONST              51 ('Underflow')
             578 LOAD_NAME               40 (Inexact)
             580 LOAD_NAME               42 (Rounded)
             582 LOAD_NAME               43 (Subnormal)
             584 UNPACK_SEQUENCE          5
             588 CALL                     5
             596 CACHE
             598 STORE_NAME              45 (Underflow)

 406         600 PUSH_NULL
             602 LOAD_BUILD_CLASS
             604 LOAD_CONST              52 (<code object FloatOperation at 0x000001E77EBDA330, file "_pydecimal.py", line 406>)
             606 MAKE_FUNCTION            0
             608 LOAD_CONST              53 ('FloatOperation')
             610 LOAD_NAME               32 (DecimalException)
             612 LOAD_NAME               46 (TypeError)
             614 UNPACK_SEQUENCE          4
             618 CALL                     4
             626 CACHE
             628 STORE_NAME              47 (FloatOperation)

 422         630 LOAD_NAME               33 (Clamped)
             632 LOAD_NAME               37 (DivisionByZero)
             634 LOAD_NAME               40 (Inexact)
             636 LOAD_NAME               44 (Overflow)
             638 LOAD_NAME               42 (Rounded)

 423         640 LOAD_NAME               45 (Underflow)
             642 LOAD_NAME               34 (InvalidOperation)
             644 LOAD_NAME               43 (Subnormal)
             646 LOAD_NAME               47 (FloatOperation)

 422         648 BUILD_LIST               9
             650 STORE_NAME              48 (_signals)

 426         652 LOAD_NAME               35 (ConversionSyntax)
             654 LOAD_NAME               34 (InvalidOperation)

 427         656 LOAD_NAME               38 (DivisionImpossible)
             658 LOAD_NAME               34 (InvalidOperation)

 428         660 LOAD_NAME               39 (DivisionUndefined)
             662 LOAD_NAME               34 (InvalidOperation)

 429         664 LOAD_NAME               41 (InvalidContext)
             666 LOAD_NAME               34 (InvalidOperation)

 426         668 BUILD_MAP                4
             670 STORE_NAME              49 (_condition_map)

 432         672 LOAD_NAME               16 (ROUND_DOWN)
             674 LOAD_NAME               17 (ROUND_HALF_UP)
             676 LOAD_NAME               18 (ROUND_HALF_EVEN)
             678 LOAD_NAME               19 (ROUND_CEILING)

 433         680 LOAD_NAME               20 (ROUND_FLOOR)
             682 LOAD_NAME               21 (ROUND_UP)
             684 LOAD_NAME               22 (ROUND_HALF_DOWN)
             686 LOAD_NAME               23 (ROUND_05UP)

 432         688 BUILD_TUPLE              8
             690 STORE_NAME              50 (_rounding_modes)

 440         692 LOAD_CONST               5 (0)
             694 LOAD_CONST               6 (None)
             696 IMPORT_NAME             51 (contextvars)
             698 STORE_NAME              51 (contextvars)

 442         700 PUSH_NULL
             702 LOAD_NAME               51 (contextvars)
             704 LOAD_ATTR               52 (maxsize)
             724 CACHE
             726 CACHE
             728 CACHE
             730 STORE_NAME              53 (_current_context_var)

 444         732 PUSH_NULL
             734 LOAD_NAME               54 (frozenset)

 445         736 BUILD_LIST               0
             738 LOAD_CONST              55 (('prec', 'Emin', 'Emax', 'capitals', 'clamp', 'rounding', 'flags', 'traps'))
             740 LIST_EXTEND              1

 444         742 UNPACK_SEQUENCE          1
             746 CALL                     1
             754 CACHE
             756 STORE_NAME              55 (_context_attributes)

 448         758 LOAD_CONST              56 (<code object getcontext at 0x000001E77E79D2E0, file "_pydecimal.py", line 448>)
             760 MAKE_FUNCTION            0
             762 STORE_NAME              56 (getcontext)

 462         764 LOAD_CONST              57 (<code object setcontext at 0x000001E77E720330, file "_pydecimal.py", line 462>)
             766 MAKE_FUNCTION            0
             768 STORE_NAME              57 (setcontext)

 469         770 DELETE_NAME             51 (contextvars)

 471         772 LOAD_CONST             117 ((None,))
             774 LOAD_CONST              58 (<code object localcontext at 0x000001E77E6DD890, file "_pydecimal.py", line 471>)
             776 MAKE_FUNCTION            1 (defaults)
             778 STORE_NAME              58 (localcontext)

 523         780 PUSH_NULL
             782 LOAD_BUILD_CLASS
             784 LOAD_CONST              59 (<code object Decimal at 0x000001E77E9D8040, file "_pydecimal.py", line 523>)
             786 MAKE_FUNCTION            0
             788 LOAD_CONST              60 ('Decimal')
             790 LOAD_NAME               59 (object)
             792 UNPACK_SEQUENCE          3
             796 CALL                     3
             804 CACHE
             806 STORE_NAME              60 (Decimal)

3844         808 LOAD_CONST             118 ((False,))
             810 LOAD_CONST              62 (<code object _dec_from_triple at 0x000001E77E76BD70, file "_pydecimal.py", line 3844>)
             812 MAKE_FUNCTION            1 (defaults)
             814 STORE_NAME              61 (_dec_from_triple)

3863         816 LOAD_NAME                9 (_numbers)
             818 LOAD_ATTR               62 (ArithmeticError)
             838 CACHE
             840 CACHE
             842 CACHE
             844 CACHE
             846 CACHE
             848 CACHE
             850 LOAD_NAME               60 (Decimal)
             852 UNPACK_SEQUENCE          1
             856 CALL                     1
             864 CACHE
             866 POP_TOP

3868         868 PUSH_NULL
             870 LOAD_BUILD_CLASS
             872 LOAD_CONST              63 (<code object _ContextManager at 0x000001E77EBCFA50, file "_pydecimal.py", line 3868>)
             874 MAKE_FUNCTION            0
             876 LOAD_CONST              64 ('_ContextManager')
             878 LOAD_NAME               59 (object)
             880 UNPACK_SEQUENCE          3
             884 CALL                     3
             892 CACHE
             894 STORE_NAME              64 (_ContextManager)

3883         896 PUSH_NULL
             898 LOAD_BUILD_CLASS
             900 LOAD_CONST              65 (<code object Context at 0x000001E77E9D6000, file "_pydecimal.py", line 3883>)
             902 MAKE_FUNCTION            0
             904 LOAD_CONST              66 ('Context')
             906 LOAD_NAME               59 (object)
             908 UNPACK_SEQUENCE          3
             912 CALL                     3
             920 CACHE
             922 STORE_NAME              65 (Context)

5628         924 PUSH_NULL
             926 LOAD_BUILD_CLASS
             928 LOAD_CONST              67 (<code object _WorkRep at 0x000001E77EBDB4B0, file "_pydecimal.py", line 5628>)
             930 MAKE_FUNCTION            0
             932 LOAD_CONST              68 ('_WorkRep')
             934 LOAD_NAME               59 (object)
             936 UNPACK_SEQUENCE          3
             940 CALL                     3
             948 CACHE
             950 STORE_NAME              66 (_WorkRep)

5654         952 LOAD_CONST             119 ((0,))
             954 LOAD_CONST              69 (<code object _normalize at 0x000001E77E8FE2A0, file "_pydecimal.py", line 5654>)
             956 MAKE_FUNCTION            1 (defaults)
             958 STORE_NAME              67 (_normalize)

5684         960 LOAD_NAME               68 (int)
             962 LOAD_ATTR               69 (NULL|self + InvalidOperation)
             982 MAKE_FUNCTION            0
             984 STORE_NAME              72 (_sqrt_nearest)

5722         986 LOAD_CONST              72 (<code object _rshift_nearest at 0x000001E77EC44AD0, file "_pydecimal.py", line 5722>)
             988 MAKE_FUNCTION            0
             990 STORE_NAME              73 (_rshift_nearest)

5730         992 LOAD_CONST              73 (<code object _div_nearest at 0x000001E77EC44BE0, file "_pydecimal.py", line 5730>)
             994 MAKE_FUNCTION            0
             996 STORE_NAME              74 (_div_nearest)

5738         998 LOAD_CONST             120 ((8,))
            1000 LOAD_CONST              75 (<code object _ilog at 0x000001E77E9D65F0, file "_pydecimal.py", line 5738>)
            1002 MAKE_FUNCTION            1 (defaults)
            1004 STORE_NAME              75 (_ilog)

5786        1006 LOAD_CONST              76 (<code object _dlog10 at 0x000001E77E9EF730, file "_pydecimal.py", line 5786>)
            1008 MAKE_FUNCTION            0
            1010 STORE_NAME              76 (_dlog10)

5820        1012 LOAD_CONST              77 (<code object _dlog at 0x000001E77E9D6990, file "_pydecimal.py", line 5820>)
            1014 MAKE_FUNCTION            0
            1016 STORE_NAME              77 (_dlog)

5864        1018 PUSH_NULL
            1020 LOAD_BUILD_CLASS
            1022 LOAD_CONST              78 (<code object _Log10Memoize at 0x000001E77EBDB750, file "_pydecimal.py", line 5864>)
            1024 MAKE_FUNCTION            0
            1026 LOAD_CONST              79 ('_Log10Memoize')
            1028 LOAD_NAME               59 (object)
            1030 UNPACK_SEQUENCE          3
            1034 CALL                     3
            1042 CACHE
            1044 STORE_NAME              78 (_Log10Memoize)

5899        1046 PUSH_NULL
            1048 LOAD_NAME               78 (_Log10Memoize)
            1050 UNPACK_SEQUENCE          0
            1054 CALL                     0
            1062 CACHE
            1064 LOAD_ATTR               79 (NULL|self + DivisionUndefined)

5938        1084 LOAD_CONST              81 (<code object _dexp at 0x000001E77E9D6C40, file "_pydecimal.py", line 5938>)
            1086 MAKE_FUNCTION            0
            1088 STORE_NAME              82 (_dexp)

5974        1090 LOAD_CONST              82 (<code object _dpower at 0x000001E77E8A65B0, file "_pydecimal.py", line 5974>)
            1092 MAKE_FUNCTION            0
            1094 STORE_NAME              83 (_dpower)

6017        1096 LOAD_CONST              83 (100)
            1098 LOAD_CONST              84 (70)
            1100 LOAD_CONST              85 (53)
            1102 LOAD_CONST              86 (40)
            1104 LOAD_CONST              87 (31)

6018        1106 LOAD_CONST              88 (23)
            1108 LOAD_CONST              89 (16)
            1110 LOAD_CONST              90 (10)
            1112 LOAD_CONST              91 (5)

6016        1114 LOAD_CONST              92 (('1', '2', '3', '4', '5', '6', '7', '8', '9'))
            1116 BUILD_CONST_KEY_MAP      9
            1118 BUILD_TUPLE              1
            1120 LOAD_CONST              93 (<code object _log10_lb at 0x000001E77EC4C030, file "_pydecimal.py", line 6016>)
            1122 MAKE_FUNCTION            1 (defaults)
            1124 STORE_NAME              84 (_log10_lb)

6027        1126 LOAD_CONST             121 ((False, False))
            1128 LOAD_CONST              94 (<code object _convert_other at 0x000001E77E6BED80, file "_pydecimal.py", line 6027>)
            1130 MAKE_FUNCTION            1 (defaults)
            1132 STORE_NAME              85 (_convert_other)

6046        1134 LOAD_CONST             118 ((False,))
            1136 LOAD_CONST              95 (<code object _convert_for_comparison at 0x000001E77E8A6B20, file "_pydecimal.py", line 6046>)
            1138 MAKE_FUNCTION            1 (defaults)
            1140 STORE_NAME              86 (_convert_for_comparison)

6088        1142 PUSH_NULL
            1144 LOAD_NAME               65 (Context)

6089        1146 LOAD_CONST              96 (28)
            1148 LOAD_NAME               18 (ROUND_HALF_EVEN)

6090        1150 LOAD_NAME               37 (DivisionByZero)
            1152 LOAD_NAME               44 (Overflow)
            1154 LOAD_NAME               34 (InvalidOperation)
            1156 BUILD_LIST               3

6091        1158 BUILD_LIST               0

6092        1160 LOAD_CONST              97 (999999)

6093        1162 LOAD_CONST              98 (-999999)

6094        1164 LOAD_CONST              25 (1)

6095        1166 LOAD_CONST               5 (0)

6088        1168 KW_NAMES                99 (('prec', 'rounding', 'traps', 'flags', 'Emax', 'Emin', 'capitals', 'clamp'))
            1170 UNPACK_SEQUENCE          8
            1174 CALL                     8
            1182 CACHE
            1184 STORE_NAME              87 (DefaultContext)

6103        1186 PUSH_NULL
            1188 LOAD_NAME               65 (Context)

6104        1190 LOAD_CONST             100 (9)
            1192 LOAD_NAME               17 (ROUND_HALF_UP)

6105        1194 LOAD_NAME               37 (DivisionByZero)
            1196 LOAD_NAME               44 (Overflow)
            1198 LOAD_NAME               34 (InvalidOperation)
            1200 LOAD_NAME               33 (Clamped)
            1202 LOAD_NAME               45 (Underflow)
            1204 BUILD_LIST               5

6106        1206 BUILD_LIST               0

6103        1208 KW_NAMES               101 (('prec', 'rounding', 'traps', 'flags'))
            1210 UNPACK_SEQUENCE          4
            1214 CALL                     4
            1222 CACHE
            1224 STORE_NAME              88 (BasicContext)

6109        1226 PUSH_NULL
            1228 LOAD_NAME               65 (Context)

6110        1230 LOAD_CONST             100 (9)
            1232 LOAD_NAME               18 (ROUND_HALF_EVEN)

6111        1234 BUILD_LIST               0

6112        1236 BUILD_LIST               0

6109        1238 KW_NAMES               101 (('prec', 'rounding', 'traps', 'flags'))
            1240 UNPACK_SEQUENCE          4
            1244 CALL                     4
            1252 CACHE
            1254 STORE_NAME              89 (ExtendedContext)

6130        1256 LOAD_CONST               5 (0)
            1258 LOAD_CONST               6 (None)
            1260 IMPORT_NAME             90 (re)
            1262 STORE_NAME              90 (re)

6131        1264 PUSH_NULL
            1266 LOAD_NAME               90 (re)
            1268 LOAD_ATTR               91 (NULL|self + Underflow)
            1288 CACHE
            1290 CACHE
            1292 LOAD_NAME               90 (re)
            1294 LOAD_ATTR               93 (NULL|self + TypeError)
            1314 CACHE
            1316 CACHE
            1318 CACHE
            1320 CACHE

6148        1322 LOAD_ATTR               94 (FloatOperation)
            1342 CACHE
            1344 CACHE
            1346 CACHE
            1348 LOAD_CONST             103 ('0*$')
            1350 UNPACK_SEQUENCE          1
            1354 CALL                     1
            1362 CACHE
            1364 LOAD_ATTR               94 (FloatOperation)
            1384 CACHE
            1386 CACHE
            1388 CACHE
            1390 LOAD_CONST             104 ('50*$')
            1392 UNPACK_SEQUENCE          1
            1396 CALL                     1
            1404 CACHE
            1406 LOAD_ATTR               94 (FloatOperation)
            1426 CACHE
            1428 CACHE
            1430 CACHE
            1432 LOAD_CONST             105 ('\\A\n(?:\n   (?P<fill>.)?\n   (?P<align>[<>=^])\n)?\n(?P<sign>[-+ ])?\n(?P<no_neg_0>z)?\n(?P<alt>\\#)?\n(?P<zeropad>0)?\n(?P<minimumwidth>(?!0)\\d+)?\n(?P<thousands_sep>,)?\n(?:\\.(?P<precision>0|(?!0)\\d+))?\n(?P<type>[eEfFgGn%])?\n\\Z\n')

6176        1434 LOAD_NAME               90 (re)
            1436 LOAD_ATTR               92 (TypeError)
            1456 CACHE
            1458 BINARY_OP                7 (|)

6162        1462 UNPACK_SEQUENCE          2
            1466 CALL                     2
            1474 CACHE
            1476 STORE_NAME              99 (_parse_format_specifier_regex)

6178        1478 DELETE_NAME             90 (re)

6183        1480 NOP

6184        1482 LOAD_CONST               5 (0)
            1484 LOAD_CONST               6 (None)
            1486 IMPORT_NAME            100 (locale)
            1488 STORE_NAME             101 (_locale)
            1490 JUMP_FORWARD            11 (to 1514)
         >> 1492 PUSH_EXC_INFO

6185        1494 LOAD_NAME               15 (ImportError)
            1496 CHECK_EXC_MATCH
            1498 POP_JUMP_IF_FALSE        3 (to 1506)
            1500 POP_TOP

6186        1502 POP_EXCEPT
            1504 JUMP_FORWARD             4 (to 1514)

6185     >> 1506 RERAISE                  0
         >> 1508 COPY                     3
            1510 POP_EXCEPT
            1512 RERAISE                  1

6188     >> 1514 LOAD_CONST             117 ((None,))
            1516 LOAD_CONST             106 (<code object _parse_format_specifier at 0x000001E77E9E1980, file "_pydecimal.py", line 6188>)
            1518 MAKE_FUNCTION            1 (defaults)
            1520 STORE_NAME             102 (_parse_format_specifier)

6268        1522 LOAD_CONST             107 (<code object _format_align at 0x000001E77E9DCAE0, file "_pydecimal.py", line 6268>)
            1524 MAKE_FUNCTION            0
            1526 STORE_NAME             103 (_format_align)

6295        1528 LOAD_CONST             108 (<code object _group_lengths at 0x000001E77E73F2F0, file "_pydecimal.py", line 6295>)
            1530 MAKE_FUNCTION            0
            1532 STORE_NAME             104 (_group_lengths)

6318        1534 LOAD_CONST             122 ((1,))
            1536 LOAD_CONST             109 (<code object _insert_thousands_sep at 0x000001E77E8A7D20, file "_pydecimal.py", line 6318>)
            1538 MAKE_FUNCTION            1 (defaults)
            1540 STORE_NAME             105 (_insert_thousands_sep)

6355        1542 LOAD_CONST             110 (<code object _format_sign at 0x000001E77EC1D630, file "_pydecimal.py", line 6355>)
            1544 MAKE_FUNCTION            0
            1546 STORE_NAME             106 (_format_sign)

6365        1548 LOAD_CONST             111 (<code object _format_number at 0x000001E77E9F8480, file "_pydecimal.py", line 6365>)
            1550 MAKE_FUNCTION            0
            1552 STORE_NAME             107 (_format_number)

6406        1554 PUSH_NULL
            1556 LOAD_NAME               60 (Decimal)
            1558 LOAD_CONST             112 ('Inf')
            1560 UNPACK_SEQUENCE          1
            1564 CALL                     1
            1572 CACHE
            1574 STORE_NAME             108 (_Infinity)

6407        1576 PUSH_NULL
            1578 LOAD_NAME               60 (Decimal)
            1580 LOAD_CONST             113 ('-Inf')
            1582 UNPACK_SEQUENCE          1
            1586 CALL                     1
            1594 CACHE
            1596 STORE_NAME             109 (_NegativeInfinity)

6408        1598 PUSH_NULL
            1600 LOAD_NAME               60 (Decimal)
            1602 LOAD_CONST             114 ('NaN')
            1604 UNPACK_SEQUENCE          1
            1608 CALL                     1
            1616 CACHE
            1618 STORE_NAME             110 (_NaN)

6409        1620 PUSH_NULL
            1622 LOAD_NAME               60 (Decimal)
            1624 LOAD_CONST               5 (0)
            1626 UNPACK_SEQUENCE          1
            1630 CALL                     1
            1638 CACHE
            1640 STORE_NAME             111 (_Zero)

6410        1642 PUSH_NULL
            1644 LOAD_NAME               60 (Decimal)
            1646 LOAD_CONST              25 (1)
            1648 UNPACK_SEQUENCE          1
            1652 CALL                     1
            1660 CACHE
            1662 STORE_NAME             112 (_One)

6411        1664 PUSH_NULL
            1666 LOAD_NAME               60 (Decimal)
            1668 LOAD_CONST             115 (-1)
            1670 UNPACK_SEQUENCE          1
            1674 CALL                     1
            1682 CACHE
            1684 STORE_NAME             113 (_NegativeOne)

6414        1686 LOAD_NAME              108 (_Infinity)
            1688 LOAD_NAME              109 (_NegativeInfinity)
            1690 BUILD_TUPLE              2
            1692 STORE_NAME             114 (_SignedInfinity)

6418        1694 LOAD_NAME               10 (sys)
            1696 LOAD_ATTR              115 (NULL|self + setcontext)
            1716 STORE_NAME             117 (_PyHASH_MODULUS)

6420        1718 LOAD_NAME               10 (sys)
            1720 LOAD_ATTR              115 (NULL|self + setcontext)
            1740 STORE_NAME             119 (_PyHASH_INF)

6421        1742 LOAD_NAME               10 (sys)
            1744 LOAD_ATTR              115 (NULL|self + setcontext)
            1764 STORE_NAME             121 (_PyHASH_NAN)

6424        1766 PUSH_NULL
            1768 LOAD_NAME              122 (pow)
            1770 LOAD_CONST              90 (10)
            1772 LOAD_NAME              117 (_PyHASH_MODULUS)
            1774 LOAD_CONST             116 (2)
            1776 BINARY_OP               10 (-)
            1780 LOAD_NAME              117 (_PyHASH_MODULUS)
            1782 UNPACK_SEQUENCE          3
            1786 CALL                     3
            1794 CACHE
            1796 STORE_NAME             123 (_PyHASH_10INV)

6425        1798 DELETE_NAME             10 (sys)
            1800 LOAD_CONST               6 (None)
            1802 RETURN_VALUE
ExceptionTable:
  56 to 90 -> 94 [0]
  94 to 108 -> 116 [1] lasti
  114 to 114 -> 116 [1] lasti
  1482 to 1488 -> 1492 [0]
  1492 to 1500 -> 1508 [1] lasti
  1506 to 1506 -> 1508 [1] lasti

Disassembly of <code object <lambda> at 0x000001E77EBC6660, file "_pydecimal.py", line 164>:
164           0 RESUME                   0
              2 LOAD_FAST                0 (args)
              4 RETURN_VALUE

Disassembly of <code object DecimalException at 0x000001E77EBD9610, file "_pydecimal.py", line 192>:
192           0 RESUME                   0
              2 LOAD_NAME                0 (__name__)
              4 STORE_NAME               1 (__module__)
              6 LOAD_CONST               0 ('DecimalException')
              8 STORE_NAME               2 (__qualname__)

193          10 LOAD_CONST               1 ("Base exception class.\n\n    Used exceptions derive from this.\n    If an exception derives from another exception besides this (such as\n    Underflow (Inexact, Rounded, Subnormal) that indicates that it is only\n    called if the others are present.  This isn't actually used for\n    anything, though.\n\n    handle  -- Called when context._raise_error is called and the\n               trap_enabler is not set.  First argument is self, second is the\n               context.  More arguments can be given, those being after\n               the explanation in _raise_error (For example,\n               context._raise_error(NewError, '(-x)!', self._sign) would\n               call NewError().handle(context, self._sign).)\n\n    To define a new exception, it should be sufficient to have it derive\n    from DecimalException.\n    ")
             12 STORE_NAME               3 (__doc__)

211          14 LOAD_CONST               2 (<code object handle at 0x000001E77EBC6730, file "_pydecimal.py", line 211>)
             16 MAKE_FUNCTION            0
             18 STORE_NAME               4 (handle)
             20 LOAD_CONST               3 (None)
             22 RETURN_VALUE

Disassembly of <code object handle at 0x000001E77EBC6730, file "_pydecimal.py", line 211>:
211           0 RESUME                   0

212           2 LOAD_CONST               0 (None)
              4 RETURN_VALUE

Disassembly of <code object Clamped at 0x000001E77EBD97D0, file "_pydecimal.py", line 215>:
215           0 RESUME                   0
              2 LOAD_NAME                0 (__name__)
              4 STORE_NAME               1 (__module__)
              6 LOAD_CONST               0 ('Clamped')
              8 STORE_NAME               2 (__qualname__)

216          10 LOAD_CONST               1 ('Exponent of a 0 changed to fit bounds.\n\n    This occurs and signals clamped if the exponent of a result has been\n    altered in order to fit the constraints of a specific concrete\n    representation.  This may occur when the exponent of a zero result would\n    be outside the bounds of a representation, or when a large normal\n    number would have an encoded exponent that cannot be represented.  In\n    this latter case, the exponent is reduced to fit and the corresponding\n    number of zero digits are appended to the coefficient ("fold-down").\n    ')
             12 STORE_NAME               3 (__doc__)
             14 LOAD_CONST               2 (None)
             16 RETURN_VALUE

Disassembly of <code object InvalidOperation at 0x000001E77EBD98B0, file "_pydecimal.py", line 227>:
227           0 RESUME                   0
              2 LOAD_NAME                0 (__name__)
              4 STORE_NAME               1 (__module__)
              6 LOAD_CONST               0 ('InvalidOperation')
              8 STORE_NAME               2 (__qualname__)

228          10 LOAD_CONST               1 ('An invalid operation was performed.\n\n    Various bad things cause this:\n\n    Something creates a signaling NaN\n    -INF + INF\n    0 * (+-)INF\n    (+-)INF / (+-)INF\n    x % 0\n    (+-)INF % x\n    x._rescale( non-integer )\n    sqrt(-x) , x > 0\n    0 ** 0\n    x ** (non-integer)\n    x ** (+-)INF\n    An operand is invalid\n\n    The result of the operation after these is a quiet positive NaN,\n    except when the cause is a signaling NaN, in which case the result is\n    also a quiet NaN, but with the original sign, and an optional\n    diagnostic information.\n    ')
             12 STORE_NAME               3 (__doc__)

250          14 LOAD_CONST               2 (<code object handle at 0x000001E77E6D63A0, file "_pydecimal.py", line 250>)
             16 MAKE_FUNCTION            0
             18 STORE_NAME               4 (handle)
             20 LOAD_CONST               3 (None)
             22 RETURN_VALUE

Disassembly of <code object handle at 0x000001E77E6D63A0, file "_pydecimal.py", line 250>:
250           0 RESUME                   0

251           2 LOAD_FAST                2 (args)
              4 POP_JUMP_IF_FALSE       61 (to 128)

252           6 LOAD_GLOBAL              1 (NULL + _dec_from_triple)
             16 CACHE
             18 LOAD_FAST                2 (args)
             20 LOAD_CONST               1 (0)
             22 BINARY_SUBSCR
             26 CACHE
             28 CACHE
             30 CACHE
             32 LOAD_ATTR                1 (NULL|self + _dec_from_triple)
             52 CACHE
             54 CACHE
             56 LOAD_ATTR                2 (_sign)
             76 CACHE
             78 CACHE
             80 CACHE
             82 CACHE
             84 STORE_FAST               3 (ans)

253          86 LOAD_FAST                3 (ans)
             88 STORE_SUBSCR
             92 CACHE
             94 CACHE
             96 CACHE
             98 CACHE
            100 CACHE
            102 CACHE
            104 CACHE
            106 CACHE
            108 CACHE
            110 LOAD_FAST                1 (context)
            112 UNPACK_SEQUENCE          1
            116 CALL                     1
            124 CACHE
            126 RETURN_VALUE

254     >>  128 LOAD_GLOBAL              8 (_NaN)
            138 CACHE
            140 RETURN_VALUE

Disassembly of <code object ConversionSyntax at 0x000001E77EBD9990, file "_pydecimal.py", line 256>:
256           0 RESUME                   0
              2 LOAD_NAME                0 (__name__)
              4 STORE_NAME               1 (__module__)
              6 LOAD_CONST               0 ('ConversionSyntax')
              8 STORE_NAME               2 (__qualname__)

257          10 LOAD_CONST               1 ('Trying to convert badly formed string.\n\n    This occurs and signals invalid-operation if a string is being\n    converted to a number and it does not conform to the numeric string\n    syntax.  The result is [0,qNaN].\n    ')
             12 STORE_NAME               3 (__doc__)

263          14 LOAD_CONST               2 (<code object handle at 0x000001E77EBC7430, file "_pydecimal.py", line 263>)
             16 MAKE_FUNCTION            0
             18 STORE_NAME               4 (handle)
             20 LOAD_CONST               3 (None)
             22 RETURN_VALUE

Disassembly of <code object handle at 0x000001E77EBC7430, file "_pydecimal.py", line 263>:
263           0 RESUME                   0

264           2 LOAD_GLOBAL              0 (_NaN)
             12 CACHE
             14 RETURN_VALUE

Disassembly of <code object DivisionByZero at 0x000001E77EBD9B50, file "_pydecimal.py", line 266>:
266           0 RESUME                   0
              2 LOAD_NAME                0 (__name__)
              4 STORE_NAME               1 (__module__)
              6 LOAD_CONST               0 ('DivisionByZero')
              8 STORE_NAME               2 (__qualname__)

267          10 LOAD_CONST               1 ('Division by 0.\n\n    This occurs and signals division-by-zero if division of a finite number\n    by zero was attempted (during a divide-integer or divide operation, or a\n    power operation with negative right-hand operand), and the dividend was\n    not zero.\n\n    The result of the operation is [sign,inf], where sign is the exclusive\n    or of the signs of the operands for divide, or is 1 for an odd power of\n    -0, for power.\n    ')
             12 STORE_NAME               3 (__doc__)

279          14 LOAD_CONST               2 (<code object handle at 0x000001E77EBD9A70, file "_pydecimal.py", line 279>)
             16 MAKE_FUNCTION            0
             18 STORE_NAME               4 (handle)
             20 LOAD_CONST               3 (None)
             22 RETURN_VALUE

Disassembly of <code object handle at 0x000001E77EBD9A70, file "_pydecimal.py", line 279>:
279           0 RESUME                   0

280           2 LOAD_GLOBAL              0 (_SignedInfinity)
             12 CACHE
             14 LOAD_FAST                2 (sign)
             16 BINARY_SUBSCR
             20 CACHE
             22 CACHE
             24 CACHE
             26 RETURN_VALUE

Disassembly of <code object DivisionImpossible at 0x000001E77EBD9C30, file "_pydecimal.py", line 282>:
282           0 RESUME                   0
              2 LOAD_NAME                0 (__name__)
              4 STORE_NAME               1 (__module__)
              6 LOAD_CONST               0 ('DivisionImpossible')
              8 STORE_NAME               2 (__qualname__)

283          10 LOAD_CONST               1 ('Cannot perform the division adequately.\n\n    This occurs and signals invalid-operation if the integer result of a\n    divide-integer or remainder operation had too many digits (would be\n    longer than precision).  The result is [0,qNaN].\n    ')
             12 STORE_NAME               3 (__doc__)

290          14 LOAD_CONST               2 (<code object handle at 0x000001E77EBC7500, file "_pydecimal.py", line 290>)
             16 MAKE_FUNCTION            0
             18 STORE_NAME               4 (handle)
             20 LOAD_CONST               3 (None)
             22 RETURN_VALUE

Disassembly of <code object handle at 0x000001E77EBC7500, file "_pydecimal.py", line 290>:
290           0 RESUME                   0

291           2 LOAD_GLOBAL              0 (_NaN)
             12 CACHE
             14 RETURN_VALUE

Disassembly of <code object DivisionUndefined at 0x000001E77EBD9D10, file "_pydecimal.py", line 293>:
293           0 RESUME                   0
              2 LOAD_NAME                0 (__name__)
              4 STORE_NAME               1 (__module__)
              6 LOAD_CONST               0 ('DivisionUndefined')
              8 STORE_NAME               2 (__qualname__)

294          10 LOAD_CONST               1 ('Undefined result of division.\n\n    This occurs and signals invalid-operation if division by zero was\n    attempted (during a divide-integer, divide, or remainder operation), and\n    the dividend is also zero.  The result is [0,qNaN].\n    ')
             12 STORE_NAME               3 (__doc__)

301          14 LOAD_CONST               2 (<code object handle at 0x000001E77EBC75D0, file "_pydecimal.py", line 301>)
             16 MAKE_FUNCTION            0
             18 STORE_NAME               4 (handle)
             20 LOAD_CONST               3 (None)
             22 RETURN_VALUE

Disassembly of <code object handle at 0x000001E77EBC75D0, file "_pydecimal.py", line 301>:
301           0 RESUME                   0

302           2 LOAD_GLOBAL              0 (_NaN)
             12 CACHE
             14 RETURN_VALUE

Disassembly of <code object Inexact at 0x000001E77EBD9DF0, file "_pydecimal.py", line 304>:
304           0 RESUME                   0
              2 LOAD_NAME                0 (__name__)
              4 STORE_NAME               1 (__module__)
              6 LOAD_CONST               0 ('Inexact')
              8 STORE_NAME               2 (__qualname__)

305          10 LOAD_CONST               1 ('Had to round, losing information.\n\n    This occurs and signals inexact whenever the result of an operation is\n    not exact (that is, it needed to be rounded and any discarded digits\n    were non-zero), or if an overflow or underflow condition occurs.  The\n    result in all cases is unchanged.\n\n    The inexact signal may be tested (or trapped) to determine if a given\n    operation (or sequence of operations) was inexact.\n    ')
             12 STORE_NAME               3 (__doc__)
             14 LOAD_CONST               2 (None)
             16 RETURN_VALUE

Disassembly of <code object InvalidContext at 0x000001E77EBD9ED0, file "_pydecimal.py", line 316>:
316           0 RESUME                   0
              2 LOAD_NAME                0 (__name__)
              4 STORE_NAME               1 (__module__)
              6 LOAD_CONST               0 ('InvalidContext')
              8 STORE_NAME               2 (__qualname__)

317          10 LOAD_CONST               1 ('Invalid context.  Unknown rounding, for example.\n\n    This occurs and signals invalid-operation if an invalid context was\n    detected during an operation.  This can occur if contexts are not checked\n    on creation and either the precision exceeds the capability of the\n    underlying concrete representation or an unknown or unsupported rounding\n    was specified.  These aspects of the context need only be checked when\n    the values are required to be used.  The result is [0,qNaN].\n    ')
             12 STORE_NAME               3 (__doc__)

327          14 LOAD_CONST               2 (<code object handle at 0x000001E77EBC76A0, file "_pydecimal.py", line 327>)
             16 MAKE_FUNCTION            0
             18 STORE_NAME               4 (handle)
             20 LOAD_CONST               3 (None)
             22 RETURN_VALUE

Disassembly of <code object handle at 0x000001E77EBC76A0, file "_pydecimal.py", line 327>:
327           0 RESUME                   0

328           2 LOAD_GLOBAL              0 (_NaN)
             12 CACHE
             14 RETURN_VALUE

Disassembly of <code object Rounded at 0x000001E77EBD9FB0, file "_pydecimal.py", line 330>:
330           0 RESUME                   0
              2 LOAD_NAME                0 (__name__)
              4 STORE_NAME               1 (__module__)
              6 LOAD_CONST               0 ('Rounded')
              8 STORE_NAME               2 (__qualname__)

331          10 LOAD_CONST               1 ('Number got rounded (not  necessarily changed during rounding).\n\n    This occurs and signals rounded whenever the result of an operation is\n    rounded (that is, some zero or non-zero digits were discarded from the\n    coefficient), or if an overflow or underflow condition occurs.  The\n    result in all cases is unchanged.\n\n    The rounded signal may be tested (or trapped) to determine if a given\n    operation (or sequence of operations) caused a loss of precision.\n    ')
             12 STORE_NAME               3 (__doc__)
             14 LOAD_CONST               2 (None)
             16 RETURN_VALUE

Disassembly of <code object Subnormal at 0x000001E77EBDA090, file "_pydecimal.py", line 342>:
342           0 RESUME                   0
              2 LOAD_NAME                0 (__name__)
              4 STORE_NAME               1 (__module__)
              6 LOAD_CONST               0 ('Subnormal')
              8 STORE_NAME               2 (__qualname__)

343          10 LOAD_CONST               1 ('Exponent < Emin before rounding.\n\n    This occurs and signals subnormal whenever the result of a conversion or\n    operation is subnormal (that is, its adjusted exponent is less than\n    Emin, before any rounding).  The result in all cases is unchanged.\n\n    The subnormal signal may be tested (or trapped) to determine if a given\n    or operation (or sequence of operations) yielded a subnormal result.\n    ')
             12 STORE_NAME               3 (__doc__)
             14 LOAD_CONST               2 (None)
             16 RETURN_VALUE

Disassembly of <code object Overflow at 0x000001E77EBDA170, file "_pydecimal.py", line 353>:
353           0 RESUME                   0
              2 LOAD_NAME                0 (__name__)
              4 STORE_NAME               1 (__module__)
              6 LOAD_CONST               0 ('Overflow')
              8 STORE_NAME               2 (__qualname__)

354          10 LOAD_CONST               1 ('Numerical overflow.\n\n    This occurs and signals overflow if the adjusted exponent of a result\n    (from a conversion or from an operation that is not an attempt to divide\n    by zero), after rounding, would be greater than the largest value that\n    can be handled by the implementation (the value Emax).\n\n    The result depends on the rounding mode:\n\n    For round-half-up and round-half-even (and for round-half-down and\n    round-up, if implemented), the result of the operation is [sign,inf],\n    where sign is the sign of the intermediate result.  For round-down, the\n    result is the largest finite number that can be represented in the\n    current precision, with the sign of the intermediate result.  For\n    round-ceiling, the result is the same as for round-down if the sign of\n    the intermediate result is 1, or is [0,inf] otherwise.  For round-floor,\n    the result is the same as for round-down if the sign of the intermediate\n    result is 0, or is [1,inf] otherwise.  In all cases, Inexact and Rounded\n    will also be raised.\n    ')
             12 STORE_NAME               3 (__doc__)

375          14 LOAD_CONST               2 (<code object handle at 0x000001E77E8FE760, file "_pydecimal.py", line 375>)
             16 MAKE_FUNCTION            0
             18 STORE_NAME               4 (handle)
             20 LOAD_CONST               3 (None)
             22 RETURN_VALUE

Disassembly of <code object handle at 0x000001E77E8FE760, file "_pydecimal.py", line 375>:
375           0 RESUME                   0

376           2 LOAD_FAST                1 (context)
              4 LOAD_ATTR                0 (rounding)
             24 CACHE
             26 LOAD_GLOBAL              4 (ROUND_HALF_EVEN)
             36 CACHE

377          38 LOAD_GLOBAL              6 (ROUND_HALF_DOWN)
             48 CACHE
             50 LOAD_GLOBAL              8 (ROUND_UP)
             60 CACHE

376          62 BUILD_TUPLE              4
             64 CONTAINS_OP              0
             66 POP_JUMP_IF_FALSE       13 (to 94)

378          68 LOAD_GLOBAL             10 (_SignedInfinity)
             78 CACHE
             80 LOAD_FAST                2 (sign)
             82 BINARY_SUBSCR
             86 CACHE
             88 CACHE
             90 CACHE
             92 RETURN_VALUE

379     >>   94 LOAD_FAST                2 (sign)
             96 LOAD_CONST               1 (0)
             98 COMPARE_OP               2 (<)
            102 CACHE
            104 POP_JUMP_IF_FALSE       70 (to 246)

380         106 LOAD_FAST                1 (context)
            108 LOAD_ATTR                0 (rounding)
            128 CACHE
            130 COMPARE_OP               2 (<)
            134 CACHE
            136 POP_JUMP_IF_FALSE       13 (to 164)

381         138 LOAD_GLOBAL             10 (_SignedInfinity)
            148 CACHE
            150 LOAD_FAST                2 (sign)
            152 BINARY_SUBSCR
            156 CACHE
            158 CACHE
            160 CACHE
            162 RETURN_VALUE

382     >>  164 LOAD_GLOBAL             15 (NULL + _dec_from_triple)
            174 CACHE
            176 LOAD_FAST                2 (sign)
            178 LOAD_CONST               2 ('9')
            180 LOAD_FAST                1 (context)
            182 LOAD_ATTR                8 (ROUND_UP)
            202 CACHE
            204 CACHE
            206 CACHE
            208 LOAD_FAST                1 (context)
            210 LOAD_ATTR                8 (ROUND_UP)

382         230 UNPACK_SEQUENCE          3
            234 CALL                     3
            242 CACHE
            244 RETURN_VALUE

384     >>  246 LOAD_FAST                2 (sign)
            248 LOAD_CONST               3 (1)
            250 COMPARE_OP               2 (<)
            254 CACHE
            256 POP_JUMP_IF_FALSE       70 (to 398)

385         258 LOAD_FAST                1 (context)
            260 LOAD_ATTR                0 (rounding)
            280 CACHE
            282 COMPARE_OP               2 (<)
            286 CACHE
            288 POP_JUMP_IF_FALSE       13 (to 316)

386         290 LOAD_GLOBAL             10 (_SignedInfinity)
            300 CACHE
            302 LOAD_FAST                2 (sign)
            304 BINARY_SUBSCR
            308 CACHE
            310 CACHE
            312 CACHE
            314 RETURN_VALUE

387     >>  316 LOAD_GLOBAL             15 (NULL + _dec_from_triple)
            326 CACHE
            328 LOAD_FAST                2 (sign)
            330 LOAD_CONST               2 ('9')
            332 LOAD_FAST                1 (context)
            334 LOAD_ATTR                8 (ROUND_UP)
            354 CACHE
            356 CACHE
            358 CACHE
            360 LOAD_FAST                1 (context)
            362 LOAD_ATTR                8 (ROUND_UP)

387         382 UNPACK_SEQUENCE          3
            386 CALL                     3
            394 CACHE
            396 RETURN_VALUE

384     >>  398 LOAD_CONST               0 (None)
            400 RETURN_VALUE

Disassembly of <code object Underflow at 0x000001E77EBDA250, file "_pydecimal.py", line 391>:
391           0 RESUME                   0
              2 LOAD_NAME                0 (__name__)
              4 STORE_NAME               1 (__module__)
              6 LOAD_CONST               0 ('Underflow')
              8 STORE_NAME               2 (__qualname__)

392          10 LOAD_CONST               1 ('Numerical underflow with result rounded to 0.\n\n    This occurs and signals underflow if a result is inexact and the\n    adjusted exponent of the result would be smaller (more negative) than\n    the smallest value that can be handled by the implementation (the value\n    Emin).  That is, the result is both inexact and subnormal.\n\n    The result after an underflow will be a subnormal number rounded, if\n    necessary, so that its exponent is not less than Etiny.  This may result\n    in 0 with the sign of the intermediate result and an exponent of Etiny.\n\n    In all cases, Inexact, Rounded, and Subnormal will also be raised.\n    ')
             12 STORE_NAME               3 (__doc__)
             14 LOAD_CONST               2 (None)
             16 RETURN_VALUE

Disassembly of <code object FloatOperation at 0x000001E77EBDA330, file "_pydecimal.py", line 406>:
406           0 RESUME                   0
              2 LOAD_NAME                0 (__name__)
              4 STORE_NAME               1 (__module__)
              6 LOAD_CONST               0 ('FloatOperation')
              8 STORE_NAME               2 (__qualname__)

407          10 LOAD_CONST               1 ('Enable stricter semantics for mixing floats and Decimals.\n\n    If the signal is not trapped (default), mixing floats and Decimals is\n    permitted in the Decimal() constructor, context.create_decimal() and\n    all comparison operators. Both conversion and comparisons are exact.\n    Any occurrence of a mixed operation is silently recorded by setting\n    FloatOperation in the context flags.  Explicit conversions with\n    Decimal.from_float() or context.create_decimal_from_float() do not\n    set the flag.\n\n    Otherwise (the signal is trapped), only equality comparisons and explicit\n    conversions are silent. All other mixed operations raise FloatOperation.\n    ')
             12 STORE_NAME               3 (__doc__)
             14 LOAD_CONST               2 (None)
             16 RETURN_VALUE

Disassembly of <code object getcontext at 0x000001E77E79D2E0, file "_pydecimal.py", line 448>:
448           0 RESUME                   0

455           2 NOP

456           4 LOAD_GLOBAL              0 (_current_context_var)
             14 CACHE
             16 STORE_SUBSCR
             20 CACHE
             22 CACHE
             24 CACHE
             26 CACHE
             28 CACHE
             30 CACHE
             32 CACHE
             34 CACHE
             36 CACHE
             38 UNPACK_SEQUENCE          0
             42 CALL                     0
             50 CACHE
             52 RETURN_VALUE
        >>   54 PUSH_EXC_INFO

457          56 LOAD_GLOBAL              4 (LookupError)
             66 CACHE
             68 CHECK_EXC_MATCH
             70 POP_JUMP_IF_FALSE       45 (to 162)
             72 POP_TOP

458          74 LOAD_GLOBAL              7 (NULL + Context)
             84 CACHE
             86 UNPACK_SEQUENCE          0
             90 CALL                     0
             98 CACHE
            100 STORE_FAST               0 (context)

459         102 LOAD_GLOBAL              0 (_current_context_var)
            112 CACHE
            114 STORE_SUBSCR
            118 CACHE
            120 CACHE
            122 CACHE
            124 CACHE
            126 CACHE
            128 CACHE
            130 CACHE
            132 CACHE
            134 CACHE
            136 LOAD_FAST                0 (context)
            138 UNPACK_SEQUENCE          1
            142 CALL                     1
            150 CACHE
            152 POP_TOP

460         154 LOAD_FAST                0 (context)
            156 SWAP                     2
            158 POP_EXCEPT
            160 RETURN_VALUE

457     >>  162 RERAISE                  0
        >>  164 COPY                     3
            166 POP_EXCEPT
            168 RERAISE                  1
ExceptionTable:
  4 to 50 -> 54 [0]
  54 to 156 -> 164 [1] lasti
  162 to 162 -> 164 [1] lasti

Disassembly of <code object setcontext at 0x000001E77E720330, file "_pydecimal.py", line 462>:
462           0 RESUME                   0

464           2 LOAD_FAST                0 (context)
              4 LOAD_GLOBAL              0 (DefaultContext)
             14 CACHE
             16 LOAD_GLOBAL              2 (BasicContext)
             26 CACHE
             28 LOAD_GLOBAL              4 (ExtendedContext)
             38 CACHE
             40 BUILD_TUPLE              3
             42 CONTAINS_OP              0
             44 POP_JUMP_IF_FALSE       40 (to 126)

465          46 LOAD_FAST                0 (context)
             48 STORE_SUBSCR
             52 CACHE
             54 CACHE
             56 CACHE
             58 CACHE
             60 CACHE
             62 CACHE
             64 CACHE
             66 CACHE
             68 CACHE
             70 UNPACK_SEQUENCE          0
             74 CALL                     0
             82 CACHE
             84 STORE_FAST               0 (context)

466          86 LOAD_FAST                0 (context)
             88 STORE_SUBSCR
             92 CACHE
             94 CACHE
             96 CACHE
             98 CACHE
            100 CACHE
            102 CACHE
            104 CACHE
            106 CACHE
            108 CACHE
            110 UNPACK_SEQUENCE          0
            114 CALL                     0
            122 CACHE
            124 POP_TOP

467     >>  126 LOAD_GLOBAL             10 (_current_context_var)
            136 CACHE
            138 STORE_SUBSCR
            142 CACHE
            144 CACHE
            146 CACHE
            148 CACHE
            150 CACHE
            152 CACHE
            154 CACHE
            156 CACHE
            158 CACHE
            160 LOAD_FAST                0 (context)
            162 UNPACK_SEQUENCE          1
            166 CALL                     1
            174 CACHE
            176 POP_TOP
            178 LOAD_CONST               1 (None)
            180 RETURN_VALUE

Disassembly of <code object localcontext at 0x000001E77E6DD890, file "_pydecimal.py", line 471>:
471           0 RESUME                   0

507           2 LOAD_FAST                0 (ctx)
              4 POP_JUMP_IF_NOT_NONE    14 (to 34)

508           6 LOAD_GLOBAL              1 (NULL + getcontext)
             16 CACHE
             18 UNPACK_SEQUENCE          0
             22 CALL                     0
             30 CACHE
             32 STORE_FAST               0 (ctx)

509     >>   34 LOAD_GLOBAL              3 (NULL + _ContextManager)
             44 CACHE
             46 LOAD_FAST                0 (ctx)
             48 UNPACK_SEQUENCE          1
             52 CALL                     1
             60 CACHE
             62 STORE_FAST               2 (ctx_manager)

510          64 LOAD_FAST                1 (kwargs)
             66 STORE_SUBSCR
             70 CACHE
             72 CACHE
             74 CACHE
             76 CACHE
             78 CACHE
             80 CACHE
             82 CACHE
             84 CACHE
             86 CACHE
             88 UNPACK_SEQUENCE          0
             92 CALL                     0
            100 CACHE
            102 GET_ITER
        >>  104 FOR_ITER                55 (to 218)
            108 CACHE
            110 STORE_FAST               3 (key)
            112 STORE_FAST               4 (value)

511         114 LOAD_FAST                3 (key)
            116 LOAD_GLOBAL              6 (_context_attributes)
            126 CACHE
            128 CONTAINS_OP              1
            130 POP_JUMP_IF_FALSE       19 (to 170)

512         132 LOAD_GLOBAL              9 (NULL + TypeError)
            142 CACHE
            144 LOAD_CONST               2 ("'")
            146 LOAD_FAST                3 (key)
            148 FORMAT_VALUE             0
            150 LOAD_CONST               3 ("' is an invalid keyword argument for this function")
            152 BUILD_STRING             3
            154 UNPACK_SEQUENCE          1
            158 CALL                     1
            166 CACHE
            168 RAISE_VARARGS            1

513     >>  170 LOAD_GLOBAL             11 (NULL + setattr)
            180 CACHE
            182 LOAD_FAST                2 (ctx_manager)
            184 LOAD_ATTR                6 (_context_attributes)
            204 CACHE
            206 CACHE
            208 CACHE
            210 CACHE
            212 POP_TOP
            214 JUMP_BACKWARD           56 (to 104)

514         216 LOAD_FAST                2 (ctx_manager)
        >>  218 RETURN_VALUE

Disassembly of <code object Decimal at 0x000001E77E9D8040, file "_pydecimal.py", line 523>:
 523           0 RESUME                   0
               2 LOAD_NAME                0 (__name__)
               4 STORE_NAME               1 (__module__)
               6 LOAD_CONST               0 ('Decimal')
               8 STORE_NAME               2 (__qualname__)

 524          10 LOAD_CONST               1 ('Floating point class for decimal arithmetic.')
              12 STORE_NAME               3 (__doc__)

 526          14 LOAD_CONST               2 (('_exp', '_int', '_sign', '_is_special'))
              16 STORE_NAME               4 (__slots__)

 532          18 LOAD_CONST             125 (('0', None))
              20 LOAD_CONST               5 (<code object __new__ at 0x000001E77C47F100, file "_pydecimal.py", line 532>)
              22 MAKE_FUNCTION            1 (defaults)
              24 STORE_NAME               5 (__new__)

 682          26 LOAD_NAME                6 (classmethod)

 683          28 LOAD_CONST               6 (<code object from_float at 0x000001E77C47FC50, file "_pydecimal.py", line 682>)
              30 MAKE_FUNCTION            0

 682          32 UNPACK_SEQUENCE          0
              36 CALL                     0
              44 CACHE

 683          46 STORE_NAME               7 (from_float)

 727          48 LOAD_CONST               7 (<code object _isnan at 0x000001E77EBF9AC0, file "_pydecimal.py", line 727>)
              50 MAKE_FUNCTION            0
              52 STORE_NAME               8 (_isnan)

 742          54 LOAD_CONST               8 (<code object _isinfinity at 0x000001E77EBC3C30, file "_pydecimal.py", line 742>)
              56 MAKE_FUNCTION            0
              58 STORE_NAME               9 (_isinfinity)

 755          60 LOAD_CONST             126 ((None, None))
              62 LOAD_CONST               9 (<code object _check_nans at 0x000001E77C470520, file "_pydecimal.py", line 755>)
              64 MAKE_FUNCTION            1 (defaults)
              66 STORE_NAME              10 (_check_nans)

 787          68 LOAD_CONST              10 (<code object _compare_check_nans at 0x000001E77C470760, file "_pydecimal.py", line 787>)
              70 MAKE_FUNCTION            0
              72 STORE_NAME              11 (_compare_check_nans)

 820          74 LOAD_CONST              11 (<code object __bool__ at 0x000001E77EBCE4C0, file "_pydecimal.py", line 820>)
              76 MAKE_FUNCTION            0
              78 STORE_NAME              12 (__bool__)

 827          80 LOAD_CONST              12 (<code object _cmp at 0x000001E77C4806F0, file "_pydecimal.py", line 827>)
              82 MAKE_FUNCTION            0
              84 STORE_NAME              13 (_cmp)

 891          86 LOAD_CONST             127 ((None,))
              88 LOAD_CONST              13 (<code object __eq__ at 0x000001E77E79DB80, file "_pydecimal.py", line 891>)
              90 MAKE_FUNCTION            1 (defaults)
              92 STORE_NAME              14 (__eq__)

 899          94 LOAD_CONST             127 ((None,))
              96 LOAD_CONST              14 (<code object __lt__ at 0x000001E77E79DCF0, file "_pydecimal.py", line 899>)
              98 MAKE_FUNCTION            1 (defaults)
             100 STORE_NAME              15 (__lt__)

 908         102 LOAD_CONST             127 ((None,))
             104 LOAD_CONST              15 (<code object __le__ at 0x000001E77E79DE60, file "_pydecimal.py", line 908>)
             106 MAKE_FUNCTION            1 (defaults)
             108 STORE_NAME              16 (__le__)

 917         110 LOAD_CONST             127 ((None,))
             112 LOAD_CONST              16 (<code object __gt__ at 0x000001E77E79DFD0, file "_pydecimal.py", line 917>)
             114 MAKE_FUNCTION            1 (defaults)
             116 STORE_NAME              17 (__gt__)

 926         118 LOAD_CONST             127 ((None,))
             120 LOAD_CONST              17 (<code object __ge__ at 0x000001E77E79E140, file "_pydecimal.py", line 926>)
             122 MAKE_FUNCTION            1 (defaults)
             124 STORE_NAME              18 (__ge__)

 935         126 LOAD_CONST             127 ((None,))
             128 LOAD_CONST              18 (<code object compare at 0x000001E77E720AB0, file "_pydecimal.py", line 935>)
             130 MAKE_FUNCTION            1 (defaults)
             132 STORE_NAME              19 (compare)

 953         134 LOAD_CONST              19 (<code object __hash__ at 0x000001E77C480A20, file "_pydecimal.py", line 953>)
             136 MAKE_FUNCTION            0
             138 STORE_NAME              20 (__hash__)

 979         140 LOAD_CONST              20 (<code object as_tuple at 0x000001E77E6D6640, file "_pydecimal.py", line 979>)
             142 MAKE_FUNCTION            0
             144 STORE_NAME              21 (as_tuple)

 986         146 LOAD_CONST              21 (<code object as_integer_ratio at 0x000001E77C47FFA0, file "_pydecimal.py", line 986>)
             148 MAKE_FUNCTION            0
             150 STORE_NAME              22 (as_integer_ratio)

1036         152 LOAD_CONST              22 (<code object __repr__ at 0x000001E77EBCE6A0, file "_pydecimal.py", line 1036>)
             154 MAKE_FUNCTION            0
             156 STORE_NAME              23 (__repr__)

1041         158 LOAD_CONST             128 ((False, None))
             160 LOAD_CONST              24 (<code object __str__ at 0x000001E77E9AD150, file "_pydecimal.py", line 1041>)
             162 MAKE_FUNCTION            1 (defaults)
             164 STORE_NAME              24 (__str__)

1093         166 LOAD_CONST             127 ((None,))
             168 LOAD_CONST              25 (<code object to_eng_string at 0x000001E77EBCE790, file "_pydecimal.py", line 1093>)
             170 MAKE_FUNCTION            1 (defaults)
             172 STORE_NAME              25 (to_eng_string)

1102         174 LOAD_CONST             127 ((None,))
             176 LOAD_CONST              26 (<code object __neg__ at 0x000001E77E6BE470, file "_pydecimal.py", line 1102>)
             178 MAKE_FUNCTION            1 (defaults)
             180 STORE_NAME              26 (__neg__)

1124         182 LOAD_CONST             127 ((None,))
             184 LOAD_CONST              27 (<code object __pos__ at 0x000001E77E73E330, file "_pydecimal.py", line 1124>)
             186 MAKE_FUNCTION            1 (defaults)
             188 STORE_NAME              27 (__pos__)

1145         190 LOAD_CONST             129 ((True, None))
             192 LOAD_CONST              29 (<code object __abs__ at 0x000001E77E6DDD70, file "_pydecimal.py", line 1145>)
             194 MAKE_FUNCTION            1 (defaults)
             196 STORE_NAME              28 (__abs__)

1167         198 LOAD_CONST             127 ((None,))
             200 LOAD_CONST              30 (<code object __add__ at 0x000001E77E9ADF70, file "_pydecimal.py", line 1167>)
             202 MAKE_FUNCTION            1 (defaults)
             204 STORE_NAME              29 (__add__)

1253         206 LOAD_NAME               29 (__add__)
             208 STORE_NAME              30 (__radd__)

1255         210 LOAD_CONST             127 ((None,))
             212 LOAD_CONST              31 (<code object __sub__ at 0x000001E77E6DDF10, file "_pydecimal.py", line 1255>)
             214 MAKE_FUNCTION            1 (defaults)
             216 STORE_NAME              31 (__sub__)

1269         218 LOAD_CONST             127 ((None,))
             220 LOAD_CONST              32 (<code object __rsub__ at 0x000001E77E76F0E0, file "_pydecimal.py", line 1269>)
             222 MAKE_FUNCTION            1 (defaults)
             224 STORE_NAME              32 (__rsub__)

1277         226 LOAD_CONST             127 ((None,))
             228 LOAD_CONST              33 (<code object __mul__ at 0x000001E77E9C3E80, file "_pydecimal.py", line 1277>)
             230 MAKE_FUNCTION            1 (defaults)
             232 STORE_NAME              33 (__mul__)

1332         234 LOAD_NAME               33 (__mul__)
             236 STORE_NAME              34 (__rmul__)

1334         238 LOAD_CONST             127 ((None,))
             240 LOAD_CONST              34 (<code object __truediv__ at 0x000001E77E958650, file "_pydecimal.py", line 1334>)
             242 MAKE_FUNCTION            1 (defaults)
             244 STORE_NAME              35 (__truediv__)

1393         246 LOAD_CONST              35 (<code object _divide at 0x000001E77E9BA340, file "_pydecimal.py", line 1393>)
             248 MAKE_FUNCTION            0
             250 STORE_NAME              36 (_divide)

1426         252 LOAD_CONST             127 ((None,))
             254 LOAD_CONST              36 (<code object __rtruediv__ at 0x000001E77E76F210, file "_pydecimal.py", line 1426>)
             256 MAKE_FUNCTION            1 (defaults)
             258 STORE_NAME              37 (__rtruediv__)

1433         260 LOAD_CONST             127 ((None,))
             262 LOAD_CONST              37 (<code object __divmod__ at 0x000001E77E9AFDE0, file "_pydecimal.py", line 1433>)
             264 MAKE_FUNCTION            1 (defaults)
             266 STORE_NAME              38 (__divmod__)

1469         268 LOAD_CONST             127 ((None,))
             270 LOAD_CONST              38 (<code object __rdivmod__ at 0x000001E77E76F340, file "_pydecimal.py", line 1469>)
             272 MAKE_FUNCTION            1 (defaults)
             274 STORE_NAME              39 (__rdivmod__)

1476         276 LOAD_CONST             127 ((None,))
             278 LOAD_CONST              39 (<code object __mod__ at 0x000001E77E9B0160, file "_pydecimal.py", line 1476>)
             280 MAKE_FUNCTION            1 (defaults)
             282 STORE_NAME              40 (__mod__)

1503         284 LOAD_CONST             127 ((None,))
             286 LOAD_CONST              40 (<code object __rmod__ at 0x000001E77E76F470, file "_pydecimal.py", line 1503>)
             288 MAKE_FUNCTION            1 (defaults)
             290 STORE_NAME              41 (__rmod__)

1510         292 LOAD_CONST             127 ((None,))
             294 LOAD_CONST              41 (<code object remainder_near at 0x000001E77E9B09D0, file "_pydecimal.py", line 1510>)
             296 MAKE_FUNCTION            1 (defaults)
             298 STORE_NAME              42 (remainder_near)

1585         300 LOAD_CONST             127 ((None,))
             302 LOAD_CONST              42 (<code object __floordiv__ at 0x000001E77E9B1280, file "_pydecimal.py", line 1585>)
             304 MAKE_FUNCTION            1 (defaults)
             306 STORE_NAME              43 (__floordiv__)

1613         308 LOAD_CONST             127 ((None,))
             310 LOAD_CONST              43 (<code object __rfloordiv__ at 0x000001E77E76F5A0, file "_pydecimal.py", line 1613>)
             312 MAKE_FUNCTION            1 (defaults)
             314 STORE_NAME              44 (__rfloordiv__)

1620         316 LOAD_CONST              44 (<code object __float__ at 0x000001E77EB6F3C0, file "_pydecimal.py", line 1620>)
             318 MAKE_FUNCTION            0
             320 STORE_NAME              45 (__float__)

1630         322 LOAD_CONST              45 (<code object __int__ at 0x000001E77E9B1560, file "_pydecimal.py", line 1630>)
             324 MAKE_FUNCTION            0
             326 STORE_NAME              46 (__int__)

1643         328 LOAD_NAME               46 (__int__)
             330 STORE_NAME              47 (__trunc__)

1645         332 LOAD_NAME               48 (property)

1646         334 LOAD_CONST              46 (<code object real at 0x000001E77EBC7D20, file "_pydecimal.py", line 1645>)
             336 MAKE_FUNCTION            0

1645         338 UNPACK_SEQUENCE          0
             342 CALL                     0
             350 CACHE

1646         352 STORE_NAME              49 (real)

1649         354 LOAD_NAME               48 (property)

1650         356 LOAD_CONST              47 (<code object imag at 0x000001E77EBDA6B0, file "_pydecimal.py", line 1649>)
             358 MAKE_FUNCTION            0

1649         360 UNPACK_SEQUENCE          0
             364 CALL                     0
             372 CACHE

1650         374 STORE_NAME              50 (imag)

1653         376 LOAD_CONST              48 (<code object conjugate at 0x000001E77EBC7DF0, file "_pydecimal.py", line 1653>)
             378 MAKE_FUNCTION            0
             380 STORE_NAME              51 (conjugate)

1656         382 LOAD_CONST              49 (<code object __complex__ at 0x000001E77EC1C230, file "_pydecimal.py", line 1656>)
             384 MAKE_FUNCTION            0
             386 STORE_NAME              52 (__complex__)

1659         388 LOAD_CONST              50 (<code object _fix_nan at 0x000001E77E6BE810, file "_pydecimal.py", line 1659>)
             390 MAKE_FUNCTION            0
             392 STORE_NAME              53 (_fix_nan)

1671         394 LOAD_CONST              51 (<code object _fix at 0x000001E77E9B2420, file "_pydecimal.py", line 1671>)
             396 MAKE_FUNCTION            0
             398 STORE_NAME              54 (_fix)

1773         400 LOAD_CONST              52 (<code object _round_down at 0x000001E77EC1C430, file "_pydecimal.py", line 1773>)
             402 MAKE_FUNCTION            0
             404 STORE_NAME              55 (_round_down)

1780         406 LOAD_CONST              53 (<code object _round_up at 0x000001E77EBCEC40, file "_pydecimal.py", line 1780>)
             408 MAKE_FUNCTION            0
             410 STORE_NAME              56 (_round_up)

1784         412 LOAD_CONST              54 (<code object _round_half_up at 0x000001E77EBF5D70, file "_pydecimal.py", line 1784>)
             414 MAKE_FUNCTION            0
             416 STORE_NAME              57 (_round_half_up)

1793         418 LOAD_CONST              55 (<code object _round_half_down at 0x000001E77EBF5E90, file "_pydecimal.py", line 1793>)
             420 MAKE_FUNCTION            0
             422 STORE_NAME              58 (_round_half_down)

1800         424 LOAD_CONST              56 (<code object _round_half_even at 0x000001E77E6D6790, file "_pydecimal.py", line 1800>)
             426 MAKE_FUNCTION            0
             428 STORE_NAME              59 (_round_half_even)

1808         430 LOAD_CONST              57 (<code object _round_ceiling at 0x000001E77E76F800, file "_pydecimal.py", line 1808>)
             432 MAKE_FUNCTION            0
             434 STORE_NAME              60 (_round_ceiling)

1815         436 LOAD_CONST              58 (<code object _round_floor at 0x000001E77E76F930, file "_pydecimal.py", line 1815>)
             438 MAKE_FUNCTION            0
             440 STORE_NAME              61 (_round_floor)

1822         442 LOAD_CONST              59 (<code object _round_05up at 0x000001E77E76AE70, file "_pydecimal.py", line 1822>)
             444 MAKE_FUNCTION            0
             446 STORE_NAME              62 (_round_05up)

1829         448 PUSH_NULL
             450 LOAD_NAME               63 (dict)

1830         452 LOAD_NAME               55 (_round_down)

1831         454 LOAD_NAME               56 (_round_up)

1832         456 LOAD_NAME               57 (_round_half_up)

1833         458 LOAD_NAME               58 (_round_half_down)

1834         460 LOAD_NAME               59 (_round_half_even)

1835         462 LOAD_NAME               60 (_round_ceiling)

1836         464 LOAD_NAME               61 (_round_floor)

1837         466 LOAD_NAME               62 (_round_05up)

1829         468 KW_NAMES                60 (('ROUND_DOWN', 'ROUND_UP', 'ROUND_HALF_UP', 'ROUND_HALF_DOWN', 'ROUND_HALF_EVEN', 'ROUND_CEILING', 'ROUND_FLOOR', 'ROUND_05UP'))
             470 UNPACK_SEQUENCE          8
             474 CALL                     8
             482 CACHE
             484 STORE_NAME              64 (_pick_rounding_function)

1840         486 LOAD_CONST             127 ((None,))
             488 LOAD_CONST              61 (<code object __round__ at 0x000001E77E9B2C10, file "_pydecimal.py", line 1840>)
             490 MAKE_FUNCTION            1 (defaults)
             492 STORE_NAME              65 (__round__)

1902         494 LOAD_CONST              62 (<code object __floor__ at 0x000001E77EB6F550, file "_pydecimal.py", line 1902>)
             496 MAKE_FUNCTION            0
             498 STORE_NAME              66 (__floor__)

1917         500 LOAD_CONST              63 (<code object __ceil__ at 0x000001E77EB6F6E0, file "_pydecimal.py", line 1917>)
             502 MAKE_FUNCTION            0
             504 STORE_NAME              67 (__ceil__)

1932         506 LOAD_CONST             127 ((None,))
             508 LOAD_CONST              64 (<code object fma at 0x000001E77E9B8580, file "_pydecimal.py", line 1932>)
             510 MAKE_FUNCTION            1 (defaults)
             512 STORE_NAME              68 (fma)

1976         514 LOAD_CONST             127 ((None,))
             516 LOAD_CONST              65 (<code object _power_modulo at 0x000001E77E9B4030, file "_pydecimal.py", line 1976>)
             518 MAKE_FUNCTION            1 (defaults)
             520 STORE_NAME              69 (_power_modulo)

2061         522 LOAD_CONST              66 (<code object _power_exact at 0x000001E77E9B5050, file "_pydecimal.py", line 2061>)
             524 MAKE_FUNCTION            0
             526 STORE_NAME              70 (_power_exact)

2298         528 LOAD_CONST             126 ((None, None))
             530 LOAD_CONST              67 (<code object __pow__ at 0x000001E77E9C8CC0, file "_pydecimal.py", line 2298>)
             532 MAKE_FUNCTION            1 (defaults)
             534 STORE_NAME              71 (__pow__)

2514         536 LOAD_CONST             127 ((None,))
             538 LOAD_CONST              68 (<code object __rpow__ at 0x000001E77E76FA60, file "_pydecimal.py", line 2514>)
             540 MAKE_FUNCTION            1 (defaults)
             542 STORE_NAME              72 (__rpow__)

2521         544 LOAD_CONST             127 ((None,))
             546 LOAD_CONST              69 (<code object normalize at 0x000001E77E9B34A0, file "_pydecimal.py", line 2521>)
             548 MAKE_FUNCTION            1 (defaults)
             550 STORE_NAME              73 (normalize)

2546         552 LOAD_CONST             126 ((None, None))
             554 LOAD_CONST              70 (<code object quantize at 0x000001E77E9C9990, file "_pydecimal.py", line 2546>)
             556 MAKE_FUNCTION            1 (defaults)
             558 STORE_NAME              74 (quantize)

2607         560 LOAD_CONST             127 ((None,))
             562 LOAD_CONST              71 (<code object same_quantum at 0x000001E77E73E4F0, file "_pydecimal.py", line 2607>)
             564 MAKE_FUNCTION            1 (defaults)
             566 STORE_NAME              75 (same_quantum)

2622         568 LOAD_CONST              72 (<code object _rescale at 0x000001E77E9CA010, file "_pydecimal.py", line 2622>)
             570 MAKE_FUNCTION            0
             572 STORE_NAME              76 (_rescale)

2656         574 LOAD_CONST              73 (<code object _round at 0x000001E77E9CA2F0, file "_pydecimal.py", line 2656>)
             576 MAKE_FUNCTION            0
             578 STORE_NAME              77 (_round)

2679         580 LOAD_CONST             126 ((None, None))
             582 LOAD_CONST              74 (<code object to_integral_exact at 0x000001E77E8FF800, file "_pydecimal.py", line 2679>)
             584 MAKE_FUNCTION            1 (defaults)
             586 STORE_NAME              78 (to_integral_exact)

2708         588 LOAD_CONST             126 ((None, None))
             590 LOAD_CONST              75 (<code object to_integral_value at 0x000001E77E73E870, file "_pydecimal.py", line 2708>)
             592 MAKE_FUNCTION            1 (defaults)
             594 STORE_NAME              79 (to_integral_value)

2725         596 LOAD_NAME               79 (to_integral_value)
             598 STORE_NAME              80 (to_integral)

2727         600 LOAD_CONST             127 ((None,))
             602 LOAD_CONST              76 (<code object sqrt at 0x000001E77E9CAC60, file "_pydecimal.py", line 2727>)
             604 MAKE_FUNCTION            1 (defaults)
             606 STORE_NAME              81 (sqrt)

2826         608 LOAD_CONST             127 ((None,))
             610 LOAD_CONST              77 (<code object max at 0x000001E77E9CB3C0, file "_pydecimal.py", line 2826>)
             612 MAKE_FUNCTION            1 (defaults)
             614 STORE_NAME              82 (max)

2868         616 LOAD_CONST             127 ((None,))
             618 LOAD_CONST              78 (<code object min at 0x000001E77E9CB8E0, file "_pydecimal.py", line 2868>)
             620 MAKE_FUNCTION            1 (defaults)
             622 STORE_NAME              83 (min)

2902         624 LOAD_CONST              79 (<code object _isinteger at 0x000001E77E6D6B80, file "_pydecimal.py", line 2902>)
             626 MAKE_FUNCTION            0
             628 STORE_NAME              84 (_isinteger)

2911         630 LOAD_CONST              80 (<code object _iseven at 0x000001E77EBFA120, file "_pydecimal.py", line 2911>)
             632 MAKE_FUNCTION            0
             634 STORE_NAME              85 (_iseven)

2917         636 LOAD_CONST              81 (<code object adjusted at 0x000001E77E76FCC0, file "_pydecimal.py", line 2917>)
             638 MAKE_FUNCTION            0
             640 STORE_NAME              86 (adjusted)

2925         642 LOAD_CONST              82 (<code object canonical at 0x000001E77EC20370, file "_pydecimal.py", line 2925>)
             644 MAKE_FUNCTION            0
             646 STORE_NAME              87 (canonical)

2933         648 LOAD_CONST             127 ((None,))
             650 LOAD_CONST              83 (<code object compare_signal at 0x000001E77E6D6CD0, file "_pydecimal.py", line 2933>)
             652 MAKE_FUNCTION            1 (defaults)
             654 STORE_NAME              88 (compare_signal)

2945         656 LOAD_CONST             127 ((None,))
             658 LOAD_CONST              84 (<code object compare_total at 0x000001E77E9BA780, file "_pydecimal.py", line 2945>)
             660 MAKE_FUNCTION            1 (defaults)
             662 STORE_NAME              89 (compare_total)

3018         664 LOAD_CONST             127 ((None,))
             666 LOAD_CONST              85 (<code object compare_total_mag at 0x000001E77E6E2550, file "_pydecimal.py", line 3018>)
             668 MAKE_FUNCTION            1 (defaults)
             670 STORE_NAME              90 (compare_total_mag)

3029         672 LOAD_CONST              86 (<code object copy_abs at 0x000001E77EBFA230, file "_pydecimal.py", line 3029>)
             674 MAKE_FUNCTION            0
             676 STORE_NAME              91 (copy_abs)

3033         678 LOAD_CONST              87 (<code object copy_negate at 0x000001E77E6E26B0, file "_pydecimal.py", line 3033>)
             680 MAKE_FUNCTION            0
             682 STORE_NAME              92 (copy_negate)

3040         684 LOAD_CONST             127 ((None,))
             686 LOAD_CONST              88 (<code object copy_sign at 0x000001E77E76FDF0, file "_pydecimal.py", line 3040>)
             688 MAKE_FUNCTION            1 (defaults)
             690 STORE_NAME              93 (copy_sign)

3046         692 LOAD_CONST             127 ((None,))
             694 LOAD_CONST              89 (<code object exp at 0x000001E77E959850, file "_pydecimal.py", line 3046>)
             696 MAKE_FUNCTION            1 (defaults)
             698 STORE_NAME              94 (exp)

3121         700 LOAD_CONST              90 (<code object is_canonical at 0x000001E77EC20440, file "_pydecimal.py", line 3121>)
             702 MAKE_FUNCTION            0
             704 STORE_NAME              95 (is_canonical)

3129         706 LOAD_CONST              91 (<code object is_finite at 0x000001E77EBDAA30, file "_pydecimal.py", line 3129>)
             708 MAKE_FUNCTION            0
             710 STORE_NAME              96 (is_finite)

3137         712 LOAD_CONST              92 (<code object is_infinite at 0x000001E77EBDAB10, file "_pydecimal.py", line 3137>)
             714 MAKE_FUNCTION            0
             716 STORE_NAME              97 (is_infinite)

3141         718 LOAD_CONST              93 (<code object is_nan at 0x000001E77EBDABF0, file "_pydecimal.py", line 3141>)
             720 MAKE_FUNCTION            0
             722 STORE_NAME              98 (is_nan)

3145         724 LOAD_CONST             127 ((None,))
             726 LOAD_CONST              94 (<code object is_normal at 0x000001E77E76B370, file "_pydecimal.py", line 3145>)
             728 MAKE_FUNCTION            1 (defaults)
             730 STORE_NAME              99 (is_normal)

3153         732 LOAD_CONST              95 (<code object is_qnan at 0x000001E77EBDACD0, file "_pydecimal.py", line 3153>)
             734 MAKE_FUNCTION            0
             736 STORE_NAME             100 (is_qnan)

3157         738 LOAD_CONST              96 (<code object is_signed at 0x000001E77EBDADB0, file "_pydecimal.py", line 3157>)
             740 MAKE_FUNCTION            0
             742 STORE_NAME             101 (is_signed)

3161         744 LOAD_CONST              97 (<code object is_snan at 0x000001E77EBDAE90, file "_pydecimal.py", line 3161>)
             746 MAKE_FUNCTION            0
             748 STORE_NAME             102 (is_snan)

3165         750 LOAD_CONST             127 ((None,))
             752 LOAD_CONST              98 (<code object is_subnormal at 0x000001E77E76B4B0, file "_pydecimal.py", line 3165>)
             754 MAKE_FUNCTION            1 (defaults)
             756 STORE_NAME             103 (is_subnormal)

3173         758 LOAD_CONST              99 (<code object is_zero at 0x000001E77EBCF3C0, file "_pydecimal.py", line 3173>)
             760 MAKE_FUNCTION            0
             762 STORE_NAME             104 (is_zero)

3177         764 LOAD_CONST             100 (<code object _ln_exp_bound at 0x000001E77E9C7ED0, file "_pydecimal.py", line 3177>)
             766 MAKE_FUNCTION            0
             768 STORE_NAME             105 (_ln_exp_bound)

3202         770 LOAD_CONST             127 ((None,))
             772 LOAD_CONST             101 (<code object ln at 0x000001E77E9B7040, file "_pydecimal.py", line 3202>)
             774 MAKE_FUNCTION            1 (defaults)
             776 STORE_NAME             106 (ln)

3252         778 LOAD_CONST             102 (<code object _log10_exp_bound at 0x000001E77E9C87A0, file "_pydecimal.py", line 3252>)
             780 MAKE_FUNCTION            0
             782 STORE_NAME             107 (_log10_exp_bound)

3282         784 LOAD_CONST             127 ((None,))
             786 LOAD_CONST             103 (<code object log10 at 0x000001E77E9C6480, file "_pydecimal.py", line 3282>)
             788 MAKE_FUNCTION            1 (defaults)
             790 STORE_NAME             108 (log10)

3333         792 LOAD_CONST             127 ((None,))
             794 LOAD_CONST             104 (<code object logb at 0x000001E77E6CB430, file "_pydecimal.py", line 3333>)
             796 MAKE_FUNCTION            1 (defaults)
             798 STORE_NAME             109 (logb)

3363         800 LOAD_CONST             105 (<code object _islogical at 0x000001E77EBF6310, file "_pydecimal.py", line 3363>)
             802 MAKE_FUNCTION            0
             804 STORE_NAME             110 (_islogical)

3377         806 LOAD_CONST             106 (<code object _fill_logical at 0x000001E77E73EA30, file "_pydecimal.py", line 3377>)
             808 MAKE_FUNCTION            0
             810 STORE_NAME             111 (_fill_logical)

3390         812 LOAD_CONST             127 ((None,))
             814 LOAD_CONST             107 (<code object logical_and at 0x000001E77E8FC540, file "_pydecimal.py", line 3390>)
             816 MAKE_FUNCTION            1 (defaults)
             818 STORE_NAME             112 (logical_and)

3407         820 LOAD_CONST             127 ((None,))
             822 LOAD_CONST             108 (<code object logical_invert at 0x000001E77E76B5F0, file "_pydecimal.py", line 3407>)
             824 MAKE_FUNCTION            1 (defaults)
             826 STORE_NAME             113 (logical_invert)

3414         828 LOAD_CONST             127 ((None,))
             830 LOAD_CONST             109 (<code object logical_or at 0x000001E77E8FC7C0, file "_pydecimal.py", line 3414>)
             832 MAKE_FUNCTION            1 (defaults)
             834 STORE_NAME             114 (logical_or)

3431         836 LOAD_CONST             127 ((None,))
             838 LOAD_CONST             110 (<code object logical_xor at 0x000001E77E8FCCC0, file "_pydecimal.py", line 3431>)
             840 MAKE_FUNCTION            1 (defaults)
             842 STORE_NAME             115 (logical_xor)

3448         844 LOAD_CONST             127 ((None,))
             846 LOAD_CONST             111 (<code object max_mag at 0x000001E77E9CD6F0, file "_pydecimal.py", line 3448>)
             848 MAKE_FUNCTION            1 (defaults)
             850 STORE_NAME             116 (max_mag)

3478         852 LOAD_CONST             127 ((None,))
             854 LOAD_CONST             112 (<code object min_mag at 0x000001E77E9CDA10, file "_pydecimal.py", line 3478>)
             856 MAKE_FUNCTION            1 (defaults)
             858 STORE_NAME             117 (min_mag)

3508         860 LOAD_CONST             127 ((None,))
             862 LOAD_CONST             113 (<code object next_minus at 0x000001E77E9CC230, file "_pydecimal.py", line 3508>)
             864 MAKE_FUNCTION            1 (defaults)
             866 STORE_NAME             118 (next_minus)

3531         868 LOAD_CONST             127 ((None,))
             870 LOAD_CONST             114 (<code object next_plus at 0x000001E77E9CC550, file "_pydecimal.py", line 3531>)
             872 MAKE_FUNCTION            1 (defaults)
             874 STORE_NAME             119 (next_plus)

3554         876 LOAD_CONST             127 ((None,))
             878 LOAD_CONST             115 (<code object next_toward at 0x000001E77E9B9680, file "_pydecimal.py", line 3554>)
             880 MAKE_FUNCTION            1 (defaults)
             882 STORE_NAME             120 (next_toward)

3600         884 LOAD_CONST             127 ((None,))
             886 LOAD_CONST             116 (<code object number_class at 0x000001E77E9CCC00, file "_pydecimal.py", line 3600>)
             888 MAKE_FUNCTION            1 (defaults)
             890 STORE_NAME             121 (number_class)

3642         892 LOAD_CONST             117 (<code object radix at 0x000001E77EBDAF70, file "_pydecimal.py", line 3642>)
             894 MAKE_FUNCTION            0
             896 STORE_NAME             122 (radix)

3646         898 LOAD_CONST             127 ((None,))
             900 LOAD_CONST             118 (<code object rotate at 0x000001E77E9CD110, file "_pydecimal.py", line 3646>)
             902 MAKE_FUNCTION            1 (defaults)
             904 STORE_NAME             123 (rotate)

3679         906 LOAD_CONST             127 ((None,))
             908 LOAD_CONST             119 (<code object scaleb at 0x000001E77E9CF240, file "_pydecimal.py", line 3679>)
             910 MAKE_FUNCTION            1 (defaults)
             912 STORE_NAME             124 (scaleb)

3704         914 LOAD_CONST             127 ((None,))
             916 LOAD_CONST             120 (<code object shift at 0x000001E77E9CF560, file "_pydecimal.py", line 3704>)
             918 MAKE_FUNCTION            1 (defaults)
             920 STORE_NAME             125 (shift)

3743         922 LOAD_CONST             121 (<code object __reduce__ at 0x000001E77EBCF780, file "_pydecimal.py", line 3743>)
             924 MAKE_FUNCTION            0
             926 STORE_NAME             126 (__reduce__)

3746         928 LOAD_CONST             122 (<code object __copy__ at 0x000001E77E76BAF0, file "_pydecimal.py", line 3746>)
             930 MAKE_FUNCTION            0
             932 STORE_NAME             127 (__copy__)

3751         934 LOAD_CONST             123 (<code object __deepcopy__ at 0x000001E77E76BC30, file "_pydecimal.py", line 3751>)
             936 MAKE_FUNCTION            0
             938 STORE_NAME             128 (__deepcopy__)

3758         940 LOAD_CONST             126 ((None, None))
             942 LOAD_CONST             124 (<code object __format__ at 0x000001E77E95C850, file "_pydecimal.py", line 3758>)
             944 MAKE_FUNCTION            1 (defaults)
             946 STORE_NAME             129 (__format__)
             948 LOAD_CONST               4 (None)
             950 RETURN_VALUE

Disassembly of <code object __new__ at 0x000001E77C47F100, file "_pydecimal.py", line 532>:
532           0 RESUME                   0

555           2 LOAD_GLOBAL              0 (object)
             12 CACHE
             14 STORE_SUBSCR
             18 CACHE
             20 CACHE
             22 CACHE
             24 CACHE
             26 CACHE
             28 CACHE
             30 CACHE
             32 CACHE
             34 CACHE
             36 LOAD_FAST                0 (cls)
             38 UNPACK_SEQUENCE          1
             42 CALL                     1
             50 CACHE
             52 STORE_FAST               3 (self)

559          54 LOAD_GLOBAL              5 (NULL + isinstance)
             64 CACHE
             66 LOAD_FAST                1 (value)
             68 LOAD_GLOBAL              6 (str)
             78 CACHE
             80 UNPACK_SEQUENCE          2
             84 CALL                     2
             92 CACHE
             94 EXTENDED_ARG             1
             96 POP_JUMP_IF_FALSE      427 (to 952)

560          98 LOAD_GLOBAL              9 (NULL + _parser)
            108 CACHE
            110 LOAD_FAST                1 (value)
            112 STORE_SUBSCR
            116 CACHE
            118 CACHE
            120 CACHE
            122 CACHE
            124 CACHE
            126 CACHE
            128 CACHE
            130 CACHE
            132 CACHE
            134 UNPACK_SEQUENCE          0
            138 CALL                     0
            146 CACHE
            148 STORE_SUBSCR
            152 CACHE
            154 CACHE
            156 CACHE
            158 CACHE
            160 CACHE
            162 CACHE
            164 CACHE
            166 CACHE
            168 CACHE
            170 LOAD_CONST               1 ('_')
            172 LOAD_CONST               2 ('')
            174 UNPACK_SEQUENCE          2
            178 CALL                     2
            186 CACHE
            188 UNPACK_SEQUENCE          1
            192 CALL                     1
            200 CACHE
            202 STORE_FAST               4 (m)

561         204 LOAD_FAST                4 (m)
            206 POP_JUMP_IF_NOT_NONE    46 (to 300)

562         208 LOAD_FAST                2 (context)
            210 POP_JUMP_IF_NOT_NONE    14 (to 240)

563         212 LOAD_GLOBAL             15 (NULL + getcontext)
            222 CACHE
            224 UNPACK_SEQUENCE          0
            228 CALL                     0
            236 CACHE
            238 STORE_FAST               2 (context)

564     >>  240 LOAD_FAST                2 (context)
            242 STORE_SUBSCR
            246 CACHE
            248 CACHE
            250 CACHE
            252 CACHE
            254 CACHE
            256 CACHE
            258 CACHE
            260 CACHE
            262 CACHE
            264 LOAD_GLOBAL             18 (ConversionSyntax)
            274 CACHE

565         276 LOAD_CONST               4 ('Invalid literal for Decimal: %r')
            278 LOAD_FAST                1 (value)
            280 BINARY_OP                6 (%)

564         284 UNPACK_SEQUENCE          2
            288 CALL                     2
            296 CACHE
            298 RETURN_VALUE

567     >>  300 LOAD_FAST                4 (m)
            302 STORE_SUBSCR
            306 CACHE
            308 CACHE
            310 CACHE
            312 CACHE
            314 CACHE
            316 CACHE
            318 CACHE
            320 CACHE
            322 CACHE
            324 LOAD_CONST               5 ('sign')
            326 UNPACK_SEQUENCE          1
            330 CALL                     1
            338 CACHE
            340 LOAD_CONST               6 ('-')
            342 COMPARE_OP               2 (<)
            346 CACHE
            348 POP_JUMP_IF_FALSE        8 (to 366)

568         350 LOAD_CONST               7 (1)
            352 LOAD_FAST                3 (self)
            354 STORE_ATTR              11 (_sign)
            364 JUMP_FORWARD             7 (to 380)

570     >>  366 LOAD_CONST               8 (0)
            368 LOAD_FAST                3 (self)
            370 STORE_ATTR              11 (_sign)

571     >>  380 LOAD_FAST                4 (m)
            382 STORE_SUBSCR
            386 CACHE
            388 CACHE
            390 CACHE
            392 CACHE
            394 CACHE
            396 CACHE
            398 CACHE
            400 CACHE
            402 CACHE
            404 LOAD_CONST               9 ('int')
            406 UNPACK_SEQUENCE          1
            410 CALL                     1
            418 CACHE
            420 STORE_FAST               5 (intpart)

572         422 LOAD_FAST                5 (intpart)
            424 POP_JUMP_IF_NONE       126 (to 678)

574         426 LOAD_FAST                4 (m)
            428 STORE_SUBSCR
            432 CACHE
            434 CACHE
            436 CACHE
            438 CACHE
            440 CACHE
            442 CACHE
            444 CACHE
            446 CACHE
            448 CACHE
            450 LOAD_CONST              10 ('frac')
            452 UNPACK_SEQUENCE          1
            456 CALL                     1
            464 CACHE
            466 LOAD_GLOBAL              1 (NULL + object)
            476 CACHE
            478 CACHE
            480 CACHE
            482 CACHE
            484 LOAD_FAST                4 (m)
            486 STORE_SUBSCR
            490 CACHE
            492 CACHE
            494 CACHE
            496 CACHE
            498 CACHE
            500 CACHE
            502 CACHE
            504 CACHE
            506 CACHE
            508 LOAD_CONST              11 ('exp')
            510 UNPACK_SEQUENCE          1
            514 CALL                     1
            522 CACHE
            524 LOAD_GLOBAL              1 (NULL + object)
            534 CACHE
            536 CACHE
            538 CACHE
            540 CACHE
            542 STORE_FAST               7 (exp)

576         544 LOAD_GLOBAL              7 (NULL + str)
            554 CACHE
            556 LOAD_GLOBAL             25 (NULL + int)
            566 CACHE
            568 LOAD_FAST                5 (intpart)
            570 LOAD_FAST                6 (fracpart)
            572 BINARY_OP                0 (+)
            576 UNPACK_SEQUENCE          1
            580 CALL                     1
            588 CACHE
            590 UNPACK_SEQUENCE          1
            594 CALL                     1
            602 CACHE
            604 LOAD_FAST                3 (self)
            606 STORE_ATTR              13 (_int)

577         616 LOAD_FAST                7 (exp)
            618 LOAD_GLOBAL             29 (NULL + len)
            628 CACHE
            630 LOAD_FAST                6 (fracpart)
            632 UNPACK_SEQUENCE          1
            636 CALL                     1
            644 CACHE
            646 BINARY_OP               10 (-)
            650 LOAD_FAST                3 (self)
            652 STORE_ATTR              15 (_exp)

578         662 LOAD_CONST              13 (False)
            664 LOAD_FAST                3 (self)
            666 STORE_ATTR              16 (_is_special)
            676 JUMP_FORWARD           135 (to 948)

580     >>  678 LOAD_FAST                4 (m)
            680 STORE_SUBSCR
            684 CACHE
            686 CACHE
            688 CACHE
            690 CACHE
            692 CACHE
            694 CACHE
            696 CACHE
            698 CACHE
            700 CACHE
            702 LOAD_CONST              14 ('diag')
            704 UNPACK_SEQUENCE          1
            708 CALL                     1
            716 CACHE
            718 STORE_FAST               8 (diag)

581         720 LOAD_FAST                8 (diag)
            722 POP_JUMP_IF_NONE        91 (to 906)

583         724 LOAD_GLOBAL              7 (NULL + str)
            734 CACHE
            736 LOAD_GLOBAL             25 (NULL + int)
            746 CACHE
            748 LOAD_FAST                8 (diag)
            750 LOAD_GLOBAL              1 (NULL + object)
            760 CACHE
            762 CACHE
            764 CACHE
            766 CACHE
            768 UNPACK_SEQUENCE          1
            772 CALL                     1
            780 CACHE
            782 STORE_SUBSCR
            786 CACHE
            788 CACHE
            790 CACHE
            792 CACHE
            794 CACHE
            796 CACHE
            798 CACHE
            800 CACHE
            802 CACHE
            804 LOAD_CONST              12 ('0')
            806 UNPACK_SEQUENCE          1
            810 CALL                     1
            818 CACHE
            820 LOAD_FAST                3 (self)
            822 STORE_ATTR              13 (_int)

584         832 LOAD_FAST                4 (m)
            834 STORE_SUBSCR
            838 CACHE
            840 CACHE
            842 CACHE
            844 CACHE
            846 CACHE
            848 CACHE
            850 CACHE
            852 CACHE
            854 CACHE
            856 LOAD_CONST              15 ('signal')
            858 UNPACK_SEQUENCE          1
            862 CALL                     1
            870 CACHE
            872 POP_JUMP_IF_FALSE        8 (to 890)

585         874 LOAD_CONST              16 ('N')
            876 LOAD_FAST                3 (self)
            878 STORE_ATTR              15 (_exp)
            888 JUMP_FORWARD            22 (to 934)

587     >>  890 LOAD_CONST              17 ('n')
            892 LOAD_FAST                3 (self)
            894 STORE_ATTR              15 (_exp)
            904 JUMP_FORWARD            14 (to 934)

590     >>  906 LOAD_CONST              12 ('0')
            908 LOAD_FAST                3 (self)
            910 STORE_ATTR              13 (_int)

591         920 LOAD_CONST              18 ('F')
            922 LOAD_FAST                3 (self)
            924 STORE_ATTR              15 (_exp)

592     >>  934 LOAD_CONST              19 (True)
            936 LOAD_FAST                3 (self)
            938 STORE_ATTR              16 (_is_special)

593     >>  948 LOAD_FAST                3 (self)
            950 RETURN_VALUE

596     >>  952 LOAD_GLOBAL              5 (NULL + isinstance)
            962 CACHE
            964 LOAD_FAST                1 (value)
            966 LOAD_GLOBAL             24 (int)
            976 CACHE
            978 UNPACK_SEQUENCE          2
            982 CALL                     2
            990 CACHE
            992 POP_JUMP_IF_FALSE       70 (to 1134)

597         994 LOAD_FAST                1 (value)
            996 LOAD_CONST               8 (0)
            998 COMPARE_OP               5 (<)
           1002 CACHE
           1004 POP_JUMP_IF_FALSE        8 (to 1022)

598        1006 LOAD_CONST               8 (0)
           1008 LOAD_FAST                3 (self)
           1010 STORE_ATTR              11 (_sign)
           1020 JUMP_FORWARD             7 (to 1036)

600     >> 1022 LOAD_CONST               7 (1)
           1024 LOAD_FAST                3 (self)
           1026 STORE_ATTR              11 (_sign)

601     >> 1036 LOAD_CONST               8 (0)
           1038 LOAD_FAST                3 (self)
           1040 STORE_ATTR              15 (_exp)

602        1050 LOAD_GLOBAL              7 (NULL + str)
           1060 CACHE
           1062 LOAD_GLOBAL             37 (NULL + abs)
           1072 CACHE
           1074 LOAD_FAST                1 (value)
           1076 UNPACK_SEQUENCE          1
           1080 CALL                     1
           1088 CACHE
           1090 UNPACK_SEQUENCE          1
           1094 CALL                     1
           1102 CACHE
           1104 LOAD_FAST                3 (self)
           1106 STORE_ATTR              13 (_int)

603        1116 LOAD_CONST              13 (False)
           1118 LOAD_FAST                3 (self)
           1120 STORE_ATTR              16 (_is_special)

604        1130 LOAD_FAST                3 (self)
           1132 RETURN_VALUE

607     >> 1134 LOAD_GLOBAL              5 (NULL + isinstance)
           1144 CACHE
           1146 LOAD_FAST                1 (value)
           1148 LOAD_GLOBAL             38 (Decimal)
           1158 CACHE
           1160 UNPACK_SEQUENCE          2
           1164 CALL                     2
           1172 CACHE
           1174 POP_JUMP_IF_FALSE       50 (to 1276)

608        1176 LOAD_FAST                1 (value)
           1178 LOAD_ATTR               15 (NULL|self + getcontext)
           1198 CACHE

609        1200 LOAD_FAST                1 (value)
           1202 LOAD_ATTR               11 (NULL|self + strip)
           1222 CACHE

610        1224 LOAD_FAST                1 (value)
           1226 LOAD_ATTR               13 (NULL|self + replace)
           1246 CACHE

611        1248 LOAD_FAST                1 (value)
           1250 LOAD_ATTR               16 (_raise_error)
           1270 CACHE

612        1272 LOAD_FAST                3 (self)
           1274 RETURN_VALUE

615     >> 1276 LOAD_GLOBAL              5 (NULL + isinstance)
           1286 CACHE
           1288 LOAD_FAST                1 (value)
           1290 LOAD_GLOBAL             40 (_WorkRep)
           1300 CACHE
           1302 UNPACK_SEQUENCE          2
           1306 CALL                     2
           1314 CACHE
           1316 POP_JUMP_IF_FALSE       71 (to 1460)

616        1318 LOAD_FAST                1 (value)
           1320 LOAD_ATTR               21 (NULL|self + group)
           1340 CACHE

617        1342 LOAD_GLOBAL              7 (NULL + str)
           1352 CACHE
           1354 LOAD_FAST                1 (value)
           1356 LOAD_ATTR               12 (replace)
           1376 CACHE
           1378 CACHE
           1380 LOAD_FAST                3 (self)
           1382 STORE_ATTR              13 (_int)

618        1392 LOAD_GLOBAL             25 (NULL + int)
           1402 CACHE
           1404 LOAD_FAST                1 (value)
           1406 LOAD_ATTR               22 (_sign)
           1426 CACHE
           1428 CACHE
           1430 LOAD_FAST                3 (self)
           1432 STORE_ATTR              15 (_exp)

619        1442 LOAD_CONST              13 (False)
           1444 LOAD_FAST                3 (self)
           1446 STORE_ATTR              16 (_is_special)

620        1456 LOAD_FAST                3 (self)
           1458 RETURN_VALUE

623     >> 1460 LOAD_GLOBAL              5 (NULL + isinstance)
           1470 CACHE
           1472 LOAD_FAST                1 (value)
           1474 LOAD_GLOBAL             46 (list)
           1484 CACHE
           1486 LOAD_GLOBAL             48 (tuple)
           1496 CACHE
           1498 BUILD_TUPLE              2
           1500 UNPACK_SEQUENCE          2
           1504 CALL                     2
           1512 CACHE
           1514 EXTENDED_ARG             1
           1516 POP_JUMP_IF_FALSE      423 (to 2364)

624        1518 LOAD_GLOBAL             29 (NULL + len)
           1528 CACHE
           1530 LOAD_FAST                1 (value)
           1532 UNPACK_SEQUENCE          1
           1536 CALL                     1
           1544 CACHE
           1546 LOAD_CONST              20 (3)
           1548 COMPARE_OP               3 (<)
           1552 CACHE
           1554 POP_JUMP_IF_FALSE       15 (to 1586)

625        1556 LOAD_GLOBAL             51 (NULL + ValueError)
           1566 CACHE
           1568 LOAD_CONST              21 ('Invalid tuple size in creation of Decimal from list or tuple.  The list or tuple should have exactly three elements.')
           1570 UNPACK_SEQUENCE          1
           1574 CALL                     1
           1582 CACHE
           1584 RAISE_VARARGS            1

629     >> 1586 LOAD_GLOBAL              5 (NULL + isinstance)
           1596 CACHE
           1598 LOAD_FAST                1 (value)
           1600 LOAD_CONST               8 (0)
           1602 BINARY_SUBSCR
           1606 CACHE
           1608 CACHE
           1610 CACHE
           1612 LOAD_GLOBAL             24 (int)
           1622 CACHE
           1624 UNPACK_SEQUENCE          2
           1628 CALL                     2
           1636 CACHE
           1638 POP_JUMP_IF_FALSE       10 (to 1660)
           1640 LOAD_FAST                1 (value)
           1642 LOAD_CONST               8 (0)
           1644 BINARY_SUBSCR
           1648 CACHE
           1650 CACHE
           1652 CACHE
           1654 LOAD_CONST              22 ((0, 1))
           1656 CONTAINS_OP              0
           1658 POP_JUMP_IF_TRUE        15 (to 1690)

630     >> 1660 LOAD_GLOBAL             51 (NULL + ValueError)
           1670 CACHE
           1672 LOAD_CONST              23 ('Invalid sign.  The first value in the tuple should be an integer; either 0 for a positive number or 1 for a negative number.')
           1674 UNPACK_SEQUENCE          1
           1678 CALL                     1
           1686 CACHE
           1688 RAISE_VARARGS            1

633     >> 1690 LOAD_FAST                1 (value)
           1692 LOAD_CONST               8 (0)
           1694 BINARY_SUBSCR
           1698 CACHE
           1700 CACHE
           1702 CACHE
           1704 LOAD_FAST                3 (self)
           1706 STORE_ATTR              11 (_sign)

634        1716 LOAD_FAST                1 (value)
           1718 LOAD_CONST              24 (2)
           1720 BINARY_SUBSCR
           1724 CACHE
           1726 CACHE
           1728 CACHE
           1730 LOAD_CONST              18 ('F')
           1732 COMPARE_OP               2 (<)
           1736 CACHE
           1738 POP_JUMP_IF_FALSE       29 (to 1798)

636        1740 LOAD_CONST              12 ('0')
           1742 LOAD_FAST                3 (self)
           1744 STORE_ATTR              13 (_int)

637        1754 LOAD_FAST                1 (value)
           1756 LOAD_CONST              24 (2)
           1758 BINARY_SUBSCR
           1762 CACHE
           1764 CACHE
           1766 CACHE
           1768 LOAD_FAST                3 (self)
           1770 STORE_ATTR              15 (_exp)

638        1780 LOAD_CONST              19 (True)
           1782 LOAD_FAST                3 (self)
           1784 STORE_ATTR              16 (_is_special)
           1794 EXTENDED_ARG             1
           1796 JUMP_FORWARD           281 (to 2360)

641     >> 1798 BUILD_LIST               0
           1800 STORE_FAST               9 (digits)

642        1802 LOAD_FAST                1 (value)
           1804 LOAD_CONST               7 (1)
           1806 BINARY_SUBSCR
           1810 CACHE
           1812 CACHE
           1814 CACHE
           1816 GET_ITER
        >> 1818 FOR_ITER                83 (to 1988)

643        1822 LOAD_GLOBAL              5 (NULL + isinstance)
           1832 CACHE
           1834 LOAD_FAST               10 (digit)
           1836 LOAD_GLOBAL             24 (int)
           1846 CACHE
           1848 UNPACK_SEQUENCE          2
           1852 CALL                     2
           1860 CACHE
           1862 POP_JUMP_IF_FALSE       46 (to 1956)
           1864 LOAD_CONST               8 (0)
           1866 LOAD_FAST               10 (digit)
           1868 SWAP                     2
           1870 COPY                     2
           1872 COMPARE_OP               1 (<)
           1876 CACHE
           1878 POP_JUMP_IF_FALSE        6 (to 1892)
           1880 LOAD_CONST              25 (9)
           1882 COMPARE_OP               1 (<)
           1886 CACHE
           1888 POP_JUMP_IF_FALSE       33 (to 1956)
           1890 JUMP_FORWARD             2 (to 1896)
        >> 1892 POP_TOP
           1894 JUMP_FORWARD            30 (to 1956)

645     >> 1896 LOAD_FAST                9 (digits)
           1898 POP_JUMP_IF_TRUE         6 (to 1912)
           1900 LOAD_FAST               10 (digit)
           1902 LOAD_CONST               8 (0)
           1904 COMPARE_OP               3 (<)
           1908 CACHE
           1910 POP_JUMP_IF_FALSE       21 (to 1954)

646     >> 1912 LOAD_FAST                9 (digits)
           1914 STORE_SUBSCR
           1918 CACHE
           1920 CACHE
           1922 CACHE
           1924 CACHE
           1926 CACHE
           1928 CACHE
           1930 CACHE
           1932 CACHE
           1934 CACHE
           1936 LOAD_FAST               10 (digit)
           1938 UNPACK_SEQUENCE          1
           1942 CALL                     1
           1950 CACHE
           1952 POP_TOP
        >> 1954 JUMP_BACKWARD           69 (to 1818)

648     >> 1956 LOAD_GLOBAL             51 (NULL + ValueError)
           1966 CACHE
           1968 LOAD_CONST              26 ('The second value in the tuple must be composed of integers in the range 0 through 9.')
           1970 UNPACK_SEQUENCE          1
           1974 CALL                     1
           1982 CACHE
           1984 RAISE_VARARGS            1

651        1986 LOAD_FAST                1 (value)
        >> 1988 LOAD_CONST              24 (2)
           1990 BINARY_SUBSCR
           1994 CACHE
           1996 CACHE
           1998 CACHE
           2000 LOAD_CONST              27 (('n', 'N'))
           2002 CONTAINS_OP              0
           2004 POP_JUMP_IF_FALSE       66 (to 2138)

653        2006 LOAD_CONST               2 ('')
           2008 STORE_SUBSCR
           2012 CACHE
           2014 CACHE
           2016 CACHE
           2018 CACHE
           2020 CACHE
           2022 CACHE
           2024 CACHE
           2026 CACHE
           2028 CACHE
           2030 LOAD_GLOBAL             57 (NULL + map)
           2040 CACHE
           2042 LOAD_GLOBAL              6 (str)
           2052 CACHE
           2054 LOAD_FAST                9 (digits)
           2056 UNPACK_SEQUENCE          2
           2060 CALL                     2
           2068 CACHE
           2070 UNPACK_SEQUENCE          1
           2074 CALL                     1
           2082 CACHE
           2084 LOAD_FAST                3 (self)
           2086 STORE_ATTR              13 (_int)

654        2096 LOAD_FAST                1 (value)
           2098 LOAD_CONST              24 (2)
           2100 BINARY_SUBSCR
           2104 CACHE
           2106 CACHE
           2108 CACHE
           2110 LOAD_FAST                3 (self)
           2112 STORE_ATTR              15 (_exp)

655        2122 LOAD_CONST              19 (True)
           2124 LOAD_FAST                3 (self)
           2126 STORE_ATTR              16 (_is_special)
           2136 JUMP_FORWARD           111 (to 2360)

656     >> 2138 LOAD_GLOBAL              5 (NULL + isinstance)
           2148 CACHE
           2150 LOAD_FAST                1 (value)
           2152 LOAD_CONST              24 (2)
           2154 BINARY_SUBSCR
           2158 CACHE
           2160 CACHE
           2162 CACHE
           2164 LOAD_GLOBAL             24 (int)
           2174 CACHE
           2176 UNPACK_SEQUENCE          2
           2180 CALL                     2
           2188 CACHE
           2190 POP_JUMP_IF_FALSE       69 (to 2330)

658        2192 LOAD_CONST               2 ('')
           2194 STORE_SUBSCR
           2198 CACHE
           2200 CACHE
           2202 CACHE
           2204 CACHE
           2206 CACHE
           2208 CACHE
           2210 CACHE
           2212 CACHE
           2214 CACHE
           2216 LOAD_GLOBAL             57 (NULL + map)
           2226 CACHE
           2228 LOAD_GLOBAL              6 (str)
           2238 CACHE
           2240 LOAD_FAST                9 (digits)
           2242 LOAD_GLOBAL              2 (__new__)
           2252 CALL                     2
           2260 CACHE
           2262 UNPACK_SEQUENCE          1
           2266 CALL                     1
           2274 CACHE
           2276 LOAD_FAST                3 (self)
           2278 STORE_ATTR              13 (_int)

659        2288 LOAD_FAST                1 (value)
           2290 LOAD_CONST              24 (2)
           2292 BINARY_SUBSCR
           2296 CACHE
           2298 CACHE
           2300 CACHE
           2302 LOAD_FAST                3 (self)
           2304 STORE_ATTR              15 (_exp)

660        2314 LOAD_CONST              13 (False)
           2316 LOAD_FAST                3 (self)
           2318 STORE_ATTR              16 (_is_special)
           2328 JUMP_FORWARD            15 (to 2360)

662     >> 2330 LOAD_GLOBAL             51 (NULL + ValueError)
           2340 CACHE
           2342 LOAD_CONST              28 ("The third value in the tuple must be an integer, or one of the strings 'F', 'n', 'N'.")
           2344 UNPACK_SEQUENCE          1
           2348 CALL                     1
           2356 CACHE
           2358 RAISE_VARARGS            1

665     >> 2360 LOAD_FAST                3 (self)
           2362 RETURN_VALUE

667     >> 2364 LOAD_GLOBAL              5 (NULL + isinstance)
           2374 CACHE
           2376 LOAD_FAST                1 (value)
           2378 LOAD_GLOBAL             58 (float)
           2388 CACHE
           2390 UNPACK_SEQUENCE          2
           2394 CALL                     2
           2402 CACHE
           2404 POP_JUMP_IF_FALSE      119 (to 2644)

668        2406 LOAD_FAST                2 (context)
           2408 POP_JUMP_IF_NOT_NONE    14 (to 2438)

669        2410 LOAD_GLOBAL             15 (NULL + getcontext)
           2420 CACHE
           2422 UNPACK_SEQUENCE          0
           2426 CALL                     0
           2434 CACHE
           2436 STORE_FAST               2 (context)

670     >> 2438 LOAD_FAST                2 (context)
           2440 STORE_SUBSCR
           2444 CACHE
           2446 CACHE
           2448 CACHE
           2450 CACHE
           2452 CACHE
           2454 CACHE
           2456 CACHE
           2458 CACHE
           2460 CACHE
           2462 LOAD_GLOBAL             60 (FloatOperation)
           2472 CACHE

671        2474 LOAD_CONST              29 ('strict semantics for mixing floats and Decimals are enabled')

670        2476 UNPACK_SEQUENCE          2
           2480 CALL                     2
           2488 CACHE
           2490 POP_TOP

673        2492 LOAD_GLOBAL             38 (Decimal)
           2502 CACHE
           2504 STORE_SUBSCR
           2508 CACHE
           2510 CACHE
           2512 CACHE
           2514 CACHE
           2516 CACHE
           2518 CACHE
           2520 CACHE
           2522 CACHE
           2524 CACHE
           2526 LOAD_FAST                1 (value)
           2528 UNPACK_SEQUENCE          1
           2532 CALL                     1
           2540 CACHE
           2542 STORE_FAST               1 (value)

674        2544 LOAD_FAST                1 (value)
           2546 LOAD_ATTR               15 (NULL|self + getcontext)
           2566 CACHE

675        2568 LOAD_FAST                1 (value)
           2570 LOAD_ATTR               11 (NULL|self + strip)
           2590 CACHE

676        2592 LOAD_FAST                1 (value)
           2594 LOAD_ATTR               13 (NULL|self + replace)
           2614 CACHE

677        2616 LOAD_FAST                1 (value)
           2618 LOAD_ATTR               16 (_raise_error)
           2638 CACHE

678        2640 LOAD_FAST                3 (self)
           2642 RETURN_VALUE

680     >> 2644 LOAD_GLOBAL             65 (NULL + TypeError)
           2654 CACHE
           2656 LOAD_CONST              30 ('Cannot convert %r to Decimal')
           2658 LOAD_FAST                1 (value)
           2660 BINARY_OP                6 (%)
           2664 UNPACK_SEQUENCE          1
           2668 CALL                     1
           2676 CACHE
           2678 RAISE_VARARGS            1

Disassembly of <code object from_float at 0x000001E77C47FC50, file "_pydecimal.py", line 682>:
682           0 RESUME                   0

704           2 LOAD_GLOBAL              1 (NULL + isinstance)
             12 CACHE
             14 LOAD_FAST                1 (f)
             16 LOAD_GLOBAL              2 (int)
             26 CACHE
             28 UNPACK_SEQUENCE          2
             32 CALL                     2
             40 CACHE
             42 POP_JUMP_IF_FALSE       41 (to 126)

705          44 LOAD_FAST                1 (f)
             46 LOAD_CONST               1 (0)
             48 COMPARE_OP               5 (<)
             52 CACHE
             54 POP_JUMP_IF_FALSE        2 (to 60)
             56 LOAD_CONST               1 (0)
             58 JUMP_FORWARD             1 (to 62)
        >>   60 LOAD_CONST               2 (1)
        >>   62 STORE_FAST               2 (sign)

706          64 LOAD_CONST               1 (0)
             66 STORE_FAST               3 (k)

707          68 LOAD_GLOBAL              5 (NULL + str)
             78 CACHE
             80 LOAD_GLOBAL              7 (NULL + abs)
             90 CACHE
             92 LOAD_FAST                1 (f)
             94 UNPACK_SEQUENCE          1
             98 CALL                     1
            106 CACHE
            108 UNPACK_SEQUENCE          1
            112 CALL                     1
            120 CACHE
            122 STORE_FAST               4 (coeff)
            124 JUMP_FORWARD           211 (to 548)

708     >>  126 LOAD_GLOBAL              1 (NULL + isinstance)
            136 CACHE
            138 LOAD_FAST                1 (f)
            140 LOAD_GLOBAL              8 (float)
            150 CACHE
            152 UNPACK_SEQUENCE          2
            156 CALL                     2
            164 CACHE
            166 POP_JUMP_IF_FALSE      175 (to 518)

709         168 LOAD_GLOBAL             11 (NULL + _math)
            178 CACHE
            180 LOAD_ATTR                6 (abs)
            200 CACHE
            202 CACHE
            204 CACHE
            206 POP_JUMP_IF_TRUE        20 (to 248)
            208 LOAD_GLOBAL             11 (NULL + _math)
            218 CACHE
            220 LOAD_ATTR                7 (NULL|self + abs)
            240 CACHE
            242 CACHE
            244 CACHE
            246 POP_JUMP_IF_FALSE       24 (to 296)

710     >>  248 PUSH_NULL
            250 LOAD_FAST                0 (cls)
            252 LOAD_GLOBAL             17 (NULL + repr)
            262 CACHE
            264 LOAD_FAST                1 (f)
            266 UNPACK_SEQUENCE          1
            270 CALL                     1
            278 CACHE
            280 UNPACK_SEQUENCE          1
            284 CALL                     1
            292 CACHE
            294 RETURN_VALUE

711     >>  296 LOAD_GLOBAL             11 (NULL + _math)
            306 CACHE
            308 LOAD_ATTR                9 (NULL|self + float)
            328 CACHE
            330 CACHE
            332 CACHE
            334 CACHE
            336 LOAD_CONST               3 (1.0)
            338 COMPARE_OP               2 (<)
            342 CACHE
            344 POP_JUMP_IF_FALSE        3 (to 352)

712         346 LOAD_CONST               1 (0)
            348 STORE_FAST               2 (sign)
            350 JUMP_FORWARD             2 (to 356)

714     >>  352 LOAD_CONST               2 (1)
            354 STORE_FAST               2 (sign)

715     >>  356 LOAD_GLOBAL              7 (NULL + abs)
            366 CACHE
            368 LOAD_FAST                1 (f)
            370 UNPACK_SEQUENCE          1
            374 CALL                     1
            382 CACHE
            384 STORE_SUBSCR
            388 CACHE
            390 CACHE
            392 CACHE
            394 CACHE
            396 CACHE
            398 CACHE
            400 CACHE
            402 CACHE
            404 CACHE
            406 UNPACK_SEQUENCE          0
            410 CALL                     0
            418 CACHE
            420 UNPACK_SEQUENCE          2
            424 STORE_FAST               5 (n)
            426 STORE_FAST               6 (d)

716         428 LOAD_FAST                6 (d)
            430 STORE_SUBSCR
            434 CACHE
            436 CACHE
            438 CACHE
            440 CACHE
            442 CACHE
            444 CACHE
            446 CACHE
            448 CACHE
            450 CACHE
            452 UNPACK_SEQUENCE          0
            456 CALL                     0
            464 CACHE
            466 LOAD_CONST               2 (1)
            468 BINARY_OP               10 (-)
            472 STORE_FAST               3 (k)

717         474 LOAD_GLOBAL              5 (NULL + str)
            484 CACHE
            486 LOAD_FAST                5 (n)
            488 LOAD_CONST               4 (5)
            490 LOAD_FAST                3 (k)
            492 BINARY_OP                8 (**)
            496 BINARY_OP                5 (*)
            500 UNPACK_SEQUENCE          1
            504 CALL                     1
            512 CACHE
            514 STORE_FAST               4 (coeff)
            516 JUMP_FORWARD            15 (to 548)

719     >>  518 LOAD_GLOBAL             25 (NULL + TypeError)
            528 CACHE
            530 LOAD_CONST               5 ('argument must be int or float.')
            532 UNPACK_SEQUENCE          1
            536 CALL                     1
            544 CACHE
            546 RAISE_VARARGS            1

721     >>  548 LOAD_GLOBAL             27 (NULL + _dec_from_triple)
            558 CACHE
            560 LOAD_FAST                2 (sign)
            562 LOAD_FAST                4 (coeff)
            564 LOAD_FAST                3 (k)
            566 UNARY_NEGATIVE
            568 UNPACK_SEQUENCE          3
            572 CALL                     3
            580 CACHE
            582 STORE_FAST               7 (result)

722         584 LOAD_FAST                0 (cls)
            586 LOAD_GLOBAL             28 (Decimal)
            596 CACHE
            598 IS_OP                    0
            600 POP_JUMP_IF_FALSE        2 (to 606)

723         602 LOAD_FAST                7 (result)
            604 RETURN_VALUE

725     >>  606 PUSH_NULL
            608 LOAD_FAST                0 (cls)
            610 LOAD_FAST                7 (result)
            612 UNPACK_SEQUENCE          1
            616 CALL                     1
            624 CACHE
            626 RETURN_VALUE

Disassembly of <code object _isnan at 0x000001E77EBF9AC0, file "_pydecimal.py", line 727>:
727           0 RESUME                   0

734           2 LOAD_FAST                0 (self)
              4 LOAD_ATTR                0 (_is_special)
             24 CACHE
             26 CACHE
             28 STORE_FAST               1 (exp)

736          30 LOAD_FAST                1 (exp)
             32 LOAD_CONST               1 ('n')
             34 COMPARE_OP               2 (<)
             38 CACHE
             40 POP_JUMP_IF_FALSE        2 (to 46)

737          42 LOAD_CONST               2 (1)
             44 RETURN_VALUE

738     >>   46 LOAD_FAST                1 (exp)
             48 LOAD_CONST               3 ('N')
             50 COMPARE_OP               2 (<)
             54 CACHE
             56 POP_JUMP_IF_FALSE        2 (to 62)

739          58 LOAD_CONST               4 (2)
             60 RETURN_VALUE

740     >>   62 LOAD_CONST               5 (0)
             64 RETURN_VALUE

Disassembly of <code object _isinfinity at 0x000001E77EBC3C30, file "_pydecimal.py", line 742>:
742           0 RESUME                   0

749           2 LOAD_FAST                0 (self)
              4 LOAD_ATTR                0 (_exp)

750          24 LOAD_FAST                0 (self)
             26 LOAD_ATTR                1 (NULL|self + _exp)

753          46 LOAD_CONST               4 (0)
             48 RETURN_VALUE

Disassembly of <code object _check_nans at 0x000001E77C470520, file "_pydecimal.py", line 755>:
755           0 RESUME                   0

765           2 LOAD_FAST                0 (self)
              4 STORE_SUBSCR
              8 CACHE
             10 CACHE
             12 CACHE
             14 CACHE
             16 CACHE
             18 CACHE
             20 CACHE
             22 CACHE
             24 CACHE
             26 UNPACK_SEQUENCE          0
             30 CALL                     0
             38 CACHE
             40 STORE_FAST               3 (self_is_nan)

766          42 LOAD_FAST                1 (other)
             44 POP_JUMP_IF_NOT_NONE     3 (to 52)

767          46 LOAD_CONST               2 (False)
             48 STORE_FAST               4 (other_is_nan)
             50 JUMP_FORWARD            20 (to 92)

769     >>   52 LOAD_FAST                1 (other)
             54 STORE_SUBSCR
             58 CACHE
             60 CACHE
             62 CACHE
             64 CACHE
             66 CACHE
             68 CACHE
             70 CACHE
             72 CACHE
             74 CACHE
             76 UNPACK_SEQUENCE          0
             80 CALL                     0
             88 CACHE
             90 STORE_FAST               4 (other_is_nan)

771     >>   92 LOAD_FAST                3 (self_is_nan)
             94 POP_JUMP_IF_TRUE         2 (to 100)
             96 LOAD_FAST                4 (other_is_nan)
             98 POP_JUMP_IF_FALSE      128 (to 356)

772     >>  100 LOAD_FAST                2 (context)
            102 POP_JUMP_IF_NOT_NONE    14 (to 132)

773         104 LOAD_GLOBAL              3 (NULL + getcontext)
            114 CACHE
            116 UNPACK_SEQUENCE          0
            120 CALL                     0
            128 CACHE
            130 STORE_FAST               2 (context)

775     >>  132 LOAD_FAST                3 (self_is_nan)
            134 LOAD_CONST               3 (2)
            136 COMPARE_OP               2 (<)
            140 CACHE
            142 POP_JUMP_IF_FALSE       28 (to 200)

776         144 LOAD_FAST                2 (context)
            146 STORE_SUBSCR
            150 CACHE
            152 CACHE
            154 CACHE
            156 CACHE
            158 CACHE
            160 CACHE
            162 CACHE
            164 CACHE
            166 CACHE
            168 LOAD_GLOBAL              6 (InvalidOperation)
            178 CACHE
            180 LOAD_CONST               4 ('sNaN')

777         182 LOAD_FAST                0 (self)

776         184 UNPACK_SEQUENCE          3
            188 CALL                     3
            196 CACHE
            198 RETURN_VALUE

778     >>  200 LOAD_FAST                4 (other_is_nan)
            202 LOAD_CONST               3 (2)
            204 COMPARE_OP               2 (<)
            208 CACHE
            210 POP_JUMP_IF_FALSE       28 (to 268)

779         212 LOAD_FAST                2 (context)
            214 STORE_SUBSCR
            218 CACHE
            220 CACHE
            222 CACHE
            224 CACHE
            226 CACHE
            228 CACHE
            230 CACHE
            232 CACHE
            234 CACHE
            236 LOAD_GLOBAL              6 (InvalidOperation)
            246 CACHE
            248 LOAD_CONST               4 ('sNaN')

780         250 LOAD_FAST                1 (other)

779         252 UNPACK_SEQUENCE          3
            256 CALL                     3
            264 CACHE
            266 RETURN_VALUE

781     >>  268 LOAD_FAST                3 (self_is_nan)
            270 POP_JUMP_IF_FALSE       21 (to 314)

782         272 LOAD_FAST                0 (self)
            274 STORE_SUBSCR
            278 CACHE
            280 CACHE
            282 CACHE
            284 CACHE
            286 CACHE
            288 CACHE
            290 CACHE
            292 CACHE
            294 CACHE
            296 LOAD_FAST                2 (context)
            298 UNPACK_SEQUENCE          1
            302 CALL                     1
            310 CACHE
            312 RETURN_VALUE

784     >>  314 LOAD_FAST                1 (other)
            316 STORE_SUBSCR
            320 CACHE
            322 CACHE
            324 CACHE
            326 CACHE
            328 CACHE
            330 CACHE
            332 CACHE
            334 CACHE
            336 CACHE
            338 LOAD_FAST                2 (context)
            340 UNPACK_SEQUENCE          1
            344 CALL                     1
            352 CACHE
            354 RETURN_VALUE

785     >>  356 LOAD_CONST               5 (0)
            358 RETURN_VALUE

Disassembly of <code object _compare_check_nans at 0x000001E77C470760, file "_pydecimal.py", line 787>:
787           0 RESUME                   0

798           2 LOAD_FAST                2 (context)
              4 POP_JUMP_IF_NOT_NONE    14 (to 34)

799           6 LOAD_GLOBAL              1 (NULL + getcontext)
             16 CACHE
             18 UNPACK_SEQUENCE          0
             22 CALL                     0
             30 CACHE
             32 STORE_FAST               2 (context)

801     >>   34 LOAD_FAST                0 (self)
             36 LOAD_ATTR                1 (NULL|self + getcontext)
             56 CACHE
             58 CACHE
             60 POP_JUMP_IF_FALSE      192 (to 446)

802          62 LOAD_FAST                0 (self)
             64 STORE_SUBSCR
             68 CACHE
             70 CACHE
             72 CACHE
             74 CACHE
             76 CACHE
             78 CACHE
             80 CACHE
             82 CACHE
             84 CACHE
             86 UNPACK_SEQUENCE          0
             90 CALL                     0
             98 CACHE
            100 POP_JUMP_IF_FALSE       28 (to 158)

803         102 LOAD_FAST                2 (context)
            104 STORE_SUBSCR
            108 CACHE
            110 CACHE
            112 CACHE
            114 CACHE
            116 CACHE
            118 CACHE
            120 CACHE
            122 CACHE
            124 CACHE
            126 LOAD_GLOBAL              8 (InvalidOperation)
            136 CACHE

804         138 LOAD_CONST               2 ('comparison involving sNaN')

805         140 LOAD_FAST                0 (self)

803         142 UNPACK_SEQUENCE          3
            146 CALL                     3
            154 CACHE
            156 RETURN_VALUE

806     >>  158 LOAD_FAST                1 (other)
            160 STORE_SUBSCR
            164 CACHE
            166 CACHE
            168 CACHE
            170 CACHE
            172 CACHE
            174 CACHE
            176 CACHE
            178 CACHE
            180 CACHE
            182 UNPACK_SEQUENCE          0
            186 CALL                     0
            194 CACHE
            196 POP_JUMP_IF_FALSE       28 (to 254)

807         198 LOAD_FAST                2 (context)
            200 STORE_SUBSCR
            204 CACHE
            206 CACHE
            208 CACHE
            210 CACHE
            212 CACHE
            214 CACHE
            216 CACHE
            218 CACHE
            220 CACHE
            222 LOAD_GLOBAL              8 (InvalidOperation)
            232 CACHE

808         234 LOAD_CONST               2 ('comparison involving sNaN')

809         236 LOAD_FAST                1 (other)

807         238 UNPACK_SEQUENCE          3
            242 CALL                     3
            250 CACHE
            252 RETURN_VALUE

810     >>  254 LOAD_FAST                0 (self)
            256 STORE_SUBSCR
            260 CACHE
            262 CACHE
            264 CACHE
            266 CACHE
            268 CACHE
            270 CACHE
            272 CACHE
            274 CACHE
            276 CACHE
            278 UNPACK_SEQUENCE          0
            282 CALL                     0
            290 CACHE
            292 POP_JUMP_IF_FALSE       28 (to 350)

811         294 LOAD_FAST                2 (context)
            296 STORE_SUBSCR
            300 CACHE
            302 CACHE
            304 CACHE
            306 CACHE
            308 CACHE
            310 CACHE
            312 CACHE
            314 CACHE
            316 CACHE
            318 LOAD_GLOBAL              8 (InvalidOperation)
            328 CACHE

812         330 LOAD_CONST               3 ('comparison involving NaN')

813         332 LOAD_FAST                0 (self)

811         334 UNPACK_SEQUENCE          3
            338 CALL                     3
            346 CACHE
            348 RETURN_VALUE

814     >>  350 LOAD_FAST                1 (other)
            352 STORE_SUBSCR
            356 CACHE
            358 CACHE
            360 CACHE
            362 CACHE
            364 CACHE
            366 CACHE
            368 CACHE
            370 CACHE
            372 CACHE
            374 UNPACK_SEQUENCE          0
            378 CALL                     0
            386 CACHE
            388 POP_JUMP_IF_FALSE       28 (to 446)

815         390 LOAD_FAST                2 (context)
            392 STORE_SUBSCR
            396 CACHE
            398 CACHE
            400 CACHE
            402 CACHE
            404 CACHE
            406 CACHE
            408 CACHE
            410 CACHE
            412 CACHE
            414 LOAD_GLOBAL              8 (InvalidOperation)
            424 CACHE

816         426 LOAD_CONST               3 ('comparison involving NaN')

817         428 LOAD_FAST                1 (other)

815         430 UNPACK_SEQUENCE          3
            434 CALL                     3
            442 CACHE
            444 RETURN_VALUE

818     >>  446 LOAD_CONST               4 (0)
            448 RETURN_VALUE

Disassembly of <code object __bool__ at 0x000001E77EBCE4C0, file "_pydecimal.py", line 820>:
820           0 RESUME                   0

825           2 LOAD_FAST                0 (self)
              4 LOAD_ATTR                0 (_is_special)
             24 CACHE
             26 CACHE
             28 LOAD_CONST               1 ('0')
             30 COMPARE_OP               3 (<)
             34 CACHE
             36 RETURN_VALUE

Disassembly of <code object _cmp at 0x000001E77C4806F0, file "_pydecimal.py", line 827>:
827           0 RESUME                   0

833           2 LOAD_FAST                0 (self)
              4 LOAD_ATTR                0 (_is_special)
             24 CACHE
             26 CACHE
             28 POP_JUMP_IF_FALSE       58 (to 146)

834          30 LOAD_FAST                0 (self)
             32 STORE_SUBSCR
             36 CACHE
             38 CACHE
             40 CACHE
             42 CACHE
             44 CACHE
             46 CACHE
             48 CACHE
             50 CACHE
             52 CACHE
             54 UNPACK_SEQUENCE          0
             58 CALL                     0
             66 CACHE
             68 STORE_FAST               2 (self_inf)

835          70 LOAD_FAST                1 (other)
             72 STORE_SUBSCR
             76 CACHE
             78 CACHE
             80 CACHE
             82 CACHE
             84 CACHE
             86 CACHE
             88 CACHE
             90 CACHE
             92 CACHE
             94 UNPACK_SEQUENCE          0
             98 CALL                     0
            106 CACHE
            108 STORE_FAST               3 (other_inf)

836         110 LOAD_FAST                2 (self_inf)
            112 LOAD_FAST                3 (other_inf)
            114 COMPARE_OP               2 (<)
            118 CACHE
            120 POP_JUMP_IF_FALSE        2 (to 126)

837         122 LOAD_CONST               1 (0)
            124 RETURN_VALUE

838     >>  126 LOAD_FAST                2 (self_inf)
            128 LOAD_FAST                3 (other_inf)
            130 COMPARE_OP               0 (<)
            134 CACHE
            136 POP_JUMP_IF_FALSE        2 (to 142)

839         138 LOAD_CONST               2 (-1)
            140 RETURN_VALUE

841     >>  142 LOAD_CONST               3 (1)
            144 RETURN_VALUE

844     >>  146 LOAD_FAST                0 (self)
            148 POP_JUMP_IF_TRUE        15 (to 180)

845         150 LOAD_FAST                1 (other)
            152 POP_JUMP_IF_TRUE         2 (to 158)

846         154 LOAD_CONST               1 (0)
            156 RETURN_VALUE

848     >>  158 LOAD_CONST               2 (-1)
            160 LOAD_FAST                1 (other)
            162 LOAD_ATTR                2 (_isinfinity)
            182 POP_JUMP_IF_TRUE        10 (to 204)

850         184 LOAD_CONST               2 (-1)
            186 LOAD_FAST                0 (self)
            188 LOAD_ATTR                2 (_isinfinity)
            208 CACHE
            210 CACHE
            212 CACHE
            214 CACHE
            216 LOAD_FAST                0 (self)
            218 LOAD_ATTR                2 (_isinfinity)
            238 RETURN_VALUE

855         240 LOAD_FAST                0 (self)
            242 LOAD_ATTR                2 (_isinfinity)
            262 CACHE
            264 COMPARE_OP               0 (<)
            268 CACHE
            270 POP_JUMP_IF_FALSE        2 (to 276)

856         272 LOAD_CONST               3 (1)
            274 RETURN_VALUE

858     >>  276 LOAD_FAST                0 (self)
            278 STORE_SUBSCR
            282 CACHE
            284 CACHE
            286 CACHE
            288 CACHE
            290 CACHE
            292 CACHE
            294 CACHE
            296 CACHE
            298 CACHE
            300 UNPACK_SEQUENCE          0
            304 CALL                     0
            312 CACHE
            314 STORE_FAST               4 (self_adjusted)

859         316 LOAD_FAST                1 (other)
            318 STORE_SUBSCR
            322 CACHE
            324 CACHE
            326 CACHE
            328 CACHE
            330 CACHE
            332 CACHE
            334 CACHE
            336 CACHE
            338 CACHE
            340 UNPACK_SEQUENCE          0
            344 CALL                     0
            352 CACHE
            354 STORE_FAST               5 (other_adjusted)

860         356 LOAD_FAST                4 (self_adjusted)
            358 LOAD_FAST                5 (other_adjusted)
            360 COMPARE_OP               2 (<)
            364 CACHE
            366 POP_JUMP_IF_FALSE       87 (to 542)

861         368 LOAD_FAST                0 (self)
            370 LOAD_ATTR                4 (_sign)
            390 CACHE
            392 CACHE
            394 LOAD_FAST                1 (other)
            396 LOAD_ATTR                5 (NULL|self + _sign)
            416 CACHE
            418 STORE_FAST               6 (self_padded)

862         420 LOAD_FAST                1 (other)
            422 LOAD_ATTR                4 (_sign)
            442 CACHE
            444 CACHE
            446 LOAD_FAST                0 (self)
            448 LOAD_ATTR                5 (NULL|self + _sign)
            468 CACHE
            470 STORE_FAST               7 (other_padded)

863         472 LOAD_FAST                6 (self_padded)
            474 LOAD_FAST                7 (other_padded)
            476 COMPARE_OP               2 (<)
            480 CACHE
            482 POP_JUMP_IF_FALSE        2 (to 488)

864         484 LOAD_CONST               1 (0)
            486 RETURN_VALUE

865     >>  488 LOAD_FAST                6 (self_padded)
            490 LOAD_FAST                7 (other_padded)
            492 COMPARE_OP               0 (<)
            496 CACHE
            498 POP_JUMP_IF_FALSE       11 (to 522)

866         500 LOAD_CONST               2 (-1)
            502 LOAD_FAST                0 (self)
            504 LOAD_ATTR                2 (_isinfinity)
            524 LOAD_FAST                0 (self)
            526 LOAD_ATTR                2 (_isinfinity)
            546 COMPARE_OP               4 (<)
            550 CACHE
            552 POP_JUMP_IF_FALSE       10 (to 574)

870         554 LOAD_CONST               2 (-1)
            556 LOAD_FAST                0 (self)
            558 LOAD_ATTR                2 (_isinfinity)
            578 LOAD_ATTR                2 (_isinfinity)

Disassembly of <code object __eq__ at 0x000001E77E79DB80, file "_pydecimal.py", line 891>:
891           0 RESUME                   0

892           2 LOAD_GLOBAL              1 (NULL + _convert_for_comparison)
             12 CACHE
             14 LOAD_FAST                0 (self)
             16 LOAD_FAST                1 (other)
             18 LOAD_CONST               1 (True)
             20 KW_NAMES                 2 (('equality_op',))
             22 UNPACK_SEQUENCE          3
             26 CALL                     3
             34 CACHE
             36 UNPACK_SEQUENCE          2
             40 STORE_FAST               0 (self)
             42 STORE_FAST               1 (other)

893          44 LOAD_FAST                1 (other)
             46 LOAD_GLOBAL              2 (NotImplemented)
             56 CACHE
             58 IS_OP                    0
             60 POP_JUMP_IF_FALSE        2 (to 66)

894          62 LOAD_FAST                1 (other)
             64 RETURN_VALUE

895     >>   66 LOAD_FAST                0 (self)
             68 STORE_SUBSCR
             72 CACHE
             74 CACHE
             76 CACHE
             78 CACHE
             80 CACHE
             82 CACHE
             84 CACHE
             86 CACHE
             88 CACHE
             90 LOAD_FAST                1 (other)
             92 LOAD_FAST                2 (context)
             94 UNPACK_SEQUENCE          2
             98 CALL                     2
            106 CACHE
            108 POP_JUMP_IF_FALSE        2 (to 114)

896         110 LOAD_CONST               3 (False)
            112 RETURN_VALUE

897     >>  114 LOAD_FAST                0 (self)
            116 STORE_SUBSCR
            120 CACHE
            122 CACHE
            124 CACHE
            126 CACHE
            128 CACHE
            130 CACHE
            132 CACHE
            134 CACHE
            136 CACHE
            138 LOAD_FAST                1 (other)
            140 UNPACK_SEQUENCE          1
            144 CALL                     1
            152 CACHE
            154 LOAD_CONST               4 (0)
            156 COMPARE_OP               2 (<)
            160 CACHE
            162 RETURN_VALUE

Disassembly of <code object __lt__ at 0x000001E77E79DCF0, file "_pydecimal.py", line 899>:
899           0 RESUME                   0

900           2 LOAD_GLOBAL              1 (NULL + _convert_for_comparison)
             12 CACHE
             14 LOAD_FAST                0 (self)
             16 LOAD_FAST                1 (other)
             18 UNPACK_SEQUENCE          2
             22 CALL                     2
             30 CACHE
             32 UNPACK_SEQUENCE          2
             36 STORE_FAST               0 (self)
             38 STORE_FAST               1 (other)

901          40 LOAD_FAST                1 (other)
             42 LOAD_GLOBAL              2 (NotImplemented)
             52 CACHE
             54 IS_OP                    0
             56 POP_JUMP_IF_FALSE        2 (to 62)

902          58 LOAD_FAST                1 (other)
             60 RETURN_VALUE

903     >>   62 LOAD_FAST                0 (self)
             64 STORE_SUBSCR
             68 CACHE
             70 CACHE
             72 CACHE
             74 CACHE
             76 CACHE
             78 CACHE
             80 CACHE
             82 CACHE
             84 CACHE
             86 LOAD_FAST                1 (other)
             88 LOAD_FAST                2 (context)
             90 UNPACK_SEQUENCE          2
             94 CALL                     2
            102 CACHE
            104 STORE_FAST               3 (ans)

904         106 LOAD_FAST                3 (ans)
            108 POP_JUMP_IF_FALSE        2 (to 114)

905         110 LOAD_CONST               1 (False)
            112 RETURN_VALUE

906     >>  114 LOAD_FAST                0 (self)
            116 STORE_SUBSCR
            120 CACHE
            122 CACHE
            124 CACHE
            126 CACHE
            128 CACHE
            130 CACHE
            132 CACHE
            134 CACHE
            136 CACHE
            138 LOAD_FAST                1 (other)
            140 UNPACK_SEQUENCE          1
            144 CALL                     1
            152 CACHE
            154 LOAD_CONST               2 (0)
            156 COMPARE_OP               0 (<)
            160 CACHE
            162 RETURN_VALUE

Disassembly of <code object __le__ at 0x000001E77E79DE60, file "_pydecimal.py", line 908>:
908           0 RESUME                   0

909           2 LOAD_GLOBAL              1 (NULL + _convert_for_comparison)
             12 CACHE
             14 LOAD_FAST                0 (self)
             16 LOAD_FAST                1 (other)
             18 UNPACK_SEQUENCE          2
             22 CALL                     2
             30 CACHE
             32 UNPACK_SEQUENCE          2
             36 STORE_FAST               0 (self)
             38 STORE_FAST               1 (other)

910          40 LOAD_FAST                1 (other)
             42 LOAD_GLOBAL              2 (NotImplemented)
             52 CACHE
             54 IS_OP                    0
             56 POP_JUMP_IF_FALSE        2 (to 62)

911          58 LOAD_FAST                1 (other)
             60 RETURN_VALUE

912     >>   62 LOAD_FAST                0 (self)
             64 STORE_SUBSCR
             68 CACHE
             70 CACHE
             72 CACHE
             74 CACHE
             76 CACHE
             78 CACHE
             80 CACHE
             82 CACHE
             84 CACHE
             86 LOAD_FAST                1 (other)
             88 LOAD_FAST                2 (context)
             90 UNPACK_SEQUENCE          2
             94 CALL                     2
            102 CACHE
            104 STORE_FAST               3 (ans)

913         106 LOAD_FAST                3 (ans)
            108 POP_JUMP_IF_FALSE        2 (to 114)

914         110 LOAD_CONST               1 (False)
            112 RETURN_VALUE

915     >>  114 LOAD_FAST                0 (self)
            116 STORE_SUBSCR
            120 CACHE
            122 CACHE
            124 CACHE
            126 CACHE
            128 CACHE
            130 CACHE
            132 CACHE
            134 CACHE
            136 CACHE
            138 LOAD_FAST                1 (other)
            140 UNPACK_SEQUENCE          1
            144 CALL                     1
            152 CACHE
            154 LOAD_CONST               2 (0)
            156 COMPARE_OP               1 (<)
            160 CACHE
            162 RETURN_VALUE

Disassembly of <code object __gt__ at 0x000001E77E79DFD0, file "_pydecimal.py", line 917>:
917           0 RESUME                   0

918           2 LOAD_GLOBAL              1 (NULL + _convert_for_comparison)
             12 CACHE
             14 LOAD_FAST                0 (self)
             16 LOAD_FAST                1 (other)
             18 UNPACK_SEQUENCE          2
             22 CALL                     2
             30 CACHE
             32 UNPACK_SEQUENCE          2
             36 STORE_FAST               0 (self)
             38 STORE_FAST               1 (other)

919          40 LOAD_FAST                1 (other)
             42 LOAD_GLOBAL              2 (NotImplemented)
             52 CACHE
             54 IS_OP                    0
             56 POP_JUMP_IF_FALSE        2 (to 62)

920          58 LOAD_FAST                1 (other)
             60 RETURN_VALUE

921     >>   62 LOAD_FAST                0 (self)
             64 STORE_SUBSCR
             68 CACHE
             70 CACHE
             72 CACHE
             74 CACHE
             76 CACHE
             78 CACHE
             80 CACHE
             82 CACHE
             84 CACHE
             86 LOAD_FAST                1 (other)
             88 LOAD_FAST                2 (context)
             90 UNPACK_SEQUENCE          2
             94 CALL                     2
            102 CACHE
            104 STORE_FAST               3 (ans)

922         106 LOAD_FAST                3 (ans)
            108 POP_JUMP_IF_FALSE        2 (to 114)

923         110 LOAD_CONST               1 (False)
            112 RETURN_VALUE

924     >>  114 LOAD_FAST                0 (self)
            116 STORE_SUBSCR
            120 CACHE
            122 CACHE
            124 CACHE
            126 CACHE
            128 CACHE
            130 CACHE
            132 CACHE
            134 CACHE
            136 CACHE
            138 LOAD_FAST                1 (other)
            140 UNPACK_SEQUENCE          1
            144 CALL                     1
            152 CACHE
            154 LOAD_CONST               2 (0)
            156 COMPARE_OP               4 (<)
            160 CACHE
            162 RETURN_VALUE

Disassembly of <code object __ge__ at 0x000001E77E79E140, file "_pydecimal.py", line 926>:
926           0 RESUME                   0

927           2 LOAD_GLOBAL              1 (NULL + _convert_for_comparison)
             12 CACHE
             14 LOAD_FAST                0 (self)
             16 LOAD_FAST                1 (other)
             18 UNPACK_SEQUENCE          2
             22 CALL                     2
             30 CACHE
             32 UNPACK_SEQUENCE          2
             36 STORE_FAST               0 (self)
             38 STORE_FAST               1 (other)

928          40 LOAD_FAST                1 (other)
             42 LOAD_GLOBAL              2 (NotImplemented)
             52 CACHE
             54 IS_OP                    0
             56 POP_JUMP_IF_FALSE        2 (to 62)

929          58 LOAD_FAST                1 (other)
             60 RETURN_VALUE

930     >>   62 LOAD_FAST                0 (self)
             64 STORE_SUBSCR
             68 CACHE
             70 CACHE
             72 CACHE
             74 CACHE
             76 CACHE
             78 CACHE
             80 CACHE
             82 CACHE
             84 CACHE
             86 LOAD_FAST                1 (other)
             88 LOAD_FAST                2 (context)
             90 UNPACK_SEQUENCE          2
             94 CALL                     2
            102 CACHE
            104 STORE_FAST               3 (ans)

931         106 LOAD_FAST                3 (ans)
            108 POP_JUMP_IF_FALSE        2 (to 114)

932         110 LOAD_CONST               1 (False)
            112 RETURN_VALUE

933     >>  114 LOAD_FAST                0 (self)
            116 STORE_SUBSCR
            120 CACHE
            122 CACHE
            124 CACHE
            126 CACHE
            128 CACHE
            130 CACHE
            132 CACHE
            134 CACHE
            136 CACHE
            138 LOAD_FAST                1 (other)
            140 UNPACK_SEQUENCE          1
            144 CALL                     1
            152 CACHE
            154 LOAD_CONST               2 (0)
            156 COMPARE_OP               5 (<)
            160 CACHE
            162 RETURN_VALUE

Disassembly of <code object compare at 0x000001E77E720AB0, file "_pydecimal.py", line 935>:
935           0 RESUME                   0

943           2 LOAD_GLOBAL              1 (NULL + _convert_other)
             12 CACHE
             14 LOAD_FAST                1 (other)
             16 LOAD_CONST               1 (True)
             18 KW_NAMES                 2 (('raiseit',))
             20 UNPACK_SEQUENCE          2
             24 CALL                     2
             32 CACHE
             34 STORE_FAST               1 (other)

946          36 LOAD_FAST                0 (self)
             38 LOAD_ATTR                1 (NULL|self + _convert_other)
             58 CACHE
             60 CACHE
             62 CACHE
             64 CACHE
             66 POP_JUMP_IF_FALSE       26 (to 120)

947          68 LOAD_FAST                0 (self)
             70 STORE_SUBSCR
             74 CACHE
             76 CACHE
             78 CACHE
             80 CACHE
             82 CACHE
             84 CACHE
             86 CACHE
             88 CACHE
             90 CACHE
             92 LOAD_FAST                1 (other)
             94 LOAD_FAST                2 (context)
             96 UNPACK_SEQUENCE          2
            100 CALL                     2
            108 CACHE
            110 STORE_FAST               3 (ans)

948         112 LOAD_FAST                3 (ans)
            114 POP_JUMP_IF_FALSE        2 (to 120)

949         116 LOAD_FAST                3 (ans)
            118 RETURN_VALUE

951     >>  120 LOAD_GLOBAL              7 (NULL + Decimal)
            130 CACHE
            132 LOAD_FAST                0 (self)
            134 STORE_SUBSCR
            138 CACHE
            140 CACHE
            142 CACHE
            144 CACHE
            146 CACHE
            148 CACHE
            150 CACHE
            152 CACHE
            154 CACHE
            156 LOAD_FAST                1 (other)
            158 UNPACK_SEQUENCE          1
            162 CALL                     1
            170 CACHE
            172 UNPACK_SEQUENCE          1
            176 CALL                     1
            184 CACHE
            186 RETURN_VALUE

Disassembly of <code object __hash__ at 0x000001E77C480A20, file "_pydecimal.py", line 953>:
953           0 RESUME                   0

960           2 LOAD_FAST                0 (self)
              4 LOAD_ATTR                0 (_is_special)
             24 CACHE
             26 CACHE
             28 CACHE
             30 CACHE
             32 CACHE
             34 CACHE
             36 CACHE
             38 CACHE
             40 UNPACK_SEQUENCE          0
             44 CALL                     0
             52 CACHE
             54 POP_JUMP_IF_FALSE       15 (to 86)

962          56 LOAD_GLOBAL              5 (NULL + TypeError)
             66 CACHE
             68 LOAD_CONST               1 ('Cannot hash a signaling NaN value.')
             70 UNPACK_SEQUENCE          1
             74 CALL                     1
             82 CACHE
             84 RAISE_VARARGS            1

963     >>   86 LOAD_FAST                0 (self)
             88 STORE_SUBSCR
             92 CACHE
             94 CACHE
             96 CACHE
             98 CACHE
            100 CACHE
            102 CACHE
            104 CACHE
            106 CACHE
            108 CACHE
            110 UNPACK_SEQUENCE          0
            114 CALL                     0
            122 CACHE
            124 POP_JUMP_IF_FALSE       26 (to 178)

964         126 LOAD_GLOBAL              8 (object)
            136 CACHE
            138 STORE_SUBSCR
            142 CACHE
            144 CACHE
            146 CACHE
            148 CACHE
            150 CACHE
            152 CACHE
            154 CACHE
            156 CACHE
            158 CACHE
            160 LOAD_FAST                0 (self)
            162 UNPACK_SEQUENCE          1
            166 CALL                     1
            174 CACHE
            176 RETURN_VALUE

966     >>  178 LOAD_FAST                0 (self)
            180 LOAD_ATTR                6 (is_nan)
            200 CACHE
            202 CACHE
            204 UNARY_NEGATIVE
            206 RETURN_VALUE

969         208 LOAD_GLOBAL             14 (_PyHASH_INF)
            218 CACHE
            220 RETURN_VALUE

971         222 LOAD_FAST                0 (self)
            224 LOAD_ATTR                8 (object)

972         244 LOAD_GLOBAL             19 (NULL + pow)
            254 CACHE
            256 LOAD_CONST               3 (10)
            258 LOAD_FAST                0 (self)
            260 LOAD_ATTR                8 (object)
            280 CACHE
            282 UNPACK_SEQUENCE          3
            286 CALL                     3
            294 CACHE
            296 STORE_FAST               1 (exp_hash)
            298 JUMP_FORWARD            33 (to 366)

974         300 LOAD_GLOBAL             19 (NULL + pow)
            310 CACHE
            312 LOAD_GLOBAL             22 (_PyHASH_10INV)
            322 CACHE
            324 LOAD_FAST                0 (self)
            326 LOAD_ATTR                8 (object)
            346 CACHE
            348 CACHE
            350 UNPACK_SEQUENCE          3
            354 CALL                     3
            362 CACHE
            364 STORE_FAST               1 (exp_hash)

975     >>  366 LOAD_GLOBAL             25 (NULL + int)
            376 CACHE
            378 LOAD_FAST                0 (self)
            380 LOAD_ATTR               13 (NULL|self + _sign)
            400 CACHE
            402 CACHE
            404 LOAD_FAST                1 (exp_hash)
            406 BINARY_OP                5 (*)
            410 LOAD_GLOBAL             20 (_PyHASH_MODULUS)
            420 CACHE
            422 BINARY_OP                6 (%)
            426 STORE_FAST               2 (hash_)

976         428 LOAD_FAST                0 (self)
            430 LOAD_CONST               2 (0)
            432 COMPARE_OP               5 (<)
            436 CACHE
            438 POP_JUMP_IF_FALSE        2 (to 444)
            440 LOAD_FAST                2 (hash_)
            442 JUMP_FORWARD             2 (to 448)
        >>  444 LOAD_FAST                2 (hash_)
            446 UNARY_NEGATIVE
        >>  448 STORE_FAST               3 (ans)

977         450 LOAD_FAST                3 (ans)
            452 LOAD_CONST               4 (-1)
            454 COMPARE_OP               2 (<)
            458 CACHE
            460 POP_JUMP_IF_FALSE        2 (to 466)
            462 LOAD_CONST               5 (-2)
            464 JUMP_FORWARD             1 (to 468)
        >>  466 LOAD_FAST                3 (ans)
        >>  468 RETURN_VALUE

Disassembly of <code object as_tuple at 0x000001E77E6D6640, file "_pydecimal.py", line 979>:
979           0 RESUME                   0

984           2 LOAD_GLOBAL              1 (NULL + DecimalTuple)
             12 CACHE
             14 LOAD_FAST                0 (self)
             16 LOAD_ATTR                1 (NULL|self + DecimalTuple)
             36 CACHE
             38 LOAD_GLOBAL              7 (NULL + map)
             48 CACHE
             50 LOAD_GLOBAL              8 (int)
             60 CACHE
             62 LOAD_FAST                0 (self)
             64 LOAD_ATTR                5 (NULL|self + tuple)
             84 CACHE
             86 CACHE
             88 UNPACK_SEQUENCE          1
             92 CALL                     1
            100 CACHE
            102 LOAD_FAST                0 (self)
            104 LOAD_ATTR                6 (map)
            124 CACHE
            126 CACHE
            128 RETURN_VALUE

Disassembly of <code object as_integer_ratio at 0x000001E77C47FFA0, file "_pydecimal.py", line 986>:
 986           0 RESUME                   0

1000           2 LOAD_FAST                0 (self)
               4 LOAD_ATTR                0 (_is_special)
              24 CACHE
              26 CACHE
              28 CACHE
              30 CACHE
              32 CACHE
              34 CACHE
              36 CACHE
              38 CACHE
              40 UNPACK_SEQUENCE          0
              44 CALL                     0
              52 CACHE
              54 POP_JUMP_IF_FALSE       15 (to 86)

1002          56 LOAD_GLOBAL              5 (NULL + ValueError)
              66 CACHE
              68 LOAD_CONST               1 ('cannot convert NaN to integer ratio')
              70 UNPACK_SEQUENCE          1
              74 CALL                     1
              82 CACHE
              84 RAISE_VARARGS            1

1004     >>   86 LOAD_GLOBAL              7 (NULL + OverflowError)
              96 CACHE
              98 LOAD_CONST               2 ('cannot convert Infinity to integer ratio')
             100 UNPACK_SEQUENCE          1
             104 CALL                     1
             112 CACHE
             114 RAISE_VARARGS            1

1006         116 LOAD_FAST                0 (self)
             118 POP_JUMP_IF_TRUE         2 (to 124)

1007         120 LOAD_CONST               3 ((0, 1))
             122 RETURN_VALUE

1011     >>  124 LOAD_GLOBAL              9 (NULL + int)
             134 CACHE
             136 LOAD_FAST                0 (self)
             138 LOAD_ATTR                5 (NULL|self + ValueError)
             158 CACHE
             160 CACHE
             162 STORE_FAST               1 (n)

1012         164 LOAD_FAST                0 (self)
             166 LOAD_ATTR                6 (OverflowError)

1014         186 LOAD_FAST                1 (n)
             188 LOAD_CONST               5 (10)
             190 LOAD_FAST                0 (self)
             192 LOAD_ATTR                6 (OverflowError)
             212 STORE_FAST               2 (d)
             214 STORE_FAST               1 (n)
             216 JUMP_FORWARD           117 (to 452)

1017         218 LOAD_FAST                0 (self)
             220 LOAD_ATTR                6 (OverflowError)
             240 CACHE
             242 CACHE
             244 POP_JUMP_IF_FALSE       34 (to 314)
             246 LOAD_FAST                1 (n)
             248 LOAD_CONST               7 (5)
             250 BINARY_OP                6 (%)
             254 LOAD_CONST               4 (0)
             256 COMPARE_OP               2 (<)
             260 CACHE
             262 POP_JUMP_IF_FALSE       25 (to 314)

1019         264 LOAD_FAST                1 (n)
             266 LOAD_CONST               7 (5)
             268 BINARY_OP               15 (//=)
             272 STORE_FAST               1 (n)

1020         274 LOAD_FAST                3 (d5)
             276 LOAD_CONST               6 (1)
             278 BINARY_OP               23 (-=)
             282 STORE_FAST               3 (d5)

1018         284 LOAD_FAST                3 (d5)
             286 LOAD_CONST               4 (0)
             288 COMPARE_OP               4 (<)
             292 CACHE
             294 POP_JUMP_IF_FALSE        9 (to 314)
             296 LOAD_FAST                1 (n)
             298 LOAD_CONST               7 (5)
             300 BINARY_OP                6 (%)
             304 LOAD_CONST               4 (0)
             306 COMPARE_OP               2 (<)
             310 CACHE
