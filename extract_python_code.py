#!/usr/bin/env python3
"""
Extract Python bytecode from executable
"""
import struct
import os
import sys
import marshal
import dis
import types

def find_pyc_files(data):
    """Find potential .pyc file headers in binary data"""
    pyc_files = []
    
    # Python 3.x magic numbers (first 4 bytes of .pyc files)
    magic_numbers = [
        b'\x42\x0d\x0d\x0a',  # Python 3.11
        b'\x6f\x0d\x0d\x0a',  # Python 3.10
        b'\x61\x0d\x0d\x0a',  # Python 3.9
        b'\x55\x0d\x0d\x0a',  # Python 3.8
        b'\x42\x0d\x0d\x0a',  # Python 3.7
        b'\x33\x0d\x0d\x0a',  # Python 3.6
        b'\x17\x0d\x0d\x0a',  # Python 3.5
        b'\xee\x0c\x0d\x0a',  # Python 3.4
        b'\x9e\x0c\x0d\x0a',  # Python 3.3
        b'\x3b\x0c\x0d\x0a',  # Python 3.2
        b'\xf2\x0b\x0d\x0a',  # Python 3.1
        b'\xd1\x0b\x0d\x0a',  # Python 3.0
    ]
    
    for magic in magic_numbers:
        offset = 0
        while True:
            pos = data.find(magic, offset)
            if pos == -1:
                break
                
            # Check if this looks like a valid .pyc header
            if pos + 16 <= len(data):  # Need at least 16 bytes for header
                header = data[pos:pos+16]
                try:
                    # Try to parse the header
                    magic_bytes = header[:4]
                    timestamp = struct.unpack('<I', header[4:8])[0]
                    size = struct.unpack('<I', header[8:12])[0] if len(header) >= 12 else 0
                    
                    pyc_files.append({
                        'position': pos,
                        'magic': magic_bytes,
                        'timestamp': timestamp,
                        'size': size,
                        'header': header
                    })
                except:
                    pass
                    
            offset = pos + 1
            
    return pyc_files

def extract_marshal_data(data, start_pos):
    """Try to extract marshal data starting from a position"""
    try:
        # Skip the .pyc header (16 bytes for Python 3.7+, 12 bytes for older)
        marshal_start = start_pos + 16
        if marshal_start >= len(data):
            marshal_start = start_pos + 12
            
        if marshal_start >= len(data):
            return None
            
        # Try to load marshal data
        marshal_data = data[marshal_start:]
        code_obj = marshal.loads(marshal_data)
        return code_obj
    except:
        try:
            # Try with 12-byte header
            marshal_start = start_pos + 12
            if marshal_start >= len(data):
                return None
                
            marshal_data = data[marshal_start:]
            code_obj = marshal.loads(marshal_data)
            return code_obj
        except:
            return None

def decompile_code_object(code_obj, output_dir, filename):
    """Attempt to decompile a code object"""
    try:
        # Create output directory
        os.makedirs(output_dir, exist_ok=True)
        
        # Try to use uncompyle6 if available
        try:
            import uncompyle6
            output_path = os.path.join(output_dir, filename + '.py')
            with open(output_path, 'w') as f:
                uncompyle6.decompile(code_obj, f)
            print(f"Decompiled to {output_path}")
            return True
        except ImportError:
            print("uncompyle6 not available, trying dis module")
        except Exception as e:
            print(f"uncompyle6 failed: {e}")
            
        # Fallback to dis module for bytecode disassembly
        output_path = os.path.join(output_dir, filename + '_disasm.txt')
        with open(output_path, 'w') as f:
            f.write(f"# Disassembly of {filename}\n")
            f.write(f"# Code object: {code_obj}\n\n")
            dis.dis(code_obj, file=f)
            
            # Also try to extract string constants
            if hasattr(code_obj, 'co_consts'):
                f.write("\n\n# Constants:\n")
                for i, const in enumerate(code_obj.co_consts):
                    f.write(f"# {i}: {repr(const)}\n")
                    
            if hasattr(code_obj, 'co_names'):
                f.write("\n\n# Names:\n")
                for i, name in enumerate(code_obj.co_names):
                    f.write(f"# {i}: {repr(name)}\n")
                    
        print(f"Disassembled to {output_path}")
        return True
        
    except Exception as e:
        print(f"Failed to decompile {filename}: {e}")
        return False

def extract_python_from_exe(exe_path, output_dir):
    """Extract Python code from executable"""
    print(f"Extracting Python code from {exe_path}")
    
    with open(exe_path, 'rb') as f:
        data = f.read()
        
    print(f"File size: {len(data)} bytes")
    
    # Find potential .pyc files
    pyc_files = find_pyc_files(data)
    print(f"Found {len(pyc_files)} potential .pyc files")
    
    extracted_count = 0
    
    for i, pyc_info in enumerate(pyc_files):
        print(f"\nProcessing .pyc file {i+1}/{len(pyc_files)} at position {pyc_info['position']}")
        
        # Try to extract marshal data
        code_obj = extract_marshal_data(data, pyc_info['position'])
        if code_obj:
            filename = f"extracted_{i+1:03d}"
            if hasattr(code_obj, 'co_filename') and code_obj.co_filename:
                # Use original filename if available
                orig_name = os.path.basename(code_obj.co_filename)
                if orig_name and orig_name != '<string>':
                    filename = orig_name.replace('.py', '')
                    
            if decompile_code_object(code_obj, output_dir, filename):
                extracted_count += 1
        else:
            print(f"  Failed to extract marshal data")
            
    print(f"\nExtracted {extracted_count} Python files to {output_dir}")

if __name__ == "__main__":
    if len(sys.argv) != 3:
        print("Usage: python extract_python_code.py <exe_file> <output_dir>")
        sys.exit(1)
        
    exe_path = sys.argv[1]
    output_dir = sys.argv[2]
    
    extract_python_from_exe(exe_path, output_dir)
