# Code object from position 7339370
# Filename: _osx_support.py
# Name: <module>
# Args: 0
# Locals: 0

  0           0 RESUME                   0

  1           2 LOAD_CONST               0 ('Shared OS X support functions.')
              4 STORE_NAME               0 (__doc__)

  3           6 LOAD_CONST               1 (0)
              8 LOAD_CONST               2 (None)
             10 IMPORT_NAME              1 (os)
             12 STORE_NAME               1 (os)

  4          14 LOAD_CONST               1 (0)
             16 LOAD_CONST               2 (None)
             18 IMPORT_NAME              2 (re)
             20 STORE_NAME               2 (re)

  5          22 LOAD_CONST               1 (0)
             24 LOAD_CONST               2 (None)
             26 IMPORT_NAME              3 (sys)
             28 STORE_NAME               3 (sys)

  7          30 BUILD_LIST               0
             32 LOAD_CONST               3 (('compiler_fixup', 'customize_config_vars', 'customize_compiler', 'get_platform_osx'))
             34 LIST_EXTEND              1
             36 STORE_NAME               4 (__all__)

 17          38 LOAD_CONST               4 (('CFLAGS', 'LDFLAGS', 'CPPFLAGS', 'BASECFLAGS', 'BLDSHARED', 'LDSHARED', 'CC', 'CXX', 'PY_CFLAGS', 'PY_LDFLAGS', 'PY_CPPFLAGS', 'PY_CORE_CFLAGS', 'PY_CORE_LDFLAGS'))
             40 STORE_NAME               5 (_UNIVERSAL_CONFIG_VARS)

 23          42 LOAD_CONST               5 (('BLDSHARED', 'LDSHARED', 'CC', 'CXX'))
             44 STORE_NAME               6 (_COMPILER_CONFIG_VARS)

 26          46 LOAD_CONST               6 ('_OSX_SUPPORT_INITIAL_')
             48 STORE_NAME               7 (_INITPRE)

 29          50 LOAD_CONST              27 ((None,))
             52 LOAD_CONST               7 (<code object _find_executable at 0x000001A2CDEA6BC0, file "_osx_support.py", line 29>)
             54 MAKE_FUNCTION            1 (defaults)
             56 STORE_NAME               8 (_find_executable)

 55          58 LOAD_CONST              28 ((False,))
             60 LOAD_CONST               9 (<code object _read_output at 0x000001A2D03BC540, file "_osx_support.py", line 55>)
             62 MAKE_FUNCTION            1 (defaults)
             64 STORE_NAME               9 (_read_output)

 77          66 LOAD_CONST              10 (<code object _find_build_tool at 0x000001A2D05F9130, file "_osx_support.py", line 77>)
             68 MAKE_FUNCTION            0
             70 STORE_NAME              10 (_find_build_tool)

 84          72 LOAD_CONST               2 (None)
             74 STORE_GLOBAL            11 (_SYSTEM_VERSION)

 86          76 LOAD_CONST              11 (<code object _get_system_version at 0x000001A2D03B9760, file "_osx_support.py", line 86>)
             78 MAKE_FUNCTION            0
             80 STORE_NAME              12 (_get_system_version)

116          82 LOAD_CONST               2 (None)
             84 STORE_GLOBAL            13 (_SYSTEM_VERSION_TUPLE)

117          86 LOAD_CONST              12 (<code object _get_system_version_tuple at 0x000001A2D01504B0, file "_osx_support.py", line 117>)
             88 MAKE_FUNCTION            0
             90 STORE_NAME              14 (_get_system_version_tuple)

136          92 LOAD_CONST              13 (<code object _remove_original_values at 0x000001A2D019EAF0, file "_osx_support.py", line 136>)
             94 MAKE_FUNCTION            0
             96 STORE_NAME              15 (_remove_original_values)

143          98 LOAD_CONST              14 (<code object _save_modified_value at 0x000001A2D0198B70, file "_osx_support.py", line 143>)
            100 MAKE_FUNCTION            0
            102 STORE_NAME              16 (_save_modified_value)

152         104 LOAD_CONST               2 (None)
            106 STORE_GLOBAL            17 (_cache_default_sysroot)

153         108 LOAD_CONST              15 (<code object _default_sysroot at 0x000001A2CDEFDB80, file "_osx_support.py", line 153>)
            110 MAKE_FUNCTION            0
            112 STORE_NAME              18 (_default_sysroot)

178         114 LOAD_CONST              16 (<code object _supports_universal_builds at 0x000001A2D05F9240, file "_osx_support.py", line 178>)
            116 MAKE_FUNCTION            0
            118 STORE_NAME              19 (_supports_universal_builds)

188         120 LOAD_CONST              17 (<code object _supports_arm64_builds at 0x000001A2D059B530, file "_osx_support.py", line 188>)
            122 MAKE_FUNCTION            0
            124 STORE_NAME              20 (_supports_arm64_builds)

198         126 LOAD_CONST              18 (<code object _find_appropriate_compiler at 0x000001A2CDE23B20, file "_osx_support.py", line 198>)
            128 MAKE_FUNCTION            0
            130 STORE_NAME              21 (_find_appropriate_compiler)

260         132 LOAD_CONST              19 (<code object _remove_universal_flags at 0x000001A2D010DBD0, file "_osx_support.py", line 260>)
            134 MAKE_FUNCTION            0
            136 STORE_NAME              22 (_remove_universal_flags)

274         138 LOAD_CONST              20 (<code object _remove_unsupported_archs at 0x000001A2CDE93140, file "_osx_support.py", line 274>)
            140 MAKE_FUNCTION            0
            142 STORE_NAME              23 (_remove_unsupported_archs)

314         144 LOAD_CONST              21 (<code object _override_all_archs at 0x000001A2D01AD110, file "_osx_support.py", line 314>)
            146 MAKE_FUNCTION            0
            148 STORE_NAME              24 (_override_all_archs)

331         150 LOAD_CONST              22 (<code object _check_for_unavailable_sdk at 0x000001A2CDE93BE0, file "_osx_support.py", line 331>)
            152 MAKE_FUNCTION            0
            154 STORE_NAME              25 (_check_for_unavailable_sdk)

358         156 LOAD_CONST              23 (<code object compiler_fixup at 0x000001A2CDE9DC40, file "_osx_support.py", line 358>)
            158 MAKE_FUNCTION            0
            160 STORE_NAME              26 (compiler_fixup)

438         162 LOAD_CONST              24 (<code object customize_config_vars at 0x000001A2D0199930, file "_osx_support.py", line 438>)
            164 MAKE_FUNCTION            0
            166 STORE_NAME              27 (customize_config_vars)

479         168 LOAD_CONST              25 (<code object customize_compiler at 0x000001A2D05F1590, file "_osx_support.py", line 479>)
            170 MAKE_FUNCTION            0
            172 STORE_NAME              28 (customize_compiler)

499         174 LOAD_CONST              26 (<code object get_platform_osx at 0x000001A2D03C6140, file "_osx_support.py", line 499>)
            176 MAKE_FUNCTION            0
            178 STORE_NAME              29 (get_platform_osx)
            180 LOAD_CONST               2 (None)
            182 RETURN_VALUE

Disassembly of <code object _find_executable at 0x000001A2CDEA6BC0, file "_osx_support.py", line 29>:
 29           0 RESUME                   0

 35           2 LOAD_FAST                1 (path)
              4 POP_JUMP_IF_NOT_NONE    18 (to 42)

 36           6 LOAD_GLOBAL              0 (os)
             16 CACHE
             18 LOAD_ATTR                1 (NULL|self + os)
             38 CACHE
             40 STORE_FAST               1 (path)

 38     >>   42 LOAD_FAST                1 (path)
             44 STORE_SUBSCR
             48 CACHE
             50 CACHE
             52 CACHE
             54 CACHE
             56 CACHE
             58 CACHE
             60 CACHE
             62 CACHE
             64 CACHE
             66 LOAD_GLOBAL              0 (os)
             76 CACHE
             78 LOAD_ATTR                3 (NULL|self + environ)
             98 CACHE
            100 CACHE
            102 STORE_FAST               2 (paths)

 39         104 LOAD_GLOBAL              0 (os)
            114 CACHE
            116 LOAD_ATTR                4 (split)
            136 CACHE
            138 CACHE
            140 CACHE
            142 CACHE
            144 CACHE
            146 CACHE
            148 LOAD_FAST                0 (executable)
            150 UNPACK_SEQUENCE          1
            154 CALL                     1
            162 CACHE
            164 UNPACK_SEQUENCE          2
            168 STORE_FAST               3 (base)
            170 STORE_FAST               4 (ext)

 41         172 LOAD_GLOBAL             12 (sys)
            182 CACHE
            184 LOAD_ATTR                7 (NULL|self + pathsep)
            204 LOAD_FAST                4 (ext)
            206 LOAD_CONST               4 ('.exe')
            208 COMPARE_OP               3 (<)
            212 CACHE
            214 POP_JUMP_IF_FALSE        5 (to 226)

 42         216 LOAD_FAST                0 (executable)
            218 LOAD_CONST               4 ('.exe')
            220 BINARY_OP                0 (+)
            224 STORE_FAST               0 (executable)

 44     >>  226 LOAD_GLOBAL              0 (os)
            236 CACHE
            238 LOAD_ATTR                4 (split)
            258 CACHE
            260 CACHE
            262 CACHE
            264 CACHE
            266 CACHE
            268 CACHE
            270 LOAD_FAST                0 (executable)
            272 UNPACK_SEQUENCE          1
            276 CALL                     1
            284 CACHE
            286 POP_JUMP_IF_TRUE        74 (to 436)

 45         288 LOAD_FAST                2 (paths)
            290 GET_ITER
        >>  292 FOR_ITER                69 (to 434)

 46         296 LOAD_GLOBAL              0 (os)
            306 CACHE
            308 LOAD_ATTR                4 (split)
            328 CACHE
            330 CACHE
            332 CACHE
            334 CACHE
            336 CACHE
            338 CACHE
            340 LOAD_FAST                5 (p)
            342 LOAD_FAST                0 (executable)
            344 UNPACK_SEQUENCE          2
            348 CALL                     2
            356 CACHE
            358 STORE_FAST               6 (f)

 47         360 LOAD_GLOBAL              0 (os)
            370 CACHE
            372 LOAD_ATTR                4 (split)
            392 CACHE
            394 CACHE
            396 CACHE
            398 CACHE
            400 CACHE
            402 CACHE
            404 LOAD_FAST                6 (f)
            406 UNPACK_SEQUENCE          1
            410 CALL                     1
            418 CACHE
            420 POP_JUMP_IF_FALSE        4 (to 430)

 49         422 LOAD_FAST                6 (f)
            424 SWAP                     2
            426 POP_TOP
            428 RETURN_VALUE

 47     >>  430 JUMP_BACKWARD           70 (to 292)

 50         432 LOAD_CONST               1 (None)
        >>  434 RETURN_VALUE

 52     >>  436 LOAD_FAST                0 (executable)
            438 RETURN_VALUE

Disassembly of <code object _read_output at 0x000001A2D03BC540, file "_osx_support.py", line 55>:
 55           0 RESUME                   0

 61           2 LOAD_CONST               1 (0)
              4 LOAD_CONST               2 (None)
              6 IMPORT_NAME              0 (contextlib)
              8 STORE_FAST               2 (contextlib)

 62          10 NOP

 63          12 LOAD_CONST               1 (0)
             14 LOAD_CONST               2 (None)
             16 IMPORT_NAME              1 (tempfile)
             18 STORE_FAST               3 (tempfile)

 64          20 LOAD_FAST                3 (tempfile)
             22 STORE_SUBSCR
             26 CACHE
             28 CACHE
             30 CACHE
             32 CACHE
             34 CACHE
             36 CACHE
             38 CACHE
             40 CACHE
             42 CACHE
             44 UNPACK_SEQUENCE          0
             48 CALL                     0
             56 CACHE
             58 STORE_FAST               4 (fp)
             60 JUMP_FORWARD            52 (to 166)
        >>   62 PUSH_EXC_INFO

 65          64 LOAD_GLOBAL              6 (ImportError)
             74 CACHE
             76 CHECK_EXC_MATCH
             78 POP_JUMP_IF_FALSE       39 (to 158)
             80 POP_TOP

 66          82 LOAD_GLOBAL              9 (NULL + open)
             92 CACHE
             94 LOAD_CONST               3 ('/tmp/_osx_support.')

 67          96 LOAD_GLOBAL             11 (NULL + os)
            106 CACHE
            108 LOAD_ATTR                6 (ImportError)
            128 CACHE
            130 CACHE
            132 FORMAT_VALUE             1 (str)

 66         134 BUILD_STRING             2

 67         136 LOAD_CONST               4 ('w+b')

 66         138 UNPACK_SEQUENCE          2
            142 CALL                     2
            150 CACHE
            152 STORE_FAST               4 (fp)
            154 POP_EXCEPT
            156 JUMP_FORWARD             4 (to 166)

 65     >>  158 RERAISE                  0
        >>  160 COPY                     3
            162 POP_EXCEPT
            164 RERAISE                  1

 69     >>  166 LOAD_FAST                2 (contextlib)
            168 STORE_SUBSCR
            172 CACHE
            174 CACHE
            176 CACHE
            178 CACHE
            180 CACHE
            182 CACHE
            184 CACHE
            186 CACHE
            188 CACHE
            190 LOAD_FAST                4 (fp)
            192 UNPACK_SEQUENCE          1
            196 CALL                     1
            204 CACHE
            206 BEFORE_WITH
            208 STORE_FAST               4 (fp)

 70         210 LOAD_FAST                1 (capture_stderr)
            212 POP_JUMP_IF_FALSE       14 (to 242)

 71         214 LOAD_FAST                0 (commandstring)
            216 FORMAT_VALUE             1 (str)
            218 LOAD_CONST               5 (" >'")
            220 LOAD_FAST                4 (fp)
            222 LOAD_ATTR                8 (open)

 73     >>  242 LOAD_FAST                0 (commandstring)
            244 FORMAT_VALUE             1 (str)
            246 LOAD_CONST               7 (" 2>/dev/null >'")
            248 LOAD_FAST                4 (fp)
            250 LOAD_ATTR                8 (open)
            270 CACHE
            272 CACHE
            274 CACHE
            276 CACHE
            278 CACHE
            280 LOAD_ATTR                9 (NULL|self + open)
            300 CACHE
            302 CACHE
            304 CACHE
            306 POP_JUMP_IF_TRUE        57 (to 422)
            308 LOAD_FAST                4 (fp)
            310 STORE_SUBSCR
            314 CACHE
            316 CACHE
            318 CACHE
            320 CACHE
            322 CACHE
            324 CACHE
            326 CACHE
            328 CACHE
            330 CACHE
            332 UNPACK_SEQUENCE          0
            336 CALL                     0
            344 CACHE
            346 STORE_SUBSCR
            350 CACHE
            352 CACHE
            354 CACHE
            356 CACHE
            358 CACHE
            360 CACHE
            362 CACHE
            364 CACHE
            366 CACHE
            368 LOAD_CONST               9 ('utf-8')
            370 UNPACK_SEQUENCE          1
            374 CALL                     1
            382 CACHE
            384 STORE_SUBSCR
            388 CACHE
            390 CACHE
            392 CACHE
            394 CACHE
            396 CACHE
            398 CACHE
            400 CACHE
            402 CACHE
            404 CACHE
            406 UNPACK_SEQUENCE          0
            410 CALL                     0
            418 CACHE
            420 JUMP_FORWARD             1 (to 424)
        >>  422 LOAD_CONST               2 (None)

 69     >>  424 SWAP                     2
            426 LOAD_CONST               2 (None)
            428 LOAD_CONST               2 (None)
            430 LOAD_CONST               2 (None)
            432 UNPACK_SEQUENCE          2
            436 CALL                     2
            444 CACHE
            446 POP_TOP
            448 RETURN_VALUE
        >>  450 PUSH_EXC_INFO
            452 WITH_EXCEPT_START
            454 POP_JUMP_IF_TRUE         4 (to 464)
            456 RERAISE                  2
        >>  458 COPY                     3
            460 POP_EXCEPT
            462 RERAISE                  1
        >>  464 POP_TOP
            466 POP_EXCEPT
            468 POP_TOP
            470 POP_TOP
            472 LOAD_CONST               2 (None)
            474 RETURN_VALUE
ExceptionTable:
  12 to 58 -> 62 [0]
  62 to 152 -> 160 [1] lasti
  158 to 158 -> 160 [1] lasti
  208 to 422 -> 450 [1] lasti
  450 to 456 -> 458 [3] lasti
  464 to 464 -> 458 [3] lasti

Disassembly of <code object _find_build_tool at 0x000001A2D05F9130, file "_osx_support.py", line 77>:
 77           0 RESUME                   0

 79           2 LOAD_GLOBAL              1 (NULL + _find_executable)
             12 CACHE
             14 LOAD_FAST                0 (toolname)
             16 UNPACK_SEQUENCE          1
             20 CALL                     1
             28 CACHE
