#!/usr/bin/env python3
"""
Extract code objects near application-specific strings
"""
import struct
import os
import sys
import marshal
import dis
import types

def find_code_near_strings(data):
    """Find code objects near application-specific strings"""
    # Application-specific string positions we found
    app_string_positions = [
        14467326,  # 'imgproc'
        14472170,  # 'ImgProc'
        14468417,  # 'align_image'
        14472295,  # 'create_object_normal_map'
        14472321,  # 'create_tangent_normal_map'
        14472348,  # 'create_specular_map'
        14472219,  # '--function'
        14472425,  # '--image_dir'
    ]
    
    print("Searching for code objects near application strings...")
    
    marshal_candidates = []
    pattern = b'\xe3'  # TYPE_CODE
    
    # Search in regions around the application strings
    search_radius = 50000  # Search 50KB around each string
    
    for string_pos in app_string_positions:
        start_pos = max(0, string_pos - search_radius)
        end_pos = min(len(data), string_pos + search_radius)
        
        print(f"Searching around position {string_pos} (range {start_pos}-{end_pos})")
        
        # Search for marshal data in this region
        search_data = data[start_pos:end_pos]
        offset = 0
        
        while True:
            pos = search_data.find(pattern, offset)
            if pos == -1:
                break
                
            absolute_pos = start_pos + pos
            
            # Try to unmarshal from this position
            try:
                remaining_data = data[absolute_pos:]
                if len(remaining_data) > 100:
                    obj = marshal.loads(remaining_data)
                    if isinstance(obj, types.CodeType):
                        # Check if we already have this code object
                        already_found = any(c['position'] == absolute_pos for c in marshal_candidates)
                        if not already_found:
                            marshal_candidates.append({
                                'position': absolute_pos,
                                'code_object': obj,
                                'near_string_pos': string_pos,
                                'distance': abs(absolute_pos - string_pos)
                            })
                            filename = getattr(obj, 'co_filename', 'unknown')
                            function = getattr(obj, 'co_name', 'unknown')
                            print(f"  Found code at {absolute_pos}: {filename} ({function})")
            except:
                pass
                
            offset = pos + 1
    
    # Sort by distance to application strings
    marshal_candidates.sort(key=lambda x: x['distance'])
    
    return marshal_candidates

def extract_code_near_app_strings(exe_path, output_dir):
    """Extract code objects near application strings"""
    print(f"Extracting code near application strings from {exe_path}")
    
    with open(exe_path, 'rb') as f:
        data = f.read()
        
    print(f"File size: {len(data)} bytes")
    
    # Create output directory
    os.makedirs(output_dir, exist_ok=True)
    
    # Find code near application strings
    marshal_candidates = find_code_near_strings(data)
    
    print(f"\nFound {len(marshal_candidates)} code objects near application strings")
    
    extracted_count = 0
    for i, candidate in enumerate(marshal_candidates[:20]):  # Process first 20
        try:
            code_obj = candidate['code_object']
            filename = f"near_app_{i+1:03d}"
            
            # Get original filename if available
            if hasattr(code_obj, 'co_filename') and code_obj.co_filename:
                orig_name = os.path.basename(code_obj.co_filename)
                if orig_name and orig_name not in ['<string>', '<frozen>', '<built-in>']:
                    filename = orig_name.replace('.py', '').replace('.', '_').replace('\\', '_').replace('/', '_')
                    
            print(f"\nProcessing {filename}")
            print(f"  Position: {candidate['position']}")
            print(f"  Near string at: {candidate['near_string_pos']}")
            print(f"  Distance: {candidate['distance']} bytes")
            print(f"  Original: {getattr(code_obj, 'co_filename', 'unknown')}")
            print(f"  Function: {getattr(code_obj, 'co_name', 'unknown')}")
            
            # Create detailed disassembly
            output_path = os.path.join(output_dir, filename + '_near_app.txt')
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write(f"# CODE OBJECT NEAR APPLICATION STRINGS\n")
                f.write(f"# Position: {candidate['position']}\n")
                f.write(f"# Near app string at: {candidate['near_string_pos']}\n")
                f.write(f"# Distance: {candidate['distance']} bytes\n")
                f.write(f"# Filename: {getattr(code_obj, 'co_filename', 'unknown')}\n")
                f.write(f"# Function: {getattr(code_obj, 'co_name', 'unknown')}\n")
                f.write(f"# Args: {getattr(code_obj, 'co_argcount', 0)}\n")
                f.write(f"# Locals: {getattr(code_obj, 'co_nlocals', 0)}\n")
                f.write(f"# Stack size: {getattr(code_obj, 'co_stacksize', 0)}\n")
                f.write(f"# Flags: {getattr(code_obj, 'co_flags', 0)}\n\n")
                
                # Disassemble
                f.write("# BYTECODE DISASSEMBLY:\n")
                f.write("=" * 50 + "\n")
                dis.dis(code_obj, file=f)
                
                # Extract constants with more detail
                if hasattr(code_obj, 'co_consts'):
                    f.write("\n\n# CONSTANTS:\n")
                    f.write("=" * 50 + "\n")
                    for j, const in enumerate(code_obj.co_consts):
                        if isinstance(const, str):
                            # Look for application-specific strings
                            app_keywords = ['imgproc', 'align_image', 'create_object_normal_map', 
                                          'create_tangent_normal_map', 'create_specular_map',
                                          'function', 'image_dir', 'output']
                            is_app_string = any(keyword in const.lower() for keyword in app_keywords)
                            marker = " *** APP STRING ***" if is_app_string else ""
                            
                            if len(const) < 200:
                                f.write(f"# {j}: {repr(const)}{marker}\n")
                            else:
                                f.write(f"# {j}: <string length {len(const)}> '{const[:100]}...'{marker}\n")
                        elif isinstance(const, (int, float, bool, type(None))):
                            f.write(f"# {j}: {repr(const)}\n")
                        elif isinstance(const, types.CodeType):
                            f.write(f"# {j}: <code object {const.co_name} from {const.co_filename}>\n")
                        else:
                            f.write(f"# {j}: {type(const).__name__} - {repr(const)[:100]}\n")
                            
                # Extract names
                if hasattr(code_obj, 'co_names'):
                    f.write("\n\n# NAMES (global/attribute references):\n")
                    f.write("=" * 50 + "\n")
                    for j, name in enumerate(code_obj.co_names):
                        # Highlight application-specific names
                        app_keywords = ['imgproc', 'align_image', 'create_object_normal_map', 
                                      'create_tangent_normal_map', 'create_specular_map',
                                      'ArgumentParser', 'argparse']
                        is_app_name = any(keyword in name.lower() for keyword in app_keywords)
                        marker = " *** APP NAME ***" if is_app_name else ""
                        f.write(f"# {j}: {repr(name)}{marker}\n")
                        
                # Extract variable names
                if hasattr(code_obj, 'co_varnames'):
                    f.write("\n\n# VARIABLE NAMES (local variables):\n")
                    f.write("=" * 50 + "\n")
                    for j, name in enumerate(code_obj.co_varnames):
                        f.write(f"# {j}: {repr(name)}\n")
                        
            print(f"  ✓ Analysis saved to {output_path}")
            extracted_count += 1
                
        except Exception as e:
            print(f"  ✗ Failed to process code object {i+1}: {e}")
    
    print(f"\nExtracted {extracted_count} code objects near application strings to {output_dir}")
    
    # Create summary
    summary_path = os.path.join(output_dir, 'NEAR_APP_SUMMARY.txt')
    with open(summary_path, 'w', encoding='utf-8') as f:
        f.write("Code Objects Near Application Strings\n")
        f.write("=" * 50 + "\n\n")
        
        for i, candidate in enumerate(marshal_candidates[:20]):
            code_obj = candidate['code_object']
            f.write(f"{i+1:2d}. Position: {candidate['position']:8d}, ")
            f.write(f"Distance: {candidate['distance']:5d}, ")
            f.write(f"File: {getattr(code_obj, 'co_filename', 'unknown')}, ")
            f.write(f"Name: {getattr(code_obj, 'co_name', 'unknown')}\n")
    
    print(f"Summary saved to {summary_path}")

if __name__ == "__main__":
    if len(sys.argv) != 3:
        print("Usage: python extract_around_strings.py <exe_file> <output_dir>")
        sys.exit(1)
        
    exe_path = sys.argv[1]
    output_dir = sys.argv[2]
    
    extract_code_near_app_strings(exe_path, output_dir)
