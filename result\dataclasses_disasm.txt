# Code object from position 8616224
# Filename: dataclasses.py
# Name: <module>
# Args: 0
# Locals: 0
# Stack size: 13
# Flags: 0

   0           0 RESUME                   0

   1           2 LOAD_CONST               0 (0)
               4 LOAD_CONST               1 (None)
               6 IMPORT_NAME              0 (re)
               8 STORE_NAME               0 (re)

   2          10 LOAD_CONST               0 (0)
              12 LOAD_CONST               1 (None)
              14 IMPORT_NAME              1 (sys)
              16 STORE_NAME               1 (sys)

   3          18 LOAD_CONST               0 (0)
              20 LOAD_CONST               1 (None)
              22 IMPORT_NAME              2 (copy)
              24 STORE_NAME               2 (copy)

   4          26 LOAD_CONST               0 (0)
              28 LOAD_CONST               1 (None)
              30 IMPORT_NAME              3 (types)
              32 STORE_NAME               3 (types)

   5          34 LOAD_CONST               0 (0)
              36 LOAD_CONST               1 (None)
              38 IMPORT_NAME              4 (inspect)
              40 STORE_NAME               4 (inspect)

   6          42 LOAD_CONST               0 (0)
              44 LOAD_CONST               1 (None)
              46 IMPORT_NAME              5 (keyword)
              48 STORE_NAME               5 (keyword)

   7          50 LOAD_CONST               0 (0)
              52 LOAD_CONST               1 (None)
              54 IMPORT_NAME              6 (builtins)
              56 STORE_NAME               6 (builtins)

   8          58 LOAD_CONST               0 (0)
              60 LOAD_CONST               1 (None)
              62 IMPORT_NAME              7 (functools)
              64 STORE_NAME               7 (functools)

   9          66 LOAD_CONST               0 (0)
              68 LOAD_CONST               1 (None)
              70 IMPORT_NAME              8 (itertools)
              72 STORE_NAME               8 (itertools)

  10          74 LOAD_CONST               0 (0)
              76 LOAD_CONST               1 (None)
              78 IMPORT_NAME              9 (abc)
              80 STORE_NAME               9 (abc)

  11          82 LOAD_CONST               0 (0)
              84 LOAD_CONST               1 (None)
              86 IMPORT_NAME             10 (_thread)
              88 STORE_NAME              10 (_thread)

  12          90 LOAD_CONST               0 (0)
              92 LOAD_CONST               2 (('FunctionType', 'GenericAlias'))
              94 IMPORT_NAME              3 (types)
              96 IMPORT_FROM             11 (FunctionType)
              98 STORE_NAME              11 (FunctionType)
             100 IMPORT_FROM             12 (GenericAlias)
             102 STORE_NAME              12 (GenericAlias)
             104 POP_TOP

  15         106 BUILD_LIST               0
             108 LOAD_CONST               3 (('dataclass', 'field', 'Field', 'FrozenInstanceError', 'InitVar', 'KW_ONLY', 'MISSING', 'fields', 'asdict', 'astuple', 'make_dataclass', 'replace', 'is_dataclass'))
             110 LIST_EXTEND              1
             112 STORE_NAME              13 (__all__)

 173         114 PUSH_NULL
             116 LOAD_BUILD_CLASS
             118 LOAD_CONST               4 (<code object FrozenInstanceError at 0x000001B2A767ACE0, file "dataclasses.py", line 173>)
             120 MAKE_FUNCTION            0
             122 LOAD_CONST               5 ('FrozenInstanceError')
             124 LOAD_NAME               14 (AttributeError)
             126 UNPACK_SEQUENCE          3
             130 CALL                     3
             138 CACHE
             140 STORE_NAME              15 (FrozenInstanceError)

 178         142 PUSH_NULL
             144 LOAD_BUILD_CLASS
             146 LOAD_CONST               6 (<code object _HAS_DEFAULT_FACTORY_CLASS at 0x000001B2A7688730, file "dataclasses.py", line 178>)
             148 MAKE_FUNCTION            0
             150 LOAD_CONST               7 ('_HAS_DEFAULT_FACTORY_CLASS')
             152 UNPACK_SEQUENCE          2
             156 CALL                     2
             164 CACHE
             166 STORE_NAME              16 (_HAS_DEFAULT_FACTORY_CLASS)

 181         168 PUSH_NULL
             170 LOAD_NAME               16 (_HAS_DEFAULT_FACTORY_CLASS)
             172 UNPACK_SEQUENCE          0
             176 CALL                     0
             184 CACHE
             186 STORE_NAME              17 (_HAS_DEFAULT_FACTORY)

 185         188 PUSH_NULL
             190 LOAD_BUILD_CLASS
             192 LOAD_CONST               8 (<code object _MISSING_TYPE at 0x000001B2A767A730, file "dataclasses.py", line 185>)
             194 MAKE_FUNCTION            0
             196 LOAD_CONST               9 ('_MISSING_TYPE')
             198 UNPACK_SEQUENCE          2
             202 CALL                     2
             210 CACHE
             212 STORE_NAME              18 (_MISSING_TYPE)

 187         214 PUSH_NULL
             216 LOAD_NAME               18 (_MISSING_TYPE)
             218 UNPACK_SEQUENCE          0
             222 CALL                     0
             230 CACHE
             232 STORE_NAME              19 (MISSING)

 191         234 PUSH_NULL
             236 LOAD_BUILD_CLASS
             238 LOAD_CONST              10 (<code object _KW_ONLY_TYPE at 0x000001B2A767AA70, file "dataclasses.py", line 191>)
             240 MAKE_FUNCTION            0
             242 LOAD_CONST              11 ('_KW_ONLY_TYPE')
             244 UNPACK_SEQUENCE          2
             248 CALL                     2
             256 CACHE
             258 STORE_NAME              20 (_KW_ONLY_TYPE)

 193         260 PUSH_NULL
             262 LOAD_NAME               20 (_KW_ONLY_TYPE)
             264 UNPACK_SEQUENCE          0
             268 CALL                     0
             276 CACHE
             278 STORE_NAME              21 (KW_ONLY)

 197         280 PUSH_NULL
             282 LOAD_NAME                3 (types)
             284 LOAD_ATTR               22 (FunctionType)
             304 CACHE
             306 CACHE
             308 CACHE
             310 STORE_NAME              23 (_EMPTY_METADATA)

 200         312 PUSH_NULL
             314 LOAD_BUILD_CLASS
             316 LOAD_CONST              12 (<code object _FIELD_BASE at 0x000001B2A768B210, file "dataclasses.py", line 200>)
             318 MAKE_FUNCTION            0
             320 LOAD_CONST              13 ('_FIELD_BASE')
             322 UNPACK_SEQUENCE          2
             326 CALL                     2
             334 CACHE
             336 STORE_NAME              24 (_FIELD_BASE)

 205         338 PUSH_NULL
             340 LOAD_NAME               24 (_FIELD_BASE)
             342 LOAD_CONST              14 ('_FIELD')
             344 UNPACK_SEQUENCE          1
             348 CALL                     1
             356 CACHE
             358 STORE_NAME              25 (_FIELD)

 206         360 PUSH_NULL
             362 LOAD_NAME               24 (_FIELD_BASE)
             364 LOAD_CONST              15 ('_FIELD_CLASSVAR')
             366 UNPACK_SEQUENCE          1
             370 CALL                     1
             378 CACHE
             380 STORE_NAME              26 (_FIELD_CLASSVAR)

 207         382 PUSH_NULL
             384 LOAD_NAME               24 (_FIELD_BASE)
             386 LOAD_CONST              16 ('_FIELD_INITVAR')
             388 UNPACK_SEQUENCE          1
             392 CALL                     1
             400 CACHE
             402 STORE_NAME              27 (_FIELD_INITVAR)

 211         404 LOAD_CONST              17 ('__dataclass_fields__')
             406 STORE_NAME              28 (_FIELDS)

 215         408 LOAD_CONST              18 ('__dataclass_params__')
             410 STORE_NAME              29 (_PARAMS)

 219         412 LOAD_CONST              19 ('__post_init__')
             414 STORE_NAME              30 (_POST_INIT_NAME)

 224         416 PUSH_NULL
             418 LOAD_NAME                0 (re)
             420 LOAD_ATTR               31 (NULL|self + FrozenInstanceError)
             440 CACHE
             442 CACHE
             444 CACHE
             446 STORE_NAME              32 (_MODULE_IDENTIFIER_RE)

 228         448 LOAD_CONST              21 (<code object _recursive_repr at 0x000001B2A725F470, file "dataclasses.py", line 228>)
             450 MAKE_FUNCTION            0
             452 STORE_NAME              33 (_recursive_repr)

 246         454 PUSH_NULL
             456 LOAD_BUILD_CLASS
             458 LOAD_CONST              22 (<code object InitVar at 0x000001B2A767E6A0, file "dataclasses.py", line 246>)
             460 MAKE_FUNCTION            0
             462 LOAD_CONST              23 ('InitVar')
             464 UNPACK_SEQUENCE          2
             468 CALL                     2
             476 CACHE
             478 STORE_NAME              34 (InitVar)

 273         480 PUSH_NULL
             482 LOAD_BUILD_CLASS
             484 LOAD_CONST              24 (<code object Field at 0x000001B2A76AD350, file "dataclasses.py", line 273>)
             486 MAKE_FUNCTION            0
             488 LOAD_CONST              25 ('Field')
             490 UNPACK_SEQUENCE          2
             494 CALL                     2
             502 CACHE
             504 STORE_NAME              35 (Field)

 337         506 PUSH_NULL
             508 LOAD_BUILD_CLASS
             510 LOAD_CONST              26 (<code object _DataclassParams at 0x000001B2A7688F10, file "dataclasses.py", line 337>)
             512 MAKE_FUNCTION            0
             514 LOAD_CONST              27 ('_DataclassParams')
             516 UNPACK_SEQUENCE          2
             520 CALL                     2
             528 CACHE
             530 STORE_NAME              36 (_DataclassParams)

 368         532 LOAD_NAME               19 (MISSING)
             534 LOAD_NAME               19 (MISSING)
             536 LOAD_CONST              28 (True)
             538 LOAD_CONST              28 (True)

 369         540 LOAD_CONST               1 (None)
             542 LOAD_CONST              28 (True)
             544 LOAD_CONST               1 (None)
             546 LOAD_NAME               19 (MISSING)

 368         548 LOAD_CONST              29 (('default', 'default_factory', 'init', 'repr', 'hash', 'compare', 'metadata', 'kw_only'))
             550 BUILD_CONST_KEY_MAP      8
             552 LOAD_CONST              30 (<code object field at 0x000001B2A76B43C0, file "dataclasses.py", line 368>)
             554 MAKE_FUNCTION            2 (kwdefaults)
             556 STORE_NAME              37 (field)

 392         558 LOAD_CONST              31 (<code object _fields_in_init_order at 0x000001B2A76B4750, file "dataclasses.py", line 392>)
             560 MAKE_FUNCTION            0
             562 STORE_NAME              38 (_fields_in_init_order)

 401         564 LOAD_CONST              32 (<code object _tuple_str at 0x000001B2A76D8930, file "dataclasses.py", line 401>)
             566 MAKE_FUNCTION            0
             568 STORE_NAME              39 (_tuple_str)

 413         570 LOAD_CONST               1 (None)
             572 LOAD_CONST               1 (None)

 414         574 LOAD_NAME               19 (MISSING)

 413         576 LOAD_CONST              33 (('globals', 'locals', 'return_type'))
             578 BUILD_CONST_KEY_MAP      3
             580 LOAD_CONST              34 (<code object _create_fn at 0x000001B2A75CA360, file "dataclasses.py", line 413>)
             582 MAKE_FUNCTION            2 (kwdefaults)
             584 STORE_NAME              40 (_create_fn)

 437         586 LOAD_CONST              35 (<code object _field_assign at 0x000001B2A7672A30, file "dataclasses.py", line 437>)
             588 MAKE_FUNCTION            0
             590 STORE_NAME              41 (_field_assign)

 449         592 LOAD_CONST              36 (<code object _field_init at 0x000001B2A50622D0, file "dataclasses.py", line 449>)
             594 MAKE_FUNCTION            0
             596 STORE_NAME              42 (_field_init)

 509         598 LOAD_CONST              37 (<code object _init_param at 0x000001B2A7211FB0, file "dataclasses.py", line 509>)
             600 MAKE_FUNCTION            0
             602 STORE_NAME              43 (_init_param)

 528         604 LOAD_CONST              38 (<code object _init_fn at 0x000001B2A75D4690, file "dataclasses.py", line 528>)
             606 MAKE_FUNCTION            0
             608 STORE_NAME              44 (_init_fn)

 588         610 LOAD_CONST              39 (<code object _repr_fn at 0x000001B2A71C63A0, file "dataclasses.py", line 588>)
             612 MAKE_FUNCTION            0
             614 STORE_NAME              45 (_repr_fn)

 599         616 LOAD_CONST              40 (<code object _frozen_get_del_attr at 0x000001B2A71CE3F0, file "dataclasses.py", line 599>)
             618 MAKE_FUNCTION            0
             620 STORE_NAME              46 (_frozen_get_del_attr)

 624         622 LOAD_CONST              41 (<code object _cmp_fn at 0x000001B2A8A0C030, file "dataclasses.py", line 624>)
             624 MAKE_FUNCTION            0
             626 STORE_NAME              47 (_cmp_fn)

 638         628 LOAD_CONST              42 (<code object _hash_fn at 0x000001B2A76D8A50, file "dataclasses.py", line 638>)
             630 MAKE_FUNCTION            0
             632 STORE_NAME              48 (_hash_fn)

 646         634 LOAD_CONST              43 (<code object _is_classvar at 0x000001B2A76D8DB0, file "dataclasses.py", line 646>)
             636 MAKE_FUNCTION            0
             638 STORE_NAME              49 (_is_classvar)

 654         640 LOAD_CONST              44 (<code object _is_initvar at 0x000001B2A8A0C130, file "dataclasses.py", line 654>)
             642 MAKE_FUNCTION            0
             644 STORE_NAME              50 (_is_initvar)

 660         646 LOAD_CONST              45 (<code object _is_kw_only at 0x000001B2A7689370, file "dataclasses.py", line 660>)
             648 MAKE_FUNCTION            0
             650 STORE_NAME              51 (_is_kw_only)

 664         652 LOAD_CONST              46 (<code object _is_type at 0x000001B2A50627F0, file "dataclasses.py", line 664>)
             654 MAKE_FUNCTION            0
             656 STORE_NAME              52 (_is_type)

 723         658 LOAD_CONST              47 (<code object _get_field at 0x000001B2A7444BC0, file "dataclasses.py", line 723>)
             660 MAKE_FUNCTION            0
             662 STORE_NAME              53 (_get_field)

 820         664 LOAD_CONST              48 (<code object _set_qualname at 0x000001B2A76DA310, file "dataclasses.py", line 820>)
             666 MAKE_FUNCTION            0
             668 STORE_NAME              54 (_set_qualname)

 827         670 LOAD_CONST              49 (<code object _set_new_attribute at 0x000001B2A76DA430, file "dataclasses.py", line 827>)
             672 MAKE_FUNCTION            0
             674 STORE_NAME              55 (_set_new_attribute)

 842         676 LOAD_CONST              50 (<code object _hash_set_none at 0x000001B2A767A9A0, file "dataclasses.py", line 842>)
             678 MAKE_FUNCTION            0
             680 STORE_NAME              56 (_hash_set_none)

 845         682 LOAD_CONST              51 (<code object _hash_add at 0x000001B2A76DA550, file "dataclasses.py", line 845>)
             684 MAKE_FUNCTION            0
             686 STORE_NAME              57 (_hash_add)

 849         688 LOAD_CONST              52 (<code object _hash_exception at 0x000001B2A767F3C0, file "dataclasses.py", line 849>)
             690 MAKE_FUNCTION            0
             692 STORE_NAME              58 (_hash_exception)

 863         694 BUILD_MAP                0
             696 LOAD_CONST              53 ((False, False, False, False))
             698 LOAD_CONST               1 (None)
             700 MAP_ADD                  1

 864         702 LOAD_CONST              54 ((False, False, False, True))
             704 LOAD_CONST               1 (None)

 863         706 MAP_ADD                  1

 865         708 LOAD_CONST              55 ((False, False, True, False))
             710 LOAD_CONST               1 (None)

 863         712 MAP_ADD                  1

 866         714 LOAD_CONST              56 ((False, False, True, True))
             716 LOAD_CONST               1 (None)

 863         718 MAP_ADD                  1

 867         720 LOAD_CONST              57 ((False, True, False, False))
             722 LOAD_NAME               56 (_hash_set_none)

 863         724 MAP_ADD                  1

 868         726 LOAD_CONST              58 ((False, True, False, True))
             728 LOAD_CONST               1 (None)

 863         730 MAP_ADD                  1

 869         732 LOAD_CONST              59 ((False, True, True, False))
             734 LOAD_NAME               57 (_hash_add)

 863         736 MAP_ADD                  1

 870         738 LOAD_CONST              60 ((False, True, True, True))
             740 LOAD_CONST               1 (None)

 863         742 MAP_ADD                  1

 871         744 LOAD_CONST              61 ((True, False, False, False))
             746 LOAD_NAME               57 (_hash_add)

 863         748 MAP_ADD                  1

 872         750 LOAD_CONST              62 ((True, False, False, True))
             752 LOAD_NAME               58 (_hash_exception)

 863         754 MAP_ADD                  1

 873         756 LOAD_CONST              63 ((True, False, True, False))
             758 LOAD_NAME               57 (_hash_add)

 863         760 MAP_ADD                  1

 874         762 LOAD_CONST              64 ((True, False, True, True))
             764 LOAD_NAME               58 (_hash_exception)

 863         766 MAP_ADD                  1

 875         768 LOAD_CONST              65 ((True, True, False, False))
             770 LOAD_NAME               57 (_hash_add)

 863         772 MAP_ADD                  1

 876         774 LOAD_CONST              66 ((True, True, False, True))
             776 LOAD_NAME               58 (_hash_exception)

 863         778 MAP_ADD                  1

 877         780 LOAD_CONST              67 ((True, True, True, False))
             782 LOAD_NAME               57 (_hash_add)

 863         784 MAP_ADD                  1

 878         786 LOAD_CONST              68 ((True, True, True, True))
             788 LOAD_NAME               58 (_hash_exception)

 863         790 MAP_ADD                  1
             792 STORE_NAME              59 (_hash_action)

 884         794 LOAD_CONST              69 (<code object _process_class at 0x000001B2A75092B0, file "dataclasses.py", line 884>)
             796 MAKE_FUNCTION            0
             798 STORE_NAME              60 (_process_class)

1123         800 LOAD_CONST              70 (<code object _dataclass_getstate at 0x000001B2A8A0C530, file "dataclasses.py", line 1123>)
             802 MAKE_FUNCTION            0
             804 STORE_NAME              61 (_dataclass_getstate)

1127         806 LOAD_CONST              71 (<code object _dataclass_setstate at 0x000001B2A71C6250, file "dataclasses.py", line 1127>)
             808 MAKE_FUNCTION            0
             810 STORE_NAME              62 (_dataclass_setstate)

1133         812 LOAD_CONST              72 (<code object _get_slots at 0x000001B2A726D2C0, file "dataclasses.py", line 1133>)
             814 MAKE_FUNCTION            0
             816 STORE_NAME              63 (_get_slots)

1149         818 LOAD_CONST              73 (<code object _add_slots at 0x000001B2A75C4000, file "dataclasses.py", line 1149>)
             820 MAKE_FUNCTION            0
             822 STORE_NAME              64 (_add_slots)

1204         824 LOAD_CONST              90 ((None,))
             826 LOAD_CONST              28 (True)
             828 LOAD_CONST              28 (True)
             830 LOAD_CONST              28 (True)
             832 LOAD_CONST              74 (False)

1205         834 LOAD_CONST              74 (False)
             836 LOAD_CONST              74 (False)
             838 LOAD_CONST              28 (True)

1206         840 LOAD_CONST              74 (False)
             842 LOAD_CONST              74 (False)
             844 LOAD_CONST              74 (False)

1204         846 LOAD_CONST              75 (('init', 'repr', 'eq', 'order', 'unsafe_hash', 'frozen', 'match_args', 'kw_only', 'slots', 'weakref_slot'))
             848 BUILD_CONST_KEY_MAP     10
             850 LOAD_CONST              76 (<code object dataclass at 0x000001B2A76ADCE0, file "dataclasses.py", line 1204>)
             852 MAKE_FUNCTION            3 (defaults, kwdefaults)
             854 STORE_NAME              65 (dataclass)

1235         856 LOAD_CONST              77 (<code object fields at 0x000001B2A761F0A0, file "dataclasses.py", line 1235>)
             858 MAKE_FUNCTION            0
             860 STORE_NAME              66 (fields)

1253         862 LOAD_CONST              78 (<code object _is_dataclass_instance at 0x000001B2A76AD570, file "dataclasses.py", line 1253>)
             864 MAKE_FUNCTION            0
             866 STORE_NAME              67 (_is_dataclass_instance)

1258         868 LOAD_CONST              79 (<code object is_dataclass at 0x000001B2A725BC30, file "dataclasses.py", line 1258>)
             870 MAKE_FUNCTION            0
             872 STORE_NAME              68 (is_dataclass)

1265         874 LOAD_NAME               69 (dict)
             876 LOAD_CONST              80 (('dict_factory',))
             878 BUILD_CONST_KEY_MAP      1
             880 LOAD_CONST              81 (<code object asdict at 0x000001B2A76DA670, file "dataclasses.py", line 1265>)
             882 MAKE_FUNCTION            2 (kwdefaults)
             884 STORE_NAME              70 (asdict)

1289         886 LOAD_CONST              82 (<code object _asdict_inner at 0x000001B2A74D3B10, file "dataclasses.py", line 1289>)
             888 MAKE_FUNCTION            0
             890 STORE_NAME              71 (_asdict_inner)

1330         892 LOAD_NAME               72 (tuple)
             894 LOAD_CONST              83 (('tuple_factory',))
             896 BUILD_CONST_KEY_MAP      1
             898 LOAD_CONST              84 (<code object astuple at 0x000001B2A76DA8B0, file "dataclasses.py", line 1330>)
             900 MAKE_FUNCTION            2 (kwdefaults)
             902 STORE_NAME              73 (astuple)

1354         904 LOAD_CONST              85 (<code object _astuple_inner at 0x000001B2A75D2B40, file "dataclasses.py", line 1354>)
             906 MAKE_FUNCTION            0
             908 STORE_NAME              74 (_astuple_inner)

1381         910 LOAD_CONST              86 (())
             912 LOAD_CONST               1 (None)
             914 LOAD_CONST              28 (True)

1382         916 LOAD_CONST              28 (True)
             918 LOAD_CONST              28 (True)
             920 LOAD_CONST              74 (False)
             922 LOAD_CONST              74 (False)

1383         924 LOAD_CONST              74 (False)
             926 LOAD_CONST              28 (True)
             928 LOAD_CONST              74 (False)
             930 LOAD_CONST              74 (False)

1384         932 LOAD_CONST              74 (False)

1381         934 LOAD_CONST              87 (('bases', 'namespace', 'init', 'repr', 'eq', 'order', 'unsafe_hash', 'frozen', 'match_args', 'kw_only', 'slots', 'weakref_slot'))
             936 BUILD_CONST_KEY_MAP     12
             938 LOAD_CONST              88 (<code object make_dataclass at 0x000001B2A75D2120, file "dataclasses.py", line 1381>)
             940 MAKE_FUNCTION            2 (kwdefaults)
             942 STORE_NAME              75 (make_dataclass)

1455         944 LOAD_CONST              89 (<code object replace at 0x000001B2A74D3E90, file "dataclasses.py", line 1455>)
             946 MAKE_FUNCTION            0
             948 STORE_NAME              76 (replace)
             950 LOAD_CONST               1 (None)
             952 RETURN_VALUE

Disassembly of <code object FrozenInstanceError at 0x000001B2A767ACE0, file "dataclasses.py", line 173>:
173           0 RESUME                   0
              2 LOAD_NAME                0 (__name__)
              4 STORE_NAME               1 (__module__)
              6 LOAD_CONST               0 ('FrozenInstanceError')
              8 STORE_NAME               2 (__qualname__)
             10 LOAD_CONST               1 (None)
             12 RETURN_VALUE

Disassembly of <code object _HAS_DEFAULT_FACTORY_CLASS at 0x000001B2A7688730, file "dataclasses.py", line 178>:
178           0 RESUME                   0
              2 LOAD_NAME                0 (__name__)
              4 STORE_NAME               1 (__module__)
              6 LOAD_CONST               0 ('_HAS_DEFAULT_FACTORY_CLASS')
              8 STORE_NAME               2 (__qualname__)

179          10 LOAD_CONST               1 (<code object __repr__ at 0x000001B2A767A660, file "dataclasses.py", line 179>)
             12 MAKE_FUNCTION            0
             14 STORE_NAME               3 (__repr__)
             16 LOAD_CONST               2 (None)
             18 RETURN_VALUE

Disassembly of <code object __repr__ at 0x000001B2A767A660, file "dataclasses.py", line 179>:
179           0 RESUME                   0

180           2 LOAD_CONST               1 ('<factory>')
              4 RETURN_VALUE

Disassembly of <code object _MISSING_TYPE at 0x000001B2A767A730, file "dataclasses.py", line 185>:
185           0 RESUME                   0
              2 LOAD_NAME                0 (__name__)
              4 STORE_NAME               1 (__module__)
              6 LOAD_CONST               0 ('_MISSING_TYPE')
              8 STORE_NAME               2 (__qualname__)

186          10 LOAD_CONST               1 (None)
             12 RETURN_VALUE

Disassembly of <code object _KW_ONLY_TYPE at 0x000001B2A767AA70, file "dataclasses.py", line 191>:
191           0 RESUME                   0
              2 LOAD_NAME                0 (__name__)
              4 STORE_NAME               1 (__module__)
              6 LOAD_CONST               0 ('_KW_ONLY_TYPE')
              8 STORE_NAME               2 (__qualname__)

192          10 LOAD_CONST               1 (None)
             12 RETURN_VALUE

Disassembly of <code object _FIELD_BASE at 0x000001B2A768B210, file "dataclasses.py", line 200>:
200           0 RESUME                   0
              2 LOAD_NAME                0 (__name__)
              4 STORE_NAME               1 (__module__)
              6 LOAD_CONST               0 ('_FIELD_BASE')
              8 STORE_NAME               2 (__qualname__)

201          10 LOAD_CONST               1 (<code object __init__ at 0x000001B2A768B130, file "dataclasses.py", line 201>)
             12 MAKE_FUNCTION            0
             14 STORE_NAME               3 (__init__)

203          16 LOAD_CONST               2 (<code object __repr__ at 0x000001B2A767B1C0, file "dataclasses.py", line 203>)
             18 MAKE_FUNCTION            0
             20 STORE_NAME               4 (__repr__)
             22 LOAD_CONST               3 (None)
             24 RETURN_VALUE

Disassembly of <code object __init__ at 0x000001B2A768B130, file "dataclasses.py", line 201>:
201           0 RESUME                   0

202           2 LOAD_FAST                1 (name)
              4 LOAD_FAST                0 (self)
              6 STORE_ATTR               0 (name)
             16 LOAD_CONST               0 (None)
             18 RETURN_VALUE

Disassembly of <code object __repr__ at 0x000001B2A767B1C0, file "dataclasses.py", line 203>:
203           0 RESUME                   0

204           2 LOAD_FAST                0 (self)
              4 LOAD_ATTR                0 (name)

Disassembly of <code object _recursive_repr at 0x000001B2A725F470, file "dataclasses.py", line 228>:
              0 MAKE_CELL                0 (user_function)
              2 MAKE_CELL                2 (repr_running)

228           4 RESUME                   0

231           6 LOAD_GLOBAL              1 (NULL + set)
             16 CACHE
             18 UNPACK_SEQUENCE          0
             22 CALL                     0
             30 CACHE
             32 STORE_DEREF              2 (repr_running)

233          34 LOAD_GLOBAL              3 (NULL + functools)
             44 CACHE
             46 LOAD_ATTR                2 (functools)
             66 CACHE
             68 CACHE
             70 CACHE

234          72 LOAD_CLOSURE             2 (repr_running)
             74 LOAD_CLOSURE             0 (user_function)
             76 BUILD_TUPLE              2
             78 LOAD_CONST               1 (<code object wrapper at 0x000001B2A722E4F0, file "dataclasses.py", line 233>)
             80 MAKE_FUNCTION            8 (closure)

233          82 UNPACK_SEQUENCE          0
             86 CALL                     0
             94 CACHE

234          96 STORE_FAST               1 (wrapper)

244          98 LOAD_FAST                1 (wrapper)
            100 RETURN_VALUE

Disassembly of <code object wrapper at 0x000001B2A722E4F0, file "dataclasses.py", line 233>:
              0 COPY_FREE_VARS           2

233           2 RESUME                   0

235           4 LOAD_GLOBAL              1 (NULL + id)
             14 CACHE
             16 LOAD_FAST                0 (self)
             18 UNPACK_SEQUENCE          1
             22 CALL                     1
             30 CACHE
             32 LOAD_GLOBAL              3 (NULL + _thread)
             42 CACHE
             44 LOAD_ATTR                2 (_thread)
             64 CACHE
             66 CACHE
             68 BUILD_TUPLE              2
             70 STORE_FAST               1 (key)

236          72 LOAD_FAST                1 (key)
             74 LOAD_DEREF               3 (repr_running)
             76 CONTAINS_OP              0
             78 POP_JUMP_IF_FALSE        2 (to 84)

237          80 LOAD_CONST               1 ('...')
             82 RETURN_VALUE

238     >>   84 LOAD_DEREF               3 (repr_running)
             86 STORE_SUBSCR
             90 CACHE
             92 CACHE
             94 CACHE
             96 CACHE
             98 CACHE
            100 CACHE
            102 CACHE
            104 CACHE
            106 CACHE
            108 LOAD_FAST                1 (key)
            110 UNPACK_SEQUENCE          1
            114 CALL                     1
            122 CACHE
            124 POP_TOP

239         126 NOP

240         128 PUSH_NULL
            130 LOAD_DEREF               4 (user_function)
            132 LOAD_FAST                0 (self)
            134 UNPACK_SEQUENCE          1
            138 CALL                     1
            146 CACHE
            148 STORE_FAST               2 (result)

242         150 LOAD_DEREF               3 (repr_running)
            152 STORE_SUBSCR
            156 CACHE
            158 CACHE
            160 CACHE
            162 CACHE
            164 CACHE
            166 CACHE
            168 CACHE
            170 CACHE
            172 CACHE
            174 LOAD_FAST                1 (key)
            176 UNPACK_SEQUENCE          1
            180 CALL                     1
            188 CACHE
            190 POP_TOP
            192 JUMP_FORWARD            26 (to 246)
        >>  194 PUSH_EXC_INFO
            196 LOAD_DEREF               3 (repr_running)
            198 STORE_SUBSCR
            202 CACHE
            204 CACHE
            206 CACHE
            208 CACHE
            210 CACHE
            212 CACHE
            214 CACHE
            216 CACHE
            218 CACHE
            220 LOAD_FAST                1 (key)
            222 UNPACK_SEQUENCE          1
            226 CALL                     1
            234 CACHE
            236 POP_TOP
            238 RERAISE                  0
        >>  240 COPY                     3
            242 POP_EXCEPT
            244 RERAISE                  1

243     >>  246 LOAD_FAST                2 (result)
            248 RETURN_VALUE
ExceptionTable:
  128 to 148 -> 194 [0]
  194 to 238 -> 240 [1] lasti

Disassembly of <code object InitVar at 0x000001B2A767E6A0, file "dataclasses.py", line 246>:
246           0 RESUME                   0
              2 LOAD_NAME                0 (__name__)
              4 STORE_NAME               1 (__module__)
              6 LOAD_CONST               0 ('InitVar')
              8 STORE_NAME               2 (__qualname__)

247          10 LOAD_CONST               1 (('type',))
             12 STORE_NAME               3 (__slots__)

249          14 LOAD_CONST               2 (<code object __init__ at 0x000001B2A76882D0, file "dataclasses.py", line 249>)
             16 MAKE_FUNCTION            0
             18 STORE_NAME               4 (__init__)

252          20 LOAD_CONST               3 (<code object __repr__ at 0x000001B2A71C79F0, file "dataclasses.py", line 252>)
             22 MAKE_FUNCTION            0
             24 STORE_NAME               5 (__repr__)

260          26 LOAD_CONST               4 (<code object __class_getitem__ at 0x000001B2A768B4B0, file "dataclasses.py", line 260>)
             28 MAKE_FUNCTION            0
             30 STORE_NAME               6 (__class_getitem__)
             32 LOAD_CONST               5 (None)
             34 RETURN_VALUE

Disassembly of <code object __init__ at 0x000001B2A76882D0, file "dataclasses.py", line 249>:
249           0 RESUME                   0

250           2 LOAD_FAST                1 (type)
              4 LOAD_FAST                0 (self)
              6 STORE_ATTR               0 (type)
             16 LOAD_CONST               0 (None)
             18 RETURN_VALUE

Disassembly of <code object __repr__ at 0x000001B2A71C79F0, file "dataclasses.py", line 252>:
252           0 RESUME                   0

253           2 LOAD_GLOBAL              1 (NULL + isinstance)
             12 CACHE
             14 LOAD_FAST                0 (self)
             16 LOAD_ATTR                1 (NULL|self + isinstance)
             36 CACHE
             38 UNPACK_SEQUENCE          2
             42 CALL                     2
             50 CACHE
             52 POP_JUMP_IF_FALSE       13 (to 80)

254          54 LOAD_FAST                0 (self)
             56 LOAD_ATTR                1 (NULL|self + isinstance)
             76 STORE_FAST               1 (type_name)
             78 JUMP_FORWARD            20 (to 120)

257     >>   80 LOAD_GLOBAL              7 (NULL + repr)
             90 CACHE
             92 LOAD_FAST                0 (self)
             94 LOAD_ATTR                1 (NULL|self + isinstance)
            114 CACHE
            116 CACHE
            118 STORE_FAST               1 (type_name)

258     >>  120 LOAD_CONST               1 ('dataclasses.InitVar[')
            122 LOAD_FAST                1 (type_name)
            124 FORMAT_VALUE             0
            126 LOAD_CONST               2 (']')
            128 BUILD_STRING             3
            130 RETURN_VALUE

Disassembly of <code object __class_getitem__ at 0x000001B2A768B4B0, file "dataclasses.py", line 260>:
260           0 RESUME                   0

261           2 LOAD_GLOBAL              1 (NULL + InitVar)
             12 CACHE
             14 LOAD_FAST                1 (type)
             16 UNPACK_SEQUENCE          1
             20 CALL                     1
             28 CACHE
             30 RETURN_VALUE

Disassembly of <code object Field at 0x000001B2A76AD350, file "dataclasses.py", line 273>:
273           0 RESUME                   0
              2 LOAD_NAME                0 (__name__)
              4 STORE_NAME               1 (__module__)
              6 LOAD_CONST               0 ('Field')
              8 STORE_NAME               2 (__qualname__)

274          10 LOAD_CONST               1 (('name', 'type', 'default', 'default_factory', 'repr', 'hash', 'init', 'compare', 'metadata', 'kw_only', '_field_type'))
             12 STORE_NAME               3 (__slots__)

287          14 LOAD_CONST               2 (<code object __init__ at 0x000001B2A71CE0B0, file "dataclasses.py", line 287>)
             16 MAKE_FUNCTION            0
             18 STORE_NAME               4 (__init__)

303          20 LOAD_NAME                5 (_recursive_repr)

304          22 LOAD_CONST               3 (<code object __repr__ at 0x000001B2A7211E30, file "dataclasses.py", line 303>)
             24 MAKE_FUNCTION            0

303          26 UNPACK_SEQUENCE          0
             30 CALL                     0
             38 CACHE

304          40 STORE_NAME               6 (__repr__)

327          42 LOAD_CONST               4 (<code object __set_name__ at 0x000001B2A725BD70, file "dataclasses.py", line 327>)
             44 MAKE_FUNCTION            0
             46 STORE_NAME               7 (__set_name__)

334          48 PUSH_NULL
             50 LOAD_NAME                8 (classmethod)
             52 LOAD_NAME                9 (GenericAlias)
             54 UNPACK_SEQUENCE          1
             58 CALL                     1
             66 CACHE
             68 STORE_NAME              10 (__class_getitem__)
             70 LOAD_CONST               5 (None)
             72 RETURN_VALUE

Disassembly of <code object __init__ at 0x000001B2A71CE0B0, file "dataclasses.py", line 287>:
287           0 RESUME                   0

289           2 LOAD_CONST               0 (None)
              4 LOAD_FAST                0 (self)
              6 STORE_ATTR               0 (name)

290          16 LOAD_CONST               0 (None)
             18 LOAD_FAST                0 (self)
             20 STORE_ATTR               1 (type)

291          30 LOAD_FAST                1 (default)
             32 LOAD_FAST                0 (self)
             34 STORE_ATTR               2 (default)

292          44 LOAD_FAST                2 (default_factory)
             46 LOAD_FAST                0 (self)
             48 STORE_ATTR               3 (default_factory)

293          58 LOAD_FAST                3 (init)
             60 LOAD_FAST                0 (self)
             62 STORE_ATTR               4 (init)

294          72 LOAD_FAST                4 (repr)
             74 LOAD_FAST                0 (self)
             76 STORE_ATTR               5 (repr)

295          86 LOAD_FAST                5 (hash)
             88 LOAD_FAST                0 (self)
             90 STORE_ATTR               6 (hash)

296         100 LOAD_FAST                6 (compare)
            102 LOAD_FAST                0 (self)
            104 STORE_ATTR               7 (compare)

298         114 LOAD_FAST                7 (metadata)
            116 POP_JUMP_IF_NOT_NONE     7 (to 132)

297         118 LOAD_GLOBAL             16 (_EMPTY_METADATA)
            128 CACHE
            130 JUMP_FORWARD            19 (to 170)

299     >>  132 LOAD_GLOBAL             19 (NULL + types)
            142 CACHE
            144 LOAD_ATTR               10 (repr)
            164 CACHE
            166 CACHE
            168 CACHE

297     >>  170 LOAD_FAST                0 (self)
            172 STORE_ATTR              11 (metadata)

300         182 LOAD_FAST                8 (kw_only)
            184 LOAD_FAST                0 (self)
            186 STORE_ATTR              12 (kw_only)

301         196 LOAD_CONST               0 (None)
            198 LOAD_FAST                0 (self)
            200 STORE_ATTR              13 (_field_type)
            210 LOAD_CONST               0 (None)
            212 RETURN_VALUE

Disassembly of <code object __repr__ at 0x000001B2A7211E30, file "dataclasses.py", line 303>:
303           0 RESUME                   0

305           2 LOAD_CONST               1 ('Field(name=')

306           4 LOAD_FAST                0 (self)
              6 LOAD_ATTR                0 (name)
             26 CACHE
             28 CACHE
             30 CACHE

305          32 FORMAT_VALUE             2 (repr)
             34 LOAD_CONST               3 (',default=')

308          36 LOAD_FAST                0 (self)
             38 LOAD_ATTR                2 (type)
             58 CACHE
             60 CACHE
             62 CACHE

305          64 FORMAT_VALUE             2 (repr)
             66 LOAD_CONST               5 (',init=')

310          68 LOAD_FAST                0 (self)
             70 LOAD_ATTR                4 (default)
             90 CACHE
             92 CACHE
             94 CACHE

305          96 FORMAT_VALUE             2 (repr)
             98 LOAD_CONST               7 (',hash=')

312         100 LOAD_FAST                0 (self)
            102 LOAD_ATTR                6 (default_factory)
            122 CACHE
            124 CACHE
            126 CACHE

305         128 FORMAT_VALUE             2 (repr)
            130 LOAD_CONST               9 (',metadata=')

314         132 LOAD_FAST                0 (self)
            134 LOAD_ATTR                8 (init)
            154 CACHE
            156 CACHE
            158 CACHE

305         160 FORMAT_VALUE             2 (repr)
            162 LOAD_CONST              11 (',_field_type=')

316         164 LOAD_FAST                0 (self)
            166 LOAD_ATTR               10 (repr)

Disassembly of <code object __set_name__ at 0x000001B2A725BD70, file "dataclasses.py", line 327>:
327           0 RESUME                   0

328           2 LOAD_GLOBAL              1 (NULL + getattr)
             12 CACHE
             14 LOAD_GLOBAL              3 (NULL + type)
             24 CACHE
             26 LOAD_FAST                0 (self)
             28 LOAD_ATTR                2 (type)
             48 CACHE
             50 CACHE
             52 LOAD_CONST               1 ('__set_name__')
             54 LOAD_CONST               0 (None)
             56 UNPACK_SEQUENCE          3
             60 CALL                     3
             68 CACHE
             70 STORE_FAST               3 (func)

329          72 LOAD_FAST                3 (func)
             74 POP_JUMP_IF_FALSE       20 (to 116)

332          76 PUSH_NULL
             78 LOAD_FAST                3 (func)
             80 LOAD_FAST                0 (self)
             82 LOAD_ATTR                2 (type)
            102 CACHE
            104 CACHE
            106 CACHE
            108 CACHE
            110 POP_TOP
            112 LOAD_CONST               0 (None)
            114 RETURN_VALUE

329     >>  116 LOAD_CONST               0 (None)
            118 RETURN_VALUE

Disassembly of <code object _DataclassParams at 0x000001B2A7688F10, file "dataclasses.py", line 337>:
337           0 RESUME                   0
              2 LOAD_NAME                0 (__name__)
              4 STORE_NAME               1 (__module__)
              6 LOAD_CONST               0 ('_DataclassParams')
              8 STORE_NAME               2 (__qualname__)

338          10 LOAD_CONST               1 (('init', 'repr', 'eq', 'order', 'unsafe_hash', 'frozen'))
             12 STORE_NAME               3 (__slots__)

346          14 LOAD_CONST               2 (<code object __init__ at 0x000001B2A76DA1F0, file "dataclasses.py", line 346>)
             16 MAKE_FUNCTION            0
             18 STORE_NAME               4 (__init__)

354          20 LOAD_CONST               3 (<code object __repr__ at 0x000001B2A76B4620, file "dataclasses.py", line 354>)
             22 MAKE_FUNCTION            0
             24 STORE_NAME               5 (__repr__)
             26 LOAD_CONST               4 (None)
             28 RETURN_VALUE

Disassembly of <code object __init__ at 0x000001B2A76DA1F0, file "dataclasses.py", line 346>:
346           0 RESUME                   0

347           2 LOAD_FAST                1 (init)
              4 LOAD_FAST                0 (self)
              6 STORE_ATTR               0 (init)

348          16 LOAD_FAST                2 (repr)
             18 LOAD_FAST                0 (self)
             20 STORE_ATTR               1 (repr)

349          30 LOAD_FAST                3 (eq)
             32 LOAD_FAST                0 (self)
             34 STORE_ATTR               2 (eq)

350          44 LOAD_FAST                4 (order)
             46 LOAD_FAST                0 (self)
             48 STORE_ATTR               3 (order)

351          58 LOAD_FAST                5 (unsafe_hash)
             60 LOAD_FAST                0 (self)
             62 STORE_ATTR               4 (unsafe_hash)

352          72 LOAD_FAST                6 (frozen)
             74 LOAD_FAST                0 (self)
             76 STORE_ATTR               5 (frozen)
             86 LOAD_CONST               0 (None)
             88 RETURN_VALUE

Disassembly of <code object __repr__ at 0x000001B2A76B4620, file "dataclasses.py", line 354>:
354           0 RESUME                   0

355           2 LOAD_CONST               1 ('_DataclassParams(init=')

356           4 LOAD_FAST                0 (self)
              6 LOAD_ATTR                0 (init)
             26 CACHE
             28 CACHE
             30 CACHE

355          32 FORMAT_VALUE             2 (repr)
             34 LOAD_CONST               3 (',eq=')

358          36 LOAD_FAST                0 (self)
             38 LOAD_ATTR                2 (repr)
             58 CACHE
             60 CACHE
             62 CACHE

355          64 FORMAT_VALUE             2 (repr)
             66 LOAD_CONST               5 (',unsafe_hash=')

360          68 LOAD_FAST                0 (self)
             70 LOAD_ATTR                4 (eq)
             90 CACHE
             92 CACHE
             94 CACHE

355          96 FORMAT_VALUE             2 (repr)
             98 LOAD_CONST               7 (')')
            100 BUILD_STRING            13
            102 RETURN_VALUE

Disassembly of <code object field at 0x000001B2A76B43C0, file "dataclasses.py", line 368>:
368           0 RESUME                   0

386           2 LOAD_FAST                0 (default)
              4 LOAD_GLOBAL              0 (MISSING)
             14 CACHE
             16 IS_OP                    1
             18 POP_JUMP_IF_FALSE       24 (to 68)
             20 LOAD_FAST                1 (default_factory)
             22 LOAD_GLOBAL              0 (MISSING)
             32 CACHE
             34 IS_OP                    1
             36 POP_JUMP_IF_FALSE       15 (to 68)

387          38 LOAD_GLOBAL              3 (NULL + ValueError)
             48 CACHE
             50 LOAD_CONST               1 ('cannot specify both default and default_factory')
             52 UNPACK_SEQUENCE          1
             56 CALL                     1
             64 CACHE
             66 RAISE_VARARGS            1

388     >>   68 LOAD_GLOBAL              5 (NULL + Field)
             78 CACHE
             80 LOAD_FAST                0 (default)
             82 LOAD_FAST                1 (default_factory)
             84 LOAD_FAST                2 (init)
             86 LOAD_FAST                3 (repr)
             88 LOAD_FAST                4 (hash)
             90 LOAD_FAST                5 (compare)

389          92 LOAD_FAST                6 (metadata)
             94 LOAD_FAST                7 (kw_only)

388          96 UNPACK_SEQUENCE          8
            100 CALL                     8
            108 CACHE
            110 RETURN_VALUE

Disassembly of <code object _fields_in_init_order at 0x000001B2A76B4750, file "dataclasses.py", line 392>:
392           0 RESUME                   0

396           2 LOAD_GLOBAL              1 (NULL + tuple)
             12 CACHE
             14 LOAD_CONST               1 (<code object <genexpr> at 0x000001B2A7673A30, file "dataclasses.py", line 396>)
             16 MAKE_FUNCTION            0
             18 LOAD_FAST                0 (fields)
             20 GET_ITER
             22 UNPACK_SEQUENCE          0
             26 CALL                     0
             34 CACHE
             36 UNPACK_SEQUENCE          1
             40 CALL                     1
             48 CACHE

397          50 LOAD_GLOBAL              1 (NULL + tuple)
             60 CACHE
             62 LOAD_CONST               2 (<code object <genexpr> at 0x000001B2A7672930, file "dataclasses.py", line 397>)
             64 MAKE_FUNCTION            0
             66 LOAD_FAST                0 (fields)
             68 GET_ITER
             70 UNPACK_SEQUENCE          0
             74 CALL                     0
             82 CACHE
             84 UNPACK_SEQUENCE          1
             88 CALL                     1
             96 CACHE

396          98 BUILD_TUPLE              2
            100 RETURN_VALUE

Disassembly of <code object <genexpr> at 0x000001B2A7673A30, file "dataclasses.py", line 396>:
396           0 RETURN_GENERATOR
              2 POP_TOP
              4 RESUME                   0
              6 LOAD_FAST                0 (.0)
        >>    8 FOR_ITER                20 (to 52)
             12 LOAD_FAST                1 (f)
             14 LOAD_ATTR                0 (init)
             34 CACHE
             36 CACHE
