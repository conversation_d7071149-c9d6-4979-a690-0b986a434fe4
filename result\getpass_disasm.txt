# Code object from position 10222841
# Filename: getpass.py
# Name: <module>
# Args: 0
# Locals: 0
# Stack size: 5
# Flags: 0

  0           0 RESUME                   0

  1           2 LOAD_CONST               0 ('Utilities to get a password and/or the current user name.\n\ngetpass(prompt[, stream]) - Prompt for a password, with echo turned off.\ngetuser() - Get the user name from the environment or password database.\n\nGetPassWarning - This UserWarning is issued when getpass() cannot prevent\n                 echoing of the password contents while reading.\n\nOn Windows, the msvcrt module will be used.\n\n')
              4 STORE_NAME               0 (__doc__)

 17           6 LOAD_CONST               1 (0)
              8 LOAD_CONST               2 (None)
             10 IMPORT_NAME              1 (contextlib)
             12 STORE_NAME               1 (contextlib)

 18          14 LOAD_CONST               1 (0)
             16 LOAD_CONST               2 (None)
             18 IMPORT_NAME              2 (io)
             20 STORE_NAME               2 (io)

 19          22 LOAD_CONST               1 (0)
             24 LOAD_CONST               2 (None)
             26 IMPORT_NAME              3 (os)
             28 STORE_NAME               3 (os)

 20          30 LOAD_CONST               1 (0)
             32 LOAD_CONST               2 (None)
             34 IMPORT_NAME              4 (sys)
             36 STORE_NAME               4 (sys)

 21          38 LOAD_CONST               1 (0)
             40 LOAD_CONST               2 (None)
             42 IMPORT_NAME              5 (warnings)
             44 STORE_NAME               5 (warnings)

 23          46 BUILD_LIST               0
             48 LOAD_CONST               3 (('getpass', 'getuser', 'GetPassWarning'))
             50 LIST_EXTEND              1
             52 STORE_NAME               6 (__all__)

 26          54 PUSH_NULL
             56 LOAD_BUILD_CLASS
             58 LOAD_CONST               4 (<code object GetPassWarning at 0x000001B2A8A10510, file "getpass.py", line 26>)
             60 MAKE_FUNCTION            0
             62 LOAD_CONST               5 ('GetPassWarning')
             64 LOAD_NAME                7 (UserWarning)
             66 UNPACK_SEQUENCE          3
             70 CALL                     3
             78 CACHE
             80 STORE_NAME               8 (GetPassWarning)

 29          82 LOAD_CONST              13 (('Password: ', None))
             84 LOAD_CONST               7 (<code object unix_getpass at 0x000001B2A7558250, file "getpass.py", line 29>)
             86 MAKE_FUNCTION            1 (defaults)
             88 STORE_NAME               9 (unix_getpass)

 97          90 LOAD_CONST              13 (('Password: ', None))
             92 LOAD_CONST               8 (<code object win_getpass at 0x000001B2A75D03F0, file "getpass.py", line 97>)
             94 MAKE_FUNCTION            1 (defaults)
             96 STORE_NAME              10 (win_getpass)

120          98 LOAD_CONST              13 (('Password: ', None))
            100 LOAD_CONST               9 (<code object fallback_getpass at 0x000001B2A71D3890, file "getpass.py", line 120>)
            102 MAKE_FUNCTION            1 (defaults)
            104 STORE_NAME              11 (fallback_getpass)

129         106 LOAD_CONST              14 (('', None, None))
            108 LOAD_CONST              11 (<code object _raw_input at 0x000001B2A74A3A40, file "getpass.py", line 129>)
            110 MAKE_FUNCTION            1 (defaults)
            112 STORE_NAME              12 (_raw_input)

154         114 LOAD_CONST              12 (<code object getuser at 0x000001B2A7211B30, file "getpass.py", line 154>)
            116 MAKE_FUNCTION            0
            118 STORE_NAME              13 (getuser)

172         120 NOP

173         122 LOAD_CONST               1 (0)
            124 LOAD_CONST               2 (None)
            126 IMPORT_NAME             14 (termios)
            128 STORE_NAME              14 (termios)

176         130 LOAD_NAME               14 (termios)
            132 LOAD_ATTR               15 (NULL|self + UserWarning)
            152 CACHE
            154 BUILD_TUPLE              2
            156 POP_TOP

185         158 LOAD_NAME                9 (unix_getpass)
            160 STORE_NAME              17 (getpass)
            162 LOAD_CONST               2 (None)
            164 RETURN_VALUE
        >>  166 PUSH_EXC_INFO

177         168 LOAD_NAME               18 (ImportError)
            170 LOAD_NAME               19 (AttributeError)
            172 BUILD_TUPLE              2
            174 CHECK_EXC_MATCH
            176 POP_JUMP_IF_FALSE       26 (to 230)
            178 POP_TOP

178         180 NOP

179         182 LOAD_CONST               1 (0)
            184 LOAD_CONST               2 (None)
            186 IMPORT_NAME             20 (msvcrt)
            188 STORE_NAME              20 (msvcrt)

183         190 LOAD_NAME               10 (win_getpass)
            192 STORE_NAME              17 (getpass)
            194 POP_EXCEPT
            196 LOAD_CONST               2 (None)
            198 RETURN_VALUE
        >>  200 PUSH_EXC_INFO

180         202 LOAD_NAME               18 (ImportError)
            204 CHECK_EXC_MATCH
            206 POP_JUMP_IF_FALSE        7 (to 222)
            208 POP_TOP

181         210 LOAD_NAME               11 (fallback_getpass)
            212 STORE_NAME              17 (getpass)
            214 POP_EXCEPT
            216 POP_EXCEPT
            218 LOAD_CONST               2 (None)
            220 RETURN_VALUE

180     >>  222 RERAISE                  0
        >>  224 COPY                     3
            226 POP_EXCEPT
            228 RERAISE                  1

177     >>  230 RERAISE                  0
        >>  232 COPY                     3
            234 POP_EXCEPT
            236 RERAISE                  1
ExceptionTable:
  122 to 156 -> 166 [0]
  166 to 178 -> 232 [1] lasti
  182 to 188 -> 200 [1]
  190 to 192 -> 232 [1] lasti
  200 to 212 -> 224 [2] lasti
  214 to 214 -> 232 [1] lasti
  222 to 222 -> 224 [2] lasti
  224 to 230 -> 232 [1] lasti

Disassembly of <code object GetPassWarning at 0x000001B2A8A10510, file "getpass.py", line 26>:
 26           0 RESUME                   0
              2 LOAD_NAME                0 (__name__)
              4 STORE_NAME               1 (__module__)
              6 LOAD_CONST               0 ('GetPassWarning')
              8 STORE_NAME               2 (__qualname__)
             10 LOAD_CONST               1 (None)
             12 RETURN_VALUE

Disassembly of <code object unix_getpass at 0x000001B2A7558250, file "getpass.py", line 29>:
 29           0 RESUME                   0

 44           2 LOAD_CONST               1 (None)
              4 STORE_FAST               2 (passwd)

 45           6 LOAD_GLOBAL              1 (NULL + contextlib)
             16 CACHE
             18 LOAD_ATTR                1 (NULL|self + contextlib)
             38 CACHE
             40 CACHE
             42 BEFORE_WITH
             44 STORE_FAST               3 (stack)

 46          46 NOP

 48          48 LOAD_GLOBAL              5 (NULL + os)
             58 CACHE
             60 LOAD_ATTR                3 (NULL|self + ExitStack)
             80 CACHE
             82 CACHE
             84 LOAD_ATTR                4 (os)
            104 CACHE
            106 LOAD_ATTR                5 (NULL|self + os)
            126 CACHE
            128 CACHE
            130 CACHE
            132 CACHE
            134 STORE_FAST               4 (fd)

 49         136 LOAD_GLOBAL             13 (NULL + io)
            146 CACHE
            148 LOAD_ATTR                7 (NULL|self + open)
            168 CACHE
            170 CACHE
            172 CACHE
            174 CACHE
            176 STORE_FAST               5 (tty)

 50         178 LOAD_FAST                3 (stack)
            180 STORE_SUBSCR
            184 CACHE
            186 CACHE
            188 CACHE
            190 CACHE
            192 CACHE
            194 CACHE
            196 CACHE
            198 CACHE
            200 CACHE
            202 LOAD_FAST                5 (tty)
            204 UNPACK_SEQUENCE          1
            208 CALL                     1
            216 CACHE
            218 POP_TOP

 51         220 LOAD_GLOBAL             13 (NULL + io)
            230 CACHE
            232 LOAD_ATTR                9 (NULL|self + O_RDWR)
            252 CACHE
            254 CACHE
            256 CACHE
            258 STORE_FAST               6 (input)

 52         260 LOAD_FAST                3 (stack)
            262 STORE_SUBSCR
            266 CACHE
            268 CACHE
            270 CACHE
            272 CACHE
            274 CACHE
            276 CACHE
            278 CACHE
            280 CACHE
            282 CACHE
            284 LOAD_FAST                6 (input)
            286 UNPACK_SEQUENCE          1
            290 CALL                     1
            298 CACHE
            300 POP_TOP

 53         302 LOAD_FAST                1 (stream)
            304 POP_JUMP_IF_TRUE         2 (to 310)

 54         306 LOAD_FAST                6 (input)
            308 STORE_FAST               1 (stream)
        >>  310 JUMP_FORWARD           135 (to 582)
        >>  312 PUSH_EXC_INFO

 55         314 LOAD_GLOBAL             20 (OSError)
            324 CACHE
            326 CHECK_EXC_MATCH
            328 POP_JUMP_IF_FALSE      122 (to 574)
            330 POP_TOP

 57         332 LOAD_FAST                3 (stack)
            334 STORE_SUBSCR
            338 CACHE
            340 CACHE
            342 CACHE
            344 CACHE
            346 CACHE
            348 CACHE
            350 CACHE
            352 CACHE
            354 CACHE
            356 UNPACK_SEQUENCE          0
            360 CALL                     0
            368 CACHE
            370 POP_TOP

 58         372 NOP

 59         374 LOAD_GLOBAL             24 (sys)
            384 CACHE
            386 LOAD_ATTR               13 (NULL|self + io)
            406 CACHE
            408 CACHE
            410 CACHE
            412 CACHE
            414 CACHE
            416 CACHE
            418 UNPACK_SEQUENCE          0
            422 CALL                     0
            430 CACHE
            432 STORE_FAST               4 (fd)
            434 JUMP_FORWARD            41 (to 518)
        >>  436 PUSH_EXC_INFO

 60         438 LOAD_GLOBAL             30 (AttributeError)
            448 CACHE
            450 LOAD_GLOBAL             32 (ValueError)
            460 CACHE
            462 BUILD_TUPLE              2
            464 CHECK_EXC_MATCH
            466 POP_JUMP_IF_FALSE       21 (to 510)
            468 POP_TOP

 61         470 LOAD_CONST               1 (None)
            472 STORE_FAST               4 (fd)

 62         474 LOAD_GLOBAL             35 (NULL + fallback_getpass)
            484 CACHE
            486 LOAD_FAST                0 (prompt)
            488 LOAD_FAST                1 (stream)
            490 UNPACK_SEQUENCE          2
            494 CALL                     2
            502 CACHE
            504 STORE_FAST               2 (passwd)
            506 POP_EXCEPT
            508 JUMP_FORWARD             4 (to 518)

 60     >>  510 RERAISE                  0
        >>  512 COPY                     3
            514 POP_EXCEPT
            516 RERAISE                  1

 63     >>  518 LOAD_GLOBAL             24 (sys)
            528 CACHE
            530 LOAD_ATTR               13 (NULL|self + io)
            550 CACHE
            552 CACHE
            554 CACHE
            556 CACHE
            558 LOAD_ATTR               18 (TextIOWrapper)
            578 POP_EXCEPT
            580 RERAISE                  1

 67     >>  582 LOAD_FAST                4 (fd)
            584 EXTENDED_ARG             1
            586 POP_JUMP_IF_NONE       302 (to 1192)

 68         588 NOP

 69         590 LOAD_GLOBAL             39 (NULL + termios)
            600 CACHE
            602 LOAD_ATTR               20 (OSError)
            622 CACHE
            624 CACHE
            626 CACHE
            628 STORE_FAST               7 (old)

 70         630 LOAD_FAST                7 (old)
            632 LOAD_CONST               1 (None)
            634 LOAD_CONST               1 (None)
            636 BUILD_SLICE              2
            638 BINARY_SUBSCR
            642 CACHE
            644 CACHE
            646 CACHE
            648 STORE_FAST               8 (new)

 71         650 LOAD_FAST                8 (new)
            652 LOAD_CONST               4 (3)
            654 COPY                     2
            656 COPY                     2
            658 BINARY_SUBSCR
            662 CACHE
            664 CACHE
            666 CACHE
            668 LOAD_GLOBAL             38 (termios)
            678 CACHE
            680 LOAD_ATTR               21 (NULL|self + OSError)
            700 STORE_SUBSCR

 72         704 LOAD_GLOBAL             38 (termios)
            714 CACHE
            716 LOAD_ATTR               22 (close)
            736 CACHE
            738 CACHE
            740 LOAD_GLOBAL             38 (termios)
            750 CACHE
            752 LOAD_CONST               5 ('TCSASOFT')
            754 UNPACK_SEQUENCE          2
            758 CALL                     2
            766 CACHE
            768 POP_JUMP_IF_FALSE       15 (to 800)

 74         770 LOAD_FAST                9 (tcsetattr_flags)
            772 LOAD_GLOBAL             38 (termios)
            782 CACHE
            784 LOAD_ATTR               24 (sys)
            804 CACHE
            806 CACHE
            808 CACHE
            810 CACHE
            812 CACHE
            814 LOAD_ATTR               25 (NULL|self + sys)
            834 CALL                     3
            842 CACHE
            844 POP_TOP

 77         846 LOAD_GLOBAL             53 (NULL + _raw_input)
            856 CACHE
            858 LOAD_FAST                0 (prompt)
            860 LOAD_FAST                1 (stream)
            862 LOAD_FAST                6 (input)
            864 KW_NAMES                 6 (('input',))
            866 UNPACK_SEQUENCE          3
            870 CALL                     3
            878 CACHE
            880 STORE_FAST               2 (passwd)

 79         882 LOAD_GLOBAL             39 (NULL + termios)
            892 CACHE
            894 LOAD_ATTR               25 (NULL|self + sys)
            914 CALL                     3
            922 CACHE
            924 POP_TOP

 80         926 LOAD_FAST                1 (stream)
            928 STORE_SUBSCR
            932 CACHE
            934 CACHE
            936 CACHE
            938 CACHE
            940 CACHE
            942 CACHE
            944 CACHE
            946 CACHE
            948 CACHE
            950 UNPACK_SEQUENCE          0
            954 CALL                     0
            962 CACHE
            964 POP_TOP
            966 JUMP_FORWARD            47 (to 1062)
        >>  968 PUSH_EXC_INFO

 79         970 LOAD_GLOBAL             39 (NULL + termios)
            980 CACHE
            982 LOAD_ATTR               25 (NULL|self + sys)
           1002 CALL                     3
           1010 CACHE
           1012 POP_TOP

 80        1014 LOAD_FAST                1 (stream)
           1016 STORE_SUBSCR
           1020 CACHE
           1022 CACHE
           1024 CACHE
           1026 CACHE
           1028 CACHE
           1030 CACHE
           1032 CACHE
           1034 CACHE
           1036 CACHE
           1038 UNPACK_SEQUENCE          0
           1042 CALL                     0
           1050 CACHE
           1052 POP_TOP
           1054 RERAISE                  0
        >> 1056 COPY                     3
           1058 POP_EXCEPT
           1060 RERAISE                  1
        >> 1062 JUMP_FORWARD            64 (to 1192)
        >> 1064 PUSH_EXC_INFO

 81        1066 LOAD_GLOBAL             38 (termios)
           1076 CACHE
           1078 LOAD_ATTR               28 (fileno)

 85        1098 RAISE_VARARGS            0

 88        1100 LOAD_FAST                1 (stream)
           1102 LOAD_FAST                6 (input)
           1104 IS_OP                    1
           1106 POP_JUMP_IF_FALSE       20 (to 1148)

 90        1108 LOAD_FAST                3 (stack)
           1110 STORE_SUBSCR
           1114 CACHE
           1116 CACHE
           1118 CACHE
           1120 CACHE
           1122 CACHE
           1124 CACHE
           1126 CACHE
           1128 CACHE
           1130 CACHE
           1132 UNPACK_SEQUENCE          0
           1136 CALL                     0
           1144 CACHE
           1146 POP_TOP

 91     >> 1148 LOAD_GLOBAL             35 (NULL + fallback_getpass)
           1158 CACHE
           1160 LOAD_FAST                0 (prompt)
           1162 LOAD_FAST                1 (stream)
           1164 UNPACK_SEQUENCE          2
           1168 CALL                     2
           1176 CACHE
           1178 STORE_FAST               2 (passwd)
           1180 POP_EXCEPT
           1182 JUMP_FORWARD             4 (to 1192)

 81        1184 RERAISE                  0
        >> 1186 COPY                     3
           1188 POP_EXCEPT
           1190 RERAISE                  1

 93     >> 1192 LOAD_FAST                1 (stream)
           1194 STORE_SUBSCR
           1198 CACHE
           1200 CACHE
           1202 CACHE
           1204 CACHE
           1206 CACHE
           1208 CACHE
           1210 CACHE
           1212 CACHE
           1214 CACHE
           1216 LOAD_CONST               7 ('\n')
           1218 UNPACK_SEQUENCE          1
           1222 CALL                     1
           1230 CACHE
           1232 POP_TOP

 94        1234 LOAD_FAST                2 (passwd)

 45        1236 SWAP                     2
           1238 LOAD_CONST               1 (None)
           1240 LOAD_CONST               1 (None)
           1242 LOAD_CONST               1 (None)
           1244 UNPACK_SEQUENCE          2
           1248 CALL                     2
           1256 CACHE
           1258 POP_TOP
           1260 RETURN_VALUE
        >> 1262 PUSH_EXC_INFO
           1264 WITH_EXCEPT_START
           1266 POP_JUMP_IF_TRUE         4 (to 1276)
           1268 RERAISE                  2
        >> 1270 COPY                     3
           1272 POP_EXCEPT
           1274 RERAISE                  1
        >> 1276 POP_TOP
           1278 POP_EXCEPT
           1280 POP_TOP
           1282 POP_TOP
           1284 LOAD_CONST               1 (None)
           1286 RETURN_VALUE
ExceptionTable:
  44 to 44 -> 1262 [1] lasti
  48 to 308 -> 312 [1]
  310 to 310 -> 1262 [1] lasti
  312 to 370 -> 576 [2] lasti
  374 to 432 -> 436 [2]
  434 to 434 -> 576 [2] lasti
  436 to 504 -> 512 [3] lasti
  506 to 508 -> 576 [2] lasti
  510 to 510 -> 512 [3] lasti
  512 to 568 -> 576 [2] lasti
  570 to 572 -> 1262 [1] lasti
  574 to 574 -> 576 [2] lasti
  576 to 586 -> 1262 [1] lasti
  590 to 798 -> 1064 [1]
  802 to 880 -> 968 [1]
  882 to 966 -> 1064 [1]
  968 to 1054 -> 1056 [2] lasti
  1056 to 1060 -> 1064 [1]
  1062 to 1062 -> 1262 [1] lasti
  1064 to 1178 -> 1186 [2] lasti
  1180 to 1182 -> 1262 [1] lasti
  1184 to 1184 -> 1186 [2] lasti
  1186 to 1234 -> 1262 [1] lasti
  1262 to 1268 -> 1270 [3] lasti
  1276 to 1276 -> 1270 [3] lasti

Disassembly of <code object win_getpass at 0x000001B2A75D03F0, file "getpass.py", line 97>:
 97           0 RESUME                   0

 99           2 LOAD_GLOBAL              0 (sys)
             12 CACHE
             14 LOAD_ATTR                1 (NULL|self + sys)
             34 CACHE
             36 LOAD_ATTR                2 (stdin)
             56 CACHE
             58 CACHE
             60 CACHE
             62 LOAD_FAST                0 (prompt)
             64 LOAD_FAST                1 (stream)
             66 UNPACK_SEQUENCE          2
             70 CALL                     2
             78 CACHE
             80 RETURN_VALUE

102          82 LOAD_FAST                0 (prompt)
             84 GET_ITER
        >>   86 FOR_ITER                22 (to 134)

103          90 LOAD_GLOBAL              9 (NULL + msvcrt)
            100 CACHE
            102 LOAD_ATTR                5 (NULL|self + __stdin__)
            122 CACHE
            124 CACHE
            126 CACHE
            128 POP_TOP
            130 JUMP_BACKWARD           23 (to 86)

104         132 LOAD_CONST               1 ('')
        >>  134 STORE_FAST               3 (pw)

105         136 NOP

106     >>  138 LOAD_GLOBAL              9 (NULL + msvcrt)
            148 CACHE
            150 LOAD_ATTR                6 (fallback_getpass)
            170 CACHE
            172 CACHE
            174 STORE_FAST               2 (c)

107         176 LOAD_FAST                2 (c)
            178 LOAD_CONST               3 ('\r')
            180 COMPARE_OP               2 (<)
            184 CACHE
            186 POP_JUMP_IF_TRUE         6 (to 200)
            188 LOAD_FAST                2 (c)
            190 LOAD_CONST               4 ('\n')
            192 COMPARE_OP               2 (<)
            196 CACHE
            198 POP_JUMP_IF_FALSE        1 (to 202)

108     >>  200 JUMP_FORWARD            36 (to 274)

109     >>  202 LOAD_FAST                2 (c)
            204 LOAD_CONST               5 ('\x03')
            206 COMPARE_OP               2 (<)
            210 CACHE
            212 POP_JUMP_IF_FALSE        7 (to 228)

110         214 LOAD_GLOBAL             14 (KeyboardInterrupt)
            224 CACHE
            226 RAISE_VARARGS            1

111     >>  228 LOAD_FAST                2 (c)
            230 LOAD_CONST               6 ('\x08')
            232 COMPARE_OP               2 (<)
            236 CACHE
            238 POP_JUMP_IF_FALSE       11 (to 262)

112         240 LOAD_FAST                3 (pw)
            242 LOAD_CONST               7 (None)
            244 LOAD_CONST               8 (-1)
            246 BUILD_SLICE              2
            248 BINARY_SUBSCR
            252 CACHE
            254 CACHE
            256 CACHE
            258 STORE_FAST               3 (pw)
            260 JUMP_FORWARD             5 (to 272)

114     >>  262 LOAD_FAST                3 (pw)
            264 LOAD_FAST                2 (c)
            266 BINARY_OP                0 (+)
            270 STORE_FAST               3 (pw)

105     >>  272 JUMP_BACKWARD           68 (to 138)

115     >>  274 LOAD_GLOBAL              9 (NULL + msvcrt)
            284 CACHE
            286 LOAD_ATTR                5 (NULL|self + __stdin__)
            306 CACHE
            308 CACHE
            310 CACHE
            312 POP_TOP

116         314 LOAD_GLOBAL              9 (NULL + msvcrt)
            324 CACHE
            326 LOAD_ATTR                5 (NULL|self + __stdin__)
            346 CACHE
            348 CACHE
            350 CACHE
            352 POP_TOP

117         354 LOAD_FAST                3 (pw)
            356 RETURN_VALUE

Disassembly of <code object fallback_getpass at 0x000001B2A71D3890, file "getpass.py", line 120>:
120           0 RESUME                   0

121           2 LOAD_GLOBAL              1 (NULL + warnings)
             12 CACHE
             14 LOAD_ATTR                1 (NULL|self + warnings)
             34 CACHE
             36 CACHE

122          38 LOAD_CONST               2 (2)

121          40 KW_NAMES                 3 (('stacklevel',))
             42 UNPACK_SEQUENCE          3
             46 CALL                     3
             54 CACHE
             56 POP_TOP

123          58 LOAD_FAST                1 (stream)
             60 POP_JUMP_IF_TRUE        12 (to 86)

124          62 LOAD_GLOBAL              6 (sys)
             72 CACHE
             74 LOAD_ATTR                4 (GetPassWarning)
             94 CACHE
             96 CACHE
             98 LOAD_CONST               4 ('Warning: Password input may be echoed.')
            100 LOAD_FAST                1 (stream)
            102 KW_NAMES                 5 (('file',))
            104 UNPACK_SEQUENCE          2
            108 CALL                     2
            116 CACHE
            118 POP_TOP

126         120 LOAD_GLOBAL             13 (NULL + _raw_input)
            130 CACHE
            132 LOAD_FAST                0 (prompt)
            134 LOAD_FAST                1 (stream)
            136 UNPACK_SEQUENCE          2
            140 CALL                     2
            148 CACHE
            150 RETURN_VALUE

Disassembly of <code object _raw_input at 0x000001B2A74A3A40, file "getpass.py", line 129>:
129           0 RESUME                   0

131           2 LOAD_FAST                1 (stream)
              4 POP_JUMP_IF_TRUE        12 (to 30)

132           6 LOAD_GLOBAL              0 (sys)
             16 CACHE
             18 LOAD_ATTR                1 (NULL|self + sys)
             38 CACHE
             40 CACHE
             42 CACHE
             44 CACHE
             46 LOAD_ATTR                2 (stderr)
             66 CACHE
             68 CACHE
             70 LOAD_FAST                0 (prompt)
             72 UNPACK_SEQUENCE          1
             76 CALL                     1
             84 CACHE
             86 STORE_FAST               0 (prompt)

136          88 LOAD_FAST                0 (prompt)
             90 POP_JUMP_IF_FALSE      133 (to 358)

137          92 NOP

138          94 LOAD_FAST                1 (stream)
             96 STORE_SUBSCR
            100 CACHE
            102 CACHE
            104 CACHE
            106 CACHE
            108 CACHE
            110 CACHE
            112 CACHE
            114 CACHE
            116 CACHE
            118 LOAD_FAST                0 (prompt)
            120 UNPACK_SEQUENCE          1
            124 CALL                     1
            132 CACHE
            134 POP_TOP
            136 JUMP_FORWARD            90 (to 318)
        >>  138 PUSH_EXC_INFO

139         140 LOAD_GLOBAL             10 (UnicodeEncodeError)
            150 CACHE
            152 CHECK_EXC_MATCH
            154 POP_JUMP_IF_FALSE       77 (to 310)
            156 POP_TOP

141         158 LOAD_FAST                0 (prompt)
            160 STORE_SUBSCR
            164 CACHE
            166 CACHE
            168 CACHE
            170 CACHE
            172 CACHE
            174 CACHE
            176 CACHE
            178 CACHE
            180 CACHE
            182 LOAD_FAST                1 (stream)
            184 LOAD_ATTR                7 (NULL|self + str)
            204 CACHE
            206 CACHE
            208 CACHE
            210 STORE_FAST               0 (prompt)

142         212 LOAD_FAST                0 (prompt)
            214 STORE_SUBSCR
            218 CACHE
            220 CACHE
            222 CACHE
            224 CACHE
            226 CACHE
            228 CACHE
            230 CACHE
            232 CACHE
            234 CACHE
            236 LOAD_FAST                1 (stream)
            238 LOAD_ATTR                7 (NULL|self + str)
            258 CACHE
            260 CACHE
            262 STORE_FAST               0 (prompt)

143         264 LOAD_FAST                1 (stream)
            266 STORE_SUBSCR
            270 CACHE
            272 CACHE
            274 CACHE
            276 CACHE
            278 CACHE
            280 CACHE
            282 CACHE
            284 CACHE
            286 CACHE
            288 LOAD_FAST                0 (prompt)
            290 UNPACK_SEQUENCE          1
            294 CALL                     1
            302 CACHE
            304 POP_TOP
            306 POP_EXCEPT
            308 JUMP_FORWARD             4 (to 318)

139     >>  310 RERAISE                  0
        >>  312 COPY                     3
            314 POP_EXCEPT
            316 RERAISE                  1

144     >>  318 LOAD_FAST                1 (stream)
            320 STORE_SUBSCR
            324 CACHE
            326 CACHE
            328 CACHE
            330 CACHE
            332 CACHE
            334 CACHE
            336 CACHE
            338 CACHE
            340 CACHE
            342 UNPACK_SEQUENCE          0
            346 CALL                     0
            354 CACHE
            356 POP_TOP

146     >>  358 LOAD_FAST                2 (input)
            360 STORE_SUBSCR
            364 CACHE
            366 CACHE
            368 CACHE
            370 CACHE
            372 CACHE
            374 CACHE
            376 CACHE
            378 CACHE
            380 CACHE
            382 UNPACK_SEQUENCE          0
            386 CALL                     0
            394 CACHE
            396 STORE_FAST               3 (line)

147         398 LOAD_FAST                3 (line)
            400 POP_JUMP_IF_TRUE         7 (to 416)

148         402 LOAD_GLOBAL             22 (EOFError)
            412 CACHE
            414 RAISE_VARARGS            1

149     >>  416 LOAD_FAST                3 (line)
            418 LOAD_CONST               2 (-1)
            420 BINARY_SUBSCR
            424 CACHE
            426 CACHE
            428 CACHE
            430 LOAD_CONST               3 ('\n')
            432 COMPARE_OP               2 (<)
            436 CACHE
            438 POP_JUMP_IF_FALSE       10 (to 460)

150         440 LOAD_FAST                3 (line)
            442 LOAD_CONST               0 (None)
            444 LOAD_CONST               2 (-1)
            446 BUILD_SLICE              2
            448 BINARY_SUBSCR
            452 CACHE
            454 CACHE
            456 CACHE
            458 STORE_FAST               3 (line)

151     >>  460 LOAD_FAST                3 (line)
            462 RETURN_VALUE
ExceptionTable:
  94 to 134 -> 138 [0]
  138 to 304 -> 312 [1] lasti
  310 to 310 -> 312 [1] lasti

Disassembly of <code object getuser at 0x000001B2A7211B30, file "getpass.py", line 154>:
154           0 RESUME                   0

162           2 LOAD_CONST               1 (('LOGNAME', 'USER', 'LNAME', 'USERNAME'))
              4 GET_ITER
        >>    6 FOR_ITER                39 (to 88)

163          10 LOAD_GLOBAL              0 (os)
             20 CACHE
             22 LOAD_ATTR                1 (NULL|self + os)
             42 CACHE
             44 CACHE
             46 CACHE
             48 CACHE
             50 CACHE
             52 CACHE
             54 LOAD_FAST                0 (name)
             56 UNPACK_SEQUENCE          1
             60 CALL                     1
             68 CACHE
             70 STORE_FAST               1 (user)

164          72 LOAD_FAST                1 (user)
             74 POP_JUMP_IF_FALSE        4 (to 84)

165          76 LOAD_FAST                1 (user)
             78 SWAP                     2
             80 POP_TOP
             82 RETURN_VALUE

164     >>   84 JUMP_BACKWARD           40 (to 6)

168          86 LOAD_CONST               2 (0)
        >>   88 LOAD_CONST               3 (None)
             90 IMPORT_NAME              3 (pwd)
             92 STORE_FAST               2 (pwd)

169          94 LOAD_FAST                2 (pwd)
             96 STORE_SUBSCR
            100 CACHE
            102 CACHE
            104 CACHE
            106 CACHE
            108 CACHE
            110 CACHE
            112 CACHE
            114 CACHE
            116 CACHE
            118 LOAD_GLOBAL              1 (NULL + os)
            128 CACHE
            130 LOAD_ATTR                5 (NULL|self + get)
            150 CACHE
            152 CACHE
            154 UNPACK_SEQUENCE          1
            158 CALL                     1
            166 CACHE
            168 LOAD_CONST               2 (0)
            170 BINARY_SUBSCR
            174 CACHE
            176 CACHE
            178 CACHE
            180 RETURN_VALUE


# Constants:
# 0: <string length 391>
# 1: 0
# 2: None
# 3: tuple
# 4: <code object GetPassWarning>
# 5: 'GetPassWarning'
# 6: 'Password: '
# 7: <code object unix_getpass>
# 8: <code object win_getpass>
# 9: <code object fallback_getpass>
# 10: ''
# 11: <code object _raw_input>
# 12: <code object getuser>
# 13: tuple
# 14: tuple


# Names:
# 0: '__doc__'
# 1: 'contextlib'
# 2: 'io'
# 3: 'os'
# 4: 'sys'
# 5: 'warnings'
# 6: '__all__'
# 7: 'UserWarning'
# 8: 'GetPassWarning'
# 9: 'unix_getpass'
# 10: 'win_getpass'
# 11: 'fallback_getpass'
# 12: '_raw_input'
# 13: 'getuser'
# 14: 'termios'
# 15: 'tcgetattr'
# 16: 'tcsetattr'
# 17: 'getpass'
# 18: 'ImportError'
# 19: 'AttributeError'
# 20: 'msvcrt'


# Variable names:
