#!/usr/bin/env python3
"""
Extract application data and try to reconstruct the main script
"""
import struct
import os
import sys
import re

def extract_strings_around_position(data, position, radius=2000):
    """Extract readable strings around a position"""
    start = max(0, position - radius)
    end = min(len(data), position + radius)
    chunk = data[start:end]
    
    # Find printable strings
    strings = []
    current_string = b''
    
    for byte in chunk:
        if 32 <= byte <= 126:  # Printable ASCII
            current_string += bytes([byte])
        else:
            if len(current_string) >= 3:  # Minimum string length
                strings.append(current_string.decode('ascii'))
            current_string = b''
            
    if len(current_string) >= 3:
        strings.append(current_string.decode('ascii'))
        
    return strings

def analyze_app_data_regions(exe_path, output_dir):
    """Analyze data regions containing application strings"""
    print(f"Analyzing application data regions in {exe_path}")
    
    with open(exe_path, 'rb') as f:
        data = f.read()
        
    print(f"File size: {len(data)} bytes")
    
    # Create output directory
    os.makedirs(output_dir, exist_ok=True)
    
    # Application-specific string positions
    app_string_positions = {
        14467326: 'imgproc',
        14472170: 'ImgProc',
        14468417: 'align_image',
        14472295: 'create_object_normal_map',
        14472321: 'create_tangent_normal_map',
        14472348: 'create_specular_map',
        14472219: '--function',
        14472425: '--image_dir',
    }
    
    # Extract strings around each position
    all_app_strings = []
    
    for pos, string_name in app_string_positions.items():
        print(f"\nAnalyzing region around '{string_name}' at position {pos}")
        
        # Extract strings in a larger radius
        strings = extract_strings_around_position(data, pos, 5000)
        
        # Filter for interesting strings
        interesting_strings = []
        for s in strings:
            if (len(s) > 2 and 
                (any(keyword in s.lower() for keyword in ['imgproc', 'align', 'image', 'normal', 'map', 'function', 'arg', 'help', 'usage']) or
                 s.startswith('--') or
                 'def ' in s or
                 'class ' in s or
                 'import ' in s or
                 '.py' in s)):
                interesting_strings.append(s)
        
        all_app_strings.extend(interesting_strings)
        print(f"  Found {len(interesting_strings)} interesting strings")
    
    # Remove duplicates and sort
    unique_strings = list(set(all_app_strings))
    unique_strings.sort(key=len, reverse=True)
    
    # Save all interesting strings
    strings_file = os.path.join(output_dir, 'app_data_strings.txt')
    with open(strings_file, 'w', encoding='utf-8') as f:
        f.write("Application Data Strings\n")
        f.write("=" * 50 + "\n\n")
        
        for i, s in enumerate(unique_strings):
            f.write(f"{i+1:3d}. {repr(s)}\n")
    
    print(f"\nSaved {len(unique_strings)} unique application strings to {strings_file}")
    
    # Try to reconstruct the main script structure
    reconstruct_main_script(unique_strings, output_dir)

def reconstruct_main_script(strings, output_dir):
    """Try to reconstruct the main script from strings"""
    print("\nAttempting to reconstruct main script structure...")
    
    # Look for patterns that suggest the main script structure
    help_strings = [s for s in strings if 'help' in s.lower() or 'usage' in s.lower()]
    function_names = [s for s in strings if any(func in s for func in ['align_image', 'create_object_normal_map', 'create_tangent_normal_map', 'create_specular_map'])]
    arg_strings = [s for s in strings if s.startswith('--')]
    
    # Create a reconstructed script
    script_file = os.path.join(output_dir, 'reconstructed_main.py')
    with open(script_file, 'w', encoding='utf-8') as f:
        f.write("#!/usr/bin/env python3\n")
        f.write('"""\n')
        f.write("Reconstructed ImgProc main script based on extracted strings\n")
        f.write('"""\n\n')
        
        f.write("import argparse\n")
        f.write("import cv2\n")
        f.write("import numpy as np\n")
        f.write("import os\n")
        f.write("import sys\n\n")
        
        # Add function definitions based on found function names
        if function_names:
            f.write("# Function definitions (reconstructed from strings)\n")
            for func_name in function_names:
                if 'align_image' in func_name:
                    f.write("def align_image():\n")
                    f.write('    """Align images"""\n')
                    f.write("    pass\n\n")
                elif 'create_object_normal_map' in func_name:
                    f.write("def create_object_normal_map():\n")
                    f.write('    """Create object normal map"""\n')
                    f.write("    pass\n\n")
                elif 'create_tangent_normal_map' in func_name:
                    f.write("def create_tangent_normal_map():\n")
                    f.write('    """Create tangent normal map"""\n')
                    f.write("    pass\n\n")
                elif 'create_specular_map' in func_name:
                    f.write("def create_specular_map():\n")
                    f.write('    """Create specular map"""\n')
                    f.write("    pass\n\n")
        
        # Add main function
        f.write("def main():\n")
        f.write('    """Main function"""\n')
        f.write("    parser = argparse.ArgumentParser(description='ImgProc CLI Tool')\n")
        
        # Add arguments based on found strings
        if arg_strings:
            f.write("    \n    # Arguments (reconstructed from strings)\n")
            for arg in arg_strings:
                if '--function' in arg:
                    f.write("    parser.add_argument('--function', \n")
                    f.write("                        choices=['align_image', 'align_image_set', 'align_image_set_threaded',\n")
                    f.write("                                'create_object_normal_map', 'create_tangent_normal_map', 'create_specular_map'],\n")
                    f.write("                        help='Function to execute')\n")
                elif '--image_dir' in arg:
                    f.write("    parser.add_argument('--image_dir', help='Directory containing images')\n")
                elif '--output' in arg:
                    f.write("    parser.add_argument('--output', help='Output path')\n")
        
        f.write("    \n    args = parser.parse_args()\n")
        f.write("    \n    # Function dispatch\n")
        f.write("    if args.function == 'align_image':\n")
        f.write("        align_image()\n")
        f.write("    elif args.function == 'create_object_normal_map':\n")
        f.write("        create_object_normal_map()\n")
        f.write("    elif args.function == 'create_tangent_normal_map':\n")
        f.write("        create_tangent_normal_map()\n")
        f.write("    elif args.function == 'create_specular_map':\n")
        f.write("        create_specular_map()\n")
        f.write("    else:\n")
        f.write("        parser.print_help()\n\n")
        
        f.write("if __name__ == '__main__':\n")
        f.write("    main()\n")
        
        # Add comments with all found strings
        f.write("\n\n# All extracted application strings:\n")
        for i, s in enumerate(strings[:50]):  # First 50 strings
            f.write(f"# {i+1}: {repr(s)}\n")
    
    print(f"Reconstructed main script saved to {script_file}")

if __name__ == "__main__":
    if len(sys.argv) != 3:
        print("Usage: python extract_app_data.py <exe_file> <output_dir>")
        sys.exit(1)
        
    exe_path = sys.argv[1]
    output_dir = sys.argv[2]
    
    analyze_app_data_regions(exe_path, output_dir)
