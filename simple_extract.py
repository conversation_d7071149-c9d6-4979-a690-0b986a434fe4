#!/usr/bin/env python3
"""
Simple Python code extraction - process first few code objects
"""
import struct
import os
import sys
import marshal
import dis
import types

def find_marshal_data_limited(data, max_objects=20):
    """Find first few marshal data objects"""
    marshal_candidates = []
    
    # Look for marshal data pattern
    pattern = b'\xe3'  # TYPE_CODE
    
    offset = 0
    count = 0
    while count < max_objects:
        pos = data.find(pattern, offset)
        if pos == -1:
            break
            
        # Try to unmarshal from this position
        try:
            remaining_data = data[pos:]
            if len(remaining_data) > 100:  # Need reasonable amount of data
                obj = marshal.loads(remaining_data)
                if isinstance(obj, types.CodeType):
                    marshal_candidates.append({
                        'position': pos,
                        'code_object': obj,
                        'pattern': pattern
                    })
                    print(f"Found code object {count+1} at position {pos}")
                    count += 1
        except:
            pass
            
        offset = pos + 1
        
    return marshal_candidates

def extract_limited_code(exe_path, output_dir, max_objects=20):
    """Extract limited number of code objects"""
    print(f"Extracting first {max_objects} code objects from {exe_path}")
    
    with open(exe_path, 'rb') as f:
        data = f.read()
        
    print(f"File size: {len(data)} bytes")
    
    # Create output directory
    os.makedirs(output_dir, exist_ok=True)
    
    # Find marshal data
    marshal_candidates = find_marshal_data_limited(data, max_objects)
    
    extracted_count = 0
    for i, candidate in enumerate(marshal_candidates):
        try:
            code_obj = candidate['code_object']
            filename = f"code_{i+1:03d}"
            
            # Get original filename if available
            if hasattr(code_obj, 'co_filename') and code_obj.co_filename:
                orig_name = os.path.basename(code_obj.co_filename)
                if orig_name and orig_name not in ['<string>', '<frozen>', '<built-in>']:
                    filename = orig_name.replace('.py', '').replace('.', '_')
                    
            print(f"Processing {filename} (original: {getattr(code_obj, 'co_filename', 'unknown')})")
            
            # Try to decompile with uncompyle6
            try:
                import uncompyle6
                output_path = os.path.join(output_dir, filename + '.py')
                with open(output_path, 'w', encoding='utf-8') as f:
                    f.write(f"# Decompiled from position {candidate['position']}\n")
                    f.write(f"# Original filename: {getattr(code_obj, 'co_filename', 'unknown')}\n")
                    f.write(f"# Function name: {getattr(code_obj, 'co_name', 'unknown')}\n\n")
                    uncompyle6.decompile(code_obj, f)
                print(f"  ✓ Decompiled to {output_path}")
                extracted_count += 1
            except Exception as e:
                print(f"  ✗ Decompilation failed: {e}")
                
                # Fallback to disassembly
                output_path = os.path.join(output_dir, filename + '_disasm.txt')
                with open(output_path, 'w', encoding='utf-8') as f:
                    f.write(f"# Code object from position {candidate['position']}\n")
                    f.write(f"# Filename: {getattr(code_obj, 'co_filename', 'unknown')}\n")
                    f.write(f"# Name: {getattr(code_obj, 'co_name', 'unknown')}\n")
                    f.write(f"# Args: {getattr(code_obj, 'co_argcount', 0)}\n")
                    f.write(f"# Locals: {getattr(code_obj, 'co_nlocals', 0)}\n\n")
                    
                    # Disassemble
                    dis.dis(code_obj, file=f)
                    
                    # Extract constants
                    if hasattr(code_obj, 'co_consts'):
                        f.write("\n\n# Constants:\n")
                        for j, const in enumerate(code_obj.co_consts):
                            if isinstance(const, str) and len(const) < 200:
                                f.write(f"# {j}: {repr(const)}\n")
                            elif isinstance(const, (int, float, bool, type(None))):
                                f.write(f"# {j}: {repr(const)}\n")
                            else:
                                f.write(f"# {j}: {type(const).__name__}\n")
                                
                    # Extract names
                    if hasattr(code_obj, 'co_names'):
                        f.write("\n\n# Names:\n")
                        for j, name in enumerate(code_obj.co_names):
                            f.write(f"# {j}: {repr(name)}\n")
                            
                    # Extract variable names
                    if hasattr(code_obj, 'co_varnames'):
                        f.write("\n\n# Variable names:\n")
                        for j, name in enumerate(code_obj.co_varnames):
                            f.write(f"# {j}: {repr(name)}\n")
                            
                print(f"  ✓ Disassembled to {output_path}")
                extracted_count += 1
                
        except Exception as e:
            print(f"  ✗ Failed to process code object {i+1}: {e}")
    
    print(f"\nExtracted {extracted_count} code objects to {output_dir}")
    
    # Create a summary file
    summary_path = os.path.join(output_dir, 'SUMMARY.txt')
    with open(summary_path, 'w', encoding='utf-8') as f:
        f.write(f"Extraction Summary\n")
        f.write(f"==================\n\n")
        f.write(f"Source file: {exe_path}\n")
        f.write(f"Total code objects found: {len(marshal_candidates)}\n")
        f.write(f"Successfully extracted: {extracted_count}\n\n")
        
        f.write("Code objects:\n")
        for i, candidate in enumerate(marshal_candidates):
            code_obj = candidate['code_object']
            f.write(f"{i+1:3d}. Position: {candidate['position']:8d}, ")
            f.write(f"File: {getattr(code_obj, 'co_filename', 'unknown')}, ")
            f.write(f"Name: {getattr(code_obj, 'co_name', 'unknown')}\n")
    
    print(f"Summary saved to {summary_path}")

if __name__ == "__main__":
    if len(sys.argv) != 3:
        print("Usage: python simple_extract.py <exe_file> <output_dir>")
        sys.exit(1)
        
    exe_path = sys.argv[1]
    output_dir = sys.argv[2]
    
    extract_limited_code(exe_path, output_dir)
