# Code object from position 10039338
# Filename: filecmp.py
# Name: <module>
# Args: 0
# Locals: 0
# Stack size: 4
# Flags: 0

  0           0 RESUME                   0

  1           2 LOAD_CONST               0 ('Utilities for comparing files and directories.\n\nClasses:\n    dircmp\n\nFunctions:\n    cmp(f1, f2, shallow=True) -> int\n    cmpfiles(a, b, common) -> ([], [], [])\n    clear_cache()\n\n')
              4 STORE_NAME               0 (__doc__)

 13           6 LOAD_CONST               1 (0)
              8 LOAD_CONST               2 (None)
             10 IMPORT_NAME              1 (os)
             12 STORE_NAME               1 (os)

 14          14 LOAD_CONST               1 (0)
             16 LOAD_CONST               2 (None)
             18 IMPORT_NAME              2 (stat)
             20 STORE_NAME               2 (stat)

 15          22 LOAD_CONST               1 (0)
             24 LOAD_CONST               3 (('filterfalse',))
             26 IMPORT_NAME              3 (itertools)
             28 IMPORT_FROM              4 (filterfalse)
             30 STORE_NAME               4 (filterfalse)
             32 POP_TOP

 16          34 LOAD_CONST               1 (0)
             36 LOAD_CONST               4 (('GenericAlias',))
             38 IMPORT_NAME              5 (types)
             40 IMPORT_FROM              6 (GenericAlias)
             42 STORE_NAME               6 (GenericAlias)
             44 POP_TOP

 18          46 BUILD_LIST               0
             48 LOAD_CONST               5 (('clear_cache', 'cmp', 'dircmp', 'cmpfiles', 'DEFAULT_IGNORES'))
             50 LIST_EXTEND              1
             52 STORE_NAME               7 (__all__)

 20          54 BUILD_MAP                0
             56 STORE_NAME               8 (_cache)

 21          58 LOAD_CONST               6 (8192)
             60 STORE_NAME               9 (BUFSIZE)

 23          62 BUILD_LIST               0
             64 LOAD_CONST               7 (('RCS', 'CVS', 'tags', '.git', '.hg', '.bzr', '_darcs', '__pycache__'))
             66 LIST_EXTEND              1
             68 STORE_NAME              10 (DEFAULT_IGNORES)

 26          70 LOAD_CONST               8 (<code object clear_cache at 0x000001B2A7672430, file "filecmp.py", line 26>)
             72 MAKE_FUNCTION            0
             74 STORE_NAME              11 (clear_cache)

 30          76 LOAD_CONST              20 ((True,))
             78 LOAD_CONST              10 (<code object cmp at 0x000001B2A74A4D00, file "filecmp.py", line 30>)
             80 MAKE_FUNCTION            1 (defaults)
             82 STORE_NAME              12 (cmp)

 70          84 LOAD_CONST              11 (<code object _sig at 0x000001B2A76AE890, file "filecmp.py", line 70>)
             86 MAKE_FUNCTION            0
             88 STORE_NAME              13 (_sig)

 75          90 LOAD_CONST              12 (<code object _do_cmp at 0x000001B2A75CF270, file "filecmp.py", line 75>)
             92 MAKE_FUNCTION            0
             94 STORE_NAME              14 (_do_cmp)

 88          96 PUSH_NULL
             98 LOAD_BUILD_CLASS
            100 LOAD_CONST              13 (<code object dircmp at 0x000001B2A71D3050, file "filecmp.py", line 88>)
            102 MAKE_FUNCTION            0
            104 LOAD_CONST              14 ('dircmp')
            106 UNPACK_SEQUENCE          2
            110 CALL                     2
            118 CACHE
            120 STORE_NAME              15 (dircmp)

258         122 LOAD_CONST              20 ((True,))
            124 LOAD_CONST              15 (<code object cmpfiles at 0x000001B2A726E8B0, file "filecmp.py", line 258>)
            126 MAKE_FUNCTION            1 (defaults)
            128 STORE_NAME              16 (cmpfiles)

285         130 LOAD_NAME               17 (abs)
            132 LOAD_NAME               12 (cmp)
            134 BUILD_TUPLE              2
            136 LOAD_CONST              16 (<code object _cmp at 0x000001B2A76DB990, file "filecmp.py", line 285>)
            138 MAKE_FUNCTION            1 (defaults)
            140 STORE_NAME              18 (_cmp)

294         142 LOAD_CONST              17 (<code object _filter at 0x000001B2A76AEEF0, file "filecmp.py", line 294>)
            144 MAKE_FUNCTION            0
            146 STORE_NAME              19 (_filter)

300         148 LOAD_CONST              18 (<code object demo at 0x000001B2A508B6F0, file "filecmp.py", line 300>)
            150 MAKE_FUNCTION            0
            152 STORE_NAME              20 (demo)

312         154 LOAD_NAME               21 (__name__)
            156 LOAD_CONST              19 ('__main__')
            158 COMPARE_OP               2 (<)
            162 CACHE
            164 POP_JUMP_IF_FALSE       12 (to 190)

313         166 PUSH_NULL
            168 LOAD_NAME               20 (demo)
            170 UNPACK_SEQUENCE          0
            174 CALL                     0
            182 CACHE
            184 POP_TOP
            186 LOAD_CONST               2 (None)
            188 RETURN_VALUE

312     >>  190 LOAD_CONST               2 (None)
            192 RETURN_VALUE

Disassembly of <code object clear_cache at 0x000001B2A7672430, file "filecmp.py", line 26>:
 26           0 RESUME                   0

 28           2 LOAD_GLOBAL              0 (_cache)
             12 CACHE
             14 STORE_SUBSCR
             18 CACHE
             20 CACHE
             22 CACHE
             24 CACHE
             26 CACHE
             28 CACHE
             30 CACHE
             32 CACHE
             34 CACHE
             36 UNPACK_SEQUENCE          0
             40 CALL                     0
             48 CACHE
             50 POP_TOP
             52 LOAD_CONST               1 (None)
             54 RETURN_VALUE

Disassembly of <code object cmp at 0x000001B2A74A4D00, file "filecmp.py", line 30>:
 30           0 RESUME                   0

 53           2 LOAD_GLOBAL              1 (NULL + _sig)
             12 CACHE
             14 LOAD_GLOBAL              3 (NULL + os)
             24 CACHE
             26 LOAD_ATTR                2 (os)
             46 CACHE
             48 CACHE
             50 CACHE
             52 UNPACK_SEQUENCE          1
             56 CALL                     1
             64 CACHE
             66 STORE_FAST               3 (s1)

 54          68 LOAD_GLOBAL              1 (NULL + _sig)
             78 CACHE
             80 LOAD_GLOBAL              3 (NULL + os)
             90 CACHE
             92 LOAD_ATTR                2 (os)
            112 CACHE
            114 CACHE
            116 CACHE
            118 UNPACK_SEQUENCE          1
            122 CALL                     1
            130 CACHE
            132 STORE_FAST               4 (s2)

 55         134 LOAD_FAST                3 (s1)
            136 LOAD_CONST               1 (0)
            138 BINARY_SUBSCR
            142 CACHE
            144 CACHE
            146 CACHE
            148 LOAD_GLOBAL              4 (stat)
            158 CACHE
            160 LOAD_ATTR                3 (NULL|self + os)
            180 LOAD_CONST               1 (0)
            182 BINARY_SUBSCR
            186 CACHE
            188 CACHE
            190 CACHE
            192 LOAD_GLOBAL              4 (stat)
            202 CACHE
            204 LOAD_ATTR                3 (NULL|self + os)
            224 RETURN_VALUE

 57         226 LOAD_FAST                2 (shallow)
            228 POP_JUMP_IF_FALSE        8 (to 246)
            230 LOAD_FAST                3 (s1)
            232 LOAD_FAST                4 (s2)
            234 COMPARE_OP               2 (<)
            238 CACHE
            240 POP_JUMP_IF_FALSE        2 (to 246)

 58         242 LOAD_CONST               3 (True)
            244 RETURN_VALUE

 59     >>  246 LOAD_FAST                3 (s1)
            248 LOAD_CONST               4 (1)
            250 BINARY_SUBSCR
            254 CACHE
            256 CACHE
            258 CACHE
            260 LOAD_FAST                4 (s2)
            262 LOAD_CONST               4 (1)
            264 BINARY_SUBSCR
            268 CACHE
            270 CACHE
            272 CACHE
            274 COMPARE_OP               3 (<)
            278 CACHE
            280 POP_JUMP_IF_FALSE        2 (to 286)

 60         282 LOAD_CONST               2 (False)
            284 RETURN_VALUE

 62     >>  286 LOAD_GLOBAL              8 (_cache)
            296 CACHE
            298 STORE_SUBSCR
            302 CACHE
            304 CACHE
            306 CACHE
            308 CACHE
            310 CACHE
            312 CACHE
            314 CACHE
            316 CACHE
            318 CACHE
            320 LOAD_FAST                0 (f1)
            322 LOAD_FAST                1 (f2)
            324 LOAD_FAST                3 (s1)
            326 LOAD_FAST                4 (s2)
            328 BUILD_TUPLE              4
            330 UNPACK_SEQUENCE          1
            334 CALL                     1
            342 CACHE
            344 STORE_FAST               5 (outcome)

 63         346 LOAD_FAST                5 (outcome)
            348 POP_JUMP_IF_NOT_NONE    68 (to 486)

 64         350 LOAD_GLOBAL             13 (NULL + _do_cmp)
            360 CACHE
            362 LOAD_FAST                0 (f1)
            364 LOAD_FAST                1 (f2)
            366 UNPACK_SEQUENCE          2
            370 CALL                     2
            378 CACHE
            380 STORE_FAST               5 (outcome)

 65         382 LOAD_GLOBAL             15 (NULL + len)
            392 CACHE
            394 LOAD_GLOBAL              8 (_cache)
            404 CACHE
            406 UNPACK_SEQUENCE          1
            410 CALL                     1
            418 CACHE
            420 LOAD_CONST               6 (100)
            422 COMPARE_OP               4 (<)
            426 CACHE
            428 POP_JUMP_IF_FALSE       14 (to 458)

 66         430 LOAD_GLOBAL             17 (NULL + clear_cache)
            440 CACHE
            442 UNPACK_SEQUENCE          0
            446 CALL                     0
            454 CACHE
            456 POP_TOP

 67     >>  458 LOAD_FAST                5 (outcome)
            460 LOAD_GLOBAL              8 (_cache)
            470 CACHE
            472 LOAD_FAST                0 (f1)
            474 LOAD_FAST                1 (f2)
            476 LOAD_FAST                3 (s1)
            478 LOAD_FAST                4 (s2)
            480 BUILD_TUPLE              4
            482 STORE_SUBSCR

 68     >>  486 LOAD_FAST                5 (outcome)
            488 RETURN_VALUE

Disassembly of <code object _sig at 0x000001B2A76AE890, file "filecmp.py", line 70>:
 70           0 RESUME                   0

 71           2 LOAD_GLOBAL              1 (NULL + stat)
             12 CACHE
             14 LOAD_ATTR                1 (NULL|self + stat)
             34 CACHE
             36 UNPACK_SEQUENCE          1
             40 CALL                     1
             48 CACHE

 72          50 LOAD_FAST                0 (st)
             52 LOAD_ATTR                3 (NULL|self + S_IFMT)
             72 CACHE

 71          74 BUILD_TUPLE              3
             76 RETURN_VALUE

Disassembly of <code object _do_cmp at 0x000001B2A75CF270, file "filecmp.py", line 75>:
 75           0 RESUME                   0

 76           2 LOAD_GLOBAL              0 (BUFSIZE)
             12 CACHE
             14 STORE_FAST               2 (bufsize)

 77          16 LOAD_GLOBAL              3 (NULL + open)
             26 CACHE
             28 LOAD_FAST                0 (f1)
             30 LOAD_CONST               1 ('rb')
             32 UNPACK_SEQUENCE          2
             36 CALL                     2
             44 CACHE
             46 BEFORE_WITH
             48 STORE_FAST               3 (fp1)
             50 LOAD_GLOBAL              3 (NULL + open)
             60 CACHE
             62 LOAD_FAST                1 (f2)
             64 LOAD_CONST               1 ('rb')
             66 UNPACK_SEQUENCE          2
             70 CALL                     2
             78 CACHE
             80 BEFORE_WITH
             82 STORE_FAST               4 (fp2)

 78          84 NOP

 79     >>   86 LOAD_FAST                3 (fp1)
             88 STORE_SUBSCR
             92 CACHE
             94 CACHE
             96 CACHE
             98 CACHE
            100 CACHE
            102 CACHE
            104 CACHE
            106 CACHE
            108 CACHE
            110 LOAD_FAST                2 (bufsize)
            112 UNPACK_SEQUENCE          1
            116 CALL                     1
            124 CACHE
            126 STORE_FAST               5 (b1)

 80         128 LOAD_FAST                4 (fp2)
            130 STORE_SUBSCR
            134 CACHE
            136 CACHE
            138 CACHE
            140 CACHE
            142 CACHE
            144 CACHE
            146 CACHE
            148 CACHE
            150 CACHE
            152 LOAD_FAST                2 (bufsize)
            154 UNPACK_SEQUENCE          1
            158 CALL                     1
            166 CACHE
            168 STORE_FAST               6 (b2)

 81         170 LOAD_FAST                5 (b1)
            172 LOAD_FAST                6 (b2)
            174 COMPARE_OP               3 (<)
            178 CACHE
            180 POP_JUMP_IF_FALSE       25 (to 232)

 82         182 NOP

 77         184 LOAD_CONST               0 (None)
            186 LOAD_CONST               0 (None)
            188 LOAD_CONST               0 (None)
            190 UNPACK_SEQUENCE          2
            194 CALL                     2
            202 CACHE
            204 POP_TOP
            206 LOAD_CONST               0 (None)
            208 LOAD_CONST               0 (None)
            210 LOAD_CONST               0 (None)
            212 UNPACK_SEQUENCE          2
            216 CALL                     2
            224 CACHE
            226 POP_TOP
            228 LOAD_CONST               3 (False)
            230 RETURN_VALUE

 83     >>  232 LOAD_FAST                5 (b1)
            234 POP_JUMP_IF_TRUE        25 (to 286)

 84         236 NOP

 77         238 LOAD_CONST               0 (None)
            240 LOAD_CONST               0 (None)
            242 LOAD_CONST               0 (None)
            244 UNPACK_SEQUENCE          2
            248 CALL                     2
            256 CACHE
            258 POP_TOP
            260 LOAD_CONST               0 (None)
            262 LOAD_CONST               0 (None)
            264 LOAD_CONST               0 (None)
            266 UNPACK_SEQUENCE          2
            270 CALL                     2
            278 CACHE
            280 POP_TOP
            282 LOAD_CONST               2 (True)
            284 RETURN_VALUE

 78     >>  286 JUMP_BACKWARD          101 (to 86)

 77     >>  288 PUSH_EXC_INFO
            290 WITH_EXCEPT_START
            292 POP_JUMP_IF_TRUE         4 (to 302)
            294 RERAISE                  2
        >>  296 COPY                     3
            298 POP_EXCEPT
            300 RERAISE                  1
        >>  302 POP_TOP
            304 POP_EXCEPT
            306 POP_TOP
            308 POP_TOP
            310 LOAD_CONST               0 (None)
            312 LOAD_CONST               0 (None)
            314 LOAD_CONST               0 (None)
            316 UNPACK_SEQUENCE          2
            320 CALL                     2
            328 CACHE
            330 POP_TOP
            332 LOAD_CONST               0 (None)
            334 RETURN_VALUE
        >>  336 PUSH_EXC_INFO
            338 WITH_EXCEPT_START
            340 POP_JUMP_IF_TRUE         4 (to 350)
            342 RERAISE                  2
        >>  344 COPY                     3
            346 POP_EXCEPT
            348 RERAISE                  1
        >>  350 POP_TOP
            352 POP_EXCEPT
            354 POP_TOP
            356 POP_TOP
            358 LOAD_CONST               0 (None)
            360 RETURN_VALUE
ExceptionTable:
  48 to 80 -> 336 [1] lasti
  82 to 182 -> 288 [2] lasti
  184 to 204 -> 336 [1] lasti
  232 to 236 -> 288 [2] lasti
  238 to 258 -> 336 [1] lasti
  286 to 286 -> 288 [2] lasti
  288 to 294 -> 296 [4] lasti
  296 to 300 -> 336 [1] lasti
  302 to 302 -> 296 [4] lasti
  304 to 308 -> 336 [1] lasti
  336 to 342 -> 344 [3] lasti
  350 to 350 -> 344 [3] lasti

Disassembly of <code object dircmp at 0x000001B2A71D3050, file "filecmp.py", line 88>:
 88           0 RESUME                   0
              2 LOAD_NAME                0 (__name__)
              4 STORE_NAME               1 (__module__)
              6 LOAD_CONST               0 ('dircmp')
              8 STORE_NAME               2 (__qualname__)

 89          10 LOAD_CONST               1 ('A class that manages the comparison of 2 directories.\n\n    dircmp(a, b, ignore=None, hide=None)\n      A and B are directories.\n      IGNORE is a list of names to ignore,\n        defaults to DEFAULT_IGNORES.\n      HIDE is a list of names to hide,\n        defaults to [os.curdir, os.pardir].\n\n    High level usage:\n      x = dircmp(dir1, dir2)\n      x.report() -> prints a report on the differences between dir1 and dir2\n       or\n      x.report_partial_closure() -> prints report on differences between dir1\n            and dir2, and reports on common immediate subdirectories.\n      x.report_full_closure() -> like report_partial_closure,\n            but fully recursive.\n\n    Attributes:\n     left_list, right_list: The files in dir1 and dir2,\n        filtered by hide and ignore.\n     common: a list of names in both dir1 and dir2.\n     left_only, right_only: names only in dir1, dir2.\n     common_dirs: subdirectories in both dir1 and dir2.\n     common_files: files in both dir1 and dir2.\n     common_funny: names in both dir1 and dir2 where the type differs between\n        dir1 and dir2, or the name is not stat-able.\n     same_files: list of identical files.\n     diff_files: list of filenames which differ.\n     funny_files: list of files which could not be compared.\n     subdirs: a dictionary of dircmp instances (or MyDirCmp instances if this\n       object is of type MyDirCmp, a subclass of dircmp), keyed by names\n       in common_dirs.\n     ')
             12 STORE_NAME               3 (__doc__)

124          14 LOAD_CONST              15 ((None, None))
             16 LOAD_CONST               3 (<code object __init__ at 0x000001B2A71D1630, file "filecmp.py", line 124>)
             18 MAKE_FUNCTION            1 (defaults)
             20 STORE_NAME               4 (__init__)

136          22 LOAD_CONST               4 (<code object phase0 at 0x000001B2A508ACA0, file "filecmp.py", line 136>)
             24 MAKE_FUNCTION            0
             26 STORE_NAME               5 (phase0)

144          28 LOAD_CONST               5 (<code object phase1 at 0x000001B2A74D3290, file "filecmp.py", line 144>)
             30 MAKE_FUNCTION            0
             32 STORE_NAME               6 (phase1)

151          34 LOAD_CONST               6 (<code object phase2 at 0x000001B2A75B6780, file "filecmp.py", line 151>)
             36 MAKE_FUNCTION            0
             38 STORE_NAME               7 (phase2)

186          40 LOAD_CONST               7 (<code object phase3 at 0x000001B2A76B5F10, file "filecmp.py", line 186>)
             42 MAKE_FUNCTION            0
             44 STORE_NAME               8 (phase3)

190          46 LOAD_CONST               8 (<code object phase4 at 0x000001B2A71ADD30, file "filecmp.py", line 190>)
             48 MAKE_FUNCTION            0
             50 STORE_NAME               9 (phase4)

201          52 LOAD_CONST               9 (<code object phase4_closure at 0x000001B2A71C68E0, file "filecmp.py", line 201>)
             54 MAKE_FUNCTION            0
             56 STORE_NAME              10 (phase4_closure)

206          58 LOAD_CONST              10 (<code object report at 0x000001B2A75B5240, file "filecmp.py", line 206>)
             60 MAKE_FUNCTION            0
             62 STORE_NAME              11 (report)

231          64 LOAD_CONST              11 (<code object report_partial_closure at 0x000001B2A728DFD0, file "filecmp.py", line 231>)
             66 MAKE_FUNCTION            0
             68 STORE_NAME              12 (report_partial_closure)

237          70 LOAD_CONST              12 (<code object report_full_closure at 0x000001B2A728DE60, file "filecmp.py", line 237>)
             72 MAKE_FUNCTION            0
             74 STORE_NAME              13 (report_full_closure)

243          76 PUSH_NULL
             78 LOAD_NAME               14 (dict)
             80 LOAD_NAME                9 (phase4)

244          82 LOAD_NAME                8 (phase3)
             84 LOAD_NAME                8 (phase3)
             86 LOAD_NAME                8 (phase3)

245          88 LOAD_NAME                7 (phase2)
             90 LOAD_NAME                7 (phase2)
             92 LOAD_NAME                7 (phase2)

246          94 LOAD_NAME                6 (phase1)
             96 LOAD_NAME                6 (phase1)
             98 LOAD_NAME                6 (phase1)

247         100 LOAD_NAME                5 (phase0)
            102 LOAD_NAME                5 (phase0)

243         104 KW_NAMES                13 (('subdirs', 'same_files', 'diff_files', 'funny_files', 'common_dirs', 'common_files', 'common_funny', 'common', 'left_only', 'right_only', 'left_list', 'right_list'))
            106 UNPACK_SEQUENCE         12
            110 CALL                    12
            118 CACHE
            120 STORE_NAME              15 (methodmap)

249         122 LOAD_CONST              14 (<code object __getattr__ at 0x000001B2A8A08DF0, file "filecmp.py", line 249>)
            124 MAKE_FUNCTION            0
            126 STORE_NAME              16 (__getattr__)

255         128 PUSH_NULL
            130 LOAD_NAME               17 (classmethod)
            132 LOAD_NAME               18 (GenericAlias)
            134 UNPACK_SEQUENCE          1
            138 CALL                     1
            146 CACHE
            148 STORE_NAME              19 (__class_getitem__)
            150 LOAD_CONST               2 (None)
            152 RETURN_VALUE

Disassembly of <code object __init__ at 0x000001B2A71D1630, file "filecmp.py", line 124>:
124           0 RESUME                   0

125           2 LOAD_FAST                1 (a)
              4 LOAD_FAST                0 (self)
              6 STORE_ATTR               0 (left)

126          16 LOAD_FAST                2 (b)
             18 LOAD_FAST                0 (self)
             20 STORE_ATTR               1 (right)

127          30 LOAD_FAST                4 (hide)
             32 POP_JUMP_IF_NOT_NONE    30 (to 94)

128          34 LOAD_GLOBAL              4 (os)
             44 CACHE
             46 LOAD_ATTR                3 (NULL|self + right)
             66 CACHE
             68 LOAD_ATTR                4 (os)
             88 CACHE
             90 CACHE
             92 JUMP_FORWARD             7 (to 108)

130     >>   94 LOAD_FAST                4 (hide)
             96 LOAD_FAST                0 (self)
             98 STORE_ATTR               5 (hide)

131     >>  108 LOAD_FAST                3 (ignore)
            110 POP_JUMP_IF_NOT_NONE    14 (to 140)

132         112 LOAD_GLOBAL             12 (DEFAULT_IGNORES)
            122 CACHE
            124 LOAD_FAST                0 (self)
            126 STORE_ATTR               7 (ignore)
            136 LOAD_CONST               0 (None)
            138 RETURN_VALUE

134     >>  140 LOAD_FAST                3 (ignore)
            142 LOAD_FAST                0 (self)
            144 STORE_ATTR               7 (ignore)
            154 LOAD_CONST               0 (None)
            156 RETURN_VALUE

Disassembly of <code object phase0 at 0x000001B2A508ACA0, file "filecmp.py", line 136>:
136           0 RESUME                   0

137           2 LOAD_GLOBAL              1 (NULL + _filter)
             12 CACHE
             14 LOAD_GLOBAL              3 (NULL + os)
             24 CACHE
             26 LOAD_ATTR                2 (os)
             46 CACHE
             48 UNPACK_SEQUENCE          1
             52 CALL                     1
             60 CACHE

138          62 LOAD_FAST                0 (self)
             64 LOAD_ATTR                4 (listdir)
             84 CACHE
             86 BINARY_OP                0 (+)

137          90 UNPACK_SEQUENCE          2
             94 CALL                     2
            102 CACHE
            104 LOAD_FAST                0 (self)
            106 STORE_ATTR               6 (left_list)

139         116 LOAD_GLOBAL              1 (NULL + _filter)
            126 CACHE
            128 LOAD_GLOBAL              3 (NULL + os)
            138 CACHE
            140 LOAD_ATTR                2 (os)
            160 CACHE
            162 UNPACK_SEQUENCE          1
            166 CALL                     1
            174 CACHE

140         176 LOAD_FAST                0 (self)
            178 LOAD_ATTR                4 (listdir)
            198 CACHE
            200 BINARY_OP                0 (+)

139         204 UNPACK_SEQUENCE          2
            208 CALL                     2
            216 CACHE
            218 LOAD_FAST                0 (self)
            220 STORE_ATTR               8 (right_list)

141         230 LOAD_FAST                0 (self)
            232 LOAD_ATTR                6 (left)
            252 CACHE
            254 CACHE
            256 CACHE
            258 CACHE
            260 CACHE
            262 CACHE
            264 UNPACK_SEQUENCE          0
            268 CALL                     0
            276 CACHE
            278 POP_TOP

142         280 LOAD_FAST                0 (self)
            282 LOAD_ATTR                8 (hide)
            302 CACHE
            304 CACHE
            306 CACHE
            308 CACHE
            310 CACHE
            312 CACHE
            314 UNPACK_SEQUENCE          0
            318 CALL                     0
            326 CACHE
            328 POP_TOP
            330 LOAD_CONST               0 (None)
            332 RETURN_VALUE

Disassembly of <code object phase1 at 0x000001B2A74D3290, file "filecmp.py", line 144>:
144           0 RESUME                   0

145           2 LOAD_GLOBAL              1 (NULL + dict)
             12 CACHE
             14 LOAD_GLOBAL              3 (NULL + zip)
             24 CACHE
             26 LOAD_GLOBAL              5 (NULL + map)
             36 CACHE
             38 LOAD_GLOBAL              6 (os)
             48 CACHE
             50 LOAD_ATTR                4 (map)
             70 LOAD_FAST                0 (self)
             72 LOAD_ATTR                6 (os)
             92 CACHE
             94 CACHE
             96 LOAD_FAST                0 (self)
             98 LOAD_ATTR                6 (os)
            118 CACHE
            120 CACHE
            122 UNPACK_SEQUENCE          1
            126 CALL                     1
            134 CACHE
            136 STORE_FAST               1 (a)

146         138 LOAD_GLOBAL              1 (NULL + dict)
            148 CACHE
            150 LOAD_GLOBAL              3 (NULL + zip)
            160 CACHE
            162 LOAD_GLOBAL              5 (NULL + map)
            172 CACHE
            174 LOAD_GLOBAL              6 (os)
            184 CACHE
            186 LOAD_ATTR                4 (map)
            206 LOAD_FAST                0 (self)
            208 LOAD_ATTR                7 (NULL|self + os)
            228 CACHE
            230 CACHE
            232 LOAD_FAST                0 (self)
            234 LOAD_ATTR                7 (NULL|self + os)
            254 CACHE
            256 CACHE
            258 UNPACK_SEQUENCE          1
            262 CALL                     1
            270 CACHE
            272 STORE_FAST               2 (b)

147         274 LOAD_GLOBAL             17 (NULL + list)
            284 CACHE
            286 LOAD_GLOBAL              5 (NULL + map)
            296 CACHE
            298 LOAD_FAST                1 (a)
            300 LOAD_ATTR                9 (NULL|self + path)
            320 CACHE
            322 LOAD_FAST                2 (b)
            324 LOAD_ATTR               11 (NULL|self + normcase)
            344 CACHE
            346 CACHE
            348 CACHE
            350 UNPACK_SEQUENCE          2
            354 CALL                     2
            362 CACHE
            364 UNPACK_SEQUENCE          1
            368 CALL                     1
            376 CACHE
            378 LOAD_FAST                0 (self)
            380 STORE_ATTR              12 (common)

148         390 LOAD_GLOBAL             17 (NULL + list)
            400 CACHE
            402 LOAD_GLOBAL              5 (NULL + map)
            412 CACHE
            414 LOAD_FAST                1 (a)
            416 LOAD_ATTR                9 (NULL|self + path)
            436 CACHE
            438 LOAD_FAST                2 (b)
            440 LOAD_ATTR               11 (NULL|self + normcase)
            460 CACHE
            462 CACHE
            464 CACHE
            466 UNPACK_SEQUENCE          2
            470 CALL                     2
            478 CACHE
            480 UNPACK_SEQUENCE          1
            484 CALL                     1
            492 CACHE
            494 LOAD_FAST                0 (self)
            496 STORE_ATTR              14 (left_only)

149         506 LOAD_GLOBAL             17 (NULL + list)
            516 CACHE
            518 LOAD_GLOBAL              5 (NULL + map)
            528 CACHE
            530 LOAD_FAST                2 (b)
            532 LOAD_ATTR                9 (NULL|self + path)
            552 CACHE
            554 LOAD_FAST                1 (a)
            556 LOAD_ATTR               11 (NULL|self + normcase)
            576 CACHE
            578 CACHE
            580 CACHE
            582 UNPACK_SEQUENCE          2
            586 CALL                     2
            594 CACHE
            596 UNPACK_SEQUENCE          1
            600 CALL                     1
            608 CACHE
            610 LOAD_FAST                0 (self)
            612 STORE_ATTR              15 (right_only)
            622 LOAD_CONST               0 (None)
            624 RETURN_VALUE

Disassembly of <code object phase2 at 0x000001B2A75B6780, file "filecmp.py", line 151>:
151           0 RESUME                   0

152           2 BUILD_LIST               0
              4 LOAD_FAST                0 (self)
              6 STORE_ATTR               0 (common_dirs)

153          16 BUILD_LIST               0
             18 LOAD_FAST                0 (self)
             20 STORE_ATTR               1 (common_files)

154          30 BUILD_LIST               0
             32 LOAD_FAST                0 (self)
             34 STORE_ATTR               2 (common_funny)

156          44 LOAD_FAST                0 (self)
             46 LOAD_ATTR                3 (NULL|self + common_files)
             66 CACHE
             68 CACHE
             70 CACHE
             72 CACHE
             74 CACHE
             76 LOAD_ATTR                5 (NULL|self + common_funny)
             96 CACHE
             98 CACHE
            100 CACHE
            102 CACHE
            104 CACHE
            106 CACHE
            108 LOAD_FAST                0 (self)
            110 LOAD_ATTR                7 (NULL|self + common)
            130 CACHE
            132 CACHE
            134 CACHE
            136 STORE_FAST               2 (a_path)

158         138 LOAD_GLOBAL              8 (os)
            148 CACHE
            150 LOAD_ATTR                5 (NULL|self + common_funny)
            170 CACHE
            172 CACHE
            174 CACHE
            176 CACHE
            178 CACHE
            180 CACHE
            182 LOAD_FAST                0 (self)
            184 LOAD_ATTR                8 (os)
            204 CACHE
            206 CACHE
            208 CACHE
            210 STORE_FAST               3 (b_path)

160         212 LOAD_CONST               1 (1)
            214 STORE_FAST               4 (ok)

161         216 NOP

162         218 LOAD_GLOBAL              9 (NULL + os)
            228 CACHE
            230 LOAD_ATTR                9 (NULL|self + os)
            250 CACHE
            252 CACHE
            254 CACHE
            256 STORE_FAST               5 (a_stat)
            258 JUMP_FORWARD            18 (to 296)
        >>  260 PUSH_EXC_INFO

163         262 LOAD_GLOBAL             20 (OSError)
            272 CACHE
            274 CHECK_EXC_MATCH
            276 POP_JUMP_IF_FALSE        5 (to 288)
            278 POP_TOP

165         280 LOAD_CONST               2 (0)
            282 STORE_FAST               4 (ok)
            284 POP_EXCEPT
            286 JUMP_FORWARD             4 (to 296)

163     >>  288 RERAISE                  0
        >>  290 COPY                     3
            292 POP_EXCEPT
            294 RERAISE                  1

166     >>  296 NOP

167         298 LOAD_GLOBAL              9 (NULL + os)
            308 CACHE
            310 LOAD_ATTR                9 (NULL|self + os)
            330 CACHE
            332 CACHE
            334 CACHE
            336 STORE_FAST               6 (b_stat)
            338 JUMP_FORWARD            18 (to 376)
        >>  340 PUSH_EXC_INFO

168         342 LOAD_GLOBAL             20 (OSError)
            352 CACHE
            354 CHECK_EXC_MATCH
            356 POP_JUMP_IF_FALSE        5 (to 368)
            358 POP_TOP

170         360 LOAD_CONST               2 (0)
            362 STORE_FAST               4 (ok)
            364 POP_EXCEPT
            366 JUMP_FORWARD             4 (to 376)

168     >>  368 RERAISE                  0
        >>  370 COPY                     3
            372 POP_EXCEPT
            374 RERAISE                  1

172     >>  376 LOAD_FAST                4 (ok)
            378 POP_JUMP_IF_FALSE      207 (to 794)

173         380 LOAD_GLOBAL             19 (NULL + stat)
            390 CACHE
            392 LOAD_ATTR               11 (NULL|self + path)
            412 CACHE
            414 UNPACK_SEQUENCE          1
            418 CALL                     1
            426 CACHE
            428 STORE_FAST               7 (a_type)

174         430 LOAD_GLOBAL             19 (NULL + stat)
            440 CACHE
            442 LOAD_ATTR               11 (NULL|self + path)
            462 CACHE
            464 UNPACK_SEQUENCE          1
            468 CALL                     1
            476 CACHE
            478 STORE_FAST               8 (b_type)

175         480 LOAD_FAST                7 (a_type)
            482 LOAD_FAST                8 (b_type)
            484 COMPARE_OP               3 (<)
            488 CACHE
            490 POP_JUMP_IF_FALSE       27 (to 546)

176         492 LOAD_FAST                0 (self)
            494 LOAD_ATTR                2 (common_files)
            514 CACHE
            516 CACHE
            518 CACHE
            520 CACHE
            522 CACHE
            524 CACHE
            526 LOAD_FAST                1 (x)
            528 UNPACK_SEQUENCE          1
            532 CALL                     1
            540 CACHE
            542 POP_TOP
            544 JUMP_BACKWARD          244 (to 58)

177     >>  546 LOAD_GLOBAL             19 (NULL + stat)
            556 CACHE
            558 LOAD_ATTR               14 (left)
            578 CACHE
            580 CACHE
            582 CACHE
            584 POP_JUMP_IF_FALSE       28 (to 642)

178         586 LOAD_FAST                0 (self)
            588 LOAD_ATTR                0 (common_dirs)
            608 CACHE
            610 CACHE
            612 CACHE
            614 CACHE
            616 CACHE
            618 CACHE
            620 LOAD_FAST                1 (x)
            622 UNPACK_SEQUENCE          1
            626 CALL                     1
            634 CACHE
            636 POP_TOP
            638 EXTENDED_ARG             1
            640 JUMP_BACKWARD          292 (to 58)

179     >>  642 LOAD_GLOBAL             19 (NULL + stat)
            652 CACHE
            654 LOAD_ATTR               15 (NULL|self + left)
            674 CACHE
            676 CACHE
            678 CACHE
            680 POP_JUMP_IF_FALSE       28 (to 738)

180         682 LOAD_FAST                0 (self)
            684 LOAD_ATTR                1 (NULL|self + common_dirs)
            704 CACHE
            706 CACHE
            708 CACHE
            710 CACHE
            712 CACHE
            714 CACHE
            716 LOAD_FAST                1 (x)
            718 UNPACK_SEQUENCE          1
            722 CALL                     1
            730 CACHE
            732 POP_TOP
            734 EXTENDED_ARG             1
            736 JUMP_BACKWARD          340 (to 58)

182     >>  738 LOAD_FAST                0 (self)
            740 LOAD_ATTR                2 (common_files)
            760 CACHE
            762 CACHE
            764 CACHE
            766 CACHE
            768 CACHE
            770 CACHE
            772 LOAD_FAST                1 (x)
            774 UNPACK_SEQUENCE          1
            778 CALL                     1
            786 CACHE
            788 POP_TOP
            790 EXTENDED_ARG             1
            792 JUMP_BACKWARD          368 (to 58)

184     >>  794 LOAD_FAST                0 (self)
            796 LOAD_ATTR                2 (common_files)
            816 CACHE
            818 CACHE
            820 CACHE
            822 CACHE
            824 CACHE
            826 CACHE
            828 LOAD_FAST                1 (x)
            830 UNPACK_SEQUENCE          1
            834 CALL                     1
            842 CACHE
            844 POP_TOP
            846 EXTENDED_ARG             1
            848 JUMP_BACKWARD          396 (to 58)

156         850 LOAD_CONST               0 (None)
            852 RETURN_VALUE
ExceptionTable:
  218 to 256 -> 260 [1]
  260 to 282 -> 290 [2] lasti
  288 to 288 -> 290 [2] lasti
  298 to 336 -> 340 [1]
  340 to 362 -> 370 [2] lasti
  368 to 368 -> 370 [2] lasti

Disassembly of <code object phase3 at 0x000001B2A76B5F10, file "filecmp.py", line 186>:
186           0 RESUME                   0

187           2 LOAD_GLOBAL              1 (NULL + cmpfiles)
             12 CACHE
             14 LOAD_FAST                0 (self)
             16 LOAD_ATTR                1 (NULL|self + cmpfiles)
             36 CACHE
             38 LOAD_FAST                0 (self)
             40 LOAD_ATTR                3 (NULL|self + left)
             60 CACHE
             62 CACHE
             64 STORE_FAST               1 (xx)

188          66 LOAD_FAST                1 (xx)
             68 UNPACK_SEQUENCE          3
             72 LOAD_FAST                0 (self)
             74 STORE_ATTR               4 (same_files)
             84 LOAD_FAST                0 (self)
             86 STORE_ATTR               5 (diff_files)
             96 LOAD_FAST                0 (self)
             98 STORE_ATTR               6 (funny_files)
            108 LOAD_CONST               0 (None)
            110 RETURN_VALUE

Disassembly of <code object phase4 at 0x000001B2A71ADD30, file "filecmp.py", line 190>:
190           0 RESUME                   0

195           2 BUILD_MAP                0
              4 LOAD_FAST                0 (self)
              6 STORE_ATTR               0 (subdirs)

196          16 LOAD_FAST                0 (self)
             18 LOAD_ATTR                1 (NULL|self + subdirs)
             38 CACHE
             40 CACHE
             42 CACHE
             44 CACHE
             46 LOAD_ATTR                3 (NULL|self + common_dirs)
             66 CACHE
             68 CACHE
             70 CACHE
             72 CACHE
             74 CACHE
             76 CACHE
             78 LOAD_FAST                0 (self)
             80 LOAD_ATTR                5 (NULL|self + os)
            100 CACHE
            102 CACHE
            104 CACHE
            106 STORE_FAST               2 (a_x)

198         108 LOAD_GLOBAL              4 (os)
            118 CACHE
            120 LOAD_ATTR                3 (NULL|self + common_dirs)
            140 CACHE
            142 CACHE
            144 CACHE
            146 CACHE
            148 CACHE
            150 CACHE
            152 LOAD_FAST                0 (self)
            154 LOAD_ATTR                6 (path)
            174 CACHE
            176 CACHE
            178 CACHE
            180 STORE_FAST               3 (b_x)

199         182 LOAD_FAST                0 (self)
            184 STORE_SUBSCR
            188 CACHE
            190 CACHE
            192 CACHE
            194 CACHE
            196 CACHE
            198 CACHE
            200 CACHE
            202 CACHE
            204 CACHE
            206 LOAD_FAST                2 (a_x)
            208 LOAD_FAST                3 (b_x)
            210 LOAD_FAST                0 (self)
            212 LOAD_ATTR                8 (join)
            232 CACHE
            234 UNPACK_SEQUENCE          4
            238 CALL                     4
            246 CACHE
            248 LOAD_FAST                0 (self)
            250 LOAD_ATTR                0 (subdirs)
            270 RETURN_VALUE

Disassembly of <code object phase4_closure at 0x000001B2A71C68E0, file "filecmp.py", line 201>:
201           0 RESUME                   0

202           2 LOAD_FAST                0 (self)
              4 STORE_SUBSCR
              8 CACHE
             10 CACHE
             12 CACHE
             14 CACHE
             16 CACHE
             18 CACHE
             20 CACHE
             22 CACHE
             24 CACHE
             26 UNPACK_SEQUENCE          0
             30 CALL                     0
             38 CACHE
             40 POP_TOP

203          42 LOAD_FAST                0 (self)
             44 LOAD_ATTR                1 (NULL|self + phase4)
             64 CACHE
             66 CACHE
             68 CACHE
             70 CACHE
             72 CACHE
             74 CACHE
             76 UNPACK_SEQUENCE          0
             80 CALL                     0
             88 CACHE
             90 GET_ITER
        >>   92 FOR_ITER                22 (to 140)

204          96 LOAD_FAST                1 (sd)
             98 STORE_SUBSCR
            102 CACHE
            104 CACHE
            106 CACHE
            108 CACHE
            110 CACHE
            112 CACHE
            114 CACHE
            116 CACHE
            118 CACHE
            120 UNPACK_SEQUENCE          0
            124 CALL                     0
            132 CACHE
            134 POP_TOP
            136 JUMP_BACKWARD           23 (to 92)

203         138 LOAD_CONST               0 (None)
        >>  140 RETURN_VALUE

Disassembly of <code object report at 0x000001B2A75B5240, file "filecmp.py", line 206>:
206           0 RESUME                   0

208           2 LOAD_GLOBAL              1 (NULL + print)
             12 CACHE
             14 LOAD_CONST               1 ('diff')
             16 LOAD_FAST                0 (self)
             18 LOAD_ATTR                1 (NULL|self + print)
             38 CACHE
             40 UNPACK_SEQUENCE          3
             44 CALL                     3
             52 CACHE
             54 POP_TOP

209          56 LOAD_FAST                0 (self)
             58 LOAD_ATTR                3 (NULL|self + left)
             78 CACHE
             80 CACHE
             82 STORE_SUBSCR
             86 CACHE
             88 CACHE
             90 CACHE
             92 CACHE
             94 CACHE
             96 CACHE
             98 CACHE
            100 CACHE
            102 CACHE
            104 UNPACK_SEQUENCE          0
            108 CALL                     0
            116 CACHE
            118 POP_TOP

211         120 LOAD_GLOBAL              1 (NULL + print)
            130 CACHE
            132 LOAD_CONST               2 ('Only in')
            134 LOAD_FAST                0 (self)
            136 LOAD_ATTR                1 (NULL|self + print)
            156 CACHE
            158 CACHE
            160 UNPACK_SEQUENCE          4
            164 CALL                     4
            172 CACHE
            174 POP_TOP

212         176 LOAD_FAST                0 (self)
            178 LOAD_ATTR                5 (NULL|self + right)
            198 CACHE
            200 CACHE
            202 STORE_SUBSCR
            206 CACHE
            208 CACHE
            210 CACHE
            212 CACHE
            214 CACHE
            216 CACHE
            218 CACHE
            220 CACHE
            222 CACHE
            224 UNPACK_SEQUENCE          0
            228 CALL                     0
            236 CACHE
            238 POP_TOP

214         240 LOAD_GLOBAL              1 (NULL + print)
            250 CACHE
            252 LOAD_CONST               2 ('Only in')
            254 LOAD_FAST                0 (self)
            256 LOAD_ATTR                2 (left)
            276 CACHE
            278 CACHE
            280 UNPACK_SEQUENCE          4
            284 CALL                     4
            292 CACHE
            294 POP_TOP

215         296 LOAD_FAST                0 (self)
            298 LOAD_ATTR                6 (left_only)
            318 CACHE
            320 CACHE
            322 STORE_SUBSCR
            326 CACHE
            328 CACHE
            330 CACHE
            332 CACHE
            334 CACHE
            336 CACHE
            338 CACHE
            340 CACHE
            342 CACHE
            344 UNPACK_SEQUENCE          0
            348 CALL                     0
            356 CACHE
            358 POP_TOP

217         360 LOAD_GLOBAL              1 (NULL + print)
            370 CACHE
            372 LOAD_CONST               4 ('Identical files :')
            374 LOAD_FAST                0 (self)
            376 LOAD_ATTR                6 (left_only)
            396 CACHE
            398 CACHE
            400 POP_TOP

218         402 LOAD_FAST                0 (self)
            404 LOAD_ATTR                7 (NULL|self + left_only)
            424 CACHE
            426 CACHE
            428 STORE_SUBSCR
            432 CACHE
            434 CACHE
            436 CACHE
            438 CACHE
            440 CACHE
            442 CACHE
            444 CACHE
            446 CACHE
            448 CACHE
            450 UNPACK_SEQUENCE          0
            454 CALL                     0
            462 CACHE
            464 POP_TOP

220         466 LOAD_GLOBAL              1 (NULL + print)
            476 CACHE
            478 LOAD_CONST               5 ('Differing files :')
            480 LOAD_FAST                0 (self)
            482 LOAD_ATTR                7 (NULL|self + left_only)
            502 CACHE
            504 CACHE
            506 POP_TOP

221         508 LOAD_FAST                0 (self)
            510 LOAD_ATTR                8 (sort)
            530 CACHE
            532 CACHE
            534 STORE_SUBSCR
            538 CACHE
            540 CACHE
            542 CACHE
            544 CACHE
            546 CACHE
            548 CACHE
            550 CACHE
            552 CACHE
            554 CACHE
            556 UNPACK_SEQUENCE          0
            560 CALL                     0
            568 CACHE
            570 POP_TOP

223         572 LOAD_GLOBAL              1 (NULL + print)
            582 CACHE
            584 LOAD_CONST               6 ('Trouble with common files :')
            586 LOAD_FAST                0 (self)
            588 LOAD_ATTR                8 (sort)
            608 CACHE
            610 CACHE
            612 POP_TOP

224         614 LOAD_FAST                0 (self)
            616 LOAD_ATTR                9 (NULL|self + sort)
            636 CACHE
            638 CACHE
            640 STORE_SUBSCR
            644 CACHE
            646 CACHE
            648 CACHE
            650 CACHE
            652 CACHE
            654 CACHE
            656 CACHE
            658 CACHE
            660 CACHE
            662 UNPACK_SEQUENCE          0
            666 CALL                     0
            674 CACHE
            676 POP_TOP

226         678 LOAD_GLOBAL              1 (NULL + print)
            688 CACHE
            690 LOAD_CONST               7 ('Common subdirectories :')
            692 LOAD_FAST                0 (self)
            694 LOAD_ATTR                9 (NULL|self + sort)
            714 CACHE
            716 CACHE
            718 POP_TOP

227         720 LOAD_FAST                0 (self)
            722 LOAD_ATTR               10 (right_only)
            742 CACHE
            744 CACHE
            746 STORE_SUBSCR
            750 CACHE
            752 CACHE
            754 CACHE
            756 CACHE
            758 CACHE
            760 CACHE
            762 CACHE
            764 CACHE
            766 CACHE
            768 UNPACK_SEQUENCE          0
            772 CALL                     0
            780 CACHE
            782 POP_TOP

229         784 LOAD_GLOBAL              1 (NULL + print)
            794 CACHE
            796 LOAD_CONST               8 ('Common funny cases :')
            798 LOAD_FAST                0 (self)
            800 LOAD_ATTR               10 (right_only)
            820 CACHE
            822 CACHE
            824 POP_TOP
            826 LOAD_CONST               0 (None)
            828 RETURN_VALUE

227         830 LOAD_CONST               0 (None)
            832 RETURN_VALUE

Disassembly of <code object report_partial_closure at 0x000001B2A728DFD0, file "filecmp.py", line 231>:
231           0 RESUME                   0

232           2 LOAD_FAST                0 (self)
              4 STORE_SUBSCR
              8 CACHE
             10 CACHE
             12 CACHE
             14 CACHE
             16 CACHE
             18 CACHE
             20 CACHE
             22 CACHE
             24 CACHE
             26 UNPACK_SEQUENCE          0
             30 CALL                     0
             38 CACHE
             40 POP_TOP

233          42 LOAD_FAST                0 (self)
             44 LOAD_ATTR                1 (NULL|self + report)
             64 CACHE
             66 CACHE
             68 CACHE
             70 CACHE
             72 CACHE
             74 CACHE
             76 UNPACK_SEQUENCE          0
             80 CALL                     0
             88 CACHE
             90 GET_ITER
        >>   92 FOR_ITER                36 (to 168)

234          96 LOAD_GLOBAL              7 (NULL + print)
            106 CACHE
            108 UNPACK_SEQUENCE          0
            112 CALL                     0
            120 CACHE
            122 POP_TOP

235         124 LOAD_FAST                1 (sd)
            126 STORE_SUBSCR
            130 CACHE
            132 CACHE
            134 CACHE
            136 CACHE
            138 CACHE
            140 CACHE
            142 CACHE
            144 CACHE
            146 CACHE
            148 UNPACK_SEQUENCE          0
            152 CALL                     0
            160 CACHE
            162 POP_TOP
            164 JUMP_BACKWARD           37 (to 92)

233         166 LOAD_CONST               0 (None)
        >>  168 RETURN_VALUE

Disassembly of <code object report_full_closure at 0x000001B2A728DE60, file "filecmp.py", line 237>:
237           0 RESUME                   0

238           2 LOAD_FAST                0 (self)
              4 STORE_SUBSCR
              8 CACHE
             10 CACHE
             12 CACHE
             14 CACHE
             16 CACHE
             18 CACHE
             20 CACHE
             22 CACHE
             24 CACHE
             26 UNPACK_SEQUENCE          0
             30 CALL                     0
             38 CACHE
             40 POP_TOP

239          42 LOAD_FAST                0 (self)
             44 LOAD_ATTR                1 (NULL|self + report)
             64 CACHE
             66 CACHE
             68 CACHE
             70 CACHE
             72 CACHE
             74 CACHE
             76 UNPACK_SEQUENCE          0
             80 CALL                     0
             88 CACHE
             90 GET_ITER
        >>   92 FOR_ITER                36 (to 168)

240          96 LOAD_GLOBAL              7 (NULL + print)
            106 CACHE
            108 UNPACK_SEQUENCE          0
            112 CALL                     0
            120 CACHE
            122 POP_TOP

241         124 LOAD_FAST                1 (sd)
            126 STORE_SUBSCR
            130 CACHE
            132 CACHE
            134 CACHE
            136 CACHE
            138 CACHE
            140 CACHE
            142 CACHE
            144 CACHE
            146 CACHE
            148 UNPACK_SEQUENCE          0
            152 CALL                     0
            160 CACHE
            162 POP_TOP
            164 JUMP_BACKWARD           37 (to 92)

239         166 LOAD_CONST               0 (None)
        >>  168 RETURN_VALUE

Disassembly of <code object __getattr__ at 0x000001B2A8A08DF0, file "filecmp.py", line 249>:
249           0 RESUME                   0

250           2 LOAD_FAST                1 (attr)
              4 LOAD_FAST                0 (self)
              6 LOAD_ATTR                0 (methodmap)
             26 CACHE
             28 CACHE
             30 CACHE
             32 LOAD_FAST                1 (attr)
             34 UNPACK_SEQUENCE          1
             38 CALL                     1
             46 CACHE
             48 RAISE_VARARGS            1

252          50 PUSH_NULL
             52 LOAD_FAST                0 (self)
             54 LOAD_ATTR                0 (methodmap)
             74 CACHE
             76 LOAD_FAST                0 (self)
             78 UNPACK_SEQUENCE          1
             82 CALL                     1
             90 CACHE
             92 POP_TOP

253          94 LOAD_GLOBAL              5 (NULL + getattr)
            104 CACHE
            106 LOAD_FAST                0 (self)
            108 LOAD_FAST                1 (attr)
            110 UNPACK_SEQUENCE          2
            114 CALL                     2
            122 CACHE
            124 RETURN_VALUE

Disassembly of <code object cmpfiles at 0x000001B2A726E8B0, file "filecmp.py", line 258>:
258           0 RESUME                   0

271           2 BUILD_LIST               0
              4 BUILD_LIST               0
              6 BUILD_LIST               0
              8 BUILD_TUPLE              3
             10 STORE_FAST               4 (res)

272          12 LOAD_FAST                2 (common)
             14 GET_ITER
        >>   16 FOR_ITER               108 (to 236)

273          20 LOAD_GLOBAL              0 (os)
             30 CACHE
             32 LOAD_ATTR                1 (NULL|self + os)
             52 CACHE
             54 CACHE
             56 CACHE
             58 CACHE
             60 CACHE
             62 CACHE
             64 LOAD_FAST                0 (a)
             66 LOAD_FAST                5 (x)
             68 UNPACK_SEQUENCE          2
             72 CALL                     2
             80 CACHE
             82 STORE_FAST               6 (ax)

274          84 LOAD_GLOBAL              0 (os)
             94 CACHE
             96 LOAD_ATTR                1 (NULL|self + os)
            116 CACHE
            118 CACHE
            120 CACHE
            122 CACHE
            124 CACHE
            126 CACHE
            128 LOAD_FAST                1 (b)
            130 LOAD_FAST                5 (x)
            132 UNPACK_SEQUENCE          2
            136 CALL                     2
            144 CACHE
            146 STORE_FAST               7 (bx)

275         148 LOAD_FAST                4 (res)
            150 LOAD_GLOBAL              7 (NULL + _cmp)
            160 CACHE
            162 LOAD_FAST                6 (ax)
            164 LOAD_FAST                7 (bx)
            166 LOAD_FAST                3 (shallow)
            168 UNPACK_SEQUENCE          3
            172 CALL                     3
            180 CACHE
            182 BINARY_SUBSCR
            186 CACHE
            188 CACHE
            190 CACHE
            192 STORE_SUBSCR
            196 CACHE
            198 CACHE
            200 CACHE
            202 CACHE
            204 CACHE
            206 CACHE
            208 CACHE
            210 CACHE
            212 CACHE
            214 LOAD_FAST                5 (x)
            216 UNPACK_SEQUENCE          1
            220 CALL                     1
            228 CACHE
            230 POP_TOP
            232 JUMP_BACKWARD          109 (to 16)

276         234 LOAD_FAST                4 (res)
        >>  236 RETURN_VALUE

Disassembly of <code object _cmp at 0x000001B2A76DB990, file "filecmp.py", line 285>:
285           0 RESUME                   0

286           2 NOP

287           4 PUSH_NULL
              6 LOAD_FAST                3 (abs)
              8 PUSH_NULL
             10 LOAD_FAST                4 (cmp)
             12 LOAD_FAST                0 (a)
             14 LOAD_FAST                1 (b)
             16 LOAD_FAST                2 (sh)
             18 UNPACK_SEQUENCE          3
             22 CALL                     3
             30 CACHE
             32 UNPACK_SEQUENCE          1
             36 CALL                     1
             44 CACHE
             46 UNARY_NOT
             48 RETURN_VALUE
        >>   50 PUSH_EXC_INFO

288          52 LOAD_GLOBAL              0 (OSError)
             62 CACHE
             64 CHECK_EXC_MATCH
             66 POP_JUMP_IF_FALSE        4 (to 76)
             68 POP_TOP

289          70 POP_EXCEPT
             72 LOAD_CONST               1 (2)
             74 RETURN_VALUE

288     >>   76 RERAISE                  0
        >>   78 COPY                     3
             80 POP_EXCEPT
             82 RERAISE                  1
ExceptionTable:
  4 to 46 -> 50 [0]
  50 to 68 -> 78 [1] lasti
  76 to 76 -> 78 [1] lasti

Disassembly of <code object _filter at 0x000001B2A76AEEF0, file "filecmp.py", line 294>:
294           0 RESUME                   0

295           2 LOAD_GLOBAL              1 (NULL + list)
             12 CACHE
             14 LOAD_GLOBAL              3 (NULL + filterfalse)
             24 CACHE
             26 LOAD_FAST                1 (skip)
             28 LOAD_ATTR                2 (filterfalse)
             48 CACHE
             50 CACHE
             52 CACHE
             54 UNPACK_SEQUENCE          1
             58 CALL                     1
             66 CACHE
             68 RETURN_VALUE

Disassembly of <code object demo at 0x000001B2A508B6F0, file "filecmp.py", line 300>:
300           0 RESUME                   0

301           2 LOAD_CONST               1 (0)
              4 LOAD_CONST               0 (None)
              6 IMPORT_NAME              0 (sys)
              8 STORE_FAST               0 (sys)

302          10 LOAD_CONST               1 (0)
             12 LOAD_CONST               0 (None)
             14 IMPORT_NAME              1 (getopt)
             16 STORE_FAST               1 (getopt)

303          18 LOAD_FAST                1 (getopt)
             20 STORE_SUBSCR
             24 CACHE
             26 CACHE
             28 CACHE
             30 CACHE
             32 CACHE
             34 CACHE
             36 CACHE
             38 CACHE
             40 CACHE
             42 LOAD_FAST                0 (sys)
             44 LOAD_ATTR                2 (getopt)
             64 CACHE
             66 CACHE
             68 CACHE
             70 LOAD_CONST               3 ('r')
             72 UNPACK_SEQUENCE          2
             76 CALL                     2
             84 CACHE
             86 UNPACK_SEQUENCE          2
             90 STORE_FAST               2 (options)
             92 STORE_FAST               3 (args)

304          94 LOAD_GLOBAL              7 (NULL + len)
            104 CACHE
            106 LOAD_FAST                3 (args)
            108 UNPACK_SEQUENCE          1
            112 CALL                     1
            120 CACHE
            122 LOAD_CONST               4 (2)
            124 COMPARE_OP               3 (<)
            128 CACHE
            130 POP_JUMP_IF_FALSE       22 (to 176)

305         132 LOAD_FAST                1 (getopt)
            134 STORE_SUBSCR
            138 CACHE
            140 CACHE
            142 CACHE
            144 CACHE
            146 CACHE
            148 CACHE
            150 CACHE
            152 CACHE
            154 CACHE
            156 LOAD_CONST               5 ('need exactly two args')
            158 LOAD_CONST               0 (None)
            160 UNPACK_SEQUENCE          2
            164 CALL                     2
            172 CACHE
            174 RAISE_VARARGS            1

306     >>  176 LOAD_GLOBAL             11 (NULL + dircmp)
            186 CACHE
            188 LOAD_FAST                3 (args)
            190 LOAD_CONST               1 (0)
            192 BINARY_SUBSCR
            196 CACHE
            198 CACHE
            200 CACHE
            202 LOAD_FAST                3 (args)
            204 LOAD_CONST               2 (1)
            206 BINARY_SUBSCR
            210 CACHE
            212 CACHE
            214 CACHE
            216 UNPACK_SEQUENCE          2
            220 CALL                     2
            228 CACHE
            230 STORE_FAST               4 (dd)

307         232 LOAD_CONST               6 (('-r', ''))
            234 LOAD_FAST                2 (options)
            236 CONTAINS_OP              0
            238 POP_JUMP_IF_FALSE       22 (to 284)

308         240 LOAD_FAST                4 (dd)
            242 STORE_SUBSCR
            246 CACHE
            248 CACHE
            250 CACHE
            252 CACHE
            254 CACHE
            256 CACHE
            258 CACHE
            260 CACHE
            262 CACHE
            264 UNPACK_SEQUENCE          0
            268 CALL                     0
            276 CACHE
            278 POP_TOP
            280 LOAD_CONST               0 (None)
            282 RETURN_VALUE

310     >>  284 LOAD_FAST                4 (dd)
            286 STORE_SUBSCR
            290 CACHE
            292 CACHE
            294 CACHE
            296 CACHE
            298 CACHE
            300 CACHE
            302 CACHE
            304 CACHE
            306 CACHE
            308 UNPACK_SEQUENCE          0
            312 CALL                     0
            320 CACHE
            322 POP_TOP
            324 LOAD_CONST               0 (None)
            326 RETURN_VALUE


# Constants:
# 0: <string length 179>
# 1: 0
# 2: None
# 3: tuple
# 4: tuple
# 5: tuple
# 6: 8192
# 7: tuple
# 8: <code object clear_cache>
# 9: True
# 10: <code object cmp>
# 11: <code object _sig>
# 12: <code object _do_cmp>
# 13: <code object dircmp>
# 14: 'dircmp'
# 15: <code object cmpfiles>
# 16: <code object _cmp>
# 17: <code object _filter>
# 18: <code object demo>
# 19: '__main__'
# 20: tuple


# Names:
# 0: '__doc__'
# 1: 'os'
# 2: 'stat'
# 3: 'itertools'
# 4: 'filterfalse'
# 5: 'types'
# 6: 'GenericAlias'
# 7: '__all__'
# 8: '_cache'
# 9: 'BUFSIZE'
# 10: 'DEFAULT_IGNORES'
# 11: 'clear_cache'
# 12: 'cmp'
# 13: '_sig'
# 14: '_do_cmp'
# 15: 'dircmp'
# 16: 'cmpfiles'
# 17: 'abs'
# 18: '_cmp'
# 19: '_filter'
# 20: 'demo'
# 21: '__name__'


# Variable names:
