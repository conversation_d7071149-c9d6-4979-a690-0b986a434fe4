# MAIN APPLICATION CODE OBJECT
# Position: 8019565
# Filename: base64.py
# Function: <module>
# Args: 0
# Locals: 0
# Stack size: 5
# Flags: 0

# BYTECODE DISASSEMBLY:
==================================================
  0           0 RESUME                   0

  3           2 LOAD_CONST               0 ('Base16, Base32, Base64 (RFC 3548), Base85 and Ascii85 data encodings')
              4 STORE_NAME               0 (__doc__)

  9           6 LOAD_CONST               1 (0)
              8 LOAD_CONST               2 (None)
             10 IMPORT_NAME              1 (re)
             12 STORE_NAME               1 (re)

 10          14 LOAD_CONST               1 (0)
             16 LOAD_CONST               2 (None)
             18 IMPORT_NAME              2 (struct)
             20 STORE_NAME               2 (struct)

 11          22 LOAD_CONST               1 (0)
             24 LOAD_CONST               2 (None)
             26 IMPORT_NAME              3 (binascii)
             28 STORE_NAME               3 (binascii)

 14          30 BUILD_LIST               0
             32 LOAD_CONST               3 (('encode', 'decode', 'encodebytes', 'decodebytes', 'b64encode', 'b64decode', 'b32encode', 'b32decode', 'b32hexencode', 'b32hexdecode', 'b16encode', 'b16decode', 'b85encode', 'b85decode', 'a85encode', 'a85decode', 'standard_b64encode', 'standard_b64decode', 'urlsafe_b64encode', 'urlsafe_b64decode'))
             34 LIST_EXTEND              1
             36 STORE_NAME               4 (__all__)

 32          38 LOAD_NAME                5 (bytes)
             40 LOAD_NAME                6 (bytearray)
             42 BUILD_TUPLE              2
             44 STORE_NAME               7 (bytes_types)

 34          46 LOAD_CONST               4 (<code object _bytes_from_decode_data at 0x000001E77EF6C040, file "base64.py", line 34>)
             48 MAKE_FUNCTION            0
             50 STORE_NAME               8 (_bytes_from_decode_data)

 51          52 LOAD_CONST              54 ((None,))
             54 LOAD_CONST               5 (<code object b64encode at 0x000001E77E77F630, file "base64.py", line 51>)
             56 MAKE_FUNCTION            1 (defaults)
             58 STORE_NAME               9 (b64encode)

 65          60 LOAD_CONST              55 ((None, False))
             62 LOAD_CONST               7 (<code object b64decode at 0x000001E77E72B690, file "base64.py", line 65>)
             64 MAKE_FUNCTION            1 (defaults)
             66 STORE_NAME              10 (b64decode)

 91          68 LOAD_CONST               8 (<code object standard_b64encode at 0x000001E77EC5FE50, file "base64.py", line 91>)
             70 MAKE_FUNCTION            0
             72 STORE_NAME              11 (standard_b64encode)

 98          74 LOAD_CONST               9 (<code object standard_b64decode at 0x000001E77ECD02D0, file "base64.py", line 98>)
             76 MAKE_FUNCTION            0
             78 STORE_NAME              12 (standard_b64decode)

109          80 LOAD_NAME                5 (bytes)
             82 STORE_SUBSCR
             86 CACHE
             88 CACHE
             90 CACHE
             92 CACHE
             94 CACHE
             96 CACHE
             98 CACHE
            100 CACHE
            102 CACHE
            104 LOAD_CONST              10 (b'+/')
            106 LOAD_CONST              11 (b'-_')
            108 UNPACK_SEQUENCE          2
            112 CALL                     2
            120 CACHE
            122 STORE_NAME              14 (_urlsafe_encode_translation)

110         124 LOAD_NAME                5 (bytes)
            126 STORE_SUBSCR
            130 CACHE
            132 CACHE
            134 CACHE
            136 CACHE
            138 CACHE
            140 CACHE
            142 CACHE
            144 CACHE
            146 CACHE
            148 LOAD_CONST              11 (b'-_')
            150 LOAD_CONST              10 (b'+/')
            152 UNPACK_SEQUENCE          2
            156 CALL                     2
            164 CACHE
            166 STORE_NAME              15 (_urlsafe_decode_translation)

112         168 LOAD_CONST              12 (<code object urlsafe_b64encode at 0x000001E77EC47BB0, file "base64.py", line 112>)
            170 MAKE_FUNCTION            0
            172 STORE_NAME              16 (urlsafe_b64encode)

121         174 LOAD_CONST              13 (<code object urlsafe_b64decode at 0x000001E77EC3BD70, file "base64.py", line 121>)
            176 MAKE_FUNCTION            0
            178 STORE_NAME              17 (urlsafe_b64decode)

139         180 LOAD_CONST              14 ('\nEncode the bytes-like objects using {encoding} and return a bytes object.\n')
            182 STORE_NAME              18 (_B32_ENCODE_DOCSTRING)

142         184 LOAD_CONST              15 ('\nDecode the {encoding} encoded bytes-like object or ASCII string s.\n\nOptional casefold is a flag specifying whether a lowercase alphabet is\nacceptable as input.  For security purposes, the default is False.\n{extra_args}\nThe result is returned as a bytes object.  A binascii.Error is raised if\nthe input is incorrectly padded or if there are non-alphabet\ncharacters present in the input.\n')
            186 STORE_NAME              19 (_B32_DECODE_DOCSTRING)

152         188 LOAD_CONST              16 ('\nRFC 3548 allows for optional mapping of the digit 0 (zero) to the\nletter O (oh), and for optional mapping of the digit 1 (one) to\neither the letter I (eye) or letter L (el).  The optional argument\nmap01 when not None, specifies which letter the digit 1 should be\nmapped to (when map01 is not None, the digit 0 is always mapped to\nthe letter O).  For security purposes the default is None, so that\n0 and 1 are not allowed in the input.\n')
            190 STORE_NAME              20 (_B32_DECODE_MAP01_DOCSTRING)

161         192 LOAD_CONST              17 (b'ABCDEFGHIJKLMNOPQRSTUVWXYZ234567')
            194 STORE_NAME              21 (_b32alphabet)

162         196 LOAD_CONST              18 (b'0123456789ABCDEFGHIJKLMNOPQRSTUV')
            198 STORE_NAME              22 (_b32hexalphabet)

163         200 BUILD_MAP                0
            202 STORE_GLOBAL            23 (_b32tab2)

164         204 BUILD_MAP                0
            206 STORE_GLOBAL            24 (_b32rev)

166         208 LOAD_CONST              19 (<code object _b32encode at 0x000001E77EF1E460, file "base64.py", line 166>)
            210 MAKE_FUNCTION            0
            212 STORE_NAME              25 (_b32encode)

202         214 LOAD_CONST              56 ((False, None))
            216 LOAD_CONST              20 (<code object _b32decode at 0x000001E77E9F2E40, file "base64.py", line 202>)
            218 MAKE_FUNCTION            1 (defaults)
            220 STORE_NAME              26 (_b32decode)

249         222 LOAD_CONST              21 (<code object b32encode at 0x000001E77EC92970, file "base64.py", line 249>)
            224 MAKE_FUNCTION            0
            226 STORE_NAME              27 (b32encode)

251         228 LOAD_NAME               18 (_B32_ENCODE_DOCSTRING)
            230 STORE_SUBSCR
            234 CACHE
            236 CACHE
            238 CACHE
            240 CACHE
            242 CACHE
            244 CACHE
            246 CACHE
            248 CACHE
            250 CACHE
            252 LOAD_CONST              22 ('base32')
            254 KW_NAMES                23 (('encoding',))
            256 UNPACK_SEQUENCE          1
            260 CALL                     1
            268 CACHE
            270 LOAD_NAME               27 (b32encode)
            272 STORE_ATTR               0 (__doc__)

253         282 LOAD_CONST              56 ((False, None))
            284 LOAD_CONST              24 (<code object b32decode at 0x000001E77EC92D30, file "base64.py", line 253>)
            286 MAKE_FUNCTION            1 (defaults)
            288 STORE_NAME              29 (b32decode)

255         290 LOAD_NAME               19 (_B32_DECODE_DOCSTRING)
            292 STORE_SUBSCR
            296 CACHE
            298 CACHE
            300 CACHE
            302 CACHE
            304 CACHE
            306 CACHE
            308 CACHE
            310 CACHE
            312 CACHE
            314 LOAD_CONST              22 ('base32')

256         316 LOAD_NAME               20 (_B32_DECODE_MAP01_DOCSTRING)

255         318 KW_NAMES                25 (('encoding', 'extra_args'))
            320 UNPACK_SEQUENCE          2
            324 CALL                     2
            332 CACHE
            334 LOAD_NAME               29 (b32decode)
            336 STORE_ATTR               0 (__doc__)

258         346 LOAD_CONST              26 (<code object b32hexencode at 0x000001E77EC93780, file "base64.py", line 258>)
            348 MAKE_FUNCTION            0
            350 STORE_NAME              30 (b32hexencode)

260         352 LOAD_NAME               18 (_B32_ENCODE_DOCSTRING)
            354 STORE_SUBSCR
            358 CACHE
            360 CACHE
            362 CACHE
            364 CACHE
            366 CACHE
            368 CACHE
            370 CACHE
            372 CACHE
            374 CACHE
            376 LOAD_CONST              27 ('base32hex')
            378 KW_NAMES                23 (('encoding',))
            380 UNPACK_SEQUENCE          1
            384 CALL                     1
            392 CACHE
            394 LOAD_NAME               30 (b32hexencode)
            396 STORE_ATTR               0 (__doc__)

262         406 LOAD_CONST              57 ((False,))
            408 LOAD_CONST              28 (<code object b32hexdecode at 0x000001E77EC93870, file "base64.py", line 262>)
            410 MAKE_FUNCTION            1 (defaults)
            412 STORE_NAME              31 (b32hexdecode)

265         414 LOAD_NAME               19 (_B32_DECODE_DOCSTRING)
            416 STORE_SUBSCR
            420 CACHE
            422 CACHE
            424 CACHE
            426 CACHE
            428 CACHE
            430 CACHE
            432 CACHE
            434 CACHE
            436 CACHE
            438 LOAD_CONST              27 ('base32hex')

266         440 LOAD_CONST              29 ('')

265         442 KW_NAMES                25 (('encoding', 'extra_args'))
            444 UNPACK_SEQUENCE          2
            448 CALL                     2
            456 CACHE
            458 LOAD_NAME               31 (b32hexdecode)
            460 STORE_ATTR               0 (__doc__)

272         470 LOAD_CONST              30 (<code object b16encode at 0x000001E77EC47CC0, file "base64.py", line 272>)
            472 MAKE_FUNCTION            0
            474 STORE_NAME              32 (b16encode)

278         476 LOAD_CONST              57 ((False,))
            478 LOAD_CONST              31 (<code object b16decode at 0x000001E77EC71DE0, file "base64.py", line 278>)
            480 MAKE_FUNCTION            1 (defaults)
            482 STORE_NAME              33 (b16decode)

299         484 LOAD_CONST               2 (None)
            486 STORE_GLOBAL            34 (_a85chars)

300         488 LOAD_CONST               2 (None)
            490 STORE_GLOBAL            35 (_a85chars2)

301         492 LOAD_CONST              32 (b'<~')
            494 STORE_NAME              36 (_A85START)

302         496 LOAD_CONST              33 (b'~>')
            498 STORE_NAME              37 (_A85END)

304         500 LOAD_CONST              58 ((False, False, False))
            502 LOAD_CONST              34 (<code object _85encode at 0x000001E77EF1E7F0, file "base64.py", line 304>)
            504 MAKE_FUNCTION            1 (defaults)
            506 STORE_NAME              38 (_85encode)

328         508 LOAD_CONST               6 (False)
            510 LOAD_CONST               1 (0)
            512 LOAD_CONST               6 (False)
            514 LOAD_CONST               6 (False)
            516 LOAD_CONST              35 (('foldspaces', 'wrapcol', 'pad', 'adobe'))
            518 BUILD_CONST_KEY_MAP      4
            520 LOAD_CONST              36 (<code object a85encode at 0x000001E77EF1D820, file "base64.py", line 328>)
            522 MAKE_FUNCTION            2 (kwdefaults)
            524 STORE_NAME              39 (a85encode)

369         526 LOAD_CONST               6 (False)
            528 LOAD_CONST               6 (False)
            530 LOAD_CONST              37 (b' \t\n\r\x0b')
            532 LOAD_CONST              38 (('foldspaces', 'adobe', 'ignorechars'))
            534 BUILD_CONST_KEY_MAP      3
            536 LOAD_CONST              39 (<code object a85decode at 0x000001E77E9D8DC0, file "base64.py", line 369>)
            538 MAKE_FUNCTION            2 (kwdefaults)
            540 STORE_NAME              40 (a85decode)

441         542 LOAD_CONST              40 (b'0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz!#$%&()*+-;<=>?@^_`{|}~')
            544 STORE_NAME              41 (_b85alphabet)

443         546 LOAD_CONST               2 (None)
            548 STORE_GLOBAL            42 (_b85chars)

444         550 LOAD_CONST               2 (None)
            552 STORE_GLOBAL            43 (_b85chars2)

445         554 LOAD_CONST               2 (None)
            556 STORE_GLOBAL            44 (_b85dec)

447         558 LOAD_CONST              57 ((False,))
            560 LOAD_CONST              41 (<code object b85encode at 0x000001E77EC4E4F0, file "base64.py", line 447>)
            562 MAKE_FUNCTION            1 (defaults)
            564 STORE_NAME              45 (b85encode)

461         566 LOAD_CONST              42 (<code object b85decode at 0x000001E77EF21E00, file "base64.py", line 461>)
            568 MAKE_FUNCTION            0
            570 STORE_NAME              46 (b85decode)

506         572 LOAD_CONST              43 (76)
            574 STORE_NAME              47 (MAXLINESIZE)

507         576 LOAD_NAME               47 (MAXLINESIZE)
            578 LOAD_CONST              44 (4)
            580 BINARY_OP                2 (//)
            584 LOAD_CONST              45 (3)
            586 BINARY_OP                5 (*)
            590 STORE_NAME              48 (MAXBINSIZE)

509         592 LOAD_CONST              46 (<code object encode at 0x000001E77EF6DBE0, file "base64.py", line 509>)
            594 MAKE_FUNCTION            0
            596 STORE_NAME              49 (encode)

524         598 LOAD_CONST              47 (<code object decode at 0x000001E77EC4EB80, file "base64.py", line 524>)
            600 MAKE_FUNCTION            0
            602 STORE_NAME              50 (decode)

533         604 LOAD_CONST              48 (<code object _input_type_check at 0x000001E77EF4EA00, file "base64.py", line 533>)
            606 MAKE_FUNCTION            0
            608 STORE_NAME              51 (_input_type_check)

549         610 LOAD_CONST              49 (<code object encodebytes at 0x000001E77ECB03D0, file "base64.py", line 549>)
            612 MAKE_FUNCTION            0
            614 STORE_NAME              52 (encodebytes)

560         616 LOAD_CONST              50 (<code object decodebytes at 0x000001E77ECF0250, file "base64.py", line 560>)
            618 MAKE_FUNCTION            0
            620 STORE_NAME              53 (decodebytes)

567         622 LOAD_CONST              51 (<code object main at 0x000001E77EF21390, file "base64.py", line 567>)
            624 MAKE_FUNCTION            0
            626 STORE_NAME              54 (main)

596         628 LOAD_CONST              52 (<code object test at 0x000001E77ECC0030, file "base64.py", line 596>)
            630 MAKE_FUNCTION            0
            632 STORE_NAME              55 (test)

606         634 LOAD_NAME               56 (__name__)
            636 LOAD_CONST              53 ('__main__')
            638 COMPARE_OP               2 (<)
            642 CACHE
            644 POP_JUMP_IF_FALSE       12 (to 670)

607         646 PUSH_NULL
            648 LOAD_NAME               54 (main)
            650 UNPACK_SEQUENCE          0
            654 CALL                     0
            662 CACHE
            664 POP_TOP
            666 LOAD_CONST               2 (None)
            668 RETURN_VALUE

606     >>  670 LOAD_CONST               2 (None)
            672 RETURN_VALUE

Disassembly of <code object _bytes_from_decode_data at 0x000001E77EF6C040, file "base64.py", line 34>:
 34           0 RESUME                   0

 35           2 LOAD_GLOBAL              1 (NULL + isinstance)
             12 CACHE
             14 LOAD_FAST                0 (s)
             16 LOAD_GLOBAL              2 (str)
             26 CACHE
             28 UNPACK_SEQUENCE          2
             32 CALL                     2
             40 CACHE
             42 POP_JUMP_IF_FALSE       51 (to 146)

 36          44 NOP

 37          46 LOAD_FAST                0 (s)
             48 STORE_SUBSCR
             52 CACHE
             54 CACHE
             56 CACHE
             58 CACHE
             60 CACHE
             62 CACHE
             64 CACHE
             66 CACHE
             68 CACHE
             70 LOAD_CONST               1 ('ascii')
             72 UNPACK_SEQUENCE          1
             76 CALL                     1
             84 CACHE
             86 RETURN_VALUE
        >>   88 PUSH_EXC_INFO

 38          90 LOAD_GLOBAL              6 (UnicodeEncodeError)
            100 CACHE
            102 CHECK_EXC_MATCH
            104 POP_JUMP_IF_FALSE       16 (to 138)
            106 POP_TOP

 39         108 LOAD_GLOBAL              9 (NULL + ValueError)
            118 CACHE
            120 LOAD_CONST               2 ('string argument should contain only ASCII characters')
            122 UNPACK_SEQUENCE          1
            126 CALL                     1
            134 CACHE
            136 RAISE_VARARGS            1

 38     >>  138 RERAISE                  0
        >>  140 COPY                     3
            142 POP_EXCEPT
            144 RERAISE                  1

 40     >>  146 LOAD_GLOBAL              1 (NULL + isinstance)
            156 CACHE
            158 LOAD_FAST                0 (s)
            160 LOAD_GLOBAL             10 (bytes_types)
            170 CACHE
            172 UNPACK_SEQUENCE          2
            176 CALL                     2
            184 CACHE
            186 POP_JUMP_IF_FALSE        2 (to 192)

 41         188 LOAD_FAST                0 (s)
            190 RETURN_VALUE

 42     >>  192 NOP

 43         194 LOAD_GLOBAL             13 (NULL + memoryview)
            204 CACHE
            206 LOAD_FAST                0 (s)
            208 UNPACK_SEQUENCE          1
            212 CALL                     1
            220 CACHE
            222 STORE_SUBSCR
            226 CACHE
            228 CACHE
            230 CACHE
            232 CACHE
            234 CACHE
            236 CACHE
            238 CACHE
            240 CACHE
            242 CACHE
            244 UNPACK_SEQUENCE          0
            248 CALL                     0
            256 CACHE
            258 RETURN_VALUE
        >>  260 PUSH_EXC_INFO

 44         262 LOAD_GLOBAL             16 (TypeError)
            272 CACHE
            274 CHECK_EXC_MATCH
            276 POP_JUMP_IF_FALSE       30 (to 338)
            278 POP_TOP

 45         280 LOAD_GLOBAL             17 (NULL + TypeError)
            290 CACHE
            292 LOAD_CONST               3 ('argument should be a bytes-like object or ASCII string, not %r')

 46         294 LOAD_FAST                0 (s)
            296 LOAD_ATTR                9 (NULL|self + ValueError)

 45         316 BINARY_OP                6 (%)
            320 UNPACK_SEQUENCE          1
            324 CALL                     1
            332 CACHE

 46         334 LOAD_CONST               0 (None)

 45         336 RAISE_VARARGS            2

 44     >>  338 RERAISE                  0
        >>  340 COPY                     3
            342 POP_EXCEPT
            344 RERAISE                  1
ExceptionTable:
  46 to 84 -> 88 [0]
  88 to 138 -> 140 [1] lasti
  194 to 256 -> 260 [0]
  260 to 338 -> 340 [1] lasti

Disassembly of <code object b64encode at 0x000001E77E77F630, file "base64.py", line 51>:
 51           0 RESUME                   0

 58           2 LOAD_GLOBAL              1 (NULL + binascii)
             12 CACHE
             14 LOAD_ATTR                1 (NULL|self + binascii)
             34 CALL                     2
             42 CACHE
             44 STORE_FAST               2 (encoded)

 59          46 LOAD_FAST                1 (altchars)
             48 POP_JUMP_IF_NONE        88 (to 226)

 60          50 LOAD_GLOBAL              5 (NULL + len)
             60 CACHE
             62 LOAD_FAST                1 (altchars)
             64 UNPACK_SEQUENCE          1
             68 CALL                     1
             76 CACHE
             78 LOAD_CONST               4 (2)
             80 COMPARE_OP               2 (<)
             84 CACHE
             86 POP_JUMP_IF_TRUE        23 (to 134)
             88 LOAD_ASSERTION_ERROR
             90 LOAD_GLOBAL              7 (NULL + repr)
            100 CACHE
            102 LOAD_FAST                1 (altchars)
            104 UNPACK_SEQUENCE          1
            108 CALL                     1
            116 CACHE
            118 UNPACK_SEQUENCE          0
            122 CALL                     0
            130 CACHE
            132 RAISE_VARARGS            1

 61     >>  134 LOAD_FAST                2 (encoded)
            136 STORE_SUBSCR
            140 CACHE
            142 CACHE
            144 CACHE
            146 CACHE
            148 CACHE
            150 CACHE
            152 CACHE
            154 CACHE
            156 CACHE
            158 LOAD_GLOBAL             10 (bytes)
            168 CACHE
            170 STORE_SUBSCR
            174 CACHE
            176 CACHE
            178 CACHE
            180 CACHE
            182 CACHE
            184 CACHE
            186 CACHE
            188 CACHE
            190 CACHE
            192 LOAD_CONST               5 (b'+/')
            194 LOAD_FAST                1 (altchars)
            196 UNPACK_SEQUENCE          2
            200 CALL                     2
            208 CACHE
            210 UNPACK_SEQUENCE          1
            214 CALL                     1
            222 CACHE
            224 RETURN_VALUE

 62     >>  226 LOAD_FAST                2 (encoded)
            228 RETURN_VALUE

Disassembly of <code object b64decode at 0x000001E77E72B690, file "base64.py", line 65>:
 65           0 RESUME                   0

 83           2 LOAD_GLOBAL              1 (NULL + _bytes_from_decode_data)
             12 CACHE
             14 LOAD_FAST                0 (s)
             16 UNPACK_SEQUENCE          1
             20 CALL                     1
             28 CACHE
             30 STORE_FAST               0 (s)

 84          32 LOAD_FAST                1 (altchars)
             34 POP_JUMP_IF_NONE       103 (to 242)

 85          36 LOAD_GLOBAL              1 (NULL + _bytes_from_decode_data)
             46 CACHE
             48 LOAD_FAST                1 (altchars)
             50 UNPACK_SEQUENCE          1
             54 CALL                     1
             62 CACHE
             64 STORE_FAST               1 (altchars)

 86          66 LOAD_GLOBAL              3 (NULL + len)
             76 CACHE
             78 LOAD_FAST                1 (altchars)
             80 UNPACK_SEQUENCE          1
             84 CALL                     1
             92 CACHE
             94 LOAD_CONST               2 (2)
             96 COMPARE_OP               2 (<)
            100 CACHE
            102 POP_JUMP_IF_TRUE        23 (to 150)
            104 LOAD_ASSERTION_ERROR
            106 LOAD_GLOBAL              5 (NULL + repr)
            116 CACHE
            118 LOAD_FAST                1 (altchars)
            120 UNPACK_SEQUENCE          1
            124 CALL                     1
            132 CACHE
            134 UNPACK_SEQUENCE          0
            138 CALL                     0
            146 CACHE
            148 RAISE_VARARGS            1

 87     >>  150 LOAD_FAST                0 (s)
            152 STORE_SUBSCR
            156 CACHE
            158 CACHE
            160 CACHE
            162 CACHE
            164 CACHE
            166 CACHE
            168 CACHE
            170 CACHE
            172 CACHE
            174 LOAD_GLOBAL              8 (bytes)
            184 CACHE
            186 STORE_SUBSCR
            190 CACHE
            192 CACHE
            194 CACHE
            196 CACHE
            198 CACHE
            200 CACHE
            202 CACHE
            204 CACHE
            206 CACHE
            208 LOAD_FAST                1 (altchars)
            210 LOAD_CONST               3 (b'+/')
            212 UNPACK_SEQUENCE          2
            216 CALL                     2
            224 CACHE
            226 UNPACK_SEQUENCE          1
            230 CALL                     1
            238 CACHE
            240 STORE_FAST               0 (s)

 88     >>  242 LOAD_GLOBAL             13 (NULL + binascii)
            252 CACHE
            254 LOAD_ATTR                7 (NULL|self + translate)
            274 CALL                     2
            282 CACHE
            284 RETURN_VALUE

Disassembly of <code object standard_b64encode at 0x000001E77EC5FE50, file "base64.py", line 91>:
 91           0 RESUME                   0

 96           2 LOAD_GLOBAL              1 (NULL + b64encode)
             12 CACHE
             14 LOAD_FAST                0 (s)
             16 UNPACK_SEQUENCE          1
             20 CALL                     1
             28 CACHE
             30 RETURN_VALUE

Disassembly of <code object standard_b64decode at 0x000001E77ECD02D0, file "base64.py", line 98>:
 98           0 RESUME                   0

106           2 LOAD_GLOBAL              1 (NULL + b64decode)
             12 CACHE
             14 LOAD_FAST                0 (s)
             16 UNPACK_SEQUENCE          1
             20 CALL                     1
             28 CACHE
             30 RETURN_VALUE

Disassembly of <code object urlsafe_b64encode at 0x000001E77EC47BB0, file "base64.py", line 112>:
112           0 RESUME                   0

119           2 LOAD_GLOBAL              1 (NULL + b64encode)
             12 CACHE
             14 LOAD_FAST                0 (s)
             16 UNPACK_SEQUENCE          1
             20 CALL                     1
             28 CACHE
             30 STORE_SUBSCR
             34 CACHE
             36 CACHE
             38 CACHE
             40 CACHE
             42 CACHE
             44 CACHE
             46 CACHE
             48 CACHE
             50 CACHE
             52 LOAD_GLOBAL              4 (_urlsafe_encode_translation)
             62 CACHE
             64 UNPACK_SEQUENCE          1
             68 CALL                     1
             76 CACHE
             78 RETURN_VALUE

Disassembly of <code object urlsafe_b64decode at 0x000001E77EC3BD70, file "base64.py", line 121>:
121           0 RESUME                   0

132           2 LOAD_GLOBAL              1 (NULL + _bytes_from_decode_data)
             12 CACHE
             14 LOAD_FAST                0 (s)
             16 UNPACK_SEQUENCE          1
             20 CALL                     1
             28 CACHE
             30 STORE_FAST               0 (s)

133          32 LOAD_FAST                0 (s)
             34 STORE_SUBSCR
             38 CACHE
             40 CACHE
             42 CACHE
             44 CACHE
             46 CACHE
             48 CACHE
             50 CACHE
             52 CACHE
             54 CACHE
             56 LOAD_GLOBAL              4 (_urlsafe_decode_translation)
             66 CACHE
             68 UNPACK_SEQUENCE          1
             72 CALL                     1
             80 CACHE
             82 STORE_FAST               0 (s)

134          84 LOAD_GLOBAL              7 (NULL + b64decode)
             94 CACHE
             96 LOAD_FAST                0 (s)
             98 UNPACK_SEQUENCE          1
            102 CALL                     1
            110 CACHE
            112 RETURN_VALUE

Disassembly of <code object _b32encode at 0x000001E77EF1E460, file "base64.py", line 166>:
              0 MAKE_CELL                8 (b32tab)

166           2 RESUME                   0

170           4 LOAD_FAST                0 (alphabet)
              6 LOAD_GLOBAL              0 (_b32tab2)
             16 CACHE
             18 CONTAINS_OP              1
             20 POP_JUMP_IF_FALSE       36 (to 94)

171          22 LOAD_CONST               1 (<code object <listcomp> at 0x000001E77EC90D50, file "base64.py", line 171>)
             24 MAKE_FUNCTION            0
             26 LOAD_FAST                0 (alphabet)
             28 GET_ITER
             30 UNPACK_SEQUENCE          0
             34 CALL                     0
             42 CACHE
             44 STORE_DEREF              8 (b32tab)

172          46 LOAD_CLOSURE             8 (b32tab)
             48 BUILD_TUPLE              1
             50 LOAD_CONST               2 (<code object <listcomp> at 0x000001E77EC914D0, file "base64.py", line 172>)
             52 MAKE_FUNCTION            8 (closure)
             54 LOAD_DEREF               8 (b32tab)
             56 GET_ITER
             58 UNPACK_SEQUENCE          0
             62 CALL                     0
             70 CACHE
             72 LOAD_GLOBAL              0 (_b32tab2)
             82 CACHE
             84 LOAD_FAST                0 (alphabet)
             86 STORE_SUBSCR

173          90 LOAD_CONST               0 (None)
             92 STORE_DEREF              8 (b32tab)

175     >>   94 LOAD_GLOBAL              3 (NULL + isinstance)
            104 CACHE
            106 LOAD_FAST                1 (s)
            108 LOAD_GLOBAL              4 (bytes_types)
            118 CACHE
            120 UNPACK_SEQUENCE          2
            124 CALL                     2
            132 CACHE
            134 POP_JUMP_IF_TRUE        33 (to 202)

176         136 LOAD_GLOBAL              7 (NULL + memoryview)
            146 CACHE
            148 LOAD_FAST                1 (s)
            150 UNPACK_SEQUENCE          1
            154 CALL                     1
            162 CACHE
            164 STORE_SUBSCR
            168 CACHE
            170 CACHE
            172 CACHE
            174 CACHE
            176 CACHE
            178 CACHE
            180 CACHE
            182 CACHE
            184 CACHE
            186 UNPACK_SEQUENCE          0
            190 CALL                     0
            198 CACHE
            200 STORE_FAST               1 (s)

177     >>  202 LOAD_GLOBAL             11 (NULL + len)
            212 CACHE
            214 LOAD_FAST                1 (s)
            216 UNPACK_SEQUENCE          1
            220 CALL                     1
            228 CACHE
            230 LOAD_CONST               3 (5)
            232 BINARY_OP                6 (%)
            236 STORE_FAST               2 (leftover)

179         238 LOAD_FAST                2 (leftover)
            240 POP_JUMP_IF_FALSE       11 (to 264)

180         242 LOAD_FAST                1 (s)
            244 LOAD_CONST               4 (b'\x00')
            246 LOAD_CONST               3 (5)
            248 LOAD_FAST                2 (leftover)
            250 BINARY_OP               10 (-)
            254 BINARY_OP                5 (*)
            258 BINARY_OP                0 (+)
            262 STORE_FAST               1 (s)

181     >>  264 LOAD_GLOBAL             13 (NULL + bytearray)
            274 CACHE
            276 UNPACK_SEQUENCE          0
            280 CALL                     0
            288 CACHE
            290 STORE_FAST               3 (encoded)

182         292 LOAD_GLOBAL             14 (int)
            302 CACHE
            304 LOAD_ATTR                8 (tobytes)
            324 CACHE
            326 CACHE
            328 LOAD_FAST                0 (alphabet)
            330 BINARY_SUBSCR
            334 CACHE
            336 CACHE
            338 CACHE
            340 STORE_FAST               5 (b32tab2)

184         342 LOAD_GLOBAL             19 (NULL + range)
            352 CACHE
            354 LOAD_CONST               5 (0)
            356 LOAD_GLOBAL             11 (NULL + len)
            366 CACHE
            368 LOAD_FAST                1 (s)
            370 UNPACK_SEQUENCE          1
            374 CALL                     1
            382 CACHE
            384 LOAD_CONST               3 (5)
            386 UNPACK_SEQUENCE          3
            390 CALL                     3
            398 CACHE
            400 GET_ITER
        >>  402 FOR_ITER                80 (to 566)

185         406 PUSH_NULL
            408 LOAD_FAST                4 (from_bytes)
            410 LOAD_FAST                1 (s)
            412 LOAD_FAST                6 (i)
            414 LOAD_FAST                6 (i)
            416 LOAD_CONST               3 (5)
            418 BINARY_OP                0 (+)
            422 BUILD_SLICE              2
            424 BINARY_SUBSCR
            428 CACHE
            430 CACHE
            432 CACHE
            434 UNPACK_SEQUENCE          1
            438 CALL                     1
            446 CACHE
            448 STORE_FAST               7 (c)

186         450 LOAD_FAST                3 (encoded)
            452 LOAD_FAST                5 (b32tab2)
            454 LOAD_FAST                7 (c)
            456 LOAD_CONST               6 (30)
            458 BINARY_OP                9 (>>)
            462 BINARY_SUBSCR
            466 CACHE
            468 CACHE
            470 CACHE

187         472 LOAD_FAST                5 (b32tab2)
            474 LOAD_FAST                7 (c)
            476 LOAD_CONST               7 (20)
            478 BINARY_OP                9 (>>)
            482 LOAD_CONST               8 (1023)
            484 BINARY_OP                1 (&)
            488 BINARY_SUBSCR
            492 CACHE
            494 CACHE
            496 CACHE

186         498 BINARY_OP                0 (+)

188         502 LOAD_FAST                5 (b32tab2)
            504 LOAD_FAST                7 (c)
            506 LOAD_CONST               9 (10)
            508 BINARY_OP                9 (>>)
            512 LOAD_CONST               8 (1023)
            514 BINARY_OP                1 (&)
            518 BINARY_SUBSCR
            522 CACHE
            524 CACHE
            526 CACHE

186         528 BINARY_OP                0 (+)

189         532 LOAD_FAST                5 (b32tab2)
            534 LOAD_FAST                7 (c)
            536 LOAD_CONST               8 (1023)
            538 BINARY_OP                1 (&)
            542 BINARY_SUBSCR
            546 CACHE
            548 CACHE
            550 CACHE

186         552 BINARY_OP                0 (+)
            556 BINARY_OP               13 (+=)
            560 STORE_FAST               3 (encoded)
            562 JUMP_BACKWARD           81 (to 402)

192         564 LOAD_FAST                2 (leftover)
        >>  566 LOAD_CONST              10 (1)
            568 COMPARE_OP               2 (<)
            572 CACHE
            574 POP_JUMP_IF_FALSE        8 (to 592)

193         576 LOAD_CONST              11 (b'======')
            578 LOAD_FAST                3 (encoded)
            580 LOAD_CONST              12 (-6)
            582 LOAD_CONST               0 (None)
            584 BUILD_SLICE              2
            586 STORE_SUBSCR
            590 JUMP_FORWARD            41 (to 674)

194     >>  592 LOAD_FAST                2 (leftover)
            594 LOAD_CONST              13 (2)
            596 COMPARE_OP               2 (<)
            600 CACHE
            602 POP_JUMP_IF_FALSE        8 (to 620)

195         604 LOAD_CONST              14 (b'====')
            606 LOAD_FAST                3 (encoded)
            608 LOAD_CONST              15 (-4)
            610 LOAD_CONST               0 (None)
            612 BUILD_SLICE              2
            614 STORE_SUBSCR
            618 JUMP_FORWARD            27 (to 674)

196     >>  620 LOAD_FAST                2 (leftover)
            622 LOAD_CONST              16 (3)
            624 COMPARE_OP               2 (<)
            628 CACHE
            630 POP_JUMP_IF_FALSE        8 (to 648)

197         632 LOAD_CONST              17 (b'===')
            634 LOAD_FAST                3 (encoded)
            636 LOAD_CONST              18 (-3)
            638 LOAD_CONST               0 (None)
            640 BUILD_SLICE              2
            642 STORE_SUBSCR
            646 JUMP_FORWARD            13 (to 674)

198     >>  648 LOAD_FAST                2 (leftover)
            650 LOAD_CONST              19 (4)
            652 COMPARE_OP               2 (<)
            656 CACHE
            658 POP_JUMP_IF_FALSE        7 (to 674)

199         660 LOAD_CONST              20 (b'=')
            662 LOAD_FAST                3 (encoded)
            664 LOAD_CONST              21 (-1)
            666 LOAD_CONST               0 (None)
            668 BUILD_SLICE              2
            670 STORE_SUBSCR

200     >>  674 LOAD_GLOBAL             21 (NULL + bytes)
            684 CACHE
            686 LOAD_FAST                3 (encoded)
            688 UNPACK_SEQUENCE          1
            692 CALL                     1
            700 CACHE
            702 RETURN_VALUE

Disassembly of <code object <listcomp> at 0x000001E77EC90D50, file "base64.py", line 171>:
171           0 RESUME                   0
              2 BUILD_LIST               0
              4 LOAD_FAST                0 (.0)
        >>    6 FOR_ITER                18 (to 46)
             10 LOAD_GLOBAL              1 (NULL + bytes)
             20 CACHE
             22 LOAD_FAST                1 (i)
             24 BUILD_TUPLE              1
             26 UNPACK_SEQUENCE          1
             30 CALL                     1
             38 CACHE
             40 LIST_APPEND              2
             42 JUMP_BACKWARD           19 (to 6)
             44 RETURN_VALUE

Disassembly of <code object <listcomp> at 0x000001E77EC914D0, file "base64.py", line 172>:
              0 COPY_FREE_VARS           1

172           2 RESUME                   0
              4 BUILD_LIST               0
              6 LOAD_FAST                0 (.0)
        >>    8 FOR_ITER                12 (to 36)
             12 LOAD_DEREF               3 (b32tab)
             14 GET_ITER
        >>   16 FOR_ITER                 7 (to 34)
             20 LOAD_FAST                1 (a)
             22 LOAD_FAST                2 (b)
             24 BINARY_OP                0 (+)
             28 LIST_APPEND              3
             30 JUMP_BACKWARD            8 (to 16)
             32 JUMP_BACKWARD           13 (to 8)
        >>   34 RETURN_VALUE

Disassembly of <code object _b32decode at 0x000001E77E9F2E40, file "base64.py", line 202>:
202           0 RESUME                   0

206           2 LOAD_FAST                0 (alphabet)
              4 LOAD_GLOBAL              0 (_b32rev)
             14 CACHE
             16 CONTAINS_OP              1
             18 POP_JUMP_IF_FALSE       33 (to 86)

207          20 LOAD_CONST               1 (<code object <dictcomp> at 0x000001E77ECD0650, file "base64.py", line 207>)
             22 MAKE_FUNCTION            0
             24 LOAD_GLOBAL              3 (NULL + enumerate)
             34 CACHE
             36 LOAD_FAST                0 (alphabet)
             38 UNPACK_SEQUENCE          1
             42 CALL                     1
             50 CACHE
             52 GET_ITER
             54 UNPACK_SEQUENCE          0
             58 CALL                     0
             66 CACHE
             68 LOAD_GLOBAL              0 (_b32rev)
             78 CACHE
             80 LOAD_FAST                0 (alphabet)
             82 STORE_SUBSCR

208     >>   86 LOAD_GLOBAL              5 (NULL + _bytes_from_decode_data)
             96 CACHE
             98 LOAD_FAST                1 (s)
            100 UNPACK_SEQUENCE          1
            104 CALL                     1
            112 CACHE
            114 STORE_FAST               1 (s)

209         116 LOAD_GLOBAL              7 (NULL + len)
            126 CACHE
            128 LOAD_FAST                1 (s)
            130 UNPACK_SEQUENCE          1
            134 CALL                     1
            142 CACHE
            144 LOAD_CONST               2 (8)
            146 BINARY_OP                6 (%)
            150 POP_JUMP_IF_FALSE       20 (to 192)

210         152 LOAD_GLOBAL              9 (NULL + binascii)
            162 CACHE
            164 LOAD_ATTR                5 (NULL|self + _bytes_from_decode_data)
            184 CACHE
            186 CACHE
            188 CACHE
            190 RAISE_VARARGS            1

214     >>  192 LOAD_FAST                3 (map01)
            194 POP_JUMP_IF_NONE       106 (to 408)

215         196 LOAD_GLOBAL              5 (NULL + _bytes_from_decode_data)
            206 CACHE
            208 LOAD_FAST                3 (map01)
            210 UNPACK_SEQUENCE          1
            214 CALL                     1
            222 CACHE
            224 STORE_FAST               3 (map01)

216         226 LOAD_GLOBAL              7 (NULL + len)
            236 CACHE
            238 LOAD_FAST                3 (map01)
            240 UNPACK_SEQUENCE          1
            244 CALL                     1
            252 CACHE
            254 LOAD_CONST               4 (1)
            256 COMPARE_OP               2 (<)
            260 CACHE
            262 POP_JUMP_IF_TRUE        23 (to 310)
            264 LOAD_ASSERTION_ERROR
            266 LOAD_GLOBAL             13 (NULL + repr)
            276 CACHE
            278 LOAD_FAST                3 (map01)
            280 UNPACK_SEQUENCE          1
            284 CALL                     1
            292 CACHE
            294 UNPACK_SEQUENCE          0
            298 CALL                     0
            306 CACHE
            308 RAISE_VARARGS            1

217     >>  310 LOAD_FAST                1 (s)
            312 STORE_SUBSCR
            316 CACHE
            318 CACHE
            320 CACHE
            322 CACHE
            324 CACHE
            326 CACHE
            328 CACHE
            330 CACHE
            332 CACHE
            334 LOAD_GLOBAL             16 (bytes)
            344 CACHE
            346 STORE_SUBSCR
            350 CACHE
            352 CACHE
            354 CACHE
            356 CACHE
            358 CACHE
            360 CACHE
            362 CACHE
            364 CACHE
            366 CACHE
            368 LOAD_CONST               5 (b'01')
            370 LOAD_CONST               6 (b'O')
            372 LOAD_FAST                3 (map01)
            374 BINARY_OP                0 (+)
            378 UNPACK_SEQUENCE          2
            382 CALL                     2
            390 CACHE
            392 UNPACK_SEQUENCE          1
            396 CALL                     1
            404 CACHE
            406 STORE_FAST               1 (s)

218     >>  408 LOAD_FAST                2 (casefold)
            410 POP_JUMP_IF_FALSE       20 (to 452)

219         412 LOAD_FAST                1 (s)
            414 STORE_SUBSCR
            418 CACHE
            420 CACHE
            422 CACHE
            424 CACHE
            426 CACHE
            428 CACHE
            430 CACHE
            432 CACHE
            434 CACHE
            436 UNPACK_SEQUENCE          0
            440 CALL                     0
            448 CACHE
            450 STORE_FAST               1 (s)

223     >>  452 LOAD_GLOBAL              7 (NULL + len)
            462 CACHE
            464 LOAD_FAST                1 (s)
            466 UNPACK_SEQUENCE          1
            470 CALL                     1
            478 CACHE
            480 STORE_FAST               4 (l)

224         482 LOAD_FAST                1 (s)
            484 STORE_SUBSCR
            488 CACHE
            490 CACHE
            492 CACHE
            494 CACHE
            496 CACHE
            498 CACHE
            500 CACHE
            502 CACHE
            504 CACHE
            506 LOAD_CONST               7 (b'=')
            508 UNPACK_SEQUENCE          1
            512 CALL                     1
            520 CACHE
            522 STORE_FAST               1 (s)

225         524 LOAD_FAST                4 (l)
            526 LOAD_GLOBAL              7 (NULL + len)
            536 CACHE
            538 LOAD_FAST                1 (s)
            540 UNPACK_SEQUENCE          1
            544 CALL                     1
            552 CACHE
            554 BINARY_OP               10 (-)
            558 STORE_FAST               5 (padchars)

227         560 LOAD_GLOBAL             25 (NULL + bytearray)
            570 CACHE
            572 UNPACK_SEQUENCE          0
            576 CALL                     0
            584 CACHE
            586 STORE_FAST               6 (decoded)

228         588 LOAD_GLOBAL              0 (_b32rev)
            598 CACHE
            600 LOAD_FAST                0 (alphabet)
            602 BINARY_SUBSCR
            606 CACHE
            608 CACHE
            610 CACHE
            612 STORE_FAST               7 (b32rev)

229         614 LOAD_GLOBAL             27 (NULL + range)
            624 CACHE
            626 LOAD_CONST               8 (0)
            628 LOAD_GLOBAL              7 (NULL + len)
            638 CACHE
            640 LOAD_FAST                1 (s)
            642 UNPACK_SEQUENCE          1
            646 CALL                     1
            654 CACHE
            656 LOAD_CONST               2 (8)
            658 UNPACK_SEQUENCE          3
            662 CALL                     3
            670 CACHE
            672 GET_ITER
        >>  674 FOR_ITER                97 (to 872)

230         678 LOAD_FAST                1 (s)
            680 LOAD_FAST                8 (i)
            682 LOAD_FAST                8 (i)
            684 LOAD_CONST               2 (8)
            686 BINARY_OP                0 (+)
            690 BUILD_SLICE              2
            692 BINARY_SUBSCR
            696 CACHE
            698 CACHE
            700 CACHE
            702 STORE_FAST               9 (quanta)

231         704 LOAD_CONST               8 (0)
            706 STORE_FAST              10 (acc)

232         708 NOP

233         710 LOAD_FAST                9 (quanta)
            712 GET_ITER
        >>  714 FOR_ITER                16 (to 750)

234         718 LOAD_FAST               10 (acc)
            720 LOAD_CONST               9 (5)
            722 BINARY_OP                3 (<<)
            726 LOAD_FAST                7 (b32rev)
            728 LOAD_FAST               11 (c)
            730 BINARY_SUBSCR
            734 CACHE
            736 CACHE
            738 CACHE
            740 BINARY_OP                0 (+)
            744 STORE_FAST              10 (acc)
            746 JUMP_BACKWARD           17 (to 714)

233         748 JUMP_FORWARD            35 (to 820)
        >>  750 PUSH_EXC_INFO

235         752 LOAD_GLOBAL             28 (KeyError)
            762 CACHE
            764 CHECK_EXC_MATCH
            766 POP_JUMP_IF_FALSE       22 (to 812)
            768 POP_TOP

236         770 LOAD_GLOBAL              9 (NULL + binascii)
            780 CACHE
            782 LOAD_ATTR                5 (NULL|self + _bytes_from_decode_data)
            802 CACHE
            804 CACHE
            806 CACHE
            808 LOAD_CONST               0 (None)
            810 RAISE_VARARGS            2

235     >>  812 RERAISE                  0
        >>  814 COPY                     3
            816 POP_EXCEPT
            818 RERAISE                  1

237     >>  820 LOAD_FAST                6 (decoded)
            822 LOAD_FAST               10 (acc)
            824 STORE_SUBSCR
            828 CACHE
            830 CACHE
            832 CACHE
            834 CACHE
            836 CACHE
            838 CACHE
            840 CACHE
            842 CACHE
            844 CACHE
            846 LOAD_CONST               9 (5)
            848 UNPACK_SEQUENCE          1
            852 CALL                     1
            860 CACHE
            862 BINARY_OP               13 (+=)
            866 STORE_FAST               6 (decoded)
            868 JUMP_BACKWARD           98 (to 674)

239         870 LOAD_FAST                4 (l)
        >>  872 LOAD_CONST               2 (8)
            874 BINARY_OP                6 (%)
            878 POP_JUMP_IF_TRUE         4 (to 888)
            880 LOAD_FAST                5 (padchars)
            882 LOAD_CONST              11 (frozenset({0, 1, 3, 4, 6}))
            884 CONTAINS_OP              1
            886 POP_JUMP_IF_FALSE       20 (to 928)

240     >>  888 LOAD_GLOBAL              9 (NULL + binascii)
            898 CACHE
            900 LOAD_ATTR                5 (NULL|self + _bytes_from_decode_data)
            920 CACHE
            922 CACHE
            924 CACHE
            926 RAISE_VARARGS            1

241     >>  928 LOAD_FAST                5 (padchars)
            930 POP_JUMP_IF_FALSE       57 (to 1046)
            932 LOAD_FAST                6 (decoded)
            934 POP_JUMP_IF_FALSE       55 (to 1046)

242         936 LOAD_FAST               10 (acc)
            938 LOAD_CONST               9 (5)
            940 LOAD_FAST                5 (padchars)
            942 BINARY_OP                5 (*)
            946 BINARY_OP               16 (<<=)
            950 STORE_FAST              10 (acc)

243         952 LOAD_FAST               10 (acc)
            954 STORE_SUBSCR
            958 CACHE
            960 CACHE
            962 CACHE
            964 CACHE
            966 CACHE
            968 CACHE
            970 CACHE
            972 CACHE
            974 CACHE
            976 LOAD_CONST               9 (5)
            978 UNPACK_SEQUENCE          1
            982 CALL                     1
            990 CACHE
            992 STORE_FAST              12 (last)

244         994 LOAD_CONST              12 (43)
            996 LOAD_CONST               9 (5)
            998 LOAD_FAST                5 (padchars)
           1000 BINARY_OP                5 (*)
           1004 BINARY_OP               10 (-)
           1008 LOAD_CONST               2 (8)
           1010 BINARY_OP                2 (//)
           1014 STORE_FAST              13 (leftover)

245        1016 LOAD_FAST               12 (last)
           1018 LOAD_CONST               0 (None)
           1020 LOAD_FAST               13 (leftover)
           1022 BUILD_SLICE              2
           1024 BINARY_SUBSCR
           1028 CACHE
           1030 CACHE
           1032 CACHE
           1034 LOAD_FAST                6 (decoded)
           1036 LOAD_CONST              13 (-5)
           1038 LOAD_CONST               0 (None)
           1040 BUILD_SLICE              2
           1042 STORE_SUBSCR

246     >> 1046 LOAD_GLOBAL             17 (NULL + bytes)
           1056 CACHE
           1058 LOAD_FAST                6 (decoded)
           1060 UNPACK_SEQUENCE          1
           1064 CALL                     1
           1072 CACHE
           1074 RETURN_VALUE
ExceptionTable:
  710 to 746 -> 750 [1]
  750 to 812 -> 814 [2] lasti

Disassembly of <code object <dictcomp> at 0x000001E77ECD0650, file "base64.py", line 207>:
207           0 RESUME                   0
              2 BUILD_MAP                0
              4 LOAD_FAST                0 (.0)
        >>    6 FOR_ITER                 8 (to 26)
             10 CACHE
             12 STORE_FAST               1 (k)
             14 STORE_FAST               2 (v)
             16 LOAD_FAST                2 (v)
             18 LOAD_FAST                1 (k)
             20 MAP_ADD                  2
             22 JUMP_BACKWARD            9 (to 6)
             24 RETURN_VALUE

Disassembly of <code object b32encode at 0x000001E77EC92970, file "base64.py", line 249>:
249           0 RESUME                   0

250           2 LOAD_GLOBAL              1 (NULL + _b32encode)
             12 CACHE
             14 LOAD_GLOBAL              2 (_b32alphabet)
             24 CACHE
             26 LOAD_FAST                0 (s)
             28 UNPACK_SEQUENCE          2
             32 CALL                     2
             40 CACHE
             42 RETURN_VALUE

Disassembly of <code object b32decode at 0x000001E77EC92D30, file "base64.py", line 253>:
253           0 RESUME                   0

254           2 LOAD_GLOBAL              1 (NULL + _b32decode)
             12 CACHE
             14 LOAD_GLOBAL              2 (_b32alphabet)
             24 CACHE
             26 LOAD_FAST                0 (s)
             28 LOAD_FAST                1 (casefold)
             30 LOAD_FAST                2 (map01)
             32 UNPACK_SEQUENCE          4
             36 CALL                     4
             44 CACHE
             46 RETURN_VALUE

Disassembly of <code object b32hexencode at 0x000001E77EC93780, file "base64.py", line 258>:
258           0 RESUME                   0

259           2 LOAD_GLOBAL              1 (NULL + _b32encode)
             12 CACHE
             14 LOAD_GLOBAL              2 (_b32hexalphabet)
             24 CACHE
             26 LOAD_FAST                0 (s)
             28 UNPACK_SEQUENCE          2
             32 CALL                     2
             40 CACHE
             42 RETURN_VALUE

Disassembly of <code object b32hexdecode at 0x000001E77EC93870, file "base64.py", line 262>:
262           0 RESUME                   0

264           2 LOAD_GLOBAL              1 (NULL + _b32decode)
             12 CACHE
             14 LOAD_GLOBAL              2 (_b32hexalphabet)
             24 CACHE
             26 LOAD_FAST                0 (s)
             28 LOAD_FAST                1 (casefold)
             30 UNPACK_SEQUENCE          3
             34 CALL                     3
             42 CACHE
             44 RETURN_VALUE

Disassembly of <code object b16encode at 0x000001E77EC47CC0, file "base64.py", line 272>:
272           0 RESUME                   0

275           2 LOAD_GLOBAL              1 (NULL + binascii)
             12 CACHE
             14 LOAD_ATTR                1 (NULL|self + binascii)
             34 CACHE
             36 CACHE
             38 CACHE
             40 STORE_SUBSCR
             44 CACHE
             46 CACHE
             48 CACHE
             50 CACHE
             52 CACHE
             54 CACHE
             56 CACHE
             58 CACHE
             60 CACHE
             62 UNPACK_SEQUENCE          0
             66 CALL                     0
             74 CACHE
             76 RETURN_VALUE

Disassembly of <code object b16decode at 0x000001E77EC71DE0, file "base64.py", line 278>:
278           0 RESUME                   0

288           2 LOAD_GLOBAL              1 (NULL + _bytes_from_decode_data)
             12 CACHE
             14 LOAD_FAST                0 (s)
             16 UNPACK_SEQUENCE          1
             20 CALL                     1
             28 CACHE
             30 STORE_FAST               0 (s)

289          32 LOAD_FAST                1 (casefold)
             34 POP_JUMP_IF_FALSE       20 (to 76)

290          36 LOAD_FAST                0 (s)
             38 STORE_SUBSCR
             42 CACHE
             44 CACHE
             46 CACHE
             48 CACHE
             50 CACHE
             52 CACHE
             54 CACHE
             56 CACHE
             58 CACHE
             60 UNPACK_SEQUENCE          0
             64 CALL                     0
             72 CACHE
             74 STORE_FAST               0 (s)

291     >>   76 LOAD_GLOBAL              5 (NULL + re)
             86 CACHE
             88 LOAD_ATTR                3 (NULL|self + upper)
            108 CACHE
            110 CACHE
            112 CACHE
            114 CACHE
            116 POP_JUMP_IF_FALSE       20 (to 158)

292         118 LOAD_GLOBAL              9 (NULL + binascii)
            128 CACHE
            130 LOAD_ATTR                5 (NULL|self + re)
            150 CACHE
            152 CACHE
            154 CACHE
            156 RAISE_VARARGS            1

293     >>  158 LOAD_GLOBAL              9 (NULL + binascii)
            168 CACHE
            170 LOAD_ATTR                6 (search)
            190 CACHE
            192 CACHE
            194 CACHE
            196 RETURN_VALUE

Disassembly of <code object _85encode at 0x000001E77EF1E7F0, file "base64.py", line 304>:
              0 MAKE_CELL                1 (chars)
              2 MAKE_CELL                2 (chars2)
              4 MAKE_CELL                4 (foldnuls)
              6 MAKE_CELL                5 (foldspaces)

304           8 RESUME                   0

306          10 LOAD_GLOBAL              1 (NULL + isinstance)
             20 CACHE
             22 LOAD_FAST                0 (b)
             24 LOAD_GLOBAL              2 (bytes_types)
             34 CACHE
             36 UNPACK_SEQUENCE          2
             40 CALL                     2
             48 CACHE
             50 POP_JUMP_IF_TRUE        33 (to 118)

307          52 LOAD_GLOBAL              5 (NULL + memoryview)
             62 CACHE
             64 LOAD_FAST                0 (b)
             66 UNPACK_SEQUENCE          1
             70 CALL                     1
             78 CACHE
             80 STORE_SUBSCR
             84 CACHE
             86 CACHE
             88 CACHE
             90 CACHE
             92 CACHE
             94 CACHE
             96 CACHE
             98 CACHE
            100 CACHE
            102 UNPACK_SEQUENCE          0
            106 CALL                     0
            114 CACHE
            116 STORE_FAST               0 (b)

309     >>  118 LOAD_GLOBAL              9 (NULL + len)
            128 CACHE
            130 LOAD_FAST                0 (b)
            132 UNPACK_SEQUENCE          1
            136 CALL                     1
            144 CACHE
            146 UNARY_NEGATIVE
            148 LOAD_CONST               1 (4)
            150 BINARY_OP                6 (%)
            154 STORE_FAST               6 (padding)

310         156 LOAD_FAST                6 (padding)
            158 POP_JUMP_IF_FALSE        8 (to 176)

311         160 LOAD_FAST                0 (b)
            162 LOAD_CONST               2 (b'\x00')
            164 LOAD_FAST                6 (padding)
            166 BINARY_OP                5 (*)
            170 BINARY_OP                0 (+)
            174 STORE_FAST               0 (b)

312     >>  176 LOAD_GLOBAL             11 (NULL + struct)
            186 CACHE
            188 LOAD_ATTR                6 (tobytes)
            208 CACHE
            210 CACHE
            212 LOAD_FAST                0 (b)
            214 UNPACK_SEQUENCE          1
            218 CALL                     1
            226 CACHE
            228 LOAD_CONST               1 (4)
            230 BINARY_OP                2 (//)
            234 BINARY_OP                6 (%)
            238 UNPACK_SEQUENCE          1
            242 CALL                     1
            250 CACHE
            252 STORE_SUBSCR
            256 CACHE
            258 CACHE
            260 CACHE
            262 CACHE
            264 CACHE
            266 CACHE
            268 CACHE
            270 CACHE
            272 CACHE
            274 LOAD_FAST                0 (b)
            276 UNPACK_SEQUENCE          1
            280 CALL                     1
            288 CACHE
            290 STORE_FAST               7 (words)

314         292 LOAD_CLOSURE             1 (chars)
            294 LOAD_CLOSURE             2 (chars2)
            296 LOAD_CLOSURE             4 (foldnuls)
            298 LOAD_CLOSURE             5 (foldspaces)
            300 BUILD_TUPLE              4
            302 LOAD_CONST               4 (<code object <listcomp> at 0x000001E77EC3BEB0, file "base64.py", line 314>)
            304 MAKE_FUNCTION            8 (closure)

319         306 LOAD_FAST                7 (words)

314         308 GET_ITER
            310 UNPACK_SEQUENCE          0
            314 CALL                     0
            322 CACHE
            324 STORE_FAST               8 (chunks)

321         326 LOAD_FAST                6 (padding)
            328 POP_JUMP_IF_FALSE       48 (to 426)
            330 LOAD_FAST                3 (pad)
            332 POP_JUMP_IF_TRUE        46 (to 426)

322         334 LOAD_FAST                8 (chunks)
            336 LOAD_CONST               5 (-1)
            338 BINARY_SUBSCR
            342 CACHE
            344 CACHE
            346 CACHE
            348 LOAD_CONST               6 (b'z')
            350 COMPARE_OP               2 (<)
            354 CACHE
            356 POP_JUMP_IF_FALSE       14 (to 386)

323         358 LOAD_DEREF               1 (chars)
            360 LOAD_CONST               7 (0)
            362 BINARY_SUBSCR
            366 CACHE
            368 CACHE
            370 CACHE
            372 LOAD_CONST               8 (5)
            374 BINARY_OP                5 (*)
            378 LOAD_FAST                8 (chunks)
            380 LOAD_CONST               5 (-1)
            382 STORE_SUBSCR

324     >>  386 LOAD_FAST                8 (chunks)
            388 LOAD_CONST               5 (-1)
            390 BINARY_SUBSCR
            394 CACHE
            396 CACHE
            398 CACHE
            400 LOAD_CONST               0 (None)
            402 LOAD_FAST                6 (padding)
            404 UNARY_NEGATIVE
            406 BUILD_SLICE              2
            408 BINARY_SUBSCR
            412 CACHE
            414 CACHE
            416 CACHE
            418 LOAD_FAST                8 (chunks)
            420 LOAD_CONST               5 (-1)
            422 STORE_SUBSCR

326     >>  426 LOAD_CONST               9 (b'')
            428 STORE_SUBSCR
            432 CACHE
            434 CACHE
            436 CACHE
            438 CACHE
            440 CACHE
            442 CACHE
            444 CACHE
            446 CACHE
            448 CACHE
            450 LOAD_FAST                8 (chunks)
            452 UNPACK_SEQUENCE          1
            456 CALL                     1
            464 CACHE
            466 RETURN_VALUE

Disassembly of <code object <listcomp> at 0x000001E77EC3BEB0, file "base64.py", line 314>:
              0 COPY_FREE_VARS           4

314           2 RESUME                   0
              4 BUILD_LIST               0
              6 LOAD_FAST                0 (.0)
        >>    8 FOR_ITER                56 (to 124)

314          12 LOAD_DEREF               4 (foldnuls)
             14 POP_JUMP_IF_FALSE        4 (to 24)
             16 LOAD_FAST                1 (word)
             18 POP_JUMP_IF_TRUE         2 (to 24)
             20 LOAD_CONST               0 (b'z')
             22 JUMP_FORWARD            47 (to 118)

315     >>   24 LOAD_DEREF               5 (foldspaces)
             26 POP_JUMP_IF_FALSE        8 (to 44)
             28 LOAD_FAST                1 (word)
             30 LOAD_CONST               1 (538976288)
             32 COMPARE_OP               2 (<)
             36 CACHE
             38 POP_JUMP_IF_FALSE        2 (to 44)
             40 LOAD_CONST               2 (b'y')
             42 JUMP_FORWARD            37 (to 118)

316     >>   44 LOAD_DEREF               3 (chars2)
             46 LOAD_FAST                1 (word)
             48 LOAD_CONST               3 (614125)
             50 BINARY_OP                2 (//)
             54 BINARY_SUBSCR
             58 CACHE
             60 CACHE
             62 CACHE

317          64 LOAD_DEREF               3 (chars2)
             66 LOAD_FAST                1 (word)
             68 LOAD_CONST               4 (85)
             70 BINARY_OP                2 (//)
             74 LOAD_CONST               5 (7225)
             76 BINARY_OP                6 (%)
             80 BINARY_SUBSCR
             84 CACHE
             86 CACHE
             88 CACHE

316          90 BINARY_OP                0 (+)

318          94 LOAD_DEREF               2 (chars)
             96 LOAD_FAST                1 (word)
             98 LOAD_CONST               4 (85)
            100 BINARY_OP                6 (%)
            104 BINARY_SUBSCR
            108 CACHE
            110 CACHE
            112 CACHE

316         114 BINARY_OP                0 (+)

314     >>  118 LIST_APPEND              2
            120 JUMP_BACKWARD           57 (to 8)
            122 RETURN_VALUE

Disassembly of <code object a85encode at 0x000001E77EF1D820, file "base64.py", line 328>:
              0 MAKE_CELL                2 (wrapcol)
              2 MAKE_CELL                6 (result)

328           4 RESUME                   0

348           6 LOAD_GLOBAL              0 (_a85chars2)
             16 CACHE
             18 POP_JUMP_IF_NOT_NONE    43 (to 106)

349          20 LOAD_CONST               2 (<code object <listcomp> at 0x000001E77EC93A50, file "base64.py", line 349>)
             22 MAKE_FUNCTION            0
             24 LOAD_GLOBAL              3 (NULL + range)
             34 CACHE
             36 LOAD_CONST               3 (33)
             38 LOAD_CONST               4 (118)
             40 UNPACK_SEQUENCE          2
             44 CALL                     2
             52 CACHE
             54 GET_ITER
             56 UNPACK_SEQUENCE          0
             60 CALL                     0
             68 CACHE
             70 STORE_GLOBAL             2 (_a85chars)

350          72 LOAD_CONST               5 (<code object <listcomp> at 0x000001E77EC93B40, file "base64.py", line 350>)
             74 MAKE_FUNCTION            0
             76 LOAD_GLOBAL              4 (_a85chars)
             86 CACHE
             88 GET_ITER
             90 UNPACK_SEQUENCE          0
             94 CALL                     0
            102 CACHE
            104 STORE_GLOBAL             0 (_a85chars2)

352     >>  106 LOAD_GLOBAL              7 (NULL + _85encode)
            116 CACHE
            118 LOAD_FAST                0 (b)
            120 LOAD_GLOBAL              4 (_a85chars)
            130 CACHE
            132 LOAD_GLOBAL              0 (_a85chars2)
            142 CACHE
            144 LOAD_FAST                3 (pad)
            146 LOAD_CONST               6 (True)
            148 LOAD_FAST                1 (foldspaces)
            150 UNPACK_SEQUENCE          6
            154 CALL                     6
            162 CACHE
            164 STORE_DEREF              6 (result)

354         166 LOAD_FAST                4 (adobe)
            168 POP_JUMP_IF_FALSE       10 (to 190)

355         170 LOAD_GLOBAL              8 (_A85START)
            180 CACHE
            182 LOAD_DEREF               6 (result)
            184 BINARY_OP                0 (+)
            188 STORE_DEREF              6 (result)

356     >>  190 LOAD_DEREF               2 (wrapcol)
            192 POP_JUMP_IF_FALSE      135 (to 464)

357         194 LOAD_GLOBAL             11 (NULL + max)
            204 CACHE
            206 LOAD_FAST                4 (adobe)
            208 POP_JUMP_IF_FALSE        2 (to 214)
            210 LOAD_CONST               7 (2)
            212 JUMP_FORWARD             1 (to 216)
        >>  214 LOAD_CONST               8 (1)
        >>  216 LOAD_DEREF               2 (wrapcol)
            218 UNPACK_SEQUENCE          2
            222 CALL                     2
            230 CACHE
            232 STORE_DEREF              2 (wrapcol)

358         234 LOAD_CLOSURE             6 (result)
            236 LOAD_CLOSURE             2 (wrapcol)
            238 BUILD_TUPLE              2
            240 LOAD_CONST               9 (<code object <listcomp> at 0x000001E77EC93C30, file "base64.py", line 358>)
            242 MAKE_FUNCTION            8 (closure)

359         244 LOAD_GLOBAL              3 (NULL + range)
            254 CACHE
            256 LOAD_CONST              10 (0)
            258 LOAD_GLOBAL             13 (NULL + len)
            268 CACHE
            270 LOAD_DEREF               6 (result)
            272 UNPACK_SEQUENCE          1
            276 CALL                     1
            284 CACHE
            286 LOAD_DEREF               2 (wrapcol)
            288 UNPACK_SEQUENCE          3
            292 CALL                     3
            300 CACHE

358         302 GET_ITER
            304 UNPACK_SEQUENCE          0
            308 CALL                     0
            316 CACHE
            318 STORE_FAST               5 (chunks)

360         320 LOAD_FAST                4 (adobe)
            322 POP_JUMP_IF_FALSE       49 (to 422)

361         324 LOAD_GLOBAL             13 (NULL + len)
            334 CACHE
            336 LOAD_FAST                5 (chunks)
            338 LOAD_CONST              11 (-1)
            340 BINARY_SUBSCR
            344 CACHE
            346 CACHE
            348 CACHE
            350 UNPACK_SEQUENCE          1
            354 CALL                     1
            362 CACHE
            364 LOAD_CONST               7 (2)
            366 BINARY_OP                0 (+)
            370 LOAD_DEREF               2 (wrapcol)
            372 COMPARE_OP               4 (<)
            376 CACHE
            378 POP_JUMP_IF_FALSE       21 (to 422)

362         380 LOAD_FAST                5 (chunks)
            382 STORE_SUBSCR
            386 CACHE
            388 CACHE
            390 CACHE
            392 CACHE
            394 CACHE
            396 CACHE
            398 CACHE
            400 CACHE
            402 CACHE
            404 LOAD_CONST              12 (b'')
            406 UNPACK_SEQUENCE          1
            410 CALL                     1
            418 CACHE
            420 POP_TOP

363     >>  422 LOAD_CONST              13 (b'\n')
            424 STORE_SUBSCR
            428 CACHE
            430 CACHE
            432 CACHE
            434 CACHE
            436 CACHE
            438 CACHE
            440 CACHE
            442 CACHE
            444 CACHE
            446 LOAD_FAST                5 (chunks)
            448 UNPACK_SEQUENCE          1
            452 CALL                     1
            460 CACHE
            462 STORE_DEREF              6 (result)

364     >>  464 LOAD_FAST                4 (adobe)
            466 POP_JUMP_IF_FALSE       10 (to 488)

365         468 LOAD_DEREF               6 (result)
            470 LOAD_GLOBAL             18 (_A85END)
            480 CACHE
            482 BINARY_OP               13 (+=)
            486 STORE_DEREF              6 (result)

367     >>  488 LOAD_DEREF               6 (result)
            490 RETURN_VALUE

Disassembly of <code object <listcomp> at 0x000001E77EC93A50, file "base64.py", line 349>:
349           0 RESUME                   0
              2 BUILD_LIST               0
              4 LOAD_FAST                0 (.0)
        >>    6 FOR_ITER                18 (to 46)
             10 LOAD_GLOBAL              1 (NULL + bytes)
             20 CACHE
             22 LOAD_FAST                1 (i)
             24 BUILD_TUPLE              1
             26 UNPACK_SEQUENCE          1
             30 CALL                     1
             38 CACHE
             40 LIST_APPEND              2
             42 JUMP_BACKWARD           19 (to 6)
             44 RETURN_VALUE

Disassembly of <code object <listcomp> at 0x000001E77EC93B40, file "base64.py", line 350>:
350           0 RESUME                   0
              2 BUILD_LIST               0
              4 LOAD_FAST                0 (.0)
        >>    6 FOR_ITER                17 (to 44)
             10 LOAD_GLOBAL              0 (_a85chars)
             20 CACHE
             22 GET_ITER
        >>   24 FOR_ITER                 7 (to 42)
             28 LOAD_FAST                1 (a)
             30 LOAD_FAST                2 (b)
             32 BINARY_OP                0 (+)
             36 LIST_APPEND              3
             38 JUMP_BACKWARD            8 (to 24)
             40 JUMP_BACKWARD           18 (to 6)
        >>   42 RETURN_VALUE

Disassembly of <code object <listcomp> at 0x000001E77EC93C30, file "base64.py", line 358>:
              0 COPY_FREE_VARS           2

358           2 RESUME                   0
              4 BUILD_LIST               0
              6 LOAD_FAST                0 (.0)
        >>    8 FOR_ITER                15 (to 42)

358          12 LOAD_DEREF               2 (result)
             14 LOAD_FAST                1 (i)
             16 LOAD_FAST                1 (i)
             18 LOAD_DEREF               3 (wrapcol)
             20 BINARY_OP                0 (+)
             24 BUILD_SLICE              2
             26 BINARY_SUBSCR
             30 CACHE
             32 CACHE
             34 CACHE
             36 LIST_APPEND              2
             38 JUMP_BACKWARD           16 (to 8)
             40 RETURN_VALUE

Disassembly of <code object a85decode at 0x000001E77E9D8DC0, file "base64.py", line 369>:
369           0 RESUME                   0

385           2 LOAD_GLOBAL              1 (NULL + _bytes_from_decode_data)
             12 CACHE
             14 LOAD_FAST                0 (b)
             16 UNPACK_SEQUENCE          1
             20 CALL                     1
             28 CACHE
             30 STORE_FAST               0 (b)

386          32 LOAD_FAST                2 (adobe)
             34 POP_JUMP_IF_FALSE      112 (to 260)

387          36 LOAD_FAST                0 (b)
             38 STORE_SUBSCR
             42 CACHE
             44 CACHE
             46 CACHE
             48 CACHE
             50 CACHE
             52 CACHE
             54 CACHE
             56 CACHE
             58 CACHE
             60 LOAD_GLOBAL              4 (_A85END)
             70 CACHE
             72 UNPACK_SEQUENCE          1
             76 CALL                     1
             84 CACHE
             86 POP_JUMP_IF_TRUE        39 (to 166)

388          88 LOAD_GLOBAL              7 (NULL + ValueError)
             98 CACHE

389         100 LOAD_CONST               1 ('Ascii85 encoded byte sequences must end with {!r}')

390         102 STORE_SUBSCR
            106 CACHE
            108 CACHE
            110 CACHE
            112 CACHE
            114 CACHE
            116 CACHE
            118 CACHE
            120 CACHE
            122 CACHE
            124 LOAD_GLOBAL              4 (_A85END)
            134 CACHE
            136 UNPACK_SEQUENCE          1
            140 CALL                     1
            148 CACHE

388         150 UNPACK_SEQUENCE          1
            154 CALL                     1
            162 CACHE
            164 RAISE_VARARGS            1

392     >>  166 LOAD_FAST                0 (b)
            168 STORE_SUBSCR
            172 CACHE
            174 CACHE
            176 CACHE
            178 CACHE
            180 CACHE
            182 CACHE
            184 CACHE
            186 CACHE
            188 CACHE
            190 LOAD_GLOBAL             12 (_A85START)
            200 CACHE
            202 UNPACK_SEQUENCE          1
            206 CALL                     1
            214 CACHE
            216 POP_JUMP_IF_FALSE       11 (to 240)

393         218 LOAD_FAST                0 (b)
            220 LOAD_CONST               2 (2)
            222 LOAD_CONST               3 (-2)
            224 BUILD_SLICE              2
            226 BINARY_SUBSCR
            230 CACHE
            232 CACHE
            234 CACHE
            236 STORE_FAST               0 (b)
            238 JUMP_FORWARD            10 (to 260)

395     >>  240 LOAD_FAST                0 (b)
            242 LOAD_CONST               4 (None)
            244 LOAD_CONST               3 (-2)
            246 BUILD_SLICE              2
            248 BINARY_SUBSCR
            252 CACHE
            254 CACHE
            256 CACHE
            258 STORE_FAST               0 (b)

400     >>  260 LOAD_GLOBAL             15 (NULL + struct)
            270 CACHE
            272 LOAD_ATTR                8 (format)
            292 CACHE
            294 CACHE
            296 CACHE
            298 LOAD_ATTR                9 (NULL|self + format)
            318 CACHE
            320 CACHE
            322 CACHE
            324 CACHE
            326 STORE_FAST               6 (decoded_append)

403         328 BUILD_LIST               0
            330 STORE_FAST               7 (curr)

404         332 LOAD_FAST                7 (curr)
            334 LOAD_ATTR               10 (startswith)
            354 CACHE
            356 CACHE
            358 STORE_FAST               9 (curr_clear)

406         360 LOAD_FAST                0 (b)
            362 LOAD_CONST               6 (b'uuuu')
            364 BINARY_OP                0 (+)
            368 GET_ITER
        >>  370 FOR_ITER               228 (to 830)

407         374 LOAD_CONST               7 (33)
            376 LOAD_FAST               10 (x)
            378 SWAP                     2
            380 COPY                     2
            382 COMPARE_OP               1 (<)
            386 CACHE
            388 POP_JUMP_IF_FALSE        6 (to 402)
            390 LOAD_CONST               8 (117)
            392 COMPARE_OP               1 (<)
            396 CACHE
            398 POP_JUMP_IF_FALSE      119 (to 638)
            400 JUMP_FORWARD             2 (to 406)
        >>  402 POP_TOP
            404 JUMP_FORWARD           116 (to 638)

408     >>  406 PUSH_NULL
            408 LOAD_FAST                8 (curr_append)
            410 LOAD_FAST               10 (x)
            412 UNPACK_SEQUENCE          1
            416 CALL                     1
            424 CACHE
            426 POP_TOP

409         428 LOAD_GLOBAL             25 (NULL + len)
            438 CACHE
            440 LOAD_FAST                7 (curr)
            442 UNPACK_SEQUENCE          1
            446 CALL                     1
            454 CACHE
            456 LOAD_CONST               9 (5)
            458 COMPARE_OP               2 (<)
            462 CACHE
            464 POP_JUMP_IF_FALSE       85 (to 636)

410         466 LOAD_CONST              10 (0)
            468 STORE_FAST              11 (acc)

411         470 LOAD_FAST                7 (curr)
            472 GET_ITER
        >>  474 FOR_ITER                13 (to 504)

412         478 LOAD_CONST              11 (85)
            480 LOAD_FAST               11 (acc)
            482 BINARY_OP                5 (*)
            486 LOAD_FAST               10 (x)
            488 LOAD_CONST               7 (33)
            490 BINARY_OP               10 (-)
            494 BINARY_OP                0 (+)
            498 STORE_FAST              11 (acc)
            500 JUMP_BACKWARD           14 (to 474)

413         502 NOP

414     >>  504 PUSH_NULL
            506 LOAD_FAST                6 (decoded_append)
            508 PUSH_NULL
            510 LOAD_FAST                4 (packI)
            512 LOAD_FAST               11 (acc)
            514 UNPACK_SEQUENCE          1
            518 CALL                     1
            526 CACHE
            528 UNPACK_SEQUENCE          1
            532 CALL                     1
            540 CACHE
            542 POP_TOP
            544 JUMP_FORWARD            35 (to 616)
        >>  546 PUSH_EXC_INFO

415         548 LOAD_GLOBAL             14 (struct)
            558 CACHE
            560 LOAD_ATTR               13 (NULL|self + _A85START)
            580 CACHE
            582 CACHE
            584 CACHE
            586 CACHE
            588 LOAD_CONST              12 ('Ascii85 overflow')
            590 UNPACK_SEQUENCE          1
            594 CALL                     1
            602 CACHE
            604 LOAD_CONST               4 (None)
            606 RAISE_VARARGS            2

415         608 RERAISE                  0
        >>  610 COPY                     3
            612 POP_EXCEPT
            614 RERAISE                  1

417     >>  616 PUSH_NULL
            618 LOAD_FAST                9 (curr_clear)
            620 UNPACK_SEQUENCE          0
            624 CALL                     0
            632 CACHE
            634 POP_TOP
        >>  636 JUMP_BACKWARD          134 (to 370)

418     >>  638 LOAD_FAST               10 (x)
            640 LOAD_CONST              13 (122)
            642 COMPARE_OP               2 (<)
            646 CACHE
            648 POP_JUMP_IF_FALSE       29 (to 708)

419         650 LOAD_FAST                7 (curr)
            652 POP_JUMP_IF_FALSE       15 (to 684)

420         654 LOAD_GLOBAL              7 (NULL + ValueError)
            664 CACHE
            666 LOAD_CONST              14 ('z inside Ascii85 5-tuple')
            668 UNPACK_SEQUENCE          1
            672 CALL                     1
            680 CACHE
            682 RAISE_VARARGS            1

421     >>  684 PUSH_NULL
            686 LOAD_FAST                6 (decoded_append)
            688 LOAD_CONST              15 (b'\x00\x00\x00\x00')
            690 UNPACK_SEQUENCE          1
            694 CALL                     1
            702 CACHE
            704 POP_TOP
            706 JUMP_BACKWARD          169 (to 370)

422     >>  708 LOAD_FAST                1 (foldspaces)
            710 POP_JUMP_IF_FALSE       35 (to 782)
            712 LOAD_FAST               10 (x)
            714 LOAD_CONST              16 (121)
            716 COMPARE_OP               2 (<)
            720 CACHE
            722 POP_JUMP_IF_FALSE       29 (to 782)

423         724 LOAD_FAST                7 (curr)
            726 POP_JUMP_IF_FALSE       15 (to 758)

424         728 LOAD_GLOBAL              7 (NULL + ValueError)
            738 CACHE
            740 LOAD_CONST              17 ('y inside Ascii85 5-tuple')
            742 UNPACK_SEQUENCE          1
            746 CALL                     1
            754 CACHE
            756 RAISE_VARARGS            1

425     >>  758 PUSH_NULL
            760 LOAD_FAST                6 (decoded_append)
            762 LOAD_CONST              18 (b'    ')
            764 UNPACK_SEQUENCE          1
            768 CALL                     1
            776 CACHE
            778 POP_TOP
            780 JUMP_BACKWARD          206 (to 370)

426     >>  782 LOAD_FAST               10 (x)
            784 LOAD_FAST                3 (ignorechars)
            786 CONTAINS_OP              0
            788 POP_JUMP_IF_FALSE        1 (to 792)

428         790 JUMP_BACKWARD          211 (to 370)

430     >>  792 LOAD_GLOBAL              7 (NULL + ValueError)
            802 CACHE
            804 LOAD_CONST              19 ('Non-Ascii85 digit found: %c')
            806 LOAD_FAST               10 (x)
            808 BINARY_OP                6 (%)
            812 UNPACK_SEQUENCE          1
            816 CALL                     1
            824 CACHE
            826 RAISE_VARARGS            1

432         828 LOAD_CONST              20 (b'')
        >>  830 STORE_SUBSCR
            834 CACHE
            836 CACHE
            838 CACHE
            840 CACHE
            842 CACHE
            844 CACHE
            846 CACHE
            848 CACHE
            850 CACHE
            852 LOAD_FAST                5 (decoded)
            854 UNPACK_SEQUENCE          1
            858 CALL                     1
            866 CACHE
            868 STORE_FAST              12 (result)

433         870 LOAD_CONST              21 (4)
            872 LOAD_GLOBAL             25 (NULL + len)
            882 CACHE
            884 LOAD_FAST                7 (curr)
            886 UNPACK_SEQUENCE          1
            890 CALL                     1
            898 CACHE
            900 BINARY_OP               10 (-)
            904 STORE_FAST              13 (padding)

434         906 LOAD_FAST               13 (padding)
            908 POP_JUMP_IF_FALSE       11 (to 932)

436         910 LOAD_FAST               12 (result)
            912 LOAD_CONST               4 (None)
            914 LOAD_FAST               13 (padding)
            916 UNARY_NEGATIVE
            918 BUILD_SLICE              2
            920 BINARY_SUBSCR
            924 CACHE
            926 CACHE
            928 CACHE
            930 STORE_FAST              12 (result)

437     >>  932 LOAD_FAST               12 (result)
            934 RETURN_VALUE
ExceptionTable:
  504 to 542 -> 546 [1]
  546 to 608 -> 610 [2] lasti

Disassembly of <code object b85encode at 0x000001E77EC4E4F0, file "base64.py", line 447>:
447           0 RESUME                   0

456           2 LOAD_GLOBAL              0 (_b85chars2)
             12 CACHE
             14 POP_JUMP_IF_NOT_NONE    34 (to 84)

457          16 LOAD_CONST               2 (<code object <listcomp> at 0x000001E77EC93E10, file "base64.py", line 457>)
             18 MAKE_FUNCTION            0
             20 LOAD_GLOBAL              2 (_b85alphabet)
             30 CACHE
             32 GET_ITER
             34 UNPACK_SEQUENCE          0
             38 CALL                     0
             46 CACHE
             48 STORE_GLOBAL             2 (_b85chars)

458          50 LOAD_CONST               3 (<code object <listcomp> at 0x000001E77EC93F00, file "base64.py", line 458>)
             52 MAKE_FUNCTION            0
             54 LOAD_GLOBAL              4 (_b85chars)
             64 CACHE
             66 GET_ITER
             68 UNPACK_SEQUENCE          0
             72 CALL                     0
             80 CACHE
             82 STORE_GLOBAL             0 (_b85chars2)

459     >>   84 LOAD_GLOBAL              7 (NULL + _85encode)
             94 CACHE
             96 LOAD_FAST                0 (b)
             98 LOAD_GLOBAL              4 (_b85chars)
            108 CACHE
            110 LOAD_GLOBAL              0 (_b85chars2)
            120 CACHE
            122 LOAD_FAST                1 (pad)
            124 UNPACK_SEQUENCE          4
            128 CALL                     4
            136 CACHE
            138 RETURN_VALUE

Disassembly of <code object <listcomp> at 0x000001E77EC93E10, file "base64.py", line 457>:
457           0 RESUME                   0
              2 BUILD_LIST               0
              4 LOAD_FAST                0 (.0)
        >>    6 FOR_ITER                18 (to 46)
             10 LOAD_GLOBAL              1 (NULL + bytes)
             20 CACHE
             22 LOAD_FAST                1 (i)
             24 BUILD_TUPLE              1
             26 UNPACK_SEQUENCE          1
             30 CALL                     1
             38 CACHE
             40 LIST_APPEND              2
             42 JUMP_BACKWARD           19 (to 6)
             44 RETURN_VALUE

Disassembly of <code object <listcomp> at 0x000001E77EC93F00, file "base64.py", line 458>:
458           0 RESUME                   0
              2 BUILD_LIST               0
              4 LOAD_FAST                0 (.0)
        >>    6 FOR_ITER                17 (to 44)
             10 LOAD_GLOBAL              0 (_b85chars)
             20 CACHE
             22 GET_ITER
        >>   24 FOR_ITER                 7 (to 42)
             28 LOAD_FAST                1 (a)
             30 LOAD_FAST                2 (b)
             32 BINARY_OP                0 (+)
             36 LIST_APPEND              3
             38 JUMP_BACKWARD            8 (to 24)
             40 JUMP_BACKWARD           18 (to 6)
        >>   42 RETURN_VALUE

Disassembly of <code object b85decode at 0x000001E77EF21E00, file "base64.py", line 461>:
461           0 RESUME                   0

469           2 LOAD_GLOBAL              0 (_b85dec)
             12 CACHE
             14 POP_JUMP_IF_NOT_NONE    42 (to 100)

470          16 LOAD_CONST               1 (None)
             18 BUILD_LIST               1
             20 LOAD_CONST               2 (256)
             22 BINARY_OP                5 (*)
             26 STORE_GLOBAL             0 (_b85dec)

471          28 LOAD_GLOBAL              3 (NULL + enumerate)
             38 CACHE
             40 LOAD_GLOBAL              4 (_b85alphabet)
             50 CACHE
             52 UNPACK_SEQUENCE          1
             56 CALL                     1
             64 CACHE
             66 GET_ITER
        >>   68 FOR_ITER                15 (to 102)
             72 CACHE
             74 STORE_FAST               1 (i)
             76 STORE_FAST               2 (c)

472          78 LOAD_FAST                1 (i)
             80 LOAD_GLOBAL              0 (_b85dec)
             90 CACHE
             92 LOAD_FAST                2 (c)
             94 STORE_SUBSCR
             98 JUMP_BACKWARD           16 (to 68)

474     >>  100 LOAD_GLOBAL              7 (NULL + _bytes_from_decode_data)
            110 CACHE
            112 LOAD_FAST                0 (b)
            114 UNPACK_SEQUENCE          1
            118 CALL                     1
            126 CACHE
            128 STORE_FAST               0 (b)

475         130 LOAD_GLOBAL              9 (NULL + len)
            140 CACHE
            142 LOAD_FAST                0 (b)
            144 UNPACK_SEQUENCE          1
            148 CALL                     1
            156 CACHE
            158 UNARY_NEGATIVE
            160 LOAD_CONST               3 (5)
            162 BINARY_OP                6 (%)
            166 STORE_FAST               3 (padding)

476         168 LOAD_FAST                0 (b)
            170 LOAD_CONST               4 (b'~')
            172 LOAD_FAST                3 (padding)
            174 BINARY_OP                5 (*)
            178 BINARY_OP                0 (+)
            182 STORE_FAST               0 (b)

477         184 BUILD_LIST               0
            186 STORE_FAST               4 (out)

478         188 LOAD_GLOBAL             11 (NULL + struct)
            198 CACHE
            200 LOAD_ATTR                6 (_bytes_from_decode_data)
            220 CACHE
            222 CACHE
            224 CACHE
            226 LOAD_ATTR                7 (NULL|self + _bytes_from_decode_data)
            246 CACHE
            248 CACHE
            250 LOAD_CONST               6 (0)
            252 LOAD_GLOBAL              9 (NULL + len)
            262 CACHE
            264 LOAD_FAST                0 (b)
            266 UNPACK_SEQUENCE          1
            270 CALL                     1
            278 CACHE
            280 LOAD_CONST               3 (5)
            282 UNPACK_SEQUENCE          3
            286 CALL                     3
            294 CACHE
            296 GET_ITER
        >>  298 FOR_ITER               183 (to 668)

480         302 LOAD_FAST                0 (b)
            304 LOAD_FAST                1 (i)
            306 LOAD_FAST                1 (i)
            308 LOAD_CONST               3 (5)
            310 BINARY_OP                0 (+)
            314 BUILD_SLICE              2
            316 BINARY_SUBSCR
            320 CACHE
            322 CACHE
            324 CACHE
            326 STORE_FAST               6 (chunk)

481         328 LOAD_CONST               6 (0)
            330 STORE_FAST               7 (acc)

482         332 NOP

483         334 LOAD_FAST                6 (chunk)
            336 GET_ITER
        >>  338 FOR_ITER                21 (to 384)

484         342 LOAD_FAST                7 (acc)
            344 LOAD_CONST               7 (85)
            346 BINARY_OP                5 (*)
            350 LOAD_GLOBAL              0 (_b85dec)
            360 CACHE
            362 LOAD_FAST                2 (c)
            364 BINARY_SUBSCR
            368 CACHE
            370 CACHE
            372 CACHE
            374 BINARY_OP                0 (+)
            378 STORE_FAST               7 (acc)
            380 JUMP_BACKWARD           22 (to 338)

483         382 JUMP_FORWARD            71 (to 526)
        >>  384 PUSH_EXC_INFO

485         386 LOAD_GLOBAL             18 (TypeError)
            396 CACHE
            398 CHECK_EXC_MATCH
            400 POP_JUMP_IF_FALSE       58 (to 518)
            402 POP_TOP

486         404 LOAD_GLOBAL              3 (NULL + enumerate)
            414 CACHE
            416 LOAD_FAST                6 (chunk)
            418 UNPACK_SEQUENCE          1
            422 CALL                     1
            430 CACHE
            432 GET_ITER
        >>  434 FOR_ITER                40 (to 518)
            438 CACHE
            440 STORE_FAST               8 (j)
            442 STORE_FAST               2 (c)

487         444 LOAD_GLOBAL              0 (_b85dec)
            454 CACHE
            456 LOAD_FAST                2 (c)
            458 BINARY_SUBSCR
            462 CACHE
            464 CACHE
            466 CACHE
            468 POP_JUMP_IF_NOT_NONE    22 (to 514)

488         470 LOAD_GLOBAL             21 (NULL + ValueError)
            480 CACHE
            482 LOAD_CONST               8 ('bad base85 character at position %d')

489         484 LOAD_FAST                1 (i)
            486 LOAD_FAST                8 (j)
            488 BINARY_OP                0 (+)

488         492 BINARY_OP                6 (%)
            496 UNPACK_SEQUENCE          1
            500 CALL                     1
            508 CACHE

489         510 LOAD_CONST               1 (None)

488         512 RAISE_VARARGS            2

487     >>  514 JUMP_BACKWARD           41 (to 434)

490         516 RAISE_VARARGS            0

485     >>  518 RERAISE                  0
        >>  520 COPY                     3
            522 POP_EXCEPT
            524 RERAISE                  1

491     >>  526 NOP

492         528 LOAD_FAST                4 (out)
            530 STORE_SUBSCR
            534 CACHE
            536 CACHE
            538 CACHE
            540 CACHE
            542 CACHE
            544 CACHE
            546 CACHE
            548 CACHE
            550 CACHE
            552 PUSH_NULL
            554 LOAD_FAST                5 (packI)
            556 LOAD_FAST                7 (acc)
            558 UNPACK_SEQUENCE          1
            562 CALL                     1
            570 CACHE
            572 UNPACK_SEQUENCE          1
            576 CALL                     1
            584 CACHE
            586 POP_TOP
            588 JUMP_BACKWARD          146 (to 298)
        >>  590 PUSH_EXC_INFO

493         592 LOAD_GLOBAL             10 (struct)
            602 CACHE
            604 LOAD_ATTR               12 (Struct)
            624 CACHE
            626 CACHE
            628 CACHE
            630 CACHE
            632 LOAD_CONST               9 ('base85 overflow in hunk starting at byte %d')

495         634 LOAD_FAST                1 (i)

494         636 BINARY_OP                6 (%)
            640 UNPACK_SEQUENCE          1
            644 CALL                     1
            652 CACHE

495         654 LOAD_CONST               1 (None)

494         656 RAISE_VARARGS            2

493         658 RERAISE                  0
        >>  660 COPY                     3
            662 POP_EXCEPT
            664 RERAISE                  1

497         666 LOAD_CONST              10 (b'')
        >>  668 STORE_SUBSCR
            672 CACHE
            674 CACHE
            676 CACHE
            678 CACHE
            680 CACHE
            682 CACHE
            684 CACHE
            686 CACHE
            688 CACHE
            690 LOAD_FAST                4 (out)
            692 UNPACK_SEQUENCE          1
            696 CALL                     1
            704 CACHE
            706 STORE_FAST               9 (result)

498         708 LOAD_FAST                3 (padding)
            710 POP_JUMP_IF_FALSE       11 (to 734)

499         712 LOAD_FAST                9 (result)
            714 LOAD_CONST               1 (None)
            716 LOAD_FAST                3 (padding)
            718 UNARY_NEGATIVE
            720 BUILD_SLICE              2
            722 BINARY_SUBSCR
            726 CACHE
            728 CACHE
            730 CACHE
            732 STORE_FAST               9 (result)

500     >>  734 LOAD_FAST                9 (result)
            736 RETURN_VALUE
ExceptionTable:
  334 to 380 -> 384 [1]
  384 to 518 -> 520 [2] lasti
  528 to 586 -> 590 [1]
  590 to 658 -> 660 [2] lasti

Disassembly of <code object encode at 0x000001E77EF6DBE0, file "base64.py", line 509>:
509           0 RESUME                   0

511           2 NOP

512     >>    4 LOAD_FAST                0 (input)
              6 STORE_SUBSCR
             10 CACHE
             12 CACHE
             14 CACHE
             16 CACHE
             18 CACHE
             20 CACHE
             22 CACHE
             24 CACHE
             26 CACHE
             28 LOAD_GLOBAL              2 (MAXBINSIZE)
             38 CACHE
             40 UNPACK_SEQUENCE          1
             44 CALL                     1
             52 CACHE
             54 STORE_FAST               2 (s)

513          56 LOAD_FAST                2 (s)
             58 POP_JUMP_IF_TRUE         2 (to 64)

514          60 LOAD_CONST               2 (None)
             62 RETURN_VALUE

515     >>   64 LOAD_GLOBAL              5 (NULL + len)
             74 CACHE
             76 LOAD_FAST                2 (s)
             78 UNPACK_SEQUENCE          1
             82 CALL                     1
             90 CACHE
             92 LOAD_GLOBAL              2 (MAXBINSIZE)
            102 CACHE
            104 COMPARE_OP               0 (<)
            108 CACHE
            110 POP_JUMP_IF_FALSE       74 (to 260)

516         112 LOAD_FAST                0 (input)
            114 STORE_SUBSCR
            118 CACHE
            120 CACHE
            122 CACHE
            124 CACHE
            126 CACHE
            128 CACHE
            130 CACHE
            132 CACHE
            134 CACHE
            136 LOAD_GLOBAL              2 (MAXBINSIZE)
            146 CACHE
            148 LOAD_GLOBAL              5 (NULL + len)
            158 CACHE
            160 LOAD_FAST                2 (s)
            162 UNPACK_SEQUENCE          1
            166 CALL                     1
            174 CACHE
            176 BINARY_OP               10 (-)
            180 UNPACK_SEQUENCE          1
            184 CALL                     1
            192 CACHE
            194 STORE_FAST               3 (ns)

517         196 LOAD_FAST                3 (ns)
            198 POP_JUMP_IF_TRUE         1 (to 202)

518         200 JUMP_FORWARD            29 (to 260)

519     >>  202 LOAD_FAST                2 (s)
            204 LOAD_FAST                3 (ns)
            206 BINARY_OP               13 (+=)
            210 STORE_FAST               2 (s)

515         212 LOAD_GLOBAL              5 (NULL + len)
            222 CACHE
            224 LOAD_FAST                2 (s)
            226 UNPACK_SEQUENCE          1
            230 CALL                     1
            238 CACHE
            240 LOAD_GLOBAL              2 (MAXBINSIZE)
            250 CACHE
            252 COMPARE_OP               0 (<)
            256 CACHE
