# MAIN APPLICATION CODE OBJECT
# Position: 7258642
# Filename: _collections_abc.py
# Function: <module>
# Args: 0
# Locals: 0
# Stack size: 8
# Flags: 0

# BYTECODE DISASSEMBLY:
==================================================
   0           0 RESUME                   0

   4           2 LOAD_CONST               0 ('Abstract Base Classes (ABCs) for collections, according to PEP 3119.\n\nUnit tests are in test_collections.\n')
               4 STORE_NAME               0 (__doc__)

   9           6 LOAD_CONST               1 (0)
               8 LOAD_CONST               2 (('ABCMeta', 'abstractmethod'))
              10 IMPORT_NAME              1 (abc)
              12 IMPORT_FROM              2 (ABCMeta)
              14 STORE_NAME               2 (ABCMeta)
              16 IMPORT_FROM              3 (abstractmethod)
              18 STORE_NAME               3 (abstractmethod)
              20 POP_TOP

  10          22 LOAD_CONST               1 (0)
              24 LOAD_CONST               3 (None)
              26 IMPORT_NAME              4 (sys)
              28 STORE_NAME               4 (sys)

  12          30 PUSH_NULL
              32 LOAD_NAME                5 (type)
              34 LOAD_NAME                6 (list)
              36 LOAD_NAME                7 (int)
              38 BINARY_SUBSCR
              42 CACHE
              44 CACHE
              46 CACHE
              48 UNPACK_SEQUENCE          1
              52 CALL                     1
              60 CACHE
              62 STORE_NAME               8 (GenericAlias)

  13          64 PUSH_NULL
              66 LOAD_NAME                5 (type)
              68 LOAD_CONST               4 (Ellipsis)
              70 UNPACK_SEQUENCE          1
              74 CALL                     1
              82 CACHE
              84 STORE_NAME               9 (EllipsisType)

  14          86 LOAD_CONST               5 (<code object _f at 0x000001E77EBC56F0, file "_collections_abc.py", line 14>)
              88 MAKE_FUNCTION            0
              90 STORE_NAME              10 (_f)

  15          92 PUSH_NULL
              94 LOAD_NAME                5 (type)
              96 LOAD_NAME               10 (_f)
              98 UNPACK_SEQUENCE          1
             102 CALL                     1
             110 CACHE
             112 STORE_NAME              11 (FunctionType)

  16         114 DELETE_NAME             10 (_f)

  18         116 BUILD_LIST               0
             118 LOAD_CONST               6 (('Awaitable', 'Coroutine', 'AsyncIterable', 'AsyncIterator', 'AsyncGenerator', 'Hashable', 'Iterable', 'Iterator', 'Generator', 'Reversible', 'Sized', 'Container', 'Callable', 'Collection', 'Set', 'MutableSet', 'Mapping', 'MutableMapping', 'MappingView', 'KeysView', 'ItemsView', 'ValuesView', 'Sequence', 'MutableSequence', 'ByteString'))
             120 LIST_EXTEND              1
             122 STORE_NAME              12 (__all__)

  33         124 LOAD_CONST               7 ('collections.abc')
             126 STORE_NAME              13 (__name__)

  42         128 PUSH_NULL
             130 LOAD_NAME                5 (type)
             132 PUSH_NULL
             134 LOAD_NAME               14 (iter)
             136 LOAD_CONST               8 (b'')
             138 UNPACK_SEQUENCE          1
             142 CALL                     1
             150 CACHE
             152 UNPACK_SEQUENCE          1
             156 CALL                     1
             164 CACHE
             166 STORE_NAME              15 (bytes_iterator)

  43         168 PUSH_NULL
             170 LOAD_NAME                5 (type)
             172 PUSH_NULL
             174 LOAD_NAME               14 (iter)
             176 PUSH_NULL
             178 LOAD_NAME               16 (bytearray)
             180 UNPACK_SEQUENCE          0
             184 CALL                     0
             192 CACHE
             194 UNPACK_SEQUENCE          1
             198 CALL                     1
             206 CACHE
             208 UNPACK_SEQUENCE          1
             212 CALL                     1
             220 CACHE
             222 STORE_NAME              17 (bytearray_iterator)

  45         224 PUSH_NULL
             226 LOAD_NAME                5 (type)
             228 PUSH_NULL
             230 LOAD_NAME               14 (iter)
             232 BUILD_MAP                0
             234 STORE_SUBSCR
             238 CACHE
             240 CACHE
             242 CACHE
             244 CACHE
             246 CACHE
             248 CACHE
             250 CACHE
             252 CACHE
             254 CACHE
             256 UNPACK_SEQUENCE          0
             260 CALL                     0
             268 CACHE
             270 UNPACK_SEQUENCE          1
             274 CALL                     1
             282 CACHE
             284 UNPACK_SEQUENCE          1
             288 CALL                     1
             296 CACHE
             298 STORE_NAME              19 (dict_keyiterator)

  46         300 PUSH_NULL
             302 LOAD_NAME                5 (type)
             304 PUSH_NULL
             306 LOAD_NAME               14 (iter)
             308 BUILD_MAP                0
             310 STORE_SUBSCR
             314 CACHE
             316 CACHE
             318 CACHE
             320 CACHE
             322 CACHE
             324 CACHE
             326 CACHE
             328 CACHE
             330 CACHE
             332 UNPACK_SEQUENCE          0
             336 CALL                     0
             344 CACHE
             346 UNPACK_SEQUENCE          1
             350 CALL                     1
             358 CACHE
             360 UNPACK_SEQUENCE          1
             364 CALL                     1
             372 CACHE
             374 STORE_NAME              21 (dict_valueiterator)

  47         376 PUSH_NULL
             378 LOAD_NAME                5 (type)
             380 PUSH_NULL
             382 LOAD_NAME               14 (iter)
             384 BUILD_MAP                0
             386 STORE_SUBSCR
             390 CACHE
             392 CACHE
             394 CACHE
             396 CACHE
             398 CACHE
             400 CACHE
             402 CACHE
             404 CACHE
             406 CACHE
             408 UNPACK_SEQUENCE          0
             412 CALL                     0
             420 CACHE
             422 UNPACK_SEQUENCE          1
             426 CALL                     1
             434 CACHE
             436 UNPACK_SEQUENCE          1
             440 CALL                     1
             448 CACHE
             450 STORE_NAME              23 (dict_itemiterator)

  48         452 PUSH_NULL
             454 LOAD_NAME                5 (type)
             456 PUSH_NULL
             458 LOAD_NAME               14 (iter)
             460 BUILD_LIST               0
             462 UNPACK_SEQUENCE          1
             466 CALL                     1
             474 CACHE
             476 UNPACK_SEQUENCE          1
             480 CALL                     1
             488 CACHE
             490 STORE_NAME              24 (list_iterator)

  49         492 PUSH_NULL
             494 LOAD_NAME                5 (type)
             496 PUSH_NULL
             498 LOAD_NAME               14 (iter)
             500 PUSH_NULL
             502 LOAD_NAME               25 (reversed)
             504 BUILD_LIST               0
             506 UNPACK_SEQUENCE          1
             510 CALL                     1
             518 CACHE
             520 UNPACK_SEQUENCE          1
             524 CALL                     1
             532 CACHE
             534 UNPACK_SEQUENCE          1
             538 CALL                     1
             546 CACHE
             548 STORE_NAME              26 (list_reverseiterator)

  50         550 PUSH_NULL
             552 LOAD_NAME                5 (type)
             554 PUSH_NULL
             556 LOAD_NAME               14 (iter)
             558 PUSH_NULL
             560 LOAD_NAME               27 (range)
             562 LOAD_CONST               1 (0)
             564 UNPACK_SEQUENCE          1
             568 CALL                     1
             576 CACHE
             578 UNPACK_SEQUENCE          1
             582 CALL                     1
             590 CACHE
             592 UNPACK_SEQUENCE          1
             596 CALL                     1
             604 CACHE
             606 STORE_NAME              28 (range_iterator)

  51         608 PUSH_NULL
             610 LOAD_NAME                5 (type)
             612 PUSH_NULL
             614 LOAD_NAME               14 (iter)
             616 PUSH_NULL
             618 LOAD_NAME               27 (range)
             620 LOAD_CONST               9 (1)
             622 LOAD_CONST              10 (1000)
             624 BINARY_OP                3 (<<)
             628 UNPACK_SEQUENCE          1
             632 CALL                     1
             640 CACHE
             642 UNPACK_SEQUENCE          1
             646 CALL                     1
             654 CACHE
             656 UNPACK_SEQUENCE          1
             660 CALL                     1
             668 CACHE
             670 STORE_NAME              29 (longrange_iterator)

  52         672 PUSH_NULL
             674 LOAD_NAME                5 (type)
             676 PUSH_NULL
             678 LOAD_NAME               14 (iter)
             680 PUSH_NULL
             682 LOAD_NAME               30 (set)
             684 UNPACK_SEQUENCE          0
             688 CALL                     0
             696 CACHE
             698 UNPACK_SEQUENCE          1
             702 CALL                     1
             710 CACHE
             712 UNPACK_SEQUENCE          1
             716 CALL                     1
             724 CACHE
             726 STORE_NAME              31 (set_iterator)

  53         728 PUSH_NULL
             730 LOAD_NAME                5 (type)
             732 PUSH_NULL
             734 LOAD_NAME               14 (iter)
             736 LOAD_CONST              11 ('')
             738 UNPACK_SEQUENCE          1
             742 CALL                     1
             750 CACHE
             752 UNPACK_SEQUENCE          1
             756 CALL                     1
             764 CACHE
             766 STORE_NAME              32 (str_iterator)

  54         768 PUSH_NULL
             770 LOAD_NAME                5 (type)
             772 PUSH_NULL
             774 LOAD_NAME               14 (iter)
             776 LOAD_CONST              12 (())
             778 UNPACK_SEQUENCE          1
             782 CALL                     1
             790 CACHE
             792 UNPACK_SEQUENCE          1
             796 CALL                     1
             804 CACHE
             806 STORE_NAME              33 (tuple_iterator)

  55         808 PUSH_NULL
             810 LOAD_NAME                5 (type)
             812 PUSH_NULL
             814 LOAD_NAME               14 (iter)
             816 PUSH_NULL
             818 LOAD_NAME               34 (zip)
             820 UNPACK_SEQUENCE          0
             824 CALL                     0
             832 CACHE
             834 UNPACK_SEQUENCE          1
             838 CALL                     1
             846 CACHE
             848 UNPACK_SEQUENCE          1
             852 CALL                     1
             860 CACHE
             862 STORE_NAME              35 (zip_iterator)

  57         864 PUSH_NULL
             866 LOAD_NAME                5 (type)
             868 BUILD_MAP                0
             870 STORE_SUBSCR
             874 CACHE
             876 CACHE
             878 CACHE
             880 CACHE
             882 CACHE
             884 CACHE
             886 CACHE
             888 CACHE
             890 CACHE
             892 UNPACK_SEQUENCE          0
             896 CALL                     0
             904 CACHE
             906 UNPACK_SEQUENCE          1
             910 CALL                     1
             918 CACHE
             920 STORE_NAME              36 (dict_keys)

  58         922 PUSH_NULL
             924 LOAD_NAME                5 (type)
             926 BUILD_MAP                0
             928 STORE_SUBSCR
             932 CACHE
             934 CACHE
             936 CACHE
             938 CACHE
             940 CACHE
             942 CACHE
             944 CACHE
             946 CACHE
             948 CACHE
             950 UNPACK_SEQUENCE          0
             954 CALL                     0
             962 CACHE
             964 UNPACK_SEQUENCE          1
             968 CALL                     1
             976 CACHE
             978 STORE_NAME              37 (dict_values)

  59         980 PUSH_NULL
             982 LOAD_NAME                5 (type)
             984 BUILD_MAP                0
             986 STORE_SUBSCR
             990 CACHE
             992 CACHE
             994 CACHE
             996 CACHE
             998 CACHE
            1000 CACHE
            1002 CACHE
            1004 CACHE
            1006 CACHE
            1008 UNPACK_SEQUENCE          0
            1012 CALL                     0
            1020 CACHE
            1022 UNPACK_SEQUENCE          1
            1026 CALL                     1
            1034 CACHE
            1036 STORE_NAME              38 (dict_items)

  61        1038 PUSH_NULL
            1040 LOAD_NAME                5 (type)
            1042 LOAD_NAME                5 (type)
            1044 LOAD_ATTR               39 (NULL|self + dict_keyiterator)
            1064 CACHE
            1066 CACHE
            1068 STORE_NAME              40 (mappingproxy)

  62        1070 PUSH_NULL
            1072 LOAD_NAME                5 (type)
            1074 PUSH_NULL
            1076 LOAD_CONST              13 (<code object <lambda> at 0x000001E77EBC57C0, file "_collections_abc.py", line 62>)
            1078 MAKE_FUNCTION            0
            1080 UNPACK_SEQUENCE          0
            1084 CALL                     0
            1092 CACHE
            1094 UNPACK_SEQUENCE          1
            1098 CALL                     1
            1106 CACHE
            1108 STORE_NAME              41 (generator)

  64        1110 LOAD_CONST              14 (<code object _coro at 0x000001E77EBC5890, file "_collections_abc.py", line 64>)
            1112 MAKE_FUNCTION            0
            1114 STORE_NAME              42 (_coro)

  65        1116 PUSH_NULL
            1118 LOAD_NAME               42 (_coro)
            1120 UNPACK_SEQUENCE          0
            1124 CALL                     0
            1132 CACHE
            1134 STORE_NAME              42 (_coro)

  66        1136 PUSH_NULL
            1138 LOAD_NAME                5 (type)
            1140 LOAD_NAME               42 (_coro)
            1142 UNPACK_SEQUENCE          1
            1146 CALL                     1
            1154 CACHE
            1156 STORE_NAME              43 (coroutine)

  67        1158 LOAD_NAME               42 (_coro)
            1160 STORE_SUBSCR
            1164 CACHE
            1166 CACHE
            1168 CACHE
            1170 CACHE
            1172 CACHE
            1174 CACHE
            1176 CACHE
            1178 CACHE
            1180 CACHE
            1182 UNPACK_SEQUENCE          0
            1186 CALL                     0
            1194 CACHE
            1196 POP_TOP

  68        1198 DELETE_NAME             42 (_coro)

  70        1200 LOAD_CONST              15 (<code object _ag at 0x000001E77EBD83B0, file "_collections_abc.py", line 70>)
            1202 MAKE_FUNCTION            0
            1204 STORE_NAME              45 (_ag)

  71        1206 PUSH_NULL
            1208 LOAD_NAME               45 (_ag)
            1210 UNPACK_SEQUENCE          0
            1214 CALL                     0
            1222 CACHE
            1224 STORE_NAME              45 (_ag)

  72        1226 PUSH_NULL
            1228 LOAD_NAME                5 (type)
            1230 LOAD_NAME               45 (_ag)
            1232 UNPACK_SEQUENCE          1
            1236 CALL                     1
            1244 CACHE
            1246 STORE_NAME              46 (async_generator)

  73        1248 DELETE_NAME             45 (_ag)

  78        1250 LOAD_CONST              16 (<code object _check_methods at 0x000001E77E769430, file "_collections_abc.py", line 78>)
            1252 MAKE_FUNCTION            0
            1254 STORE_NAME              47 (_check_methods)

  90        1256 PUSH_NULL
            1258 LOAD_BUILD_CLASS
            1260 LOAD_CONST              17 (<code object Hashable at 0x000001E77EBC1A30, file "_collections_abc.py", line 90>)
            1262 MAKE_FUNCTION            0
            1264 LOAD_CONST              18 ('Hashable')
            1266 LOAD_NAME                2 (ABCMeta)
            1268 KW_NAMES                19 (('metaclass',))
            1270 UNPACK_SEQUENCE          3
            1274 CALL                     3
            1282 CACHE
            1284 STORE_NAME              48 (Hashable)

 105        1286 PUSH_NULL
            1288 LOAD_BUILD_CLASS
            1290 LOAD_CONST              20 (<code object Awaitable at 0x000001E77E707AB0, file "_collections_abc.py", line 105>)
            1292 MAKE_FUNCTION            0
            1294 LOAD_CONST              21 ('Awaitable')
            1296 LOAD_NAME                2 (ABCMeta)
            1298 KW_NAMES                19 (('metaclass',))
            1300 UNPACK_SEQUENCE          3
            1304 CALL                     3
            1312 CACHE
            1314 STORE_NAME              49 (Awaitable)

 122        1316 PUSH_NULL
            1318 LOAD_BUILD_CLASS
            1320 LOAD_CONST              22 (<code object Coroutine at 0x000001E77E707CF0, file "_collections_abc.py", line 122>)
            1322 MAKE_FUNCTION            0
            1324 LOAD_CONST              23 ('Coroutine')
            1326 LOAD_NAME               49 (Awaitable)
            1328 UNPACK_SEQUENCE          3
            1332 CALL                     3
            1340 CACHE
            1342 STORE_NAME              50 (Coroutine)

 163        1344 LOAD_NAME               50 (Coroutine)
            1346 STORE_SUBSCR
            1350 CACHE
            1352 CACHE
            1354 CACHE
            1356 CACHE
            1358 CACHE
            1360 CACHE
            1362 CACHE
            1364 CACHE
            1366 CACHE
            1368 LOAD_NAME               43 (coroutine)
            1370 UNPACK_SEQUENCE          1
            1374 CALL                     1
            1382 CACHE
            1384 POP_TOP

 166        1386 PUSH_NULL
            1388 LOAD_BUILD_CLASS
            1390 LOAD_CONST              24 (<code object AsyncIterable at 0x000001E77E707E10, file "_collections_abc.py", line 166>)
            1392 MAKE_FUNCTION            0
            1394 LOAD_CONST              25 ('AsyncIterable')
            1396 LOAD_NAME                2 (ABCMeta)
            1398 KW_NAMES                19 (('metaclass',))
            1400 UNPACK_SEQUENCE          3
            1404 CALL                     3
            1412 CACHE
            1414 STORE_NAME              52 (AsyncIterable)

 183        1416 PUSH_NULL
            1418 LOAD_BUILD_CLASS
            1420 LOAD_CONST              26 (<code object AsyncIterator at 0x000001E77E713CC0, file "_collections_abc.py", line 183>)
            1422 MAKE_FUNCTION            0
            1424 LOAD_CONST              27 ('AsyncIterator')
            1426 LOAD_NAME               52 (AsyncIterable)
            1428 UNPACK_SEQUENCE          3
            1432 CALL                     3
            1440 CACHE
            1442 STORE_NAME              53 (AsyncIterator)

 202        1444 PUSH_NULL
            1446 LOAD_BUILD_CLASS
            1448 LOAD_CONST              28 (<code object AsyncGenerator at 0x000001E77E76E040, file "_collections_abc.py", line 202>)
            1450 MAKE_FUNCTION            0
            1452 LOAD_CONST              29 ('AsyncGenerator')
            1454 LOAD_NAME               53 (AsyncIterator)
            1456 UNPACK_SEQUENCE          3
            1460 CALL                     3
            1468 CACHE
            1470 STORE_NAME              54 (AsyncGenerator)

 250        1472 LOAD_NAME               54 (AsyncGenerator)
            1474 STORE_SUBSCR
            1478 CACHE
            1480 CACHE
            1482 CACHE
            1484 CACHE
            1486 CACHE
            1488 CACHE
            1490 CACHE
            1492 CACHE
            1494 CACHE
            1496 LOAD_NAME               46 (async_generator)
            1498 UNPACK_SEQUENCE          1
            1502 CALL                     1
            1510 CACHE
            1512 POP_TOP

 253        1514 PUSH_NULL
            1516 LOAD_BUILD_CLASS
            1518 LOAD_CONST              30 (<code object Iterable at 0x000001E77EBF4150, file "_collections_abc.py", line 253>)
            1520 MAKE_FUNCTION            0
            1522 LOAD_CONST              31 ('Iterable')
            1524 LOAD_NAME                2 (ABCMeta)
            1526 KW_NAMES                19 (('metaclass',))
            1528 UNPACK_SEQUENCE          3
            1532 CALL                     3
            1540 CACHE
            1542 STORE_NAME              55 (Iterable)

 271        1544 PUSH_NULL
            1546 LOAD_BUILD_CLASS
            1548 LOAD_CONST              32 (<code object Iterator at 0x000001E77EBF8140, file "_collections_abc.py", line 271>)
            1550 MAKE_FUNCTION            0
            1552 LOAD_CONST              33 ('Iterator')
            1554 LOAD_NAME               55 (Iterable)
            1556 UNPACK_SEQUENCE          3
            1560 CALL                     3
            1568 CACHE
            1570 STORE_NAME              56 (Iterator)

 290        1572 LOAD_NAME               56 (Iterator)
            1574 STORE_SUBSCR
            1578 CACHE
            1580 CACHE
            1582 CACHE
            1584 CACHE
            1586 CACHE
            1588 CACHE
            1590 CACHE
            1592 CACHE
            1594 CACHE
            1596 LOAD_NAME               15 (bytes_iterator)
            1598 UNPACK_SEQUENCE          1
            1602 CALL                     1
            1610 CACHE
            1612 POP_TOP

 291        1614 LOAD_NAME               56 (Iterator)
            1616 STORE_SUBSCR
            1620 CACHE
            1622 CACHE
            1624 CACHE
            1626 CACHE
            1628 CACHE
            1630 CACHE
            1632 CACHE
            1634 CACHE
            1636 CACHE
            1638 LOAD_NAME               17 (bytearray_iterator)
            1640 UNPACK_SEQUENCE          1
            1644 CALL                     1
            1652 CACHE
            1654 POP_TOP

 293        1656 LOAD_NAME               56 (Iterator)
            1658 STORE_SUBSCR
            1662 CACHE
            1664 CACHE
            1666 CACHE
            1668 CACHE
            1670 CACHE
            1672 CACHE
            1674 CACHE
            1676 CACHE
            1678 CACHE
            1680 LOAD_NAME               19 (dict_keyiterator)
            1682 UNPACK_SEQUENCE          1
            1686 CALL                     1
            1694 CACHE
            1696 POP_TOP

 294        1698 LOAD_NAME               56 (Iterator)
            1700 STORE_SUBSCR
            1704 CACHE
            1706 CACHE
            1708 CACHE
            1710 CACHE
            1712 CACHE
            1714 CACHE
            1716 CACHE
            1718 CACHE
            1720 CACHE
            1722 LOAD_NAME               21 (dict_valueiterator)
            1724 UNPACK_SEQUENCE          1
            1728 CALL                     1
            1736 CACHE
            1738 POP_TOP

 295        1740 LOAD_NAME               56 (Iterator)
            1742 STORE_SUBSCR
            1746 CACHE
            1748 CACHE
            1750 CACHE
            1752 CACHE
            1754 CACHE
            1756 CACHE
            1758 CACHE
            1760 CACHE
            1762 CACHE
            1764 LOAD_NAME               23 (dict_itemiterator)
            1766 UNPACK_SEQUENCE          1
            1770 CALL                     1
            1778 CACHE
            1780 POP_TOP

 296        1782 LOAD_NAME               56 (Iterator)
            1784 STORE_SUBSCR
            1788 CACHE
            1790 CACHE
            1792 CACHE
            1794 CACHE
            1796 CACHE
            1798 CACHE
            1800 CACHE
            1802 CACHE
            1804 CACHE
            1806 LOAD_NAME               24 (list_iterator)
            1808 UNPACK_SEQUENCE          1
            1812 CALL                     1
            1820 CACHE
            1822 POP_TOP

 297        1824 LOAD_NAME               56 (Iterator)
            1826 STORE_SUBSCR
            1830 CACHE
            1832 CACHE
            1834 CACHE
            1836 CACHE
            1838 CACHE
            1840 CACHE
            1842 CACHE
            1844 CACHE
            1846 CACHE
            1848 LOAD_NAME               26 (list_reverseiterator)
            1850 UNPACK_SEQUENCE          1
            1854 CALL                     1
            1862 CACHE
            1864 POP_TOP

 298        1866 LOAD_NAME               56 (Iterator)
            1868 STORE_SUBSCR
            1872 CACHE
            1874 CACHE
            1876 CACHE
            1878 CACHE
            1880 CACHE
            1882 CACHE
            1884 CACHE
            1886 CACHE
            1888 CACHE
            1890 LOAD_NAME               28 (range_iterator)
            1892 UNPACK_SEQUENCE          1
            1896 CALL                     1
            1904 CACHE
            1906 POP_TOP

 299        1908 LOAD_NAME               56 (Iterator)
            1910 STORE_SUBSCR
            1914 CACHE
            1916 CACHE
            1918 CACHE
            1920 CACHE
            1922 CACHE
            1924 CACHE
            1926 CACHE
            1928 CACHE
            1930 CACHE
            1932 LOAD_NAME               29 (longrange_iterator)
            1934 UNPACK_SEQUENCE          1
            1938 CALL                     1
            1946 CACHE
            1948 POP_TOP

 300        1950 LOAD_NAME               56 (Iterator)
            1952 STORE_SUBSCR
            1956 CACHE
            1958 CACHE
            1960 CACHE
            1962 CACHE
            1964 CACHE
            1966 CACHE
            1968 CACHE
            1970 CACHE
            1972 CACHE
            1974 LOAD_NAME               31 (set_iterator)
            1976 UNPACK_SEQUENCE          1
            1980 CALL                     1
            1988 CACHE
            1990 POP_TOP

 301        1992 LOAD_NAME               56 (Iterator)
            1994 STORE_SUBSCR
            1998 CACHE
            2000 CACHE
            2002 CACHE
            2004 CACHE
            2006 CACHE
            2008 CACHE
            2010 CACHE
            2012 CACHE
            2014 CACHE
            2016 LOAD_NAME               32 (str_iterator)
            2018 UNPACK_SEQUENCE          1
            2022 CALL                     1
            2030 CACHE
            2032 POP_TOP

 302        2034 LOAD_NAME               56 (Iterator)
            2036 STORE_SUBSCR
            2040 CACHE
            2042 CACHE
            2044 CACHE
            2046 CACHE
            2048 CACHE
            2050 CACHE
            2052 CACHE
            2054 CACHE
            2056 CACHE
            2058 LOAD_NAME               33 (tuple_iterator)
            2060 UNPACK_SEQUENCE          1
            2064 CALL                     1
            2072 CACHE
            2074 POP_TOP

 303        2076 LOAD_NAME               56 (Iterator)
            2078 STORE_SUBSCR
            2082 CACHE
            2084 CACHE
            2086 CACHE
            2088 CACHE
            2090 CACHE
            2092 CACHE
            2094 CACHE
            2096 CACHE
            2098 CACHE
            2100 LOAD_NAME               35 (zip_iterator)
            2102 UNPACK_SEQUENCE          1
            2106 CALL                     1
            2114 CACHE
            2116 POP_TOP

 306        2118 PUSH_NULL
            2120 LOAD_BUILD_CLASS
            2122 LOAD_CONST              34 (<code object Reversible at 0x000001E77EBC1C30, file "_collections_abc.py", line 306>)
            2124 MAKE_FUNCTION            0
            2126 LOAD_CONST              35 ('Reversible')
            2128 LOAD_NAME               55 (Iterable)
            2130 UNPACK_SEQUENCE          3
            2134 CALL                     3
            2142 CACHE
            2144 STORE_NAME              57 (Reversible)

 322        2146 PUSH_NULL
            2148 LOAD_BUILD_CLASS
            2150 LOAD_CONST              36 (<code object Generator at 0x000001E77E76D7F0, file "_collections_abc.py", line 322>)
            2152 MAKE_FUNCTION            0
            2154 LOAD_CONST              37 ('Generator')
            2156 LOAD_NAME               56 (Iterator)
            2158 UNPACK_SEQUENCE          3
            2162 CALL                     3
            2170 CACHE
            2172 STORE_NAME              58 (Generator)

 370        2174 LOAD_NAME               58 (Generator)
            2176 STORE_SUBSCR
            2180 CACHE
            2182 CACHE
            2184 CACHE
            2186 CACHE
            2188 CACHE
            2190 CACHE
            2192 CACHE
            2194 CACHE
            2196 CACHE
            2198 LOAD_NAME               41 (generator)
            2200 UNPACK_SEQUENCE          1
            2204 CALL                     1
            2212 CACHE
            2214 POP_TOP

 373        2216 PUSH_NULL
            2218 LOAD_BUILD_CLASS
            2220 LOAD_CONST              38 (<code object Sized at 0x000001E77EBC1D30, file "_collections_abc.py", line 373>)
            2222 MAKE_FUNCTION            0
            2224 LOAD_CONST              39 ('Sized')
            2226 LOAD_NAME                2 (ABCMeta)
            2228 KW_NAMES                19 (('metaclass',))
            2230 UNPACK_SEQUENCE          3
            2234 CALL                     3
            2242 CACHE
            2244 STORE_NAME              59 (Sized)

 388        2246 PUSH_NULL
            2248 LOAD_BUILD_CLASS
            2250 LOAD_CONST              40 (<code object Container at 0x000001E77EBF4390, file "_collections_abc.py", line 388>)
            2252 MAKE_FUNCTION            0
            2254 LOAD_CONST              41 ('Container')
            2256 LOAD_NAME                2 (ABCMeta)
            2258 KW_NAMES                19 (('metaclass',))
            2260 UNPACK_SEQUENCE          3
            2264 CALL                     3
            2272 CACHE
            2274 STORE_NAME              60 (Container)

 405        2276 PUSH_NULL
            2278 LOAD_BUILD_CLASS
            2280 LOAD_CONST              42 (<code object Collection at 0x000001E77EBCCB70, file "_collections_abc.py", line 405>)
            2282 MAKE_FUNCTION            0
            2284 LOAD_CONST              43 ('Collection')
            2286 LOAD_NAME               59 (Sized)
            2288 LOAD_NAME               55 (Iterable)
            2290 LOAD_NAME               60 (Container)
            2292 UNPACK_SEQUENCE          5
            2296 CALL                     5
            2304 CACHE
            2306 STORE_NAME              61 (Collection)

 416        2308 PUSH_NULL
            2310 LOAD_BUILD_CLASS
            2312 LOAD_CONST              44 (<code object _CallableGenericAlias at 0x000001E77EBC1F30, file "_collections_abc.py", line 416>)
            2314 MAKE_FUNCTION            0
            2316 LOAD_CONST              45 ('_CallableGenericAlias')
            2318 LOAD_NAME                8 (GenericAlias)
            2320 UNPACK_SEQUENCE          3
            2324 CALL                     3
            2332 CACHE
            2334 STORE_NAME              62 (_CallableGenericAlias)

 477        2336 LOAD_CONST              46 (<code object _is_param_expr at 0x000001E77E720630, file "_collections_abc.py", line 477>)
            2338 MAKE_FUNCTION            0
            2340 STORE_NAME              63 (_is_param_expr)

 489        2342 LOAD_CONST              47 (<code object _type_repr at 0x000001E77E7607F0, file "_collections_abc.py", line 489>)
            2344 MAKE_FUNCTION            0
            2346 STORE_NAME              64 (_type_repr)

 508        2348 PUSH_NULL
            2350 LOAD_BUILD_CLASS
            2352 LOAD_CONST              48 (<code object Callable at 0x000001E77EBF44B0, file "_collections_abc.py", line 508>)
            2354 MAKE_FUNCTION            0
            2356 LOAD_CONST              49 ('Callable')
            2358 LOAD_NAME                2 (ABCMeta)
            2360 KW_NAMES                19 (('metaclass',))
            2362 UNPACK_SEQUENCE          3
            2366 CALL                     3
            2374 CACHE
            2376 STORE_NAME              65 (Callable)

 528        2378 PUSH_NULL
            2380 LOAD_BUILD_CLASS
            2382 LOAD_CONST              50 (<code object Set at 0x000001E77E768B70, file "_collections_abc.py", line 528>)
            2384 MAKE_FUNCTION            0
            2386 LOAD_CONST              51 ('Set')
            2388 LOAD_NAME               61 (Collection)
            2390 UNPACK_SEQUENCE          3
            2394 CALL                     3
            2402 CACHE
            2404 STORE_NAME              66 (Set)

 666        2406 LOAD_NAME               66 (Set)
            2408 STORE_SUBSCR
            2412 CACHE
            2414 CACHE
            2416 CACHE
            2418 CACHE
            2420 CACHE
            2422 CACHE
            2424 CACHE
            2426 CACHE
            2428 CACHE
            2430 LOAD_NAME               67 (frozenset)
            2432 UNPACK_SEQUENCE          1
            2436 CALL                     1
            2444 CACHE
            2446 POP_TOP

 669        2448 PUSH_NULL
            2450 LOAD_BUILD_CLASS
            2452 LOAD_CONST              52 (<code object MutableSet at 0x000001E77E76E500, file "_collections_abc.py", line 669>)
            2454 MAKE_FUNCTION            0
            2456 LOAD_CONST              53 ('MutableSet')
            2458 LOAD_NAME               66 (Set)
            2460 UNPACK_SEQUENCE          3
            2464 CALL                     3
            2472 CACHE
            2474 STORE_NAME              68 (MutableSet)

 749        2476 LOAD_NAME               68 (MutableSet)
            2478 STORE_SUBSCR
            2482 CACHE
            2484 CACHE
            2486 CACHE
            2488 CACHE
            2490 CACHE
            2492 CACHE
            2494 CACHE
            2496 CACHE
            2498 CACHE
            2500 LOAD_NAME               30 (set)
            2502 UNPACK_SEQUENCE          1
            2506 CALL                     1
            2514 CACHE
            2516 POP_TOP

 754        2518 PUSH_NULL
            2520 LOAD_BUILD_CLASS
            2522 LOAD_CONST              54 (<code object Mapping at 0x000001E77EBF4930, file "_collections_abc.py", line 754>)
            2524 MAKE_FUNCTION            0
            2526 LOAD_CONST              55 ('Mapping')
            2528 LOAD_NAME               61 (Collection)
            2530 UNPACK_SEQUENCE          3
            2534 CALL                     3
            2542 CACHE
            2544 STORE_NAME              69 (Mapping)

 805        2546 LOAD_NAME               69 (Mapping)
            2548 STORE_SUBSCR
            2552 CACHE
            2554 CACHE
            2556 CACHE
            2558 CACHE
            2560 CACHE
            2562 CACHE
            2564 CACHE
            2566 CACHE
            2568 CACHE
            2570 LOAD_NAME               40 (mappingproxy)
            2572 UNPACK_SEQUENCE          1
            2576 CALL                     1
            2584 CACHE
            2586 POP_TOP

 808        2588 PUSH_NULL
            2590 LOAD_BUILD_CLASS
            2592 LOAD_CONST              56 (<code object MappingView at 0x000001E77EBC2730, file "_collections_abc.py", line 808>)
            2594 MAKE_FUNCTION            0
            2596 LOAD_CONST              57 ('MappingView')
            2598 LOAD_NAME               59 (Sized)
            2600 UNPACK_SEQUENCE          3
            2604 CALL                     3
            2612 CACHE
            2614 STORE_NAME              70 (MappingView)

 824        2616 PUSH_NULL
            2618 LOAD_BUILD_CLASS
            2620 LOAD_CONST              58 (<code object KeysView at 0x000001E77EBC2830, file "_collections_abc.py", line 824>)
            2622 MAKE_FUNCTION            0
            2624 LOAD_CONST              59 ('KeysView')
            2626 LOAD_NAME               70 (MappingView)
            2628 LOAD_NAME               66 (Set)
            2630 UNPACK_SEQUENCE          4
            2634 CALL                     4
            2642 CACHE
            2644 STORE_NAME              71 (KeysView)

 839        2646 LOAD_NAME               71 (KeysView)
            2648 STORE_SUBSCR
            2652 CACHE
            2654 CACHE
            2656 CACHE
            2658 CACHE
            2660 CACHE
            2662 CACHE
            2664 CACHE
            2666 CACHE
            2668 CACHE
            2670 LOAD_NAME               36 (dict_keys)
            2672 UNPACK_SEQUENCE          1
            2676 CALL                     1
            2684 CACHE
            2686 POP_TOP

 842        2688 PUSH_NULL
            2690 LOAD_BUILD_CLASS
            2692 LOAD_CONST              60 (<code object ItemsView at 0x000001E77EBC2A30, file "_collections_abc.py", line 842>)
            2694 MAKE_FUNCTION            0
            2696 LOAD_CONST              61 ('ItemsView')
            2698 LOAD_NAME               70 (MappingView)
            2700 LOAD_NAME               66 (Set)
            2702 UNPACK_SEQUENCE          4
            2706 CALL                     4
            2714 CACHE
            2716 STORE_NAME              72 (ItemsView)

 864        2718 LOAD_NAME               72 (ItemsView)
            2720 STORE_SUBSCR
            2724 CACHE
            2726 CACHE
            2728 CACHE
            2730 CACHE
            2732 CACHE
            2734 CACHE
            2736 CACHE
            2738 CACHE
            2740 CACHE
            2742 LOAD_NAME               38 (dict_items)
            2744 UNPACK_SEQUENCE          1
            2748 CALL                     1
            2756 CACHE
            2758 POP_TOP

 867        2760 PUSH_NULL
            2762 LOAD_BUILD_CLASS
            2764 LOAD_CONST              62 (<code object ValuesView at 0x000001E77EBD91B0, file "_collections_abc.py", line 867>)
            2766 MAKE_FUNCTION            0
            2768 LOAD_CONST              63 ('ValuesView')
            2770 LOAD_NAME               70 (MappingView)
            2772 LOAD_NAME               61 (Collection)
            2774 UNPACK_SEQUENCE          4
            2778 CALL                     4
            2786 CACHE
            2788 STORE_NAME              73 (ValuesView)

 883        2790 LOAD_NAME               73 (ValuesView)
            2792 STORE_SUBSCR
            2796 CACHE
            2798 CACHE
            2800 CACHE
            2802 CACHE
            2804 CACHE
            2806 CACHE
            2808 CACHE
            2810 CACHE
            2812 CACHE
            2814 LOAD_NAME               37 (dict_values)
            2816 UNPACK_SEQUENCE          1
            2820 CALL                     1
            2828 CACHE
            2830 POP_TOP

 886        2832 PUSH_NULL
            2834 LOAD_BUILD_CLASS
            2836 LOAD_CONST              64 (<code object MutableMapping at 0x000001E77E76A5B0, file "_collections_abc.py", line 886>)
            2838 MAKE_FUNCTION            0
            2840 LOAD_CONST              65 ('MutableMapping')
            2842 LOAD_NAME               69 (Mapping)
            2844 UNPACK_SEQUENCE          3
            2848 CALL                     3
            2856 CACHE
            2858 STORE_NAME              74 (MutableMapping)

 968        2860 LOAD_NAME               74 (MutableMapping)
            2862 STORE_SUBSCR
            2866 CACHE
            2868 CACHE
            2870 CACHE
            2872 CACHE
            2874 CACHE
            2876 CACHE
            2878 CACHE
            2880 CACHE
            2882 CACHE
            2884 LOAD_NAME               75 (dict)
            2886 UNPACK_SEQUENCE          1
            2890 CALL                     1
            2898 CACHE
            2900 POP_TOP

 973        2902 PUSH_NULL
            2904 LOAD_BUILD_CLASS
            2906 LOAD_CONST              66 (<code object Sequence at 0x000001E77EBF8BE0, file "_collections_abc.py", line 973>)
            2908 MAKE_FUNCTION            0
            2910 LOAD_CONST              67 ('Sequence')
            2912 LOAD_NAME               57 (Reversible)
            2914 LOAD_NAME               61 (Collection)
            2916 UNPACK_SEQUENCE          4
            2920 CALL                     4
            2928 CACHE
            2930 STORE_NAME              76 (Sequence)

1036        2932 LOAD_NAME               76 (Sequence)
            2934 STORE_SUBSCR
            2938 CACHE
            2940 CACHE
            2942 CACHE
            2944 CACHE
            2946 CACHE
            2948 CACHE
            2950 CACHE
            2952 CACHE
            2954 CACHE
            2956 LOAD_NAME               77 (tuple)
            2958 UNPACK_SEQUENCE          1
            2962 CALL                     1
            2970 CACHE
            2972 POP_TOP

1037        2974 LOAD_NAME               76 (Sequence)
            2976 STORE_SUBSCR
            2980 CACHE
            2982 CACHE
            2984 CACHE
            2986 CACHE
            2988 CACHE
            2990 CACHE
            2992 CACHE
            2994 CACHE
            2996 CACHE
            2998 LOAD_NAME               78 (str)
            3000 UNPACK_SEQUENCE          1
            3004 CALL                     1
            3012 CACHE
            3014 POP_TOP

1038        3016 LOAD_NAME               76 (Sequence)
            3018 STORE_SUBSCR
            3022 CACHE
            3024 CACHE
            3026 CACHE
            3028 CACHE
            3030 CACHE
            3032 CACHE
            3034 CACHE
            3036 CACHE
            3038 CACHE
            3040 LOAD_NAME               27 (range)
            3042 UNPACK_SEQUENCE          1
            3046 CALL                     1
            3054 CACHE
            3056 POP_TOP

1039        3058 LOAD_NAME               76 (Sequence)
            3060 STORE_SUBSCR
            3064 CACHE
            3066 CACHE
            3068 CACHE
            3070 CACHE
            3072 CACHE
            3074 CACHE
            3076 CACHE
            3078 CACHE
            3080 CACHE
            3082 LOAD_NAME               79 (memoryview)
            3084 UNPACK_SEQUENCE          1
            3088 CALL                     1
            3096 CACHE
            3098 POP_TOP

1042        3100 PUSH_NULL
            3102 LOAD_BUILD_CLASS
            3104 LOAD_CONST              68 (<code object ByteString at 0x000001E77EBD9370, file "_collections_abc.py", line 1042>)
            3106 MAKE_FUNCTION            0
            3108 LOAD_CONST              69 ('ByteString')
            3110 LOAD_NAME               76 (Sequence)
            3112 UNPACK_SEQUENCE          3
            3116 CALL                     3
            3124 CACHE
            3126 STORE_NAME              80 (ByteString)

1050        3128 LOAD_NAME               80 (ByteString)
            3130 STORE_SUBSCR
            3134 CACHE
            3136 CACHE
            3138 CACHE
            3140 CACHE
            3142 CACHE
            3144 CACHE
            3146 CACHE
            3148 CACHE
            3150 CACHE
            3152 LOAD_NAME               81 (bytes)
            3154 UNPACK_SEQUENCE          1
            3158 CALL                     1
            3166 CACHE
            3168 POP_TOP

1051        3170 LOAD_NAME               80 (ByteString)
            3172 STORE_SUBSCR
            3176 CACHE
            3178 CACHE
            3180 CACHE
            3182 CACHE
            3184 CACHE
            3186 CACHE
            3188 CACHE
            3190 CACHE
            3192 CACHE
            3194 LOAD_NAME               16 (bytearray)
            3196 UNPACK_SEQUENCE          1
            3200 CALL                     1
            3208 CACHE
            3210 POP_TOP

1054        3212 PUSH_NULL
            3214 LOAD_BUILD_CLASS
            3216 LOAD_CONST              70 (<code object MutableSequence at 0x000001E77E6D6100, file "_collections_abc.py", line 1054>)
            3218 MAKE_FUNCTION            0
            3220 LOAD_CONST              71 ('MutableSequence')
            3222 LOAD_NAME               76 (Sequence)
            3224 UNPACK_SEQUENCE          3
            3228 CALL                     3
            3236 CACHE
            3238 STORE_NAME              82 (MutableSequence)

1120        3240 LOAD_NAME               82 (MutableSequence)
            3242 STORE_SUBSCR
            3246 CACHE
            3248 CACHE
            3250 CACHE
            3252 CACHE
            3254 CACHE
            3256 CACHE
            3258 CACHE
            3260 CACHE
            3262 CACHE
            3264 LOAD_NAME                6 (list)
            3266 UNPACK_SEQUENCE          1
            3270 CALL                     1
            3278 CACHE
            3280 POP_TOP

1121        3282 LOAD_NAME               82 (MutableSequence)
            3284 STORE_SUBSCR
            3288 CACHE
            3290 CACHE
            3292 CACHE
            3294 CACHE
            3296 CACHE
            3298 CACHE
            3300 CACHE
            3302 CACHE
            3304 CACHE
            3306 LOAD_NAME               16 (bytearray)
            3308 UNPACK_SEQUENCE          1
            3312 CALL                     1
            3320 CACHE
            3322 POP_TOP
            3324 LOAD_CONST               3 (None)
            3326 RETURN_VALUE

Disassembly of <code object _f at 0x000001E77EBC56F0, file "_collections_abc.py", line 14>:
 14           0 RESUME                   0
              2 LOAD_CONST               0 (None)
              4 RETURN_VALUE

Disassembly of <code object <lambda> at 0x000001E77EBC57C0, file "_collections_abc.py", line 62>:
 62           0 RETURN_GENERATOR
              2 POP_TOP
              4 RESUME                   0
              6 LOAD_CONST               0 (None)
