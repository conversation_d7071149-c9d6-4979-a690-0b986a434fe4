# Code object from position 7249883
# Filename: _aix_support.py
# Name: <module>
# Args: 0
# Locals: 0

  0           0 RESUME                   0

  1           2 LOAD_CONST               0 ('Shared AIX support functions.')
              4 STORE_NAME               0 (__doc__)

  3           6 LOAD_CONST               1 (0)
              8 LOAD_CONST               2 (None)
             10 IMPORT_NAME              1 (sys)
             12 STORE_NAME               1 (sys)

  4          14 LOAD_CONST               1 (0)
             16 LOAD_CONST               2 (None)
             18 IMPORT_NAME              2 (sysconfig)
             20 STORE_NAME               2 (sysconfig)

  6          22 NOP

  7          24 LOAD_CONST               1 (0)
             26 LOAD_CONST               2 (None)
             28 IMPORT_NAME              3 (subprocess)
             30 STORE_NAME               3 (subprocess)
             32 JUMP_FORWARD            15 (to 64)
        >>   34 PUSH_EXC_INFO

  8          36 LOAD_NAME                4 (ImportError)
             38 CHECK_EXC_MATCH
             40 POP_JUMP_IF_FALSE        7 (to 56)
             42 POP_TOP

 11          44 LOAD_CONST               1 (0)
             46 LOAD_CONST               2 (None)
             48 IMPORT_NAME              5 (_bootsubprocess)
             50 STORE_NAME               3 (subprocess)
             52 POP_EXCEPT
             54 JUMP_FORWARD             4 (to 64)

  8     >>   56 RERAISE                  0
        >>   58 COPY                     3
             60 POP_EXCEPT
             62 RERAISE                  1

 14     >>   64 LOAD_CONST               3 (<code object _aix_tag at 0x000001A2D0111210, file "_aix_support.py", line 14>)
             66 MAKE_FUNCTION            0
             68 STORE_NAME               6 (_aix_tag)

 24          70 LOAD_CONST               4 (<code object _aix_vrtl at 0x000001A2D01CCD20, file "_aix_support.py", line 24>)
             72 MAKE_FUNCTION            0
             74 STORE_NAME               7 (_aix_vrtl)

 30          76 LOAD_CONST               5 (<code object _aix_bos_rte at 0x000001A2D0159A70, file "_aix_support.py", line 30>)
             78 MAKE_FUNCTION            0
             80 STORE_NAME               8 (_aix_bos_rte)

 46          82 LOAD_CONST               6 (<code object aix_platform at 0x000001A2D0135E90, file "_aix_support.py", line 46>)
             84 MAKE_FUNCTION            0
             86 STORE_NAME               9 (aix_platform)

 70          88 LOAD_CONST               7 (<code object _aix_bgt at 0x000001A2D019D6C0, file "_aix_support.py", line 70>)
             90 MAKE_FUNCTION            0
             92 STORE_NAME              10 (_aix_bgt)

 78          94 LOAD_CONST               8 (<code object aix_buildtag at 0x000001A2D010C9F0, file "_aix_support.py", line 78>)
             96 MAKE_FUNCTION            0
             98 STORE_NAME              11 (aix_buildtag)
            100 LOAD_CONST               2 (None)
            102 RETURN_VALUE
ExceptionTable:
  24 to 30 -> 34 [0]
  34 to 50 -> 58 [1] lasti
  56 to 56 -> 58 [1] lasti

Disassembly of <code object _aix_tag at 0x000001A2D0111210, file "_aix_support.py", line 14>:
 14           0 RESUME                   0

 17           2 LOAD_GLOBAL              0 (sys)
             12 CACHE
             14 LOAD_ATTR                1 (NULL|self + sys)
             34 LOAD_CONST               2 (32)
             36 JUMP_FORWARD             1 (to 40)
             38 LOAD_CONST               3 (64)
        >>   40 STORE_FAST               2 (_sz)

 18          42 LOAD_FAST                1 (bd)
             44 LOAD_CONST               4 (0)
             46 COMPARE_OP               3 (<)
             50 CACHE
             52 POP_JUMP_IF_FALSE        2 (to 58)
             54 LOAD_FAST                1 (bd)
             56 JUMP_FORWARD             1 (to 60)
        >>   58 LOAD_CONST               5 (9988)
        >>   60 STORE_FAST               3 (_bd)

 20          62 LOAD_CONST               6 ('aix-{:1x}{:1d}{:02d}-{:04d}-{}')
             64 STORE_SUBSCR
             68 CACHE
             70 CACHE
             72 CACHE
             74 CACHE
             76 CACHE
             78 CACHE
             80 CACHE
             82 CACHE
             84 CACHE
             86 LOAD_FAST                0 (vrtl)
             88 LOAD_CONST               4 (0)
             90 BINARY_SUBSCR
             94 CACHE
             96 CACHE
             98 CACHE
            100 LOAD_FAST                0 (vrtl)
            102 LOAD_CONST               7 (1)
            104 BINARY_SUBSCR
            108 CACHE
            110 CACHE
            112 CACHE
            114 LOAD_FAST                0 (vrtl)
            116 LOAD_CONST               8 (2)
            118 BINARY_SUBSCR
            122 CACHE
            124 CACHE
            126 CACHE
            128 LOAD_FAST                3 (_bd)
            130 LOAD_FAST                2 (_sz)
            132 UNPACK_SEQUENCE          5
            136 CALL                     5
            144 CACHE
            146 RETURN_VALUE

Disassembly of <code object _aix_vrtl at 0x000001A2D01CCD20, file "_aix_support.py", line 24>:
 24           0 RESUME                   0

 26           2 LOAD_FAST                0 (vrmf)
              4 STORE_SUBSCR
              8 CACHE
             10 CACHE
             12 CACHE
             14 CACHE
             16 CACHE
             18 CACHE
             20 CACHE
             22 CACHE
             24 CACHE
             26 LOAD_CONST               1 ('.')
             28 UNPACK_SEQUENCE          1
             32 CALL                     1
             40 CACHE
             42 LOAD_CONST               0 (None)
             44 LOAD_CONST               2 (3)
             46 BUILD_SLICE              2
             48 BINARY_SUBSCR
             52 CACHE
             54 CACHE
             56 CACHE
             58 UNPACK_SEQUENCE          3
             62 STORE_FAST               1 (v)
             64 STORE_FAST               2 (r)
             66 STORE_FAST               3 (tl)

 27          68 LOAD_GLOBAL              3 (NULL + int)
             78 CACHE
             80 LOAD_FAST                1 (v)
             82 LOAD_CONST               3 (-1)
             84 BINARY_SUBSCR
             88 CACHE
             90 CACHE
             92 CACHE
             94 UNPACK_SEQUENCE          1
             98 CALL                     1
            106 CACHE
            108 LOAD_GLOBAL              3 (NULL + int)
            118 CACHE
            120 LOAD_FAST                2 (r)
            122 UNPACK_SEQUENCE          1
            126 CALL                     1
            134 CACHE
            136 LOAD_GLOBAL              3 (NULL + int)
            146 CACHE
            148 LOAD_FAST                3 (tl)
            150 UNPACK_SEQUENCE          1
            154 CALL                     1
            162 CACHE
            164 BUILD_LIST               3
            166 RETURN_VALUE

Disassembly of <code object _aix_bos_rte at 0x000001A2D0159A70, file "_aix_support.py", line 30>:
 30           0 RESUME                   0

 39           2 LOAD_GLOBAL              1 (NULL + subprocess)
             12 CACHE
             14 LOAD_ATTR                1 (NULL|self + subprocess)
             34 CALL                     1
             42 CACHE
             44 STORE_FAST               0 (out)

 40          46 LOAD_FAST                0 (out)
             48 STORE_SUBSCR
             52 CACHE
             54 CACHE
             56 CACHE
             58 CACHE
             60 CACHE
             62 CACHE
             64 CACHE
             66 CACHE
             68 CACHE
             70 LOAD_CONST               2 ('utf-8')
             72 UNPACK_SEQUENCE          1
             76 CALL                     1
             84 CACHE
             86 STORE_FAST               0 (out)

 41          88 LOAD_FAST                0 (out)
             90 STORE_SUBSCR
             94 CACHE
             96 CACHE
             98 CACHE
            100 CACHE
            102 CACHE
            104 CACHE
            106 CACHE
            108 CACHE
            110 CACHE
            112 UNPACK_SEQUENCE          0
            116 CALL                     0
            124 CACHE
            126 STORE_SUBSCR
            130 CACHE
            132 CACHE
            134 CACHE
            136 CACHE
            138 CACHE
            140 CACHE
            142 CACHE
            144 CACHE
            146 CACHE
            148 LOAD_CONST               3 (':')
            150 UNPACK_SEQUENCE          1
            154 CALL                     1
            162 CACHE
            164 STORE_FAST               0 (out)

 42         166 LOAD_FAST                0 (out)
            168 LOAD_CONST               4 (-1)
            170 BINARY_SUBSCR
            174 CACHE
            176 CACHE
            178 CACHE
            180 LOAD_CONST               5 ('')
            182 COMPARE_OP               3 (<)
            186 CACHE
            188 POP_JUMP_IF_FALSE       21 (to 232)
            190 LOAD_GLOBAL             11 (NULL + int)
            200 CACHE
            202 LOAD_FAST                0 (out)
            204 LOAD_CONST               4 (-1)
            206 BINARY_SUBSCR
            210 CACHE
            212 CACHE
            214 CACHE
            216 UNPACK_SEQUENCE          1
            220 CALL                     1
            228 CACHE
            230 JUMP_FORWARD             1 (to 234)
        >>  232 LOAD_CONST               6 (9988)
        >>  234 STORE_FAST               1 (_bd)

 43         236 LOAD_GLOBAL             13 (NULL + str)
            246 CACHE
            248 LOAD_FAST                0 (out)
            250 LOAD_CONST               7 (2)
            252 BINARY_SUBSCR
            256 CACHE
            258 CACHE
            260 CACHE
            262 UNPACK_SEQUENCE          1
            266 CALL                     1
            274 CACHE
            276 LOAD_FAST                1 (_bd)
            278 BUILD_TUPLE              2
            280 RETURN_VALUE

Disassembly of <code object aix_platform at 0x000001A2D0135E90, file "_aix_support.py", line 46>:
 46           0 RESUME                   0

 65           2 LOAD_GLOBAL              1 (NULL + _aix_bos_rte)
             12 CACHE
             14 UNPACK_SEQUENCE          0
             18 CALL                     0
             26 CACHE
             28 UNPACK_SEQUENCE          2
             32 STORE_FAST               0 (vrmf)
             34 STORE_FAST               1 (bd)

 66          36 LOAD_GLOBAL              3 (NULL + _aix_tag)
             46 CACHE
             48 LOAD_GLOBAL              5 (NULL + _aix_vrtl)
             58 CACHE
             60 LOAD_FAST                0 (vrmf)
             62 UNPACK_SEQUENCE          1
             66 CALL                     1
             74 CACHE
             76 LOAD_FAST                1 (bd)
             78 UNPACK_SEQUENCE          2
             82 CALL                     2
             90 CACHE
             92 RETURN_VALUE

Disassembly of <code object _aix_bgt at 0x000001A2D019D6C0, file "_aix_support.py", line 70>:
 70           0 RESUME                   0

 72           2 LOAD_GLOBAL              1 (NULL + sysconfig)
             12 CACHE
             14 LOAD_ATTR                1 (NULL|self + sysconfig)
             34 CACHE
             36 CACHE
             38 CACHE
             40 STORE_FAST               0 (gnu_type)

 73          42 LOAD_FAST                0 (gnu_type)
             44 POP_JUMP_IF_TRUE        15 (to 76)

 74          46 LOAD_GLOBAL              5 (NULL + ValueError)
             56 CACHE
             58 LOAD_CONST               2 ('BUILD_GNU_TYPE is not defined')
             60 UNPACK_SEQUENCE          1
             64 CALL                     1
             72 CACHE
             74 RAISE_VARARGS            1

 75     >>   76 LOAD_GLOBAL              7 (NULL + _aix_vrtl)
             86 CACHE
             88 LOAD_FAST                0 (gnu_type)
             90 KW_NAMES                 3 (('vrmf',))
             92 UNPACK_SEQUENCE          1
             96 CALL                     1
            104 CACHE
            106 RETURN_VALUE

Disassembly of <code object aix_buildtag at 0x000001A2D010C9F0, file "_aix_support.py", line 78>:
 78           0 RESUME                   0

 85           2 LOAD_GLOBAL              1 (NULL + sysconfig)
             12 CACHE
             14 LOAD_ATTR                1 (NULL|self + sysconfig)
             34 CACHE
             36 CACHE
             38 CACHE
             40 STORE_FAST               0 (build_date)

 86          42 NOP

 87          44 LOAD_GLOBAL              5 (NULL + int)
             54 CACHE
             56 LOAD_FAST                0 (build_date)
             58 UNPACK_SEQUENCE          1
             62 CALL                     1
             70 CACHE
             72 STORE_FAST               0 (build_date)
             74 JUMP_FORWARD            39 (to 154)
        >>   76 PUSH_EXC_INFO

 88          78 LOAD_GLOBAL              6 (ValueError)
             88 CACHE
             90 LOAD_GLOBAL              8 (TypeError)
            100 CACHE
            102 BUILD_TUPLE              2
            104 CHECK_EXC_MATCH
            106 POP_JUMP_IF_FALSE       19 (to 146)
            108 POP_TOP

 89         110 LOAD_GLOBAL              7 (NULL + ValueError)
            120 CACHE
            122 LOAD_CONST               2 ('AIX_BUILDDATE is not defined or invalid: ')

 90         124 LOAD_FAST                0 (build_date)

 89         126 FORMAT_VALUE             2 (repr)
            128 BUILD_STRING             2
            130 UNPACK_SEQUENCE          1
            134 CALL                     1
            142 CACHE
            144 RAISE_VARARGS            1

 88     >>  146 RERAISE                  0
        >>  148 COPY                     3
            150 POP_EXCEPT
            152 RERAISE                  1

 91     >>  154 LOAD_GLOBAL             11 (NULL + _aix_tag)
            164 CACHE
            166 LOAD_GLOBAL             13 (NULL + _aix_bgt)
            176 CACHE
            178 UNPACK_SEQUENCE          0
            182 CALL                     0
            190 CACHE
            192 LOAD_FAST                0 (build_date)
            194 UNPACK_SEQUENCE          2
            198 CALL                     2
            206 CACHE
            208 RETURN_VALUE
ExceptionTable:
  44 to 72 -> 76 [0]
  76 to 146 -> 148 [1] lasti


# Constants:
# 0: 'Shared AIX support functions.'
# 1: 0
# 2: None
# 3: code
# 4: code
# 5: code
# 6: code
# 7: code
# 8: code


# Names:
# 0: '__doc__'
# 1: 'sys'
# 2: 'sysconfig'
# 3: 'subprocess'
# 4: 'ImportError'
# 5: '_bootsubprocess'
# 6: '_aix_tag'
# 7: '_aix_vrtl'
# 8: '_aix_bos_rte'
# 9: 'aix_platform'
# 10: '_aix_bgt'
# 11: 'aix_buildtag'


# Variable names:
