# MAIN APPLICATION CODE OBJECT
# Position: 8067774
# Filename: calendar.py
# Function: <module>
# Args: 0
# Locals: 0
# Stack size: 7
# Flags: 0

# BYTECODE DISASSEMBLY:
==================================================
  0           0 RESUME                   0

  1           2 LOAD_CONST               0 ('Calendar printing functions\n\nNote when comparing these calendars to the ones printed by cal(1): By\ndefault, these calendars have Monday as the first day of the week, and\nSunday as the last (the European convention). Use setfirstweekday() to\nset the first day of the week (0=Monday, 6=Sunday).')
              4 STORE_NAME               0 (__doc__)

  8           6 LOAD_CONST               1 (0)
              8 LOAD_CONST               2 (None)
             10 IMPORT_NAME              1 (sys)
             12 STORE_NAME               1 (sys)

  9          14 LOAD_CONST               1 (0)
             16 LOAD_CONST               2 (None)
             18 IMPORT_NAME              2 (datetime)
             20 STORE_NAME               2 (datetime)

 10          22 LOAD_CONST               1 (0)
             24 LOAD_CONST               2 (None)
             26 IMPORT_NAME              3 (locale)
             28 STORE_NAME               4 (_locale)

 11          30 LOAD_CONST               1 (0)
             32 LOAD_CONST               3 (('repeat',))
             34 IMPORT_NAME              5 (itertools)
             36 IMPORT_FROM              6 (repeat)
             38 STORE_NAME               6 (repeat)
             40 POP_TOP

 13          42 BUILD_LIST               0
             44 LOAD_CONST               4 (('IllegalMonthError', 'IllegalWeekdayError', 'setfirstweekday', 'firstweekday', 'isleap', 'leapdays', 'weekday', 'monthrange', 'monthcalendar', 'prmonth', 'month', 'prcal', 'calendar', 'timegm', 'month_name', 'month_abbr', 'day_name', 'day_abbr', 'Calendar', 'TextCalendar', 'HTMLCalendar', 'LocaleTextCalendar', 'LocaleHTMLCalendar', 'weekheader', 'MONDAY', 'TUESDAY', 'WEDNESDAY', 'THURSDAY', 'FRIDAY', 'SATURDAY', 'SUNDAY'))
             46 LIST_EXTEND              1
             48 STORE_NAME               7 (__all__)

 23          50 LOAD_NAME                8 (ValueError)
             52 STORE_NAME               9 (error)

 26          54 PUSH_NULL
             56 LOAD_BUILD_CLASS
             58 LOAD_CONST               5 (<code object IllegalMonthError at 0x000001E77ECD0D50, file "calendar.py", line 26>)
             60 MAKE_FUNCTION            0
             62 LOAD_CONST               6 ('IllegalMonthError')
             64 LOAD_NAME                8 (ValueError)
             66 UNPACK_SEQUENCE          3
             70 CALL                     3
             78 CACHE
             80 STORE_NAME              10 (IllegalMonthError)

 33          82 PUSH_NULL
             84 LOAD_BUILD_CLASS
             86 LOAD_CONST               7 (<code object IllegalWeekdayError at 0x000001E77ECD0FF0, file "calendar.py", line 33>)
             88 MAKE_FUNCTION            0
             90 LOAD_CONST               8 ('IllegalWeekdayError')
             92 LOAD_NAME                8 (ValueError)
             94 UNPACK_SEQUENCE          3
             98 CALL                     3
            106 CACHE
            108 STORE_NAME              11 (IllegalWeekdayError)

 41         110 LOAD_CONST               9 (1)
            112 STORE_NAME              12 (January)

 42         114 LOAD_CONST              10 (2)
            116 STORE_NAME              13 (February)

 45         118 BUILD_LIST               0
            120 LOAD_CONST              11 ((0, 31, 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31))
            122 LIST_EXTEND              1
            124 STORE_NAME              14 (mdays)

 52         126 PUSH_NULL
            128 LOAD_BUILD_CLASS
            130 LOAD_CONST              12 (<code object _localized_month at 0x000001E77ECEC7B0, file "calendar.py", line 52>)
            132 MAKE_FUNCTION            0
            134 LOAD_CONST              13 ('_localized_month')
            136 UNPACK_SEQUENCE          2
            140 CALL                     2
            148 CACHE
            150 STORE_NAME              15 (_localized_month)

 71         152 PUSH_NULL
            154 LOAD_BUILD_CLASS
            156 LOAD_CONST              14 (<code object _localized_day at 0x000001E77ECF08B0, file "calendar.py", line 71>)
            158 MAKE_FUNCTION            0
            160 LOAD_CONST              15 ('_localized_day')
            162 UNPACK_SEQUENCE          2
            166 CALL                     2
            174 CACHE
            176 STORE_NAME              16 (_localized_day)

 91         178 PUSH_NULL
            180 LOAD_NAME               16 (_localized_day)
            182 LOAD_CONST              16 ('%A')
            184 UNPACK_SEQUENCE          1
            188 CALL                     1
            196 CACHE
            198 STORE_NAME              17 (day_name)

 92         200 PUSH_NULL
            202 LOAD_NAME               16 (_localized_day)
            204 LOAD_CONST              17 ('%a')
            206 UNPACK_SEQUENCE          1
            210 CALL                     1
            218 CACHE
            220 STORE_NAME              18 (day_abbr)

 95         222 PUSH_NULL
            224 LOAD_NAME               15 (_localized_month)
            226 LOAD_CONST              18 ('%B')
            228 UNPACK_SEQUENCE          1
            232 CALL                     1
            240 CACHE
            242 STORE_NAME              19 (month_name)

 96         244 PUSH_NULL
            246 LOAD_NAME               15 (_localized_month)
            248 LOAD_CONST              19 ('%b')
            250 UNPACK_SEQUENCE          1
            254 CALL                     1
            262 CACHE
            264 STORE_NAME              20 (month_abbr)

 99         266 PUSH_NULL
            268 LOAD_NAME               21 (range)
            270 LOAD_CONST              20 (7)
            272 UNPACK_SEQUENCE          1
            276 CALL                     1
            284 CACHE
            286 UNPACK_SEQUENCE          7
            290 STORE_NAME              22 (MONDAY)
            292 STORE_NAME              23 (TUESDAY)
            294 STORE_NAME              24 (WEDNESDAY)
            296 STORE_NAME              25 (THURSDAY)
            298 STORE_NAME              26 (FRIDAY)
            300 STORE_NAME              27 (SATURDAY)
            302 STORE_NAME              28 (SUNDAY)

102         304 LOAD_CONST              21 (<code object isleap at 0x000001E77ECD8330, file "calendar.py", line 102>)
            306 MAKE_FUNCTION            0
            308 STORE_NAME              29 (isleap)

107         310 LOAD_CONST              22 (<code object leapdays at 0x000001E77ECCD350, file "calendar.py", line 107>)
            312 MAKE_FUNCTION            0
            314 STORE_NAME              30 (leapdays)

115         316 LOAD_CONST              23 (<code object weekday at 0x000001E77ECB52E0, file "calendar.py", line 115>)
            318 MAKE_FUNCTION            0
            320 STORE_NAME              31 (weekday)

122         322 LOAD_CONST              24 (<code object monthrange at 0x000001E77ECB8AB0, file "calendar.py", line 122>)
            324 MAKE_FUNCTION            0
            326 STORE_NAME              32 (monthrange)

132         328 LOAD_CONST              25 (<code object _monthlen at 0x000001E77ECCD470, file "calendar.py", line 132>)
            330 MAKE_FUNCTION            0
            332 STORE_NAME              33 (_monthlen)

136         334 LOAD_CONST              26 (<code object _prevmonth at 0x000001E77ECF4A80, file "calendar.py", line 136>)
            336 MAKE_FUNCTION            0
            338 STORE_NAME              34 (_prevmonth)

143         340 LOAD_CONST              27 (<code object _nextmonth at 0x000001E77ECF4B70, file "calendar.py", line 143>)
            342 MAKE_FUNCTION            0
            344 STORE_NAME              35 (_nextmonth)

150         346 PUSH_NULL
            348 LOAD_BUILD_CLASS
            350 LOAD_CONST              28 (<code object Calendar at 0x000001E77EC4F8A0, file "calendar.py", line 150>)
            352 MAKE_FUNCTION            0
            354 LOAD_CONST              29 ('Calendar')
            356 LOAD_NAME               36 (object)
            358 UNPACK_SEQUENCE          3
            362 CALL                     3
            370 CACHE
            372 STORE_NAME              37 (Calendar)

295         374 PUSH_NULL
            376 LOAD_BUILD_CLASS
            378 LOAD_CONST              30 (<code object TextCalendar at 0x000001E77ECCDC50, file "calendar.py", line 295>)
            380 MAKE_FUNCTION            0
            382 LOAD_CONST              31 ('TextCalendar')
            384 LOAD_NAME               37 (Calendar)
            386 UNPACK_SEQUENCE          3
            390 CALL                     3
            398 CACHE
            400 STORE_NAME              38 (TextCalendar)

412         402 PUSH_NULL
            404 LOAD_BUILD_CLASS
            406 LOAD_CONST              32 (<code object HTMLCalendar at 0x000001E77ECBD6C0, file "calendar.py", line 412>)
            408 MAKE_FUNCTION            0
            410 LOAD_CONST              33 ('HTMLCalendar')
            412 LOAD_NAME               37 (Calendar)
            414 UNPACK_SEQUENCE          3
            418 CALL                     3
            426 CACHE
            428 STORE_NAME              39 (HTMLCalendar)

548         430 PUSH_NULL
            432 LOAD_BUILD_CLASS
            434 LOAD_CONST              34 (<code object different_locale at 0x000001E77ECD1C30, file "calendar.py", line 548>)
            436 MAKE_FUNCTION            0
            438 LOAD_CONST              35 ('different_locale')
            440 UNPACK_SEQUENCE          2
            444 CALL                     2
            452 CACHE
            454 STORE_NAME              40 (different_locale)

563         456 LOAD_CONST              36 (<code object _get_default_locale at 0x000001E77ECAC850, file "calendar.py", line 563>)
            458 MAKE_FUNCTION            0
            460 STORE_NAME              41 (_get_default_locale)

573         462 PUSH_NULL
            464 LOAD_BUILD_CLASS
            466 LOAD_CONST              37 (<code object LocaleTextCalendar at 0x000001E77ECD9430, file "calendar.py", line 573>)
            468 MAKE_FUNCTION            0
            470 LOAD_CONST              38 ('LocaleTextCalendar')
            472 LOAD_NAME               38 (TextCalendar)
            474 UNPACK_SEQUENCE          3
            478 CALL                     3
            486 CACHE
            488 STORE_NAME              42 (LocaleTextCalendar)

594         490 PUSH_NULL
            492 LOAD_BUILD_CLASS
            494 LOAD_CONST              39 (<code object LocaleHTMLCalendar at 0x000001E77ECD9530, file "calendar.py", line 594>)
            496 MAKE_FUNCTION            0
            498 LOAD_CONST              40 ('LocaleHTMLCalendar')
            500 LOAD_NAME               39 (HTMLCalendar)
            502 UNPACK_SEQUENCE          3
            506 CALL                     3
            514 CACHE
            516 STORE_NAME              43 (LocaleHTMLCalendar)

614         518 PUSH_NULL
            520 LOAD_NAME               38 (TextCalendar)
            522 UNPACK_SEQUENCE          0
            526 CALL                     0
            534 CACHE
            536 STORE_NAME              44 (c)

616         538 LOAD_NAME               44 (c)
            540 LOAD_ATTR               45 (NULL|self + MONDAY)
            560 LOAD_ATTR               48 (WEDNESDAY)
            580 CACHE
            582 CACHE
            584 STORE_NAME              50 (prweek)

625         586 LOAD_NAME               44 (c)
            588 LOAD_ATTR               51 (NULL|self + THURSDAY)
            608 CACHE
            610 CACHE
            612 STORE_NAME              54 (weekheader)

627         614 LOAD_NAME               44 (c)
            616 LOAD_ATTR               55 (NULL|self + SATURDAY)
            636 CACHE
            638 CACHE
            640 STORE_NAME              57 (month)

629         642 LOAD_NAME               44 (c)
            644 LOAD_ATTR               58 (isleap)
            664 CACHE
            666 CACHE
            668 STORE_NAME              61 (prcal)

634         670 LOAD_CONST              42 (20)
            672 STORE_NAME              62 (_colwidth)

635         674 LOAD_CONST              43 (6)
            676 STORE_NAME              63 (_spacing)

638         678 LOAD_NAME               62 (_colwidth)
            680 LOAD_NAME               63 (_spacing)
            682 BUILD_TUPLE              2
            684 LOAD_CONST              44 (<code object format at 0x000001E77ECF1680, file "calendar.py", line 638>)
            686 MAKE_FUNCTION            1 (defaults)
            688 STORE_NAME              64 (format)

643         690 LOAD_NAME               62 (_colwidth)
            692 LOAD_NAME               63 (_spacing)
            694 BUILD_TUPLE              2
            696 LOAD_CONST              45 (<code object formatstring at 0x000001E77ECF18A0, file "calendar.py", line 643>)
            698 MAKE_FUNCTION            1 (defaults)
            700 STORE_NAME              65 (formatstring)

649         702 LOAD_CONST              46 (1970)
            704 STORE_NAME              66 (EPOCH)

650         706 PUSH_NULL
            708 LOAD_NAME                2 (datetime)
            710 LOAD_ATTR               67 (NULL|self + _monthlen)
            730 CALL                     3
            738 CACHE
            740 STORE_SUBSCR
            744 CACHE
            746 CACHE
            748 CACHE
            750 CACHE
            752 CACHE
            754 CACHE
            756 CACHE
            758 CACHE
            760 CACHE
            762 UNPACK_SEQUENCE          0
            766 CALL                     0
            774 CACHE
            776 STORE_NAME              69 (_EPOCH_ORD)

653         778 LOAD_CONST              47 (<code object timegm at 0x000001E77EC725B0, file "calendar.py", line 653>)
            780 MAKE_FUNCTION            0
            782 STORE_NAME              70 (timegm)

663         784 LOAD_CONST              48 (<code object main at 0x000001E77EF249F0, file "calendar.py", line 663>)
            786 MAKE_FUNCTION            0
            788 STORE_NAME              71 (main)

767         790 LOAD_NAME               72 (__name__)
            792 LOAD_CONST              49 ('__main__')
            794 COMPARE_OP               2 (<)
            798 CACHE
            800 POP_JUMP_IF_FALSE       18 (to 838)

768         802 PUSH_NULL
            804 LOAD_NAME               71 (main)
            806 LOAD_NAME                1 (sys)
            808 LOAD_ATTR               73 (NULL|self + object)
            828 CACHE
            830 CACHE
            832 POP_TOP
            834 LOAD_CONST               2 (None)
            836 RETURN_VALUE

767     >>  838 LOAD_CONST               2 (None)
            840 RETURN_VALUE

Disassembly of <code object IllegalMonthError at 0x000001E77ECD0D50, file "calendar.py", line 26>:
 26           0 RESUME                   0
              2 LOAD_NAME                0 (__name__)
              4 STORE_NAME               1 (__module__)
              6 LOAD_CONST               0 ('IllegalMonthError')
              8 STORE_NAME               2 (__qualname__)

 27          10 LOAD_CONST               1 (<code object __init__ at 0x000001E77ECD0B90, file "calendar.py", line 27>)
             12 MAKE_FUNCTION            0
             14 STORE_NAME               3 (__init__)

 29          16 LOAD_CONST               2 (<code object __str__ at 0x000001E77ECD0C70, file "calendar.py", line 29>)
             18 MAKE_FUNCTION            0
             20 STORE_NAME               4 (__str__)
             22 LOAD_CONST               3 (None)
             24 RETURN_VALUE

Disassembly of <code object __init__ at 0x000001E77ECD0B90, file "calendar.py", line 27>:
 27           0 RESUME                   0

 28           2 LOAD_FAST                1 (month)
              4 LOAD_FAST                0 (self)
              6 STORE_ATTR               0 (month)
             16 LOAD_CONST               0 (None)
             18 RETURN_VALUE

Disassembly of <code object __str__ at 0x000001E77ECD0C70, file "calendar.py", line 29>:
 29           0 RESUME                   0

 30           2 LOAD_CONST               1 ('bad month number %r; must be 1-12')
              4 LOAD_FAST                0 (self)
              6 LOAD_ATTR                0 (month)

Disassembly of <code object IllegalWeekdayError at 0x000001E77ECD0FF0, file "calendar.py", line 33>:
 33           0 RESUME                   0
              2 LOAD_NAME                0 (__name__)
              4 STORE_NAME               1 (__module__)
              6 LOAD_CONST               0 ('IllegalWeekdayError')
              8 STORE_NAME               2 (__qualname__)

 34          10 LOAD_CONST               1 (<code object __init__ at 0x000001E77ECD0E30, file "calendar.py", line 34>)
             12 MAKE_FUNCTION            0
             14 STORE_NAME               3 (__init__)

 36          16 LOAD_CONST               2 (<code object __str__ at 0x000001E77ECD0F10, file "calendar.py", line 36>)
             18 MAKE_FUNCTION            0
             20 STORE_NAME               4 (__str__)
             22 LOAD_CONST               3 (None)
             24 RETURN_VALUE

Disassembly of <code object __init__ at 0x000001E77ECD0E30, file "calendar.py", line 34>:
 34           0 RESUME                   0

 35           2 LOAD_FAST                1 (weekday)
              4 LOAD_FAST                0 (self)
              6 STORE_ATTR               0 (weekday)
             16 LOAD_CONST               0 (None)
             18 RETURN_VALUE

Disassembly of <code object __str__ at 0x000001E77ECD0F10, file "calendar.py", line 36>:
 36           0 RESUME                   0

 37           2 LOAD_CONST               1 ('bad weekday number %r; must be 0 (Monday) to 6 (Sunday)')
              4 LOAD_FAST                0 (self)
              6 LOAD_ATTR                0 (weekday)

Disassembly of <code object _localized_month at 0x000001E77ECEC7B0, file "calendar.py", line 52>:
 52           0 RESUME                   0
              2 LOAD_NAME                0 (__name__)
              4 STORE_NAME               1 (__module__)
              6 LOAD_CONST               0 ('_localized_month')
              8 STORE_NAME               2 (__qualname__)

 54          10 LOAD_CONST               1 (<code object <listcomp> at 0x000001E77ECF0580, file "calendar.py", line 54>)
             12 MAKE_FUNCTION            0
             14 PUSH_NULL
             16 LOAD_NAME                3 (range)
             18 LOAD_CONST               2 (12)
             20 UNPACK_SEQUENCE          1
             24 CALL                     1
             32 CACHE
             34 GET_ITER
             36 UNPACK_SEQUENCE          0
             40 CALL                     0
             48 CACHE
             50 STORE_NAME               4 (_months)

 55          52 LOAD_NAME                4 (_months)
             54 STORE_SUBSCR
             58 CACHE
             60 CACHE
             62 CACHE
             64 CACHE
             66 CACHE
             68 CACHE
             70 CACHE
             72 CACHE
             74 CACHE
             76 LOAD_CONST               3 (0)
             78 LOAD_CONST               4 (<code object <lambda> at 0x000001E77EC22660, file "calendar.py", line 55>)
             80 MAKE_FUNCTION            0
             82 UNPACK_SEQUENCE          2
             86 CALL                     2
             94 CACHE
             96 POP_TOP

 57          98 LOAD_CONST               5 (<code object __init__ at 0x000001E77ECD10D0, file "calendar.py", line 57>)
            100 MAKE_FUNCTION            0
            102 STORE_NAME               6 (__init__)

 60         104 LOAD_CONST               6 (<code object __getitem__ at 0x000001E77EC4F360, file "calendar.py", line 60>)
            106 MAKE_FUNCTION            0
            108 STORE_NAME               7 (__getitem__)

 67         110 LOAD_CONST               7 (<code object __len__ at 0x000001E77EC235D0, file "calendar.py", line 67>)
            112 MAKE_FUNCTION            0
            114 STORE_NAME               8 (__len__)
            116 LOAD_CONST               8 (None)
            118 RETURN_VALUE

Disassembly of <code object <listcomp> at 0x000001E77ECF0580, file "calendar.py", line 54>:
 54           0 RESUME                   0
              2 BUILD_LIST               0
              4 LOAD_FAST                0 (.0)
              6 FOR_ITER                32 (to 74)
             10 LOAD_GLOBAL              1 (NULL + datetime)
             20 CACHE
             22 LOAD_ATTR                1 (NULL|self + datetime)
             42 LOAD_CONST               1 (1)
             44 UNPACK_SEQUENCE          3
             48 CALL                     3
             56 CACHE
             58 LOAD_ATTR                2 (date)

Disassembly of <code object <lambda> at 0x000001E77EC22660, file "calendar.py", line 55>:
 55           0 RESUME                   0
              2 LOAD_CONST               1 ('')
              4 RETURN_VALUE

Disassembly of <code object __init__ at 0x000001E77ECD10D0, file "calendar.py", line 57>:
 57           0 RESUME                   0

 58           2 LOAD_FAST                1 (format)
              4 LOAD_FAST                0 (self)
              6 STORE_ATTR               0 (format)
             16 LOAD_CONST               0 (None)
             18 RETURN_VALUE

Disassembly of <code object __getitem__ at 0x000001E77EC4F360, file "calendar.py", line 60>:
              0 MAKE_CELL                0 (self)

 60           2 RESUME                   0

 61           4 LOAD_DEREF               0 (self)
              6 LOAD_ATTR                0 (_months)
             26 CACHE
             28 STORE_FAST               2 (funcs)

 62          30 LOAD_GLOBAL              3 (NULL + isinstance)
             40 CACHE
             42 LOAD_FAST                1 (i)
             44 LOAD_GLOBAL              4 (slice)
             54 CACHE
             56 UNPACK_SEQUENCE          2
             60 CALL                     2
             68 CACHE
             70 POP_JUMP_IF_FALSE       14 (to 100)

 63          72 LOAD_CLOSURE             0 (self)
             74 BUILD_TUPLE              1
             76 LOAD_CONST               1 (<code object <listcomp> at 0x000001E77ECF48A0, file "calendar.py", line 63>)
             78 MAKE_FUNCTION            8 (closure)
             80 LOAD_FAST                2 (funcs)
             82 GET_ITER
             84 UNPACK_SEQUENCE          0
             88 CALL                     0
             96 CACHE
             98 RETURN_VALUE

 65     >>  100 PUSH_NULL
            102 LOAD_FAST                2 (funcs)
            104 LOAD_DEREF               0 (self)
            106 LOAD_ATTR                3 (NULL|self + isinstance)
            126 CACHE
            128 CACHE
            130 RETURN_VALUE

Disassembly of <code object <listcomp> at 0x000001E77ECF48A0, file "calendar.py", line 63>:
              0 COPY_FREE_VARS           1

 63           2 RESUME                   0
              4 BUILD_LIST               0
              6 LOAD_FAST                0 (.0)
        >>    8 FOR_ITER                18 (to 48)
             12 PUSH_NULL
             14 LOAD_FAST                1 (f)
             16 LOAD_DEREF               2 (self)
             18 LOAD_ATTR                0 (format)
             38 CACHE
             40 CACHE
             42 LIST_APPEND              2
             44 JUMP_BACKWARD           19 (to 8)
             46 RETURN_VALUE

Disassembly of <code object __len__ at 0x000001E77EC235D0, file "calendar.py", line 67>:
 67           0 RESUME                   0

 68           2 LOAD_CONST               1 (13)
              4 RETURN_VALUE

Disassembly of <code object _localized_day at 0x000001E77ECF08B0, file "calendar.py", line 71>:
 71           0 RESUME                   0
              2 LOAD_NAME                0 (__name__)
              4 STORE_NAME               1 (__module__)
              6 LOAD_CONST               0 ('_localized_day')
              8 STORE_NAME               2 (__qualname__)

 74          10 LOAD_CONST               1 (<code object <listcomp> at 0x000001E77ECF0690, file "calendar.py", line 74>)
             12 MAKE_FUNCTION            0
             14 PUSH_NULL
             16 LOAD_NAME                3 (range)
             18 LOAD_CONST               2 (7)
             20 UNPACK_SEQUENCE          1
             24 CALL                     1
             32 CACHE
             34 GET_ITER
             36 UNPACK_SEQUENCE          0
             40 CALL                     0
             48 CACHE
             50 STORE_NAME               4 (_days)

 76          52 LOAD_CONST               3 (<code object __init__ at 0x000001E77ECD11B0, file "calendar.py", line 76>)
             54 MAKE_FUNCTION            0
             56 STORE_NAME               5 (__init__)

 79          58 LOAD_CONST               4 (<code object __getitem__ at 0x000001E77EC4F4B0, file "calendar.py", line 79>)
             60 MAKE_FUNCTION            0
             62 STORE_NAME               6 (__getitem__)

 86          64 LOAD_CONST               5 (<code object __len__ at 0x000001E77EC236A0, file "calendar.py", line 86>)
             66 MAKE_FUNCTION            0
             68 STORE_NAME               7 (__len__)
             70 LOAD_CONST               6 (None)
             72 RETURN_VALUE

Disassembly of <code object <listcomp> at 0x000001E77ECF0690, file "calendar.py", line 74>:
 74           0 RESUME                   0
              2 BUILD_LIST               0
              4 LOAD_FAST                0 (.0)
              6 FOR_ITER                32 (to 74)
             10 LOAD_GLOBAL              1 (NULL + datetime)
             20 CACHE
             22 LOAD_ATTR                1 (NULL|self + datetime)
             42 CACHE
             44 UNPACK_SEQUENCE          3
             48 CALL                     3
             56 CACHE
             58 LOAD_ATTR                2 (date)

Disassembly of <code object __init__ at 0x000001E77ECD11B0, file "calendar.py", line 76>:
 76           0 RESUME                   0

 77           2 LOAD_FAST                1 (format)
              4 LOAD_FAST                0 (self)
              6 STORE_ATTR               0 (format)
             16 LOAD_CONST               0 (None)
             18 RETURN_VALUE

Disassembly of <code object __getitem__ at 0x000001E77EC4F4B0, file "calendar.py", line 79>:
              0 MAKE_CELL                0 (self)

 79           2 RESUME                   0

 80           4 LOAD_DEREF               0 (self)
              6 LOAD_ATTR                0 (_days)
             26 CACHE
             28 STORE_FAST               2 (funcs)

 81          30 LOAD_GLOBAL              3 (NULL + isinstance)
             40 CACHE
             42 LOAD_FAST                1 (i)
             44 LOAD_GLOBAL              4 (slice)
             54 CACHE
             56 UNPACK_SEQUENCE          2
             60 CALL                     2
             68 CACHE
             70 POP_JUMP_IF_FALSE       14 (to 100)

 82          72 LOAD_CLOSURE             0 (self)
             74 BUILD_TUPLE              1
             76 LOAD_CONST               1 (<code object <listcomp> at 0x000001E77ECF4990, file "calendar.py", line 82>)
             78 MAKE_FUNCTION            8 (closure)
             80 LOAD_FAST                2 (funcs)
             82 GET_ITER
             84 UNPACK_SEQUENCE          0
             88 CALL                     0
             96 CACHE
             98 RETURN_VALUE

 84     >>  100 PUSH_NULL
            102 LOAD_FAST                2 (funcs)
            104 LOAD_DEREF               0 (self)
            106 LOAD_ATTR                3 (NULL|self + isinstance)
            126 CACHE
            128 CACHE
            130 RETURN_VALUE

Disassembly of <code object <listcomp> at 0x000001E77ECF4990, file "calendar.py", line 82>:
              0 COPY_FREE_VARS           1

 82           2 RESUME                   0
              4 BUILD_LIST               0
              6 LOAD_FAST                0 (.0)
        >>    8 FOR_ITER                18 (to 48)
             12 PUSH_NULL
             14 LOAD_FAST                1 (f)
             16 LOAD_DEREF               2 (self)
             18 LOAD_ATTR                0 (format)
             38 CACHE
             40 CACHE
             42 LIST_APPEND              2
             44 JUMP_BACKWARD           19 (to 8)
             46 RETURN_VALUE

Disassembly of <code object __len__ at 0x000001E77EC236A0, file "calendar.py", line 86>:
 86           0 RESUME                   0

 87           2 LOAD_CONST               1 (7)
              4 RETURN_VALUE

Disassembly of <code object isleap at 0x000001E77ECD8330, file "calendar.py", line 102>:
102           0 RESUME                   0

104           2 LOAD_FAST                0 (year)
              4 LOAD_CONST               1 (4)
              6 BINARY_OP                6 (%)
             10 LOAD_CONST               2 (0)
             12 COMPARE_OP               2 (<)
             16 CACHE
