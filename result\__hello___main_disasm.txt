# MAIN APPLICATION CODE OBJECT
# Position: 7247821
# Filename: __hello__.py
# Function: <module>
# Args: 0
# Locals: 0
# Stack size: 4
# Flags: 0

# BYTECODE DISASSEMBLY:
==================================================
  0           0 RESUME                   0

  1           2 LOAD_CONST               0 (True)
              4 STORE_NAME               0 (initialized)

  3           6 PUSH_NULL
              8 LOAD_BUILD_CLASS
             10 LOAD_CONST               1 (<code object TestFrozenUtf8_1 at 0x000001E77EB66090, file "__hello__.py", line 3>)
             12 MAKE_FUNCTION            0
             14 LOAD_CONST               2 ('TestFrozenUtf8_1')
             16 UNPACK_SEQUENCE          2
             20 CALL                     2
             28 CACHE
             30 STORE_NAME               1 (TestFrozenUtf8_1)

  6          32 PUSH_NULL
             34 LOAD_BUILD_CLASS
             36 LOAD_CONST               3 (<code object TestFrozenUtf8_2 at 0x000001E77EB65FB0, file "__hello__.py", line 6>)
             38 MAKE_FUNCTION            0
             40 LOAD_CONST               4 ('TestFrozenUtf8_2')
             42 UNPACK_SEQUENCE          2
             46 CALL                     2
             54 CACHE
             56 STORE_NAME               2 (TestFrozenUtf8_2)

  9          58 PUSH_NULL
             60 LOAD_BUILD_CLASS
             62 LOAD_CONST               5 (<code object TestFrozenUtf8_4 at 0x000001E77EB66F70, file "__hello__.py", line 9>)
             64 MAKE_FUNCTION            0
             66 LOAD_CONST               6 ('TestFrozenUtf8_4')
             68 UNPACK_SEQUENCE          2
             72 CALL                     2
             80 CACHE
             82 STORE_NAME               3 (TestFrozenUtf8_4)

 12          84 LOAD_CONST               7 (<code object main at 0x000001E77EBCC3F0, file "__hello__.py", line 12>)
             86 MAKE_FUNCTION            0
             88 STORE_NAME               4 (main)

 15          90 LOAD_NAME                5 (__name__)
             92 LOAD_CONST               8 ('__main__')
             94 COMPARE_OP               2 (<)
             98 CACHE
            100 POP_JUMP_IF_FALSE       12 (to 126)

 16         102 PUSH_NULL
            104 LOAD_NAME                4 (main)
            106 UNPACK_SEQUENCE          0
            110 CALL                     0
            118 CACHE
            120 POP_TOP
            122 LOAD_CONST               9 (None)
            124 RETURN_VALUE

 15     >>  126 LOAD_CONST               9 (None)
            128 RETURN_VALUE

Disassembly of <code object TestFrozenUtf8_1 at 0x000001E77EB66090, file "__hello__.py", line 3>:
  3           0 RESUME                   0
              2 LOAD_NAME                0 (__name__)
              4 STORE_NAME               1 (__module__)
              6 LOAD_CONST               0 ('TestFrozenUtf8_1')
              8 STORE_NAME               2 (__qualname__)

  4          10 LOAD_CONST               1 ('¶')
             12 STORE_NAME               3 (__doc__)
             14 LOAD_CONST               2 (None)
             16 RETURN_VALUE

Disassembly of <code object TestFrozenUtf8_2 at 0x000001E77EB65FB0, file "__hello__.py", line 6>:
  6           0 RESUME                   0
              2 LOAD_NAME                0 (__name__)
              4 STORE_NAME               1 (__module__)
              6 LOAD_CONST               0 ('TestFrozenUtf8_2')
              8 STORE_NAME               2 (__qualname__)

  7          10 LOAD_CONST               1 ('π')
             12 STORE_NAME               3 (__doc__)
             14 LOAD_CONST               2 (None)
             16 RETURN_VALUE

Disassembly of <code object TestFrozenUtf8_4 at 0x000001E77EB66F70, file "__hello__.py", line 9>:
  9           0 RESUME                   0
              2 LOAD_NAME                0 (__name__)
              4 STORE_NAME               1 (__module__)
              6 LOAD_CONST               0 ('TestFrozenUtf8_4')
              8 STORE_NAME               2 (__qualname__)

 10          10 LOAD_CONST               1 ('😀')
             12 STORE_NAME               3 (__doc__)
             14 LOAD_CONST               2 (None)
             16 RETURN_VALUE

Disassembly of <code object main at 0x000001E77EBCC3F0, file "__hello__.py", line 12>:
 12           0 RESUME                   0

 13           2 LOAD_GLOBAL              1 (NULL + print)
             12 CACHE
             14 LOAD_CONST               1 ('Hello world!')
             16 UNPACK_SEQUENCE          1
             20 CALL                     1
             28 CACHE
             30 POP_TOP
             32 LOAD_CONST               0 (None)
             34 RETURN_VALUE


# CONSTANTS:
==================================================
# 0: True
# 1: <code object TestFrozenUtf8_1 from __hello__.py>
# 2: 'TestFrozenUtf8_1'
# 3: <code object TestFrozenUtf8_2 from __hello__.py>
# 4: 'TestFrozenUtf8_2'
# 5: <code object TestFrozenUtf8_4 from __hello__.py>
# 6: 'TestFrozenUtf8_4'
# 7: <code object main from __hello__.py>
# 8: '__main__'
# 9: None


# NAMES (global/attribute references):
==================================================
# 0: 'initialized'
# 1: 'TestFrozenUtf8_1'
# 2: 'TestFrozenUtf8_2'
# 3: 'TestFrozenUtf8_4'
# 4: 'main'
# 5: '__name__'


# VARIABLE NAMES (local variables):
==================================================


# FREE VARIABLES:
==================================================
