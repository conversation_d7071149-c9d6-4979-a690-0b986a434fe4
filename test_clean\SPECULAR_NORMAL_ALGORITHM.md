# Specular Normal Map算法详细说明

## 概述

本文档详细说明了基于论文研究实现的高级Specular Normal Map生成算法。该算法基于以下两篇重要论文：

1. **"Rapid Acquisition of Specular and Diffuse Normal Maps from Polarized Spherical Gradient Illumination"** (CiteSeer)
2. **"Real-time Acquisition and Rendering of Dynamic 3D Geometry and Reflectance"** (<PERSON> et al. 2007)

## 算法原理

### 1. 双色反射模型 (Dichromatic Reflection Model)

传统的光度立体法假设表面为完全漫反射（Lambertian），但真实材料同时具有漫反射和镜面反射特性。双色反射模型描述了这种复合反射：

```
I = I_diffuse + I_specular
I = ρ_d * (n · l) + ρ_s * (r · v)^α
```

其中：
- `I`: 观察到的总强度
- `ρ_d`: 漫反射反照率 (diffuse albedo)
- `ρ_s`: 镜面反射反照率 (specular albedo)
- `n`: 表面法线向量
- `l`: 光照方向向量
- `r`: 反射方向向量 = 2(n·l)n - l
- `v`: 观察方向向量
- `α`: 镜面反射指数 (specular exponent)，控制高光锐度

### 2. 分离算法

#### 步骤1: 初始漫反射估计
使用传统光度立体法获得初始法线估计：
```python
# 求解 L * (ρ_d * n) = I
diffuse_solution = np.linalg.lstsq(light_dirs, intensities)[0]
diffuse_rho = np.linalg.norm(diffuse_solution)
normal_estimate = diffuse_solution / diffuse_rho
```

#### 步骤2: 镜面反射分析
计算每个光源的反射方向和镜面项：
```python
# 计算反射方向: r = 2(n·l)n - l
dot_nl = np.dot(normal_estimate, light_dir)
reflection_dir = 2.0 * dot_nl * normal_estimate - light_dir

# 计算镜面项: (r·v)^α
dot_rv = np.dot(reflection_dir, view_dir)
specular_term = np.power(max(0, dot_rv), alpha)
```

#### 步骤3: 分量分离
通过残差分析分离镜面反射分量：
```python
# 计算漫反射分量
diffuse_intensities = diffuse_rho * np.maximum(0, np.dot(light_dirs, normal_estimate))

# 残差即为镜面分量
residual_intensities = intensities - diffuse_intensities

# 拟合镜面反照率
specular_rho = np.sum(residual_intensities * specular_terms) / np.sum(specular_terms**2)
```

#### 步骤4: 迭代优化
使用梯度下降法精化法线估计：
```python
for iteration in range(max_iterations):
    # 计算预测强度
    predicted = diffuse_term + specular_term
    
    # 计算误差梯度
    error = intensities - predicted
    
    # 更新法线
    normal = normal - learning_rate * gradient
    normal = normal / np.linalg.norm(normal)
```

## 输入要求

### 必需输入

1. **多方向光照图像** (至少4张，推荐6-8张)
   - 每张图像对应一个已知的光照方向
   - 图像必须完全对齐
   - 支持8位或16位灰度/彩色图像

2. **光源位置信息**
   - 可以手动指定或使用默认半球采样
   - 格式：3D坐标 (x, y, z)
   - 自动归一化为单位向量

### 可选参数

1. **观察方向** (`view_direction`)
   - 默认值：(0, 0, 1) - 垂直向下观察
   - 影响镜面反射计算

2. **粗糙度阈值** (`roughness_threshold`)
   - 默认值：0.1
   - 用于分离漫反射和镜面反射
   - 较小值：更敏感的镜面检测
   - 较大值：更保守的镜面检测

3. **自定义光源位置** (`light_positions`)
   - 格式：`"x1,y1,z1" "x2,y2,z2" ...`
   - 如未指定，使用默认半球分布

## 算法优势

### 相比传统光度立体法

1. **更高精度**: 考虑镜面反射，减少法线估计误差
2. **材质分离**: 同时获得漫反射和镜面反射属性
3. **真实感**: 更好地处理金属、塑料等有光泽材料

### 相比简单差分法

1. **物理准确**: 基于真实的反射物理模型
2. **鲁棒性**: 对噪声和光照变化更稳定
3. **信息丰富**: 提供法线、漫反射率、镜面反射率

## 输出结果

### 主要输出

1. **Specular Normal Map** (`output.png`)
   - 高精度表面法线贴图
   - 标准RGB格式，适用于渲染管线

2. **Diffuse Albedo Map** (`output_diffuse_albedo.png`)
   - 漫反射反照率贴图
   - 表示材料的基础颜色

3. **Specular Albedo Map** (`output_specular_albedo.png`)
   - 镜面反射反照率贴图
   - 表示材料的光泽度分布

## 使用示例

### 基本用法
```bash
python reconstructed_main.py --function create_specular_normal_map \
    --directional_images img1.tif img2.tif img3.tif img4.tif img5.tif img6.tif \
    --output specular_normal.png
```

### 高级用法
```bash
python reconstructed_main.py --function create_specular_normal_map \
    --directional_images img1.tif img2.tif img3.tif img4.tif img5.tif img6.tif \
    --output specular_normal.png \
    --view_direction 0.0 0.0 1.0 \
    --roughness_threshold 0.05 \
    --light_positions "-1,0,1" "1,0,1" "0,-1,1" "0,1,1" "0,0,1" "0,0,-1"
```

## 性能特性

### 计算复杂度
- **时间复杂度**: O(N × W × H × I)
  - N: 光源数量
  - W×H: 图像分辨率  
  - I: 迭代次数 (通常5-10次)

### 内存需求
- **基础内存**: ~3 × W × H × 4 bytes (法线贴图)
- **工作内存**: ~N × W × H × 4 bytes (输入图像)
- **总计**: 约 (N+3) × W × H × 16 bytes

### 典型性能
- **512×512图像, 6光源**: ~2-5秒
- **1024×1024图像, 6光源**: ~8-20秒
- **2048×2048图像, 6光源**: ~30-80秒

## 质量控制

### 输入验证
```python
# 检查图像数量
if len(directional_images) < 4:
    raise ValueError("至少需要4张方向性图像")

# 检查图像尺寸一致性
for img in images[1:]:
    if img.shape != images[0].shape:
        raise ValueError("所有图像尺寸必须一致")
```

### 结果验证
```python
# 检查法线向量长度
normal_lengths = np.linalg.norm(normal_map, axis=2)
if np.any(np.abs(normal_lengths - 1.0) > 0.1):
    print("警告: 检测到非单位法线向量")

# 检查反照率范围
if np.any(diffuse_albedo < 0) or np.any(diffuse_albedo > 1):
    print("警告: 漫反射反照率超出有效范围")
```

## 故障排除

### 常见问题

1. **法线估计不准确**
   - 检查光源位置是否正确
   - 调整粗糙度阈值
   - 增加光源数量

2. **镜面分量过强/过弱**
   - 调整`roughness_threshold`参数
   - 检查观察方向设置
   - 验证图像对齐质量

3. **计算时间过长**
   - 减少迭代次数
   - 降低图像分辨率
   - 使用更少的光源

### 参数调优指南

- **金属材料**: `roughness_threshold = 0.05-0.1`
- **塑料材料**: `roughness_threshold = 0.1-0.2`  
- **布料材料**: `roughness_threshold = 0.2-0.3`
- **皮肤材料**: `roughness_threshold = 0.1-0.15`

---

**注意**: 该算法实现基于学术研究，在实际应用中可能需要根据具体材料和光照条件进行参数调优。
