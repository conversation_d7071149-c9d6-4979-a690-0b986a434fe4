# Code object from position 8051645
# Filename: bz2.py
# Name: <module>
# Args: 0
# Locals: 0
# Stack size: 5
# Flags: 0

  0           0 RESUME                   0

  1           2 LOAD_CONST               0 ('Interface to the libbzip2 compression library.\n\nThis module provides a file interface, classes for incremental\n(de)compression, and functions for one-shot (de)compression.\n')
              4 STORE_NAME               0 (__doc__)

  7           6 BUILD_LIST               0
              8 LOAD_CONST               1 (('BZ2File', 'BZ2Compressor', 'BZ2Decompressor', 'open', 'compress', 'decompress'))
             10 LIST_EXTEND              1
             12 STORE_NAME               1 (__all__)

 10          14 LOAD_CONST               2 ('Nadeem <PERSON>awda <<EMAIL>>')
             16 STORE_NAME               2 (__author__)

 12          18 LOAD_CONST               3 (0)
             20 LOAD_CONST               4 (('open',))
             22 IMPORT_NAME              3 (builtins)
             24 IMPORT_FROM              4 (open)
             26 STORE_NAME               5 (_builtin_open)
             28 POP_TOP

 13          30 LOAD_CONST               3 (0)
             32 LOAD_CONST               5 (None)
             34 IMPORT_NAME              6 (io)
             36 STORE_NAME               6 (io)

 14          38 LOAD_CONST               3 (0)
             40 LOAD_CONST               5 (None)
             42 IMPORT_NAME              7 (os)
             44 STORE_NAME               7 (os)

 15          46 LOAD_CONST               3 (0)
             48 LOAD_CONST               5 (None)
             50 IMPORT_NAME              8 (_compression)
             52 STORE_NAME               8 (_compression)

 17          54 LOAD_CONST               3 (0)
             56 LOAD_CONST               6 (('BZ2Compressor', 'BZ2Decompressor'))
             58 IMPORT_NAME              9 (_bz2)
             60 IMPORT_FROM             10 (BZ2Compressor)
             62 STORE_NAME              10 (BZ2Compressor)
             64 IMPORT_FROM             11 (BZ2Decompressor)
             66 STORE_NAME              11 (BZ2Decompressor)
             68 POP_TOP

 20          70 LOAD_CONST               3 (0)
             72 STORE_NAME              12 (_MODE_CLOSED)

 21          74 LOAD_CONST               7 (1)
             76 STORE_NAME              13 (_MODE_READ)

 23          78 LOAD_CONST               8 (3)
             80 STORE_NAME              14 (_MODE_WRITE)

 26          82 PUSH_NULL
             84 LOAD_BUILD_CLASS
             86 LOAD_CONST               9 (<code object BZ2File at 0x000001B2A728D730, file "bz2.py", line 26>)
             88 MAKE_FUNCTION            0
             90 LOAD_CONST              10 ('BZ2File')
             92 LOAD_NAME                8 (_compression)
             94 LOAD_ATTR               15 (NULL|self + os)
            114 CACHE
            116 CACHE
            118 STORE_NAME              16 (BZ2File)

271         120 NOP

272         122 NOP

271         124 LOAD_CONST              16 (('rb', 9, None, None, None))
            126 LOAD_CONST              13 (<code object open at 0x000001B2A508B0C0, file "bz2.py", line 271>)
            128 MAKE_FUNCTION            1 (defaults)
            130 STORE_NAME               4 (open)

313         132 LOAD_CONST              17 ((9,))
            134 LOAD_CONST              14 (<code object compress at 0x000001B2A7259430, file "bz2.py", line 313>)
            136 MAKE_FUNCTION            1 (defaults)
            138 STORE_NAME              17 (compress)

324         140 LOAD_CONST              15 (<code object decompress at 0x000001B2A71AEBB0, file "bz2.py", line 324>)
            142 MAKE_FUNCTION            0
            144 STORE_NAME              18 (decompress)
            146 LOAD_CONST               5 (None)
            148 RETURN_VALUE

Disassembly of <code object BZ2File at 0x000001B2A728D730, file "bz2.py", line 26>:
 26           0 RESUME                   0
              2 LOAD_NAME                0 (__name__)
              4 STORE_NAME               1 (__module__)
              6 LOAD_CONST               0 ('BZ2File')
              8 STORE_NAME               2 (__qualname__)

 28          10 LOAD_CONST               1 ('A file object providing transparent bzip2 (de)compression.\n\n    A BZ2File can act as a wrapper for an existing file object, or refer\n    directly to a named file on disk.\n\n    Note that BZ2File provides a *binary* file interface - data read is\n    returned as bytes, and data to be written should be given as bytes.\n    ')
             12 STORE_NAME               3 (__doc__)

 37          14 LOAD_CONST              25 (('r',))
             16 LOAD_CONST               3 (9)
             18 LOAD_CONST               4 (('compresslevel',))
             20 BUILD_CONST_KEY_MAP      1
             22 LOAD_CONST               5 (<code object __init__ at 0x000001B2A75B6340, file "bz2.py", line 37>)
             24 MAKE_FUNCTION            3 (defaults, kwdefaults)
             26 STORE_NAME               4 (__init__)

 97          28 LOAD_CONST               6 (<code object close at 0x000001B2A5095910, file "bz2.py", line 97>)
             30 MAKE_FUNCTION            0
             32 STORE_NAME               5 (close)

121          34 LOAD_NAME                6 (property)

122          36 LOAD_CONST               7 (<code object closed at 0x000001B2A71D5D40, file "bz2.py", line 121>)
             38 MAKE_FUNCTION            0

121          40 UNPACK_SEQUENCE          0
             44 CALL                     0
             52 CACHE

122          54 STORE_NAME               7 (closed)

126          56 LOAD_CONST               8 (<code object fileno at 0x000001B2A71F7990, file "bz2.py", line 126>)
             58 MAKE_FUNCTION            0
             60 STORE_NAME               8 (fileno)

131          62 LOAD_CONST               9 (<code object seekable at 0x000001B2A71F7BD0, file "bz2.py", line 131>)
             64 MAKE_FUNCTION            0
             66 STORE_NAME               9 (seekable)

135          68 LOAD_CONST              10 (<code object readable at 0x000001B2A7203BB0, file "bz2.py", line 135>)
             70 MAKE_FUNCTION            0
             72 STORE_NAME              10 (readable)

140          74 LOAD_CONST              11 (<code object writable at 0x000001B2A7203220, file "bz2.py", line 140>)
             76 MAKE_FUNCTION            0
             78 STORE_NAME              11 (writable)

145          80 LOAD_CONST              26 ((0,))
             82 LOAD_CONST              13 (<code object peek at 0x000001B2A71F7E10, file "bz2.py", line 145>)
             84 MAKE_FUNCTION            1 (defaults)
             86 STORE_NAME              12 (peek)

157          88 LOAD_CONST              27 ((-1,))
             90 LOAD_CONST              15 (<code object read at 0x000001B2A71F7AB0, file "bz2.py", line 157>)
             92 MAKE_FUNCTION            1 (defaults)
             94 STORE_NAME              13 (read)

166          96 LOAD_CONST              27 ((-1,))
             98 LOAD_CONST              16 (<code object read1 at 0x000001B2A71C7600, file "bz2.py", line 166>)
            100 MAKE_FUNCTION            1 (defaults)
            102 STORE_NAME              14 (read1)

178         104 LOAD_CONST              17 (<code object readinto at 0x000001B2A76D8150, file "bz2.py", line 178>)
            106 MAKE_FUNCTION            0
            108 STORE_NAME              15 (readinto)

186         110 LOAD_CONST              27 ((-1,))
            112 LOAD_CONST              18 (<code object readline at 0x000001B2A726DCE0, file "bz2.py", line 186>)
            114 MAKE_FUNCTION            1 (defaults)
            116 STORE_NAME              16 (readline)

200         118 LOAD_CONST              27 ((-1,))
            120 LOAD_CONST              19 (<code object readlines at 0x000001B2A726D620, file "bz2.py", line 200>)
            122 MAKE_FUNCTION            1 (defaults)
            124 STORE_NAME              17 (readlines)

214         126 LOAD_CONST              20 (<code object write at 0x000001B2A71BBC30, file "bz2.py", line 214>)
            128 MAKE_FUNCTION            0
            130 STORE_NAME              18 (write)

235         132 LOAD_CONST              21 (<code object writelines at 0x000001B2A7203330, file "bz2.py", line 235>)
            134 MAKE_FUNCTION            0
            136 STORE_NAME              19 (writelines)

245         138 LOAD_NAME               20 (io)
            140 LOAD_ATTR               21 (NULL|self + readable)
            160 MAKE_FUNCTION            0
            162 STORE_NAME              23 (tell)
            164 LOAD_CONST              24 (None)
            166 RETURN_VALUE

Disassembly of <code object __init__ at 0x000001B2A75B6340, file "bz2.py", line 37>:
 37           0 RESUME                   0

 55           2 LOAD_CONST               1 (None)
              4 LOAD_FAST                0 (self)
              6 STORE_ATTR               0 (_fp)

 56          16 LOAD_CONST               2 (False)
             18 LOAD_FAST                0 (self)
             20 STORE_ATTR               1 (_closefp)

 57          30 LOAD_GLOBAL              4 (_MODE_CLOSED)
             40 CACHE
             42 LOAD_FAST                0 (self)
             44 STORE_ATTR               3 (_mode)

 59          54 LOAD_CONST               3 (1)
             56 LOAD_FAST                3 (compresslevel)
             58 SWAP                     2
             60 COPY                     2
             62 COMPARE_OP               1 (<)
             66 CACHE
             68 POP_JUMP_IF_FALSE        6 (to 82)
             70 LOAD_CONST               4 (9)
             72 COMPARE_OP               1 (<)
             76 CACHE
             78 POP_JUMP_IF_TRUE        17 (to 114)
             80 JUMP_FORWARD             1 (to 84)
        >>   82 POP_TOP

 60     >>   84 LOAD_GLOBAL              9 (NULL + ValueError)
             94 CACHE
             96 LOAD_CONST               5 ('compresslevel must be between 1 and 9')
             98 UNPACK_SEQUENCE          1
            102 CALL                     1
            110 CACHE
            112 RAISE_VARARGS            1

 62     >>  114 LOAD_FAST                2 (mode)
            116 LOAD_CONST               6 (('', 'r', 'rb'))
            118 CONTAINS_OP              0
            120 POP_JUMP_IF_FALSE       10 (to 142)

 63         122 LOAD_CONST               7 ('rb')
            124 STORE_FAST               2 (mode)

 64         126 LOAD_GLOBAL             10 (_MODE_READ)
            136 CACHE
            138 STORE_FAST               4 (mode_code)
            140 JUMP_FORWARD           120 (to 382)

 65     >>  142 LOAD_FAST                2 (mode)
            144 LOAD_CONST               8 (('w', 'wb'))
            146 CONTAINS_OP              0
            148 POP_JUMP_IF_FALSE       30 (to 210)

 66         150 LOAD_CONST               9 ('wb')
            152 STORE_FAST               2 (mode)

 67         154 LOAD_GLOBAL             12 (_MODE_WRITE)
            164 CACHE
            166 STORE_FAST               4 (mode_code)

 68         168 LOAD_GLOBAL             15 (NULL + BZ2Compressor)
            178 CACHE
            180 LOAD_FAST                3 (compresslevel)
            182 UNPACK_SEQUENCE          1
            186 CALL                     1
            194 CACHE
            196 LOAD_FAST                0 (self)
            198 STORE_ATTR               8 (_compressor)
            208 JUMP_FORWARD            86 (to 382)

 69     >>  210 LOAD_FAST                2 (mode)
            212 LOAD_CONST              10 (('x', 'xb'))
            214 CONTAINS_OP              0
            216 POP_JUMP_IF_FALSE       30 (to 278)

 70         218 LOAD_CONST              11 ('xb')
            220 STORE_FAST               2 (mode)

 71         222 LOAD_GLOBAL             12 (_MODE_WRITE)
            232 CACHE
            234 STORE_FAST               4 (mode_code)

 72         236 LOAD_GLOBAL             15 (NULL + BZ2Compressor)
            246 CACHE
            248 LOAD_FAST                3 (compresslevel)
            250 UNPACK_SEQUENCE          1
            254 CALL                     1
            262 CACHE
            264 LOAD_FAST                0 (self)
            266 STORE_ATTR               8 (_compressor)
            276 JUMP_FORWARD            52 (to 382)

 73     >>  278 LOAD_FAST                2 (mode)
            280 LOAD_CONST              12 (('a', 'ab'))
            282 CONTAINS_OP              0
            284 POP_JUMP_IF_FALSE       30 (to 346)

 74         286 LOAD_CONST              13 ('ab')
            288 STORE_FAST               2 (mode)

 75         290 LOAD_GLOBAL             12 (_MODE_WRITE)
            300 CACHE
            302 STORE_FAST               4 (mode_code)

 76         304 LOAD_GLOBAL             15 (NULL + BZ2Compressor)
            314 CACHE
            316 LOAD_FAST                3 (compresslevel)
            318 UNPACK_SEQUENCE          1
            322 CALL                     1
            330 CACHE
            332 LOAD_FAST                0 (self)
            334 STORE_ATTR               8 (_compressor)
            344 JUMP_FORWARD            18 (to 382)

 78     >>  346 LOAD_GLOBAL              9 (NULL + ValueError)
            356 CACHE
            358 LOAD_CONST              14 ('Invalid mode: ')
            360 LOAD_FAST                2 (mode)
            362 FORMAT_VALUE             2 (repr)
            364 BUILD_STRING             2
            366 UNPACK_SEQUENCE          1
            370 CALL                     1
            378 CACHE
            380 RAISE_VARARGS            1

 80     >>  382 LOAD_GLOBAL             19 (NULL + isinstance)
            392 CACHE
            394 LOAD_FAST                1 (filename)
            396 LOAD_GLOBAL             20 (str)
            406 CACHE
            408 LOAD_GLOBAL             22 (bytes)
            418 CACHE
            420 LOAD_GLOBAL             24 (os)
            430 CACHE
            432 LOAD_ATTR               13 (NULL|self + _MODE_WRITE)
            452 CACHE
            454 CACHE
            456 CACHE
            458 POP_JUMP_IF_FALSE       36 (to 532)

 81         460 LOAD_GLOBAL             29 (NULL + _builtin_open)
            470 CACHE
            472 LOAD_FAST                1 (filename)
            474 LOAD_FAST                2 (mode)
            476 UNPACK_SEQUENCE          2
            480 CALL                     2
            488 CACHE
            490 LOAD_FAST                0 (self)
            492 STORE_ATTR               0 (_fp)

 82         502 LOAD_CONST              15 (True)
            504 LOAD_FAST                0 (self)
            506 STORE_ATTR               1 (_closefp)

 83         516 LOAD_FAST                4 (mode_code)
            518 LOAD_FAST                0 (self)
            520 STORE_ATTR               3 (_mode)
            530 JUMP_FORWARD            62 (to 656)

 84     >>  532 LOAD_GLOBAL             31 (NULL + hasattr)
            542 CACHE
            544 LOAD_FAST                1 (filename)
            546 LOAD_CONST              16 ('read')
            548 UNPACK_SEQUENCE          2
            552 CALL                     2
            560 CACHE
            562 POP_JUMP_IF_TRUE        16 (to 596)
            564 LOAD_GLOBAL             31 (NULL + hasattr)
            574 CACHE
            576 LOAD_FAST                1 (filename)
            578 LOAD_CONST              17 ('write')
            580 UNPACK_SEQUENCE          2
            584 CALL                     2
            592 CACHE
            594 POP_JUMP_IF_FALSE       15 (to 626)

 85     >>  596 LOAD_FAST                1 (filename)
            598 LOAD_FAST                0 (self)
            600 STORE_ATTR               0 (_fp)

 86         610 LOAD_FAST                4 (mode_code)
            612 LOAD_FAST                0 (self)
            614 STORE_ATTR               3 (_mode)
            624 JUMP_FORWARD            15 (to 656)

 88     >>  626 LOAD_GLOBAL             33 (NULL + TypeError)
            636 CACHE
            638 LOAD_CONST              18 ('filename must be a str, bytes, file or PathLike object')
            640 UNPACK_SEQUENCE          1
            644 CALL                     1
            652 CACHE
            654 RAISE_VARARGS            1

 90     >>  656 LOAD_FAST                0 (self)
            658 LOAD_ATTR                3 (NULL|self + _closefp)
            678 CACHE
            680 COMPARE_OP               2 (<)
            684 CACHE
            686 POP_JUMP_IF_FALSE       65 (to 818)

 91         688 LOAD_GLOBAL             35 (NULL + _compression)
            698 CACHE
            700 LOAD_ATTR               18 (isinstance)
            720 CACHE

 92         722 LOAD_GLOBAL             38 (BZ2Decompressor)
            732 CACHE
            734 LOAD_GLOBAL             40 (OSError)
            744 CACHE

 91         746 KW_NAMES                19 (('trailing_error',))
            748 UNPACK_SEQUENCE          3
            752 CALL                     3
            760 CACHE
            762 STORE_FAST               5 (raw)

 93         764 LOAD_GLOBAL             43 (NULL + io)
            774 CACHE
            776 LOAD_ATTR               22 (bytes)
            796 CACHE
            798 CACHE
            800 CACHE
            802 LOAD_FAST                0 (self)
            804 STORE_ATTR              23 (_buffer)
            814 LOAD_CONST               1 (None)
            816 RETURN_VALUE

 95     >>  818 LOAD_CONST              20 (0)
            820 LOAD_FAST                0 (self)
            822 STORE_ATTR              24 (_pos)
            832 LOAD_CONST               1 (None)
            834 RETURN_VALUE

Disassembly of <code object close at 0x000001B2A5095910, file "bz2.py", line 97>:
 97           0 RESUME                   0

103           2 LOAD_FAST                0 (self)
              4 LOAD_ATTR                0 (_mode)
             24 CACHE
             26 COMPARE_OP               2 (<)
             30 CACHE
             32 POP_JUMP_IF_FALSE        2 (to 38)

104          34 LOAD_CONST               1 (None)
             36 RETURN_VALUE

105     >>   38 NOP

106          40 LOAD_FAST                0 (self)
             42 LOAD_ATTR                0 (_mode)
             62 CACHE
             64 COMPARE_OP               2 (<)
             68 CACHE
             70 POP_JUMP_IF_FALSE       26 (to 124)

107          72 LOAD_FAST                0 (self)
             74 LOAD_ATTR                3 (NULL|self + _MODE_CLOSED)
             94 CACHE
             96 CACHE
             98 CACHE
            100 CACHE
            102 CACHE
            104 CACHE
            106 UNPACK_SEQUENCE          0
            110 CALL                     0
            118 CACHE
            120 POP_TOP
            122 JUMP_FORWARD            72 (to 268)

108     >>  124 LOAD_FAST                0 (self)
            126 LOAD_ATTR                0 (_mode)
            146 CACHE
            148 COMPARE_OP               2 (<)
            152 CACHE
            154 POP_JUMP_IF_FALSE       56 (to 268)

109         156 LOAD_FAST                0 (self)
            158 LOAD_ATTR                6 (_buffer)
            178 CACHE
            180 CACHE
            182 CACHE
            184 CACHE
            186 CACHE
            188 CACHE
            190 LOAD_FAST                0 (self)
            192 LOAD_ATTR                8 (close)
            212 CACHE
            214 CACHE
            216 CACHE
            218 CACHE
            220 CACHE
            222 CACHE
            224 UNPACK_SEQUENCE          0
            228 CALL                     0
            236 CACHE
            238 UNPACK_SEQUENCE          1
            242 CALL                     1
            250 CACHE
            252 POP_TOP

110         254 LOAD_CONST               1 (None)
            256 LOAD_FAST                0 (self)
            258 STORE_ATTR               8 (_compressor)

112     >>  268 NOP

113         270 LOAD_FAST                0 (self)
            272 LOAD_ATTR               10 (_MODE_WRITE)
            292 CACHE
            294 CACHE
            296 STORE_SUBSCR
            300 CACHE
            302 CACHE
            304 CACHE
            306 CACHE
            308 CACHE
            310 CACHE
            312 CACHE
            314 CACHE
            316 CACHE
            318 UNPACK_SEQUENCE          0
            322 CALL                     0
            330 CACHE
            332 POP_TOP

116         334 LOAD_CONST               1 (None)
            336 LOAD_FAST                0 (self)
            338 STORE_ATTR               6 (_fp)

117         348 LOAD_CONST               2 (False)
            350 LOAD_FAST                0 (self)
            352 STORE_ATTR              10 (_closefp)

118         362 LOAD_GLOBAL              2 (_MODE_CLOSED)
            372 CACHE
            374 LOAD_FAST                0 (self)
            376 STORE_ATTR               0 (_mode)

119         386 LOAD_CONST               1 (None)
            388 LOAD_FAST                0 (self)
            390 STORE_ATTR               3 (_buffer)
            400 LOAD_CONST               1 (None)
            402 RETURN_VALUE
        >>  404 PUSH_EXC_INFO

116         406 LOAD_CONST               1 (None)
            408 LOAD_FAST                0 (self)
            410 STORE_ATTR               6 (_fp)

117         420 LOAD_CONST               2 (False)
            422 LOAD_FAST                0 (self)
            424 STORE_ATTR              10 (_closefp)

118         434 LOAD_GLOBAL              2 (_MODE_CLOSED)
            444 CACHE
            446 LOAD_FAST                0 (self)
            448 STORE_ATTR               0 (_mode)

119         458 LOAD_CONST               1 (None)
            460 LOAD_FAST                0 (self)
            462 STORE_ATTR               3 (_buffer)
            472 RERAISE                  0
        >>  474 COPY                     3
            476 POP_EXCEPT
            478 RERAISE                  1
        >>  480 PUSH_EXC_INFO

112         482 NOP

113         484 LOAD_FAST                0 (self)
            486 LOAD_ATTR               10 (_MODE_WRITE)
            506 CACHE
            508 CACHE
            510 STORE_SUBSCR
            514 CACHE
            516 CACHE
            518 CACHE
            520 CACHE
            522 CACHE
            524 CACHE
            526 CACHE
            528 CACHE
            530 CACHE
            532 UNPACK_SEQUENCE          0
            536 CALL                     0
            544 CACHE
            546 POP_TOP

116         548 LOAD_CONST               1 (None)
            550 LOAD_FAST                0 (self)
            552 STORE_ATTR               6 (_fp)

117         562 LOAD_CONST               2 (False)
            564 LOAD_FAST                0 (self)
            566 STORE_ATTR              10 (_closefp)

118         576 LOAD_GLOBAL              2 (_MODE_CLOSED)
            586 CACHE
            588 LOAD_FAST                0 (self)
            590 STORE_ATTR               0 (_mode)

119         600 LOAD_CONST               1 (None)
            602 LOAD_FAST                0 (self)
            604 STORE_ATTR               3 (_buffer)
            614 JUMP_FORWARD            38 (to 692)
        >>  616 PUSH_EXC_INFO

116         618 LOAD_CONST               1 (None)
            620 LOAD_FAST                0 (self)
            622 STORE_ATTR               6 (_fp)

117         632 LOAD_CONST               2 (False)
            634 LOAD_FAST                0 (self)
            636 STORE_ATTR              10 (_closefp)

118         646 LOAD_GLOBAL              2 (_MODE_CLOSED)
            656 CACHE
            658 LOAD_FAST                0 (self)
            660 STORE_ATTR               0 (_mode)

119         670 LOAD_CONST               1 (None)
            672 LOAD_FAST                0 (self)
            674 STORE_ATTR               3 (_buffer)
            684 RERAISE                  0
        >>  686 COPY                     3
            688 POP_EXCEPT
            690 RERAISE                  1
        >>  692 RERAISE                  0
        >>  694 COPY                     3
            696 POP_EXCEPT
            698 RERAISE                  1
ExceptionTable:
  40 to 266 -> 480 [0]
  270 to 332 -> 404 [0]
  404 to 472 -> 474 [1] lasti
  480 to 480 -> 694 [1] lasti
  484 to 546 -> 616 [2]
  548 to 614 -> 694 [1] lasti
  616 to 684 -> 686 [3] lasti
  686 to 692 -> 694 [1] lasti

Disassembly of <code object closed at 0x000001B2A71D5D40, file "bz2.py", line 121>:
121           0 RESUME                   0

124           2 LOAD_FAST                0 (self)
              4 LOAD_ATTR                0 (_mode)
             24 CACHE
             26 COMPARE_OP               2 (<)
             30 CACHE
             32 RETURN_VALUE

Disassembly of <code object fileno at 0x000001B2A71F7990, file "bz2.py", line 126>:
126           0 RESUME                   0

128           2 LOAD_FAST                0 (self)
              4 STORE_SUBSCR
              8 CACHE
             10 CACHE
             12 CACHE
             14 CACHE
             16 CACHE
             18 CACHE
             20 CACHE
             22 CACHE
             24 CACHE
             26 UNPACK_SEQUENCE          0
             30 CALL                     0
             38 CACHE
             40 POP_TOP

129          42 LOAD_FAST                0 (self)
             44 LOAD_ATTR                1 (NULL|self + _check_not_closed)
             64 CACHE
             66 CACHE
             68 CACHE
             70 CACHE
             72 CACHE
             74 CACHE
             76 UNPACK_SEQUENCE          0
             80 CALL                     0
             88 CACHE
             90 RETURN_VALUE

Disassembly of <code object seekable at 0x000001B2A71F7BD0, file "bz2.py", line 131>:
131           0 RESUME                   0

133           2 LOAD_FAST                0 (self)
              4 STORE_SUBSCR
              8 CACHE
             10 CACHE
             12 CACHE
             14 CACHE
             16 CACHE
             18 CACHE
             20 CACHE
             22 CACHE
             24 CACHE
             26 UNPACK_SEQUENCE          0
             30 CALL                     0
             38 CACHE
