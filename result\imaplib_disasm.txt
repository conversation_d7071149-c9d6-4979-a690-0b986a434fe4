# Code object from position 10636224
# Filename: imaplib.py
# Name: <module>
# Args: 0
# Locals: 0
# Stack size: 14
# Flags: 0

   0           0 RESUME                   0

   1           2 LOAD_CONST               0 ('IMAP4 client.\n\nBased on RFC 2060.\n\nPublic class:           IMAP4\nPublic variable:        Debug\nPublic functions:       Internaldate2tuple\n                        Int2AP\n                        ParseFlags\n                        Time2Internaldate\n')
               4 STORE_NAME               0 (__doc__)

  23           6 LOAD_CONST               1 ('2.58')
               8 STORE_NAME               1 (__version__)

  25          10 LOAD_CONST               2 (0)
              12 LOAD_CONST               3 (None)
              14 IMPORT_NAME              2 (binascii)
              16 STORE_NAME               2 (binascii)
              18 LOAD_CONST               2 (0)
              20 LOAD_CONST               3 (None)
              22 IMPORT_NAME              3 (errno)
              24 STORE_NAME               3 (errno)
              26 LOAD_CONST               2 (0)
              28 LOAD_CONST               3 (None)
              30 IMPORT_NAME              4 (random)
              32 STORE_NAME               4 (random)
              34 LOAD_CONST               2 (0)
              36 LOAD_CONST               3 (None)
              38 IMPORT_NAME              5 (re)
              40 STORE_NAME               5 (re)
              42 LOAD_CONST               2 (0)
              44 LOAD_CONST               3 (None)
              46 IMPORT_NAME              6 (socket)
              48 STORE_NAME               6 (socket)
              50 LOAD_CONST               2 (0)
              52 LOAD_CONST               3 (None)
              54 IMPORT_NAME              7 (subprocess)
              56 STORE_NAME               7 (subprocess)
              58 LOAD_CONST               2 (0)
              60 LOAD_CONST               3 (None)
              62 IMPORT_NAME              8 (sys)
              64 STORE_NAME               8 (sys)
              66 LOAD_CONST               2 (0)
              68 LOAD_CONST               3 (None)
              70 IMPORT_NAME              9 (time)
              72 STORE_NAME               9 (time)
              74 LOAD_CONST               2 (0)
              76 LOAD_CONST               3 (None)
              78 IMPORT_NAME             10 (calendar)
              80 STORE_NAME              10 (calendar)

  26          82 LOAD_CONST               2 (0)
              84 LOAD_CONST               4 (('datetime', 'timezone', 'timedelta'))
              86 IMPORT_NAME             11 (datetime)
              88 IMPORT_FROM             11 (datetime)
              90 STORE_NAME              11 (datetime)
              92 IMPORT_FROM             12 (timezone)
              94 STORE_NAME              12 (timezone)
              96 IMPORT_FROM             13 (timedelta)
              98 STORE_NAME              13 (timedelta)
             100 POP_TOP

  27         102 LOAD_CONST               2 (0)
             104 LOAD_CONST               5 (('DEFAULT_BUFFER_SIZE',))
             106 IMPORT_NAME             14 (io)
             108 IMPORT_FROM             15 (DEFAULT_BUFFER_SIZE)
             110 STORE_NAME              15 (DEFAULT_BUFFER_SIZE)
             112 POP_TOP

  29         114 NOP

  30         116 LOAD_CONST               2 (0)
             118 LOAD_CONST               3 (None)
             120 IMPORT_NAME             16 (ssl)
             122 STORE_NAME              16 (ssl)

  31         124 LOAD_CONST               6 (True)
             126 STORE_NAME              17 (HAVE_SSL)
             128 JUMP_FORWARD            13 (to 156)
         >>  130 PUSH_EXC_INFO

  32         132 LOAD_NAME               18 (ImportError)
             134 CHECK_EXC_MATCH
             136 POP_JUMP_IF_FALSE        5 (to 148)
             138 POP_TOP

  33         140 LOAD_CONST               7 (False)
             142 STORE_NAME              17 (HAVE_SSL)
             144 POP_EXCEPT
             146 JUMP_FORWARD             4 (to 156)

  32     >>  148 RERAISE                  0
         >>  150 COPY                     3
             152 POP_EXCEPT
             154 RERAISE                  1

  35     >>  156 BUILD_LIST               0
             158 LOAD_CONST               8 (('IMAP4', 'IMAP4_stream', 'Internaldate2tuple', 'Int2AP', 'ParseFlags', 'Time2Internaldate'))
             160 LIST_EXTEND              1
             162 STORE_NAME              19 (__all__)

  40         164 LOAD_CONST               9 (b'\r\n')
             166 STORE_NAME              20 (CRLF)

  41         168 LOAD_CONST               2 (0)
             170 STORE_NAME              21 (Debug)

  42         172 LOAD_CONST              10 (143)
             174 STORE_NAME              22 (IMAP4_PORT)

  43         176 LOAD_CONST              11 (993)
             178 STORE_NAME              23 (IMAP4_SSL_PORT)

  44         180 LOAD_CONST              12 (('IMAP4REV1', 'IMAP4'))
             182 STORE_NAME              24 (AllowedVersions)

  53         184 LOAD_CONST              13 (1000000)
             186 STORE_NAME              25 (_MAXLINE)

  58         188 BUILD_MAP                0

  60         190 LOAD_CONST              14 ('APPEND')
             192 LOAD_CONST              15 (('AUTH', 'SELECTED'))

  58         194 MAP_ADD                  1

  61         196 LOAD_CONST              16 ('AUTHENTICATE')
             198 LOAD_CONST              17 (('NONAUTH',))

  58         200 MAP_ADD                  1

  62         202 LOAD_CONST              18 ('CAPABILITY')
             204 LOAD_CONST              19 (('NONAUTH', 'AUTH', 'SELECTED', 'LOGOUT'))

  58         206 MAP_ADD                  1

  63         208 LOAD_CONST              20 ('CHECK')
             210 LOAD_CONST              21 (('SELECTED',))

  58         212 MAP_ADD                  1

  64         214 LOAD_CONST              22 ('CLOSE')
             216 LOAD_CONST              21 (('SELECTED',))

  58         218 MAP_ADD                  1

  65         220 LOAD_CONST              23 ('COPY')
             222 LOAD_CONST              21 (('SELECTED',))

  58         224 MAP_ADD                  1

  66         226 LOAD_CONST              24 ('CREATE')
             228 LOAD_CONST              15 (('AUTH', 'SELECTED'))

  58         230 MAP_ADD                  1

  67         232 LOAD_CONST              25 ('DELETE')
             234 LOAD_CONST              15 (('AUTH', 'SELECTED'))

  58         236 MAP_ADD                  1

  68         238 LOAD_CONST              26 ('DELETEACL')
             240 LOAD_CONST              15 (('AUTH', 'SELECTED'))

  58         242 MAP_ADD                  1

  69         244 LOAD_CONST              27 ('ENABLE')
             246 LOAD_CONST              28 (('AUTH',))

  58         248 MAP_ADD                  1

  70         250 LOAD_CONST              29 ('EXAMINE')
             252 LOAD_CONST              15 (('AUTH', 'SELECTED'))

  58         254 MAP_ADD                  1

  71         256 LOAD_CONST              30 ('EXPUNGE')
             258 LOAD_CONST              21 (('SELECTED',))

  58         260 MAP_ADD                  1

  72         262 LOAD_CONST              31 ('FETCH')
             264 LOAD_CONST              21 (('SELECTED',))

  58         266 MAP_ADD                  1

  73         268 LOAD_CONST              32 ('GETACL')
             270 LOAD_CONST              15 (('AUTH', 'SELECTED'))

  58         272 MAP_ADD                  1

  74         274 LOAD_CONST              33 ('GETANNOTATION')
             276 LOAD_CONST              15 (('AUTH', 'SELECTED'))

  58         278 MAP_ADD                  1

  75         280 LOAD_CONST              34 ('GETQUOTA')
             282 LOAD_CONST              15 (('AUTH', 'SELECTED'))

  58         284 MAP_ADD                  1

  76         286 LOAD_CONST              35 ('GETQUOTAROOT')
             288 LOAD_CONST              15 (('AUTH', 'SELECTED'))

  58         290 MAP_ADD                  1
             292 BUILD_MAP                0

  77         294 LOAD_CONST              36 ('MYRIGHTS')
             296 LOAD_CONST              15 (('AUTH', 'SELECTED'))

  58         298 MAP_ADD                  1

  78         300 LOAD_CONST              37 ('LIST')
             302 LOAD_CONST              15 (('AUTH', 'SELECTED'))

  58         304 MAP_ADD                  1

  79         306 LOAD_CONST              38 ('LOGIN')
             308 LOAD_CONST              17 (('NONAUTH',))

  58         310 MAP_ADD                  1

  80         312 LOAD_CONST              39 ('LOGOUT')
             314 LOAD_CONST              19 (('NONAUTH', 'AUTH', 'SELECTED', 'LOGOUT'))

  58         316 MAP_ADD                  1

  81         318 LOAD_CONST              40 ('LSUB')
             320 LOAD_CONST              15 (('AUTH', 'SELECTED'))

  58         322 MAP_ADD                  1

  82         324 LOAD_CONST              41 ('MOVE')
             326 LOAD_CONST              21 (('SELECTED',))

  58         328 MAP_ADD                  1

  83         330 LOAD_CONST              42 ('NAMESPACE')
             332 LOAD_CONST              15 (('AUTH', 'SELECTED'))

  58         334 MAP_ADD                  1

  84         336 LOAD_CONST              43 ('NOOP')
             338 LOAD_CONST              19 (('NONAUTH', 'AUTH', 'SELECTED', 'LOGOUT'))

  58         340 MAP_ADD                  1

  85         342 LOAD_CONST              44 ('PARTIAL')
             344 LOAD_CONST              21 (('SELECTED',))

  58         346 MAP_ADD                  1

  86         348 LOAD_CONST              45 ('PROXYAUTH')
             350 LOAD_CONST              28 (('AUTH',))

  58         352 MAP_ADD                  1

  87         354 LOAD_CONST              46 ('RENAME')
             356 LOAD_CONST              15 (('AUTH', 'SELECTED'))

  58         358 MAP_ADD                  1

  88         360 LOAD_CONST              47 ('SEARCH')
             362 LOAD_CONST              21 (('SELECTED',))

  58         364 MAP_ADD                  1

  89         366 LOAD_CONST              48 ('SELECT')
             368 LOAD_CONST              15 (('AUTH', 'SELECTED'))

  58         370 MAP_ADD                  1

  90         372 LOAD_CONST              49 ('SETACL')
             374 LOAD_CONST              15 (('AUTH', 'SELECTED'))

  58         376 MAP_ADD                  1

  91         378 LOAD_CONST              50 ('SETANNOTATION')
             380 LOAD_CONST              15 (('AUTH', 'SELECTED'))

  58         382 MAP_ADD                  1

  92         384 LOAD_CONST              51 ('SETQUOTA')
             386 LOAD_CONST              15 (('AUTH', 'SELECTED'))

  58         388 MAP_ADD                  1

  93         390 LOAD_CONST              52 ('SORT')
             392 LOAD_CONST              21 (('SELECTED',))

  58         394 MAP_ADD                  1
             396 DICT_UPDATE              1

  94         398 LOAD_CONST              17 (('NONAUTH',))

  95         400 LOAD_CONST              15 (('AUTH', 'SELECTED'))

  96         402 LOAD_CONST              21 (('SELECTED',))

  97         404 LOAD_CONST              15 (('AUTH', 'SELECTED'))

  98         406 LOAD_CONST              21 (('SELECTED',))

  99         408 LOAD_CONST              21 (('SELECTED',))

 100         410 LOAD_CONST              15 (('AUTH', 'SELECTED'))

 101         412 LOAD_CONST              21 (('SELECTED',))

  58         414 LOAD_CONST              53 (('STARTTLS', 'STATUS', 'STORE', 'SUBSCRIBE', 'THREAD', 'UID', 'UNSUBSCRIBE', 'UNSELECT'))
             416 BUILD_CONST_KEY_MAP      8
             418 DICT_UPDATE              1
             420 STORE_NAME              26 (Commands)

 106         422 PUSH_NULL
             424 LOAD_NAME                5 (re)
             426 LOAD_ATTR               27 (NULL|self + timedelta)
             446 CACHE
             448 CACHE
             450 CACHE
             452 STORE_NAME              28 (Continuation)

 107         454 PUSH_NULL
             456 LOAD_NAME                5 (re)
             458 LOAD_ATTR               27 (NULL|self + timedelta)
             478 CACHE
             480 CACHE
             482 CACHE
             484 STORE_NAME              29 (Flags)

 108         486 PUSH_NULL
             488 LOAD_NAME                5 (re)
             490 LOAD_ATTR               27 (NULL|self + timedelta)
             510 CACHE
             512 CACHE
             514 CACHE
             516 STORE_NAME              30 (InternalDate)

 114         518 PUSH_NULL
             520 LOAD_NAME                5 (re)
             522 LOAD_ATTR               27 (NULL|self + timedelta)
             542 CACHE
             544 CACHE
             546 UNPACK_SEQUENCE          2
             550 CALL                     2
             558 CACHE
             560 STORE_NAME              32 (Literal)

 115         562 PUSH_NULL
             564 LOAD_NAME                5 (re)
             566 LOAD_ATTR               27 (NULL|self + timedelta)
             586 CACHE
             588 CACHE
             590 CACHE
             592 STORE_NAME              33 (MapCRLF)

 124         594 PUSH_NULL
             596 LOAD_NAME                5 (re)
             598 LOAD_ATTR               27 (NULL|self + timedelta)
             618 CACHE
             620 CACHE
             622 CACHE
             624 STORE_NAME              34 (Response_code)

 125         626 PUSH_NULL
             628 LOAD_NAME                5 (re)
             630 LOAD_ATTR               27 (NULL|self + timedelta)
             650 CACHE
             652 CACHE
             654 CACHE
             656 STORE_NAME              35 (Untagged_response)

 127         658 PUSH_NULL
             660 LOAD_NAME                5 (re)
             662 LOAD_ATTR               27 (NULL|self + timedelta)
             682 CACHE
             684 CACHE

 127         686 UNPACK_SEQUENCE          2
             690 CALL                     2
             698 CACHE
             700 STORE_NAME              36 (Untagged_status)

 130         702 LOAD_CONST              57 (b'.*{(?P<size>\\d+)}$')
             704 STORE_NAME              37 (_Literal)

 131         706 LOAD_CONST              61 (b'\\* (?P<data>\\d+) (?P<type>[A-Z-]+)( (?P<data2>.*))?')
             708 STORE_NAME              38 (_Untagged_status)

 135         710 PUSH_NULL
             712 LOAD_BUILD_CLASS
             714 LOAD_CONST              62 (<code object IMAP4 at 0x000001B2A74AE640, file "imaplib.py", line 135>)
             716 MAKE_FUNCTION            0
             718 LOAD_CONST              63 ('IMAP4')
             720 UNPACK_SEQUENCE          2
             724 CALL                     2
             732 CACHE
             734 STORE_NAME              39 (IMAP4)

1282         736 LOAD_NAME               17 (HAVE_SSL)
             738 POP_JUMP_IF_FALSE       35 (to 810)

1284         740 PUSH_NULL
             742 LOAD_BUILD_CLASS
             744 LOAD_CONST              64 (<code object IMAP4_SSL at 0x000001B2A8A0E330, file "imaplib.py", line 1284>)
             746 MAKE_FUNCTION            0
             748 LOAD_CONST              65 ('IMAP4_SSL')
             750 LOAD_NAME               39 (IMAP4)
             752 UNPACK_SEQUENCE          3
             756 CALL                     3
             764 CACHE
             766 STORE_NAME              40 (IMAP4_SSL)

1338         768 LOAD_NAME               19 (__all__)
             770 STORE_SUBSCR
             774 CACHE
             776 CACHE
             778 CACHE
             780 CACHE
             782 CACHE
             784 CACHE
             786 CACHE
             788 CACHE
             790 CACHE
             792 LOAD_CONST              65 ('IMAP4_SSL')
             794 UNPACK_SEQUENCE          1
             798 CALL                     1
             806 CACHE
             808 POP_TOP

1341     >>  810 PUSH_NULL
             812 LOAD_BUILD_CLASS
             814 LOAD_CONST              66 (<code object IMAP4_stream at 0x000001B2A8A0F230, file "imaplib.py", line 1341>)
             816 MAKE_FUNCTION            0
             818 LOAD_CONST              67 ('IMAP4_stream')
             820 LOAD_NAME               39 (IMAP4)
             822 UNPACK_SEQUENCE          3
             826 CALL                     3
             834 CACHE
             836 STORE_NAME              42 (IMAP4_stream)

1398         838 PUSH_NULL
             840 LOAD_BUILD_CLASS
             842 LOAD_CONST              68 (<code object _Authenticator at 0x000001B2A8A495C0, file "imaplib.py", line 1398>)
             844 MAKE_FUNCTION            0
             846 LOAD_CONST              69 ('_Authenticator')
             848 UNPACK_SEQUENCE          2
             852 CALL                     2
             860 CACHE
             862 STORE_NAME              43 (_Authenticator)

1442         864 LOAD_CONST              70 (' Jan Feb Mar Apr May Jun Jul Aug Sep Oct Nov Dec')
             866 STORE_SUBSCR
             870 CACHE
             872 CACHE
             874 CACHE
             876 CACHE
             878 CACHE
             880 CACHE
             882 CACHE
             884 CACHE
             886 CACHE
             888 LOAD_CONST              71 (' ')
             890 UNPACK_SEQUENCE          1
             894 CALL                     1
             902 CACHE
             904 STORE_NAME              45 (Months)

1443         906 LOAD_CONST              72 (<code object <dictcomp> at 0x000001B2A76AF000, file "imaplib.py", line 1443>)
             908 MAKE_FUNCTION            0
             910 PUSH_NULL
             912 LOAD_NAME               46 (enumerate)
             914 LOAD_NAME               45 (Months)
             916 LOAD_CONST              73 (1)
             918 LOAD_CONST               3 (None)
             920 BUILD_SLICE              2
             922 BINARY_SUBSCR
             926 CACHE
             928 CACHE
             930 CACHE
             932 UNPACK_SEQUENCE          1
             936 CALL                     1
             944 CACHE
             946 GET_ITER
             948 UNPACK_SEQUENCE          0
             952 CALL                     0
             960 CACHE
             962 STORE_NAME              47 (Mon2num)

1445         964 LOAD_CONST              74 (<code object Internaldate2tuple at 0x000001B2A74AECB0, file "imaplib.py", line 1445>)
             966 MAKE_FUNCTION            0
             968 STORE_NAME              48 (Internaldate2tuple)

1480         970 LOAD_CONST              75 (<code object Int2AP at 0x000001B2A8A690B0, file "imaplib.py", line 1480>)
             972 MAKE_FUNCTION            0
             974 STORE_NAME              49 (Int2AP)

1493         976 LOAD_CONST              76 (<code object ParseFlags at 0x000001B2A728DCF0, file "imaplib.py", line 1493>)
             978 MAKE_FUNCTION            0
             980 STORE_NAME              50 (ParseFlags)

1504         982 LOAD_CONST              77 (<code object Time2Internaldate at 0x000001B2A75BD4C0, file "imaplib.py", line 1504>)
             984 MAKE_FUNCTION            0
             986 STORE_NAME              51 (Time2Internaldate)

1545         988 LOAD_NAME               52 (__name__)
             990 LOAD_CONST              78 ('__main__')
             992 COMPARE_OP               2 (<)
             996 CACHE
             998 EXTENDED_ARG             2
            1000 POP_JUMP_IF_FALSE      575 (to 2152)

1551        1002 LOAD_CONST               2 (0)
            1004 LOAD_CONST               3 (None)
            1006 IMPORT_NAME             53 (getopt)
            1008 STORE_NAME              53 (getopt)
            1010 LOAD_CONST               2 (0)
            1012 LOAD_CONST               3 (None)
            1014 IMPORT_NAME             54 (getpass)
            1016 STORE_NAME              54 (getpass)

1553        1018 NOP

1554        1020 PUSH_NULL
            1022 LOAD_NAME               53 (getopt)
            1024 LOAD_ATTR               53 (NULL|self + Commands)
            1044 CACHE
            1046 LOAD_CONST              73 (1)
            1048 LOAD_CONST               3 (None)
            1050 BUILD_SLICE              2
            1052 BINARY_SUBSCR
            1056 CACHE
            1058 CACHE
            1060 CACHE
            1062 LOAD_CONST              79 ('d:s:')
            1064 UNPACK_SEQUENCE          2
            1068 CALL                     2
            1076 CACHE
            1078 UNPACK_SEQUENCE          2
            1082 STORE_NAME              56 (optlist)
            1084 STORE_NAME              57 (args)
            1086 JUMP_FORWARD            28 (to 1144)
         >> 1088 PUSH_EXC_INFO

1555        1090 LOAD_NAME               53 (getopt)
            1092 LOAD_ATTR               58 (Flags)
            1112 CACHE
            1114 STORE_NAME              56 (optlist)
            1116 STORE_NAME              57 (args)
            1118 POP_EXCEPT
            1120 LOAD_CONST               3 (None)
            1122 STORE_NAME              59 (val)
            1124 DELETE_NAME             59 (val)
            1126 JUMP_FORWARD             8 (to 1144)
         >> 1128 LOAD_CONST               3 (None)
            1130 STORE_NAME              59 (val)
            1132 DELETE_NAME             59 (val)
            1134 RERAISE                  1

1555        1136 RERAISE                  0
         >> 1138 COPY                     3
            1140 POP_EXCEPT
            1142 RERAISE                  1

1558     >> 1144 LOAD_CONST               3 (None)
            1146 STORE_NAME              60 (stream_command)

1559        1148 LOAD_NAME               56 (optlist)
            1150 GET_ITER
         >> 1152 FOR_ITER                36 (to 1228)
            1156 CACHE
            1158 STORE_NAME              61 (opt)
            1160 STORE_NAME              59 (val)

1560        1162 LOAD_NAME               61 (opt)
            1164 LOAD_CONST              81 ('-d')
            1166 COMPARE_OP               2 (<)
            1170 CACHE
            1172 POP_JUMP_IF_FALSE       12 (to 1198)

1561        1174 PUSH_NULL
            1176 LOAD_NAME               62 (int)
            1178 LOAD_NAME               59 (val)
            1180 UNPACK_SEQUENCE          1
            1184 CALL                     1
            1192 CACHE
            1194 STORE_NAME              21 (Debug)
            1196 JUMP_BACKWARD           23 (to 1152)

1562     >> 1198 LOAD_NAME               61 (opt)
            1200 LOAD_CONST              82 ('-s')
            1202 COMPARE_OP               2 (<)
            1206 CACHE
            1208 POP_JUMP_IF_FALSE        7 (to 1224)

1563        1210 LOAD_NAME               59 (val)
            1212 STORE_NAME              60 (stream_command)

1564        1214 LOAD_NAME               57 (args)
            1216 POP_JUMP_IF_TRUE         3 (to 1224)
            1218 LOAD_NAME               60 (stream_command)
            1220 BUILD_TUPLE              1
            1222 STORE_NAME              57 (args)
         >> 1224 JUMP_BACKWARD           37 (to 1152)

1566        1226 LOAD_NAME               57 (args)
         >> 1228 POP_JUMP_IF_TRUE         2 (to 1234)
            1230 LOAD_CONST              83 (('',))
            1232 STORE_NAME              57 (args)

1568     >> 1234 LOAD_NAME               57 (args)
            1236 LOAD_CONST               2 (0)
            1238 BINARY_SUBSCR
            1242 CACHE
            1244 CACHE
            1246 CACHE
            1248 STORE_NAME              63 (host)

1570        1250 PUSH_NULL
            1252 LOAD_NAME               54 (getpass)
            1254 LOAD_ATTR               64 (Literal)
            1274 CACHE
            1276 CACHE
            1278 STORE_NAME              65 (USER)

1571        1280 PUSH_NULL
            1282 LOAD_NAME               54 (getpass)
            1284 LOAD_ATTR               54 (compile)
            1304 LOAD_GLOBAL              1 (NULL + __doc__)
            1314 UNPACK_SEQUENCE          1
            1318 CALL                     1
            1326 CACHE
            1328 STORE_NAME              66 (PASSWD)

1573        1330 LOAD_CONST              88 ('From: %(user)s@localhost%(lf)sSubject: IMAP4 test%(lf)s%(lf)sdata...%(lf)s')
            1332 LOAD_NAME               65 (USER)
            1334 LOAD_CONST              89 ('\n')
            1336 LOAD_CONST              90 (('user', 'lf'))
            1338 BUILD_CONST_KEY_MAP      2
            1340 BINARY_OP                6 (%)
            1344 STORE_NAME              67 (test_mesg)

1575        1346 LOAD_CONST              91 ('login')
            1348 LOAD_NAME               65 (USER)
            1350 LOAD_NAME               66 (PASSWD)
            1352 BUILD_TUPLE              2
            1354 BUILD_TUPLE              2

1576        1356 LOAD_CONST              92 (('create', ('/tmp/xxx 1',)))

1577        1358 LOAD_CONST              93 (('rename', ('/tmp/xxx 1', '/tmp/yyy')))

1578        1360 LOAD_CONST              94 (('CREATE', ('/tmp/yyz 2',)))

1579        1362 LOAD_CONST              95 ('append')
            1364 LOAD_CONST              96 ('/tmp/yyz 2')
            1366 LOAD_CONST               3 (None)
            1368 LOAD_CONST               3 (None)
            1370 LOAD_NAME               67 (test_mesg)
            1372 BUILD_TUPLE              4
            1374 BUILD_TUPLE              2

1580        1376 LOAD_CONST              97 (('list', ('/tmp', 'yy*')))

1581        1378 LOAD_CONST              98 (('select', ('/tmp/yyz 2',)))

1582        1380 LOAD_CONST              99 (('search', (None, 'SUBJECT', 'test')))

1583        1382 LOAD_CONST             100 (('fetch', ('1', '(FLAGS INTERNALDATE RFC822)')))

1584        1384 LOAD_CONST             101 (('store', ('1', 'FLAGS', '(\\Deleted)')))

1585        1386 LOAD_CONST             102 (('namespace', ()))

1586        1388 LOAD_CONST             103 (('expunge', ()))

1587        1390 LOAD_CONST             104 (('recent', ()))

1588        1392 LOAD_CONST             105 (('close', ()))

1574        1394 BUILD_TUPLE             14
            1396 STORE_NAME              68 (test_seq1)

1592        1398 LOAD_CONST             106 (('select', ()))

1593        1400 LOAD_CONST             107 (('response', ('UIDVALIDITY',)))

1594        1402 LOAD_CONST             108 (('uid', ('SEARCH', 'ALL')))

1595        1404 LOAD_CONST             109 (('response', ('EXISTS',)))

1596        1406 LOAD_CONST              95 ('append')
            1408 LOAD_CONST               3 (None)
            1410 LOAD_CONST               3 (None)
            1412 LOAD_CONST               3 (None)
            1414 LOAD_NAME               67 (test_mesg)
            1416 BUILD_TUPLE              4
            1418 BUILD_TUPLE              2

1597        1420 LOAD_CONST             104 (('recent', ()))

1598        1422 LOAD_CONST             110 (('logout', ()))

1591        1424 BUILD_TUPLE              7
            1426 STORE_NAME              69 (test_seq2)

1601        1428 LOAD_CONST             111 (<code object run at 0x000001B2A8A746B0, file "imaplib.py", line 1601>)
            1430 MAKE_FUNCTION            0
            1432 STORE_NAME              70 (run)

1608        1434 NOP

1609        1436 LOAD_NAME               60 (stream_command)
            1438 POP_JUMP_IF_FALSE       12 (to 1464)

1610        1440 PUSH_NULL
            1442 LOAD_NAME               42 (IMAP4_stream)
            1444 LOAD_NAME               60 (stream_command)
            1446 UNPACK_SEQUENCE          1
            1450 CALL                     1
            1458 CACHE
            1460 STORE_NAME              71 (M)
            1462 JUMP_FORWARD            11 (to 1486)

1612     >> 1464 PUSH_NULL
            1466 LOAD_NAME               39 (IMAP4)
            1468 LOAD_NAME               63 (host)
            1470 UNPACK_SEQUENCE          1
            1474 CALL                     1
            1482 CACHE
            1484 STORE_NAME              71 (M)

1613     >> 1486 LOAD_NAME               71 (M)
            1488 LOAD_ATTR               72 (Untagged_status)

1614        1508 LOAD_NAME               68 (test_seq1)
            1510 LOAD_CONST              73 (1)
            1512 LOAD_CONST               3 (None)
            1514 BUILD_SLICE              2
            1516 BINARY_SUBSCR
            1520 CACHE
            1522 CACHE
            1524 CACHE
            1526 STORE_NAME              68 (test_seq1)

1615        1528 LOAD_NAME               71 (M)
            1530 STORE_SUBSCR
            1534 CACHE
            1536 CACHE
            1538 CACHE
            1540 CACHE
            1542 CACHE
            1544 CACHE
            1546 CACHE
            1548 CACHE
            1550 CACHE
            1552 LOAD_CONST             113 ('PROTOCOL_VERSION = %s')
            1554 LOAD_NAME               71 (M)
            1556 LOAD_ATTR               74 (_Literal)
            1576 CACHE
            1578 CACHE
            1580 CACHE
            1582 CACHE
            1584 POP_TOP

1616        1586 LOAD_NAME               71 (M)
            1588 STORE_SUBSCR
            1592 CACHE
            1594 CACHE
            1596 CACHE
            1598 CACHE
            1600 CACHE
            1602 CACHE
            1604 CACHE
            1606 CACHE
            1608 CACHE
            1610 LOAD_CONST             114 ('CAPABILITIES = ')
            1612 LOAD_NAME               71 (M)
            1614 LOAD_ATTR               75 (NULL|self + _Literal)
            1634 CACHE
            1636 CACHE
            1638 CACHE
            1640 CACHE
            1642 POP_TOP

1618        1644 LOAD_NAME               68 (test_seq1)
            1646 GET_ITER
         >> 1648 FOR_ITER                17 (to 1686)
            1652 CACHE
            1654 STORE_NAME              76 (cmd)
            1656 STORE_NAME              57 (args)

1619        1658 PUSH_NULL
            1660 LOAD_NAME               70 (run)
            1662 LOAD_NAME               76 (cmd)
            1664 LOAD_NAME               57 (args)
            1666 UNPACK_SEQUENCE          2
            1670 CALL                     2
            1678 CACHE
            1680 POP_TOP
            1682 JUMP_BACKWARD           18 (to 1648)

1621        1684 PUSH_NULL
         >> 1686 LOAD_NAME               70 (run)
            1688 LOAD_CONST             115 ('list')
            1690 LOAD_CONST             116 (('/tmp/', 'yy%'))
            1692 UNPACK_SEQUENCE          2
            1696 CALL                     2
            1704 CACHE
            1706 GET_ITER
         >> 1708 FOR_ITER                82 (to 1876)

1622        1712 PUSH_NULL
            1714 LOAD_NAME                5 (re)
            1716 LOAD_ATTR               78 (IMAP4)
            1736 CACHE
            1738 CACHE
            1740 CACHE
            1742 CACHE
            1744 STORE_NAME              79 (mo)

1623        1746 LOAD_NAME               79 (mo)
            1748 POP_JUMP_IF_FALSE       22 (to 1794)
            1750 LOAD_NAME               79 (mo)
            1752 STORE_SUBSCR
            1756 CACHE
            1758 CACHE
            1760 CACHE
            1762 CACHE
            1764 CACHE
            1766 CACHE
            1768 CACHE
            1770 CACHE
            1772 CACHE
            1774 LOAD_CONST              73 (1)
            1776 UNPACK_SEQUENCE          1
            1780 CALL                     1
            1788 CACHE
            1790 STORE_NAME              81 (path)
            1792 JUMP_FORWARD            26 (to 1846)

1624     >> 1794 LOAD_NAME               77 (ml)
            1796 STORE_SUBSCR
            1800 CACHE
            1802 CACHE
            1804 CACHE
            1806 CACHE
            1808 CACHE
            1810 CACHE
            1812 CACHE
            1814 CACHE
            1816 CACHE
            1818 UNPACK_SEQUENCE          0
            1822 CALL                     0
            1830 CACHE
            1832 LOAD_CONST             118 (-1)
            1834 BINARY_SUBSCR
            1838 CACHE
            1840 CACHE
            1842 CACHE
            1844 STORE_NAME              81 (path)

1625     >> 1846 PUSH_NULL
            1848 LOAD_NAME               70 (run)
            1850 LOAD_CONST             119 ('delete')
            1852 LOAD_NAME               81 (path)
            1854 BUILD_TUPLE              1
            1856 UNPACK_SEQUENCE          2
            1860 CALL                     2
            1868 CACHE
            1870 POP_TOP
            1872 JUMP_BACKWARD           83 (to 1708)

1627        1874 LOAD_NAME               69 (test_seq2)
         >> 1876 GET_ITER
         >> 1878 FOR_ITER                79 (to 2040)
            1882 CACHE
            1884 STORE_NAME              76 (cmd)
            1886 STORE_NAME              57 (args)

1628        1888 PUSH_NULL
            1890 LOAD_NAME               70 (run)
            1892 LOAD_NAME               76 (cmd)
            1894 LOAD_NAME               57 (args)
            1896 UNPACK_SEQUENCE          2
            1900 CALL                     2
            1908 CACHE
            1910 STORE_NAME              82 (dat)

1630        1912 LOAD_NAME               76 (cmd)
            1914 LOAD_NAME               57 (args)
            1916 BUILD_TUPLE              2
            1918 LOAD_CONST             108 (('uid', ('SEARCH', 'ALL')))
            1920 COMPARE_OP               3 (<)
            1924 CACHE
            1926 POP_JUMP_IF_FALSE        1 (to 1930)

1631        1928 JUMP_BACKWARD           26 (to 1878)

1633     >> 1930 LOAD_NAME               82 (dat)
            1932 LOAD_CONST             118 (-1)
            1934 BINARY_SUBSCR
            1938 CACHE
            1940 CACHE
            1942 CACHE
            1944 STORE_SUBSCR
            1948 CACHE
            1950 CACHE
            1952 CACHE
            1954 CACHE
            1956 CACHE
            1958 CACHE
            1960 CACHE
            1962 CACHE
            1964 CACHE
            1966 UNPACK_SEQUENCE          0
            1970 CALL                     0
            1978 CACHE
            1980 STORE_NAME              83 (uid)

1634        1982 LOAD_NAME               83 (uid)
            1984 POP_JUMP_IF_TRUE         1 (to 1988)
            1986 JUMP_BACKWARD           55 (to 1878)

1635     >> 1988 PUSH_NULL
            1990 LOAD_NAME               70 (run)
            1992 LOAD_CONST             120 ('uid')
            1994 LOAD_CONST              31 ('FETCH')
            1996 LOAD_CONST             121 ('%s')
            1998 LOAD_NAME               83 (uid)
            2000 LOAD_CONST             118 (-1)
            2002 BINARY_SUBSCR
            2006 CACHE
            2008 CACHE
            2010 CACHE
            2012 BINARY_OP                6 (%)

1636        2016 LOAD_CONST             122 ('(FLAGS INTERNALDATE RFC822.SIZE RFC822.HEADER RFC822.TEXT)')

1635        2018 BUILD_TUPLE              3
            2020 UNPACK_SEQUENCE          2
            2024 CALL                     2
            2032 CACHE
            2034 POP_TOP
            2036 JUMP_BACKWARD           80 (to 1878)

1638        2038 PUSH_NULL
         >> 2040 LOAD_NAME               84 (print)
            2042 LOAD_CONST             123 ('\nAll tests OK.')
            2044 UNPACK_SEQUENCE          1
            2048 CALL                     1
            2056 CACHE
            2058 POP_TOP
            2060 LOAD_CONST               3 (None)
            2062 RETURN_VALUE
         >> 2064 PUSH_EXC_INFO

1640        2066 POP_TOP

1641        2068 PUSH_NULL
            2070 LOAD_NAME               84 (print)
            2072 LOAD_CONST             124 ('\nTests failed.')
            2074 UNPACK_SEQUENCE          1
            2078 CALL                     1
            2086 CACHE
            2088 POP_TOP

1643        2090 LOAD_NAME               21 (Debug)
            2092 POP_JUMP_IF_TRUE        25 (to 2144)

1644        2094 PUSH_NULL
            2096 LOAD_NAME               84 (print)
            2098 LOAD_CONST             125 ('\nIf you would like to see debugging output,\ntry: %s -d5\n')

1647        2100 LOAD_NAME                8 (sys)
            2102 LOAD_ATTR               55 (NULL|self + compile)
            2122 CACHE

1644        2124 BINARY_OP                6 (%)
            2128 UNPACK_SEQUENCE          1
            2132 CALL                     1
            2140 CACHE
            2142 POP_TOP

1649     >> 2144 RAISE_VARARGS            0
         >> 2146 COPY                     3
            2148 POP_EXCEPT
            2150 RERAISE                  1

1545     >> 2152 LOAD_CONST               3 (None)
            2154 RETURN_VALUE
ExceptionTable:
  116 to 126 -> 130 [0]
  130 to 142 -> 150 [1] lasti
  148 to 148 -> 150 [1] lasti
  1020 to 1084 -> 1088 [0]
  1088 to 1106 -> 1138 [1] lasti
  1108 to 1116 -> 1128 [1] lasti
  1128 to 1136 -> 1138 [1] lasti
  1436 to 2058 -> 2064 [0]
  2064 to 2144 -> 2146 [1] lasti

Disassembly of <code object IMAP4 at 0x000001B2A74AE640, file "imaplib.py", line 135>:
 135           0 RESUME                   0
               2 LOAD_NAME                0 (__name__)
               4 STORE_NAME               1 (__module__)
               6 LOAD_CONST               0 ('IMAP4')
               8 STORE_NAME               2 (__qualname__)

 137          10 LOAD_CONST               1 ('IMAP4 client class.\n\n    Instantiate with: IMAP4([host[, port[, timeout=None]]])\n\n            host - host\'s name (default: localhost);\n            port - port number (default: standard IMAP4 port).\n            timeout - socket timeout (default: None)\n                      If timeout is not given or is None,\n                      the global default socket timeout is used\n\n    All IMAP4rev1 commands are supported by methods of the same\n    name (in lowercase).\n\n    All arguments to commands are converted to strings, except for\n    AUTHENTICATE, and the last argument to APPEND which is passed as\n    an IMAP4 literal.  If necessary (the string contains any\n    non-printing characters or white-space and isn\'t enclosed with\n    either parentheses or double quotes) each string is quoted.\n    However, the \'password\' argument to the LOGIN command is always\n    quoted.  If you want to avoid having an argument string quoted\n    (eg: the \'flags\' argument to STORE) then enclose the string in\n    parentheses (eg: "(\\Deleted)").\n\n    Each command returns a tuple: (type, [data, ...]) where \'type\'\n    is usually \'OK\' or \'NO\', and \'data\' is either the text from the\n    tagged response, or untagged results from command. Each \'data\'\n    is either a string, or a tuple. If a tuple, then the first part\n    is the header of the response, and the second part contains\n    the data (ie: \'literal\' value).\n\n    Errors raise the exception class <instance>.error("<reason>").\n    IMAP4 server errors raise <instance>.abort("<reason>"),\n    which is a sub-class of \'error\'. Mailbox status changes\n    from READ-WRITE to READ-ONLY raise the exception class\n    <instance>.readonly("<reason>"), which is a sub-class of \'abort\'.\n\n    "error" exceptions imply a program error.\n    "abort" exceptions imply the connection should be reset, and\n            the command re-tried.\n    "readonly" exceptions imply the command should be re-tried.\n\n    Note: to use this module, you must read the RFCs pertaining to the\n    IMAP4 protocol, as the semantics of the arguments to each IMAP4\n    command are left to the invoker, not to mention the results. Also,\n    most IMAP servers implement a sub-set of the commands available here.\n    ')
              12 STORE_NAME               3 (__doc__)

 184          14 PUSH_NULL
              16 LOAD_BUILD_CLASS
              18 LOAD_CONST               2 (<code object error at 0x000001B2A8A102A0, file "imaplib.py", line 184>)
              20 MAKE_FUNCTION            0
              22 LOAD_CONST               3 ('error')
              24 LOAD_NAME                4 (Exception)
              26 UNPACK_SEQUENCE          3
              30 CALL                     3
              38 CACHE
              40 STORE_NAME               5 (error)

 185          42 PUSH_NULL
              44 LOAD_BUILD_CLASS
              46 LOAD_CONST               4 (<code object abort at 0x000001B2A8A105E0, file "imaplib.py", line 185>)
              48 MAKE_FUNCTION            0
              50 LOAD_CONST               5 ('abort')
              52 LOAD_NAME                5 (error)
              54 UNPACK_SEQUENCE          3
              58 CALL                     3
              66 CACHE
              68 STORE_NAME               6 (abort)

 186          70 PUSH_NULL
              72 LOAD_BUILD_CLASS
              74 LOAD_CONST               6 (<code object readonly at 0x000001B2A8A11210, file "imaplib.py", line 186>)
              76 MAKE_FUNCTION            0
              78 LOAD_CONST               7 ('readonly')
              80 LOAD_NAME                6 (abort)
              82 UNPACK_SEQUENCE          3
              86 CALL                     3
              94 CACHE
              96 STORE_NAME               7 (readonly)

 188          98 LOAD_CONST               8 ('')
             100 LOAD_NAME                8 (IMAP4_PORT)
             102 LOAD_CONST               9 (None)
             104 BUILD_TUPLE              3
             106 LOAD_CONST              10 (<code object __init__ at 0x000001B2A75E3200, file "imaplib.py", line 188>)
             108 MAKE_FUNCTION            1 (defaults)
             110 STORE_NAME               9 (__init__)

 213         112 LOAD_CONST              11 (<code object _mode_ascii at 0x000001B2A8A2C4E0, file "imaplib.py", line 213>)
             114 MAKE_FUNCTION            0
             116 STORE_NAME              10 (_mode_ascii)

 220         118 LOAD_CONST              12 (<code object _mode_utf8 at 0x000001B2A71D39F0, file "imaplib.py", line 220>)
             120 MAKE_FUNCTION            0
             122 STORE_NAME              11 (_mode_utf8)

 227         124 LOAD_CONST              13 (<code object _connect at 0x000001B2A74AA920, file "imaplib.py", line 227>)
             126 MAKE_FUNCTION            0
             128 STORE_NAME              12 (_connect)

 269         130 LOAD_CONST              14 (<code object __getattr__ at 0x000001B2A8A09570, file "imaplib.py", line 269>)
             132 MAKE_FUNCTION            0
             134 STORE_NAME              13 (__getattr__)

 275         136 LOAD_CONST              15 (<code object __enter__ at 0x000001B2A8A11070, file "imaplib.py", line 275>)
             138 MAKE_FUNCTION            0
             140 STORE_NAME              14 (__enter__)

 278         142 LOAD_CONST              16 (<code object __exit__ at 0x000001B2A76B5CB0, file "imaplib.py", line 278>)
             144 MAKE_FUNCTION            0
             146 STORE_NAME              15 (__exit__)

 291         148 LOAD_CONST              17 (<code object _create_socket at 0x000001B2A722EA30, file "imaplib.py", line 291>)
             150 MAKE_FUNCTION            0
             152 STORE_NAME              16 (_create_socket)

 304         154 LOAD_CONST               8 ('')
             156 LOAD_NAME                8 (IMAP4_PORT)
             158 LOAD_CONST               9 (None)
             160 BUILD_TUPLE              3
             162 LOAD_CONST              18 (<code object open at 0x000001B2A71D3B50, file "imaplib.py", line 304>)
             164 MAKE_FUNCTION            1 (defaults)
             166 STORE_NAME              17 (open)

 316         168 LOAD_CONST              19 (<code object read at 0x000001B2A8A0CF30, file "imaplib.py", line 316>)
             170 MAKE_FUNCTION            0
             172 STORE_NAME              18 (read)

 321         174 LOAD_CONST              20 (<code object readline at 0x000001B2A72116B0, file "imaplib.py", line 321>)
             176 MAKE_FUNCTION            0
             178 STORE_NAME              19 (readline)

 329         180 LOAD_CONST              21 (<code object send at 0x000001B2A76B63D0, file "imaplib.py", line 329>)
             182 MAKE_FUNCTION            0
             184 STORE_NAME              20 (send)

 335         186 LOAD_CONST              22 (<code object shutdown at 0x000001B2A75E2900, file "imaplib.py", line 335>)
             188 MAKE_FUNCTION            0
             190 STORE_NAME              21 (shutdown)

 351         192 LOAD_CONST              23 (<code object socket at 0x000001B2A8A10030, file "imaplib.py", line 351>)
             194 MAKE_FUNCTION            0
             196 STORE_NAME              22 (socket)

 363         198 LOAD_CONST              24 (<code object recent at 0x000001B2A728FB20, file "imaplib.py", line 363>)
             200 MAKE_FUNCTION            0
             202 STORE_NAME              23 (recent)

 380         204 LOAD_CONST              25 (<code object response at 0x000001B2A8A4C810, file "imaplib.py", line 380>)
             206 MAKE_FUNCTION            0
             208 STORE_NAME              24 (response)

 394         210 LOAD_CONST              26 (<code object append at 0x000001B2A71AE810, file "imaplib.py", line 394>)
             212 MAKE_FUNCTION            0
             214 STORE_NAME              25 (append)

 420         216 LOAD_CONST              27 (<code object authenticate at 0x000001B2A71AF860, file "imaplib.py", line 420>)
             218 MAKE_FUNCTION            0
             220 STORE_NAME              26 (authenticate)

 449         222 LOAD_CONST              28 (<code object capability at 0x000001B2A76B6890, file "imaplib.py", line 449>)
             224 MAKE_FUNCTION            0
             226 STORE_NAME              27 (capability)

 458         228 LOAD_CONST              29 (<code object check at 0x000001B2A8A483F0, file "imaplib.py", line 458>)
             230 MAKE_FUNCTION            0
             232 STORE_NAME              28 (check)

 466         234 LOAD_CONST              30 (<code object close at 0x000001B2A76B6AF0, file "imaplib.py", line 466>)
             236 MAKE_FUNCTION            0
             238 STORE_NAME              29 (close)

 481         240 LOAD_CONST              31 (<code object copy at 0x000001B2A8A484E0, file "imaplib.py", line 481>)
             242 MAKE_FUNCTION            0
             244 STORE_NAME              30 (copy)

 489         246 LOAD_CONST              32 (<code object create at 0x000001B2A8A485D0, file "imaplib.py", line 489>)
             248 MAKE_FUNCTION            0
             250 STORE_NAME              31 (create)

 497         252 LOAD_CONST              33 (<code object delete at 0x000001B2A8A487B0, file "imaplib.py", line 497>)
             254 MAKE_FUNCTION            0
             256 STORE_NAME              32 (delete)

 504         258 LOAD_CONST              34 (<code object deleteacl at 0x000001B2A8A488A0, file "imaplib.py", line 504>)
             260 MAKE_FUNCTION            0
             262 STORE_NAME              33 (deleteacl)

 511         264 LOAD_CONST              35 (<code object enable at 0x000001B2A726F2D0, file "imaplib.py", line 511>)
             266 MAKE_FUNCTION            0
             268 STORE_NAME              34 (enable)

 523         270 LOAD_CONST              36 (<code object expunge at 0x000001B2A76B49B0, file "imaplib.py", line 523>)
             272 MAKE_FUNCTION            0
             274 STORE_NAME              35 (expunge)

 537         276 LOAD_CONST              37 (<code object fetch at 0x000001B2A76B70E0, file "imaplib.py", line 537>)
             278 MAKE_FUNCTION            0
             280 STORE_NAME              36 (fetch)

 552         282 LOAD_CONST              38 (<code object getacl at 0x000001B2A76B7210, file "imaplib.py", line 552>)
             284 MAKE_FUNCTION            0
             286 STORE_NAME              37 (getacl)

 561         288 LOAD_CONST              39 (<code object getannotation at 0x000001B2A76B7340, file "imaplib.py", line 561>)
             290 MAKE_FUNCTION            0
             292 STORE_NAME              38 (getannotation)

 569         294 LOAD_CONST              40 (<code object getquota at 0x000001B2A76B7470, file "imaplib.py", line 569>)
             296 MAKE_FUNCTION            0
             298 STORE_NAME              39 (getquota)

 580         300 LOAD_CONST              41 (<code object getquotaroot at 0x000001B2A728F840, file "imaplib.py", line 580>)
             302 MAKE_FUNCTION            0
             304 STORE_NAME              40 (getquotaroot)

 591         306 LOAD_CONST              91 (('""', '*'))
             308 LOAD_CONST              44 (<code object list at 0x000001B2A76B75A0, file "imaplib.py", line 591>)
             310 MAKE_FUNCTION            1 (defaults)
             312 STORE_NAME              41 (list)

 603         314 LOAD_CONST              45 (<code object login at 0x000001B2A7212730, file "imaplib.py", line 603>)
             316 MAKE_FUNCTION            0
             318 STORE_NAME              42 (login)

 617         320 LOAD_CONST              46 (<code object login_cram_md5 at 0x000001B2A8A4CC90, file "imaplib.py", line 617>)
             322 MAKE_FUNCTION            0
             324 STORE_NAME              43 (login_cram_md5)

 626         326 LOAD_CONST              47 (<code object _CRAM_MD5_AUTH at 0x000001B2A726F120, file "imaplib.py", line 626>)
             328 MAKE_FUNCTION            0
             330 STORE_NAME              44 (_CRAM_MD5_AUTH)

 634         332 LOAD_CONST              48 (<code object logout at 0x000001B2A76B76D0, file "imaplib.py", line 634>)
             334 MAKE_FUNCTION            0
             336 STORE_NAME              45 (logout)

 647         338 LOAD_CONST              91 (('""', '*'))
             340 LOAD_CONST              49 (<code object lsub at 0x000001B2A76B7800, file "imaplib.py", line 647>)
             342 MAKE_FUNCTION            1 (defaults)
             344 STORE_NAME              46 (lsub)

 658         346 LOAD_CONST              50 (<code object myrights at 0x000001B2A76B7930, file "imaplib.py", line 658>)
             348 MAKE_FUNCTION            0
             350 STORE_NAME              47 (myrights)

 666         352 LOAD_CONST              51 (<code object namespace at 0x000001B2A76B7A60, file "imaplib.py", line 666>)
             354 MAKE_FUNCTION            0
             356 STORE_NAME              48 (namespace)

 676         358 LOAD_CONST              52 (<code object noop at 0x000001B2A8A09430, file "imaplib.py", line 676>)
             360 MAKE_FUNCTION            0
             362 STORE_NAME              49 (noop)

 687         364 LOAD_CONST              53 (<code object partial at 0x000001B2A76B7B90, file "imaplib.py", line 687>)
             366 MAKE_FUNCTION            0
             368 STORE_NAME              50 (partial)

 699         370 LOAD_CONST              54 (<code object proxyauth at 0x000001B2A8A0D830, file "imaplib.py", line 699>)
             372 MAKE_FUNCTION            0
             374 STORE_NAME              51 (proxyauth)

 712         376 LOAD_CONST              55 (<code object rename at 0x000001B2A8A48210, file "imaplib.py", line 712>)
             378 MAKE_FUNCTION            0
             380 STORE_NAME              52 (rename)

 720         382 LOAD_CONST              56 (<code object search at 0x000001B2A8A2C800, file "imaplib.py", line 720>)
             384 MAKE_FUNCTION            0
             386 STORE_NAME              53 (search)

 738         388 LOAD_CONST              92 (('INBOX', False))
             390 LOAD_CONST              59 (<code object select at 0x000001B2A75D7680, file "imaplib.py", line 738>)
             392 MAKE_FUNCTION            1 (defaults)
             394 STORE_NAME              54 (select)

 770         396 LOAD_CONST              60 (<code object setacl at 0x000001B2A8A0DD30, file "imaplib.py", line 770>)
             398 MAKE_FUNCTION            0
             400 STORE_NAME              55 (setacl)

 778         402 LOAD_CONST              61 (<code object setannotation at 0x000001B2A8A4CED0, file "imaplib.py", line 778>)
             404 MAKE_FUNCTION            0
             406 STORE_NAME              56 (setannotation)

 786         408 LOAD_CONST              62 (<code object setquota at 0x000001B2A76B7CC0, file "imaplib.py", line 786>)
             410 MAKE_FUNCTION            0
             412 STORE_NAME              57 (setquota)

 795         414 LOAD_CONST              63 (<code object sort at 0x000001B2A71C7C90, file "imaplib.py", line 795>)
             416 MAKE_FUNCTION            0
             418 STORE_NAME              58 (sort)

 809         420 LOAD_CONST              93 ((None,))
             422 LOAD_CONST              64 (<code object starttls at 0x000001B2A7565540, file "imaplib.py", line 809>)
             424 MAKE_FUNCTION            1 (defaults)
             426 STORE_NAME              59 (starttls)

 832         428 LOAD_CONST              65 (<code object status at 0x000001B2A76B7DF0, file "imaplib.py", line 832>)
             430 MAKE_FUNCTION            0
             432 STORE_NAME              60 (status)

 844         434 LOAD_CONST              66 (<code object store at 0x000001B2A71D3CB0, file "imaplib.py", line 844>)
             436 MAKE_FUNCTION            0
             438 STORE_NAME              61 (store)

 855         440 LOAD_CONST              67 (<code object subscribe at 0x000001B2A8A48C60, file "imaplib.py", line 855>)
             442 MAKE_FUNCTION            0
             444 STORE_NAME              62 (subscribe)

 863         446 LOAD_CONST              68 (<code object thread at 0x000001B2A8A4CFF0, file "imaplib.py", line 863>)
             448 MAKE_FUNCTION            0
             450 STORE_NAME              63 (thread)

 873         452 LOAD_CONST              69 (<code object uid at 0x000001B2A75E3D40, file "imaplib.py", line 873>)
             454 MAKE_FUNCTION            0
             456 STORE_NAME              64 (uid)

 898         458 LOAD_CONST              70 (<code object unsubscribe at 0x000001B2A8A48F30, file "imaplib.py", line 898>)
             460 MAKE_FUNCTION            0
             462 STORE_NAME              65 (unsubscribe)

 906         464 LOAD_CONST              71 (<code object unselect at 0x000001B2A8A90030, file "imaplib.py", line 906>)
             466 MAKE_FUNCTION            0
             468 STORE_NAME              66 (unselect)

 922         470 LOAD_CONST              72 (<code object xatom at 0x000001B2A8A09930, file "imaplib.py", line 922>)
             472 MAKE_FUNCTION            0
             474 STORE_NAME              67 (xatom)

 944         476 LOAD_CONST              73 (<code object _append_untagged at 0x000001B2A71AF120, file "imaplib.py", line 944>)
             478 MAKE_FUNCTION            0
             480 STORE_NAME              68 (_append_untagged)

 958         482 LOAD_CONST              74 (<code object _check_bye at 0x000001B2A728E420, file "imaplib.py", line 958>)
             484 MAKE_FUNCTION            0
             486 STORE_NAME              69 (_check_bye)

 964         488 LOAD_CONST              75 (<code object _command at 0x000001B2A74A2F20, file "imaplib.py", line 964>)
             490 MAKE_FUNCTION            0
             492 STORE_NAME              70 (_command)

1041         494 LOAD_CONST              76 (<code object _command_complete at 0x000001B2A755F800, file "imaplib.py", line 1041>)
             496 MAKE_FUNCTION            0
             498 STORE_NAME              71 (_command_complete)

1059         500 LOAD_CONST              77 (<code object _get_capabilities at 0x000001B2A721A010, file "imaplib.py", line 1059>)
             502 MAKE_FUNCTION            0
             504 STORE_NAME              72 (_get_capabilities)

1068         506 LOAD_CONST              78 (<code object _get_response at 0x000001B2A74ACE90, file "imaplib.py", line 1068>)
             508 MAKE_FUNCTION            0
             510 STORE_NAME              73 (_get_response)

1148         512 LOAD_CONST              94 ((False,))
             514 LOAD_CONST              79 (<code object _get_tagged_response at 0x000001B2A8A1C230, file "imaplib.py", line 1148>)
             516 MAKE_FUNCTION            1 (defaults)
             518 STORE_NAME              74 (_get_tagged_response)

1181         520 LOAD_CONST              80 (<code object _get_line at 0x000001B2A75DE4E0, file "imaplib.py", line 1181>)
             522 MAKE_FUNCTION            0
             524 STORE_NAME              75 (_get_line)

1200         526 LOAD_CONST              81 (<code object _match at 0x000001B2A8A74510, file "imaplib.py", line 1200>)
             528 MAKE_FUNCTION            0
             530 STORE_NAME              76 (_match)

1212         532 LOAD_CONST              82 (<code object _new_tag at 0x000001B2A8A682F0, file "imaplib.py", line 1212>)
             534 MAKE_FUNCTION            0
             536 STORE_NAME              77 (_new_tag)

1220         538 LOAD_CONST              83 (<code object _quote at 0x000001B2A8A90290, file "imaplib.py", line 1220>)
             540 MAKE_FUNCTION            0
             542 STORE_NAME              78 (_quote)

1228         544 LOAD_CONST              84 (<code object _simple_command at 0x000001B2A76AF990, file "imaplib.py", line 1228>)
             546 MAKE_FUNCTION            0
             548 STORE_NAME              79 (_simple_command)

1233         550 LOAD_CONST              85 (<code object _untagged_response at 0x000001B2A7212BB0, file "imaplib.py", line 1233>)
             552 MAKE_FUNCTION            0
             554 STORE_NAME              80 (_untagged_response)

1245         556 NOP

1247         558 LOAD_CONST              93 ((None,))
             560 LOAD_CONST              87 (<code object _mesg at 0x000001B2A71AF690, file "imaplib.py", line 1247>)
             562 MAKE_FUNCTION            1 (defaults)
             564 STORE_NAME              81 (_mesg)

1254         566 LOAD_CONST              88 (<code object _dump_ur at 0x000001B2A8A68C90, file "imaplib.py", line 1254>)
             568 MAKE_FUNCTION            0
             570 STORE_NAME              82 (_dump_ur)

1261         572 LOAD_CONST              89 (<code object _log at 0x000001B2A8A68DF0, file "imaplib.py", line 1261>)
             574 MAKE_FUNCTION            0
             576 STORE_NAME              83 (_log)

1268         578 LOAD_CONST              90 (<code object print_log at 0x000001B2A726F480, file "imaplib.py", line 1268>)
             580 MAKE_FUNCTION            0
             582 STORE_NAME              84 (print_log)
             584 LOAD_CONST               9 (None)
             586 RETURN_VALUE

Disassembly of <code object error at 0x000001B2A8A102A0, file "imaplib.py", line 184>:
184           0 RESUME                   0
              2 LOAD_NAME                0 (__name__)
              4 STORE_NAME               1 (__module__)
              6 LOAD_CONST               0 ('IMAP4.error')
              8 STORE_NAME               2 (__qualname__)
             10 LOAD_CONST               1 (None)
             12 RETURN_VALUE

Disassembly of <code object abort at 0x000001B2A8A105E0, file "imaplib.py", line 185>:
185           0 RESUME                   0
              2 LOAD_NAME                0 (__name__)
              4 STORE_NAME               1 (__module__)
              6 LOAD_CONST               0 ('IMAP4.abort')
              8 STORE_NAME               2 (__qualname__)
             10 LOAD_CONST               1 (None)
             12 RETURN_VALUE

Disassembly of <code object readonly at 0x000001B2A8A11210, file "imaplib.py", line 186>:
186           0 RESUME                   0
              2 LOAD_NAME                0 (__name__)
              4 STORE_NAME               1 (__module__)
              6 LOAD_CONST               0 ('IMAP4.readonly')
              8 STORE_NAME               2 (__qualname__)
             10 LOAD_CONST               1 (None)
             12 RETURN_VALUE

Disassembly of <code object __init__ at 0x000001B2A75E3200, file "imaplib.py", line 188>:
188           0 RESUME                   0

189           2 LOAD_GLOBAL              0 (Debug)
             12 CACHE
             14 LOAD_FAST                0 (self)
             16 STORE_ATTR               1 (debug)

190          26 LOAD_CONST               1 ('LOGOUT')
             28 LOAD_FAST                0 (self)
             30 STORE_ATTR               2 (state)

191          40 LOAD_CONST               0 (None)
             42 LOAD_FAST                0 (self)
             44 STORE_ATTR               3 (literal)

192          54 BUILD_MAP                0
             56 LOAD_FAST                0 (self)
             58 STORE_ATTR               4 (tagged_commands)

193          68 BUILD_MAP                0
             70 LOAD_FAST                0 (self)
             72 STORE_ATTR               5 (untagged_responses)

194          82 LOAD_CONST               2 ('')
             84 LOAD_FAST                0 (self)
             86 STORE_ATTR               6 (continuation_response)

195          96 LOAD_CONST               3 (False)
             98 LOAD_FAST                0 (self)
            100 STORE_ATTR               7 (is_readonly)

196         110 LOAD_CONST               4 (0)
            112 LOAD_FAST                0 (self)
            114 STORE_ATTR               8 (tagnum)

197         124 LOAD_CONST               3 (False)
            126 LOAD_FAST                0 (self)
            128 STORE_ATTR               9 (_tls_established)

198         138 LOAD_FAST                0 (self)
            140 STORE_SUBSCR
            144 CACHE
            146 CACHE
            148 CACHE
            150 CACHE
            152 CACHE
            154 CACHE
            156 CACHE
            158 CACHE
            160 CACHE
            162 UNPACK_SEQUENCE          0
            166 CALL                     0
            174 CACHE
            176 POP_TOP

202         178 LOAD_FAST                0 (self)
            180 STORE_SUBSCR
            184 CACHE
            186 CACHE
            188 CACHE
            190 CACHE
            192 CACHE
            194 CACHE
            196 CACHE
            198 CACHE
            200 CACHE
            202 LOAD_FAST                1 (host)
            204 LOAD_FAST                2 (port)
            206 LOAD_FAST                3 (timeout)
            208 UNPACK_SEQUENCE          3
            212 CALL                     3
            220 CACHE
            222 POP_TOP

204         224 NOP

205         226 LOAD_FAST                0 (self)
            228 STORE_SUBSCR
            232 CACHE
            234 CACHE
            236 CACHE
            238 CACHE
            240 CACHE
            242 CACHE
            244 CACHE
            246 CACHE
            248 CACHE
            250 UNPACK_SEQUENCE          0
            254 CALL                     0
            262 CACHE
            264 POP_TOP
            266 LOAD_CONST               0 (None)
            268 RETURN_VALUE
        >>  270 PUSH_EXC_INFO

206         272 LOAD_GLOBAL             26 (Exception)
            282 CACHE
            284 CHECK_EXC_MATCH
            286 POP_JUMP_IF_FALSE       40 (to 368)
            288 POP_TOP

207         290 NOP

208         292 LOAD_FAST                0 (self)
            294 STORE_SUBSCR
            298 CACHE
            300 CACHE
            302 CACHE
            304 CACHE
            306 CACHE
            308 CACHE
            310 CACHE
            312 CACHE
            314 CACHE
            316 UNPACK_SEQUENCE          0
            320 CALL                     0
            328 CACHE
            330 POP_TOP
            332 JUMP_FORWARD            16 (to 366)
        >>  334 PUSH_EXC_INFO

209         336 LOAD_GLOBAL             30 (OSError)
            346 CACHE
            348 CHECK_EXC_MATCH
            350 POP_JUMP_IF_FALSE        3 (to 358)
            352 POP_TOP

210         354 POP_EXCEPT
            356 JUMP_FORWARD             4 (to 366)

209     >>  358 RERAISE                  0
        >>  360 COPY                     3
            362 POP_EXCEPT
            364 RERAISE                  1

211     >>  366 RAISE_VARARGS            0

206     >>  368 RERAISE                  0
        >>  370 COPY                     3
            372 POP_EXCEPT
            374 RERAISE                  1
ExceptionTable:
  226 to 264 -> 270 [0]
  270 to 288 -> 370 [1] lasti
  292 to 330 -> 334 [1]
  332 to 332 -> 370 [1] lasti
  334 to 352 -> 360 [2] lasti
  354 to 356 -> 370 [1] lasti
  358 to 358 -> 360 [2] lasti
  360 to 368 -> 370 [1] lasti

Disassembly of <code object _mode_ascii at 0x000001B2A8A2C4E0, file "imaplib.py", line 213>:
213           0 RESUME                   0

214           2 LOAD_CONST               1 (False)
              4 LOAD_FAST                0 (self)
              6 STORE_ATTR               0 (utf8_enabled)

215          16 LOAD_CONST               2 ('ascii')
             18 LOAD_FAST                0 (self)
             20 STORE_ATTR               1 (_encoding)

216          30 LOAD_GLOBAL              5 (NULL + re)
             40 CACHE
             42 LOAD_ATTR                3 (NULL|self + _encoding)
             62 CACHE
             64 LOAD_GLOBAL              4 (re)
             74 CACHE
             76 LOAD_ATTR                5 (NULL|self + re)
             96 CACHE
             98 CACHE
            100 LOAD_FAST                0 (self)
            102 STORE_ATTR               6 (Literal)

217         112 LOAD_GLOBAL              5 (NULL + re)
            122 CACHE
            124 LOAD_ATTR                3 (NULL|self + _encoding)
            144 CACHE
            146 LOAD_GLOBAL              4 (re)
            156 CACHE
            158 LOAD_ATTR                5 (NULL|self + re)
            178 CACHE
            180 CACHE
            182 LOAD_FAST                0 (self)
            184 STORE_ATTR               8 (Untagged_status)
            194 LOAD_CONST               0 (None)
            196 RETURN_VALUE

Disassembly of <code object _mode_utf8 at 0x000001B2A71D39F0, file "imaplib.py", line 220>:
220           0 RESUME                   0

221           2 LOAD_CONST               1 (True)
              4 LOAD_FAST                0 (self)
              6 STORE_ATTR               0 (utf8_enabled)

222          16 LOAD_CONST               2 ('utf-8')
             18 LOAD_FAST                0 (self)
             20 STORE_ATTR               1 (_encoding)

223          30 LOAD_GLOBAL              5 (NULL + re)
             40 CACHE
             42 LOAD_ATTR                3 (NULL|self + _encoding)
             62 CACHE
             64 UNPACK_SEQUENCE          1
             68 CALL                     1
             76 CACHE
             78 LOAD_FAST                0 (self)
             80 STORE_ATTR               5 (Literal)

224          90 LOAD_GLOBAL              5 (NULL + re)
            100 CACHE
            102 LOAD_ATTR                3 (NULL|self + _encoding)
            122 CACHE
            124 UNPACK_SEQUENCE          1
            128 CALL                     1
            136 CACHE
            138 LOAD_FAST                0 (self)
            140 STORE_ATTR               7 (Untagged_status)
            150 LOAD_CONST               0 (None)
            152 RETURN_VALUE

Disassembly of <code object _connect at 0x000001B2A74AA920, file "imaplib.py", line 227>:
227           0 RESUME                   0

231           2 LOAD_GLOBAL              1 (NULL + Int2AP)
             12 CACHE
             14 LOAD_GLOBAL              3 (NULL + random)
             24 CACHE
             26 LOAD_ATTR                2 (random)
             46 CACHE
             48 CACHE
             50 CACHE
             52 CACHE
             54 UNPACK_SEQUENCE          1
             58 CALL                     1
             66 CACHE
             68 LOAD_FAST                0 (self)
             70 STORE_ATTR               3 (tagpre)

232          80 LOAD_GLOBAL              9 (NULL + re)
             90 CACHE
             92 LOAD_ATTR                5 (NULL|self + randint)
            112 CACHE
            114 CACHE

232         116 BINARY_OP                0 (+)

234         120 LOAD_CONST               4 (b'\\d+) (?P<type>[A-Z]+) (?P<data>.*)')

232         122 BINARY_OP                0 (+)

234         126 LOAD_GLOBAL              8 (re)
            136 CACHE
            138 LOAD_ATTR                6 (tagpre)
            158 CACHE
            160 CACHE
            162 LOAD_FAST                0 (self)
            164 STORE_ATTR               7 (tagre)

239         174 NOP

240         176 LOAD_CONST               6 (10)
            178 LOAD_FAST                0 (self)
            180 STORE_ATTR               8 (_cmd_log_len)

241         190 LOAD_CONST               7 (0)
            192 LOAD_FAST                0 (self)
            194 STORE_ATTR               9 (_cmd_log_idx)

242         204 BUILD_MAP                0
            206 LOAD_FAST                0 (self)
            208 STORE_ATTR              10 (_cmd_log)

243         218 LOAD_FAST                0 (self)
            220 LOAD_ATTR               11 (NULL|self + compile)

244         240 LOAD_FAST                0 (self)
            242 STORE_SUBSCR
            246 CACHE
            248 CACHE
            250 CACHE
            252 CACHE
            254 CACHE
            256 CACHE
            258 CACHE
            260 CACHE
            262 CACHE
            264 LOAD_CONST               9 ('imaplib version %s')
            266 LOAD_GLOBAL             26 (__version__)
            276 CACHE
            278 BINARY_OP                6 (%)
            282 UNPACK_SEQUENCE          1
            286 CALL                     1
            294 CACHE
            296 POP_TOP

245         298 LOAD_FAST                0 (self)
            300 STORE_SUBSCR
            304 CACHE
            306 CACHE
            308 CACHE
            310 CACHE
            312 CACHE
            314 CACHE
            316 CACHE
            318 CACHE
            320 CACHE
            322 LOAD_CONST              10 ('new IMAP4 connection, tag=%s')
            324 LOAD_FAST                0 (self)
            326 LOAD_ATTR                3 (NULL|self + random)
            346 CACHE
            348 CACHE
            350 CACHE
            352 CACHE
            354 POP_TOP

247         356 LOAD_FAST                0 (self)
            358 STORE_SUBSCR
            362 CACHE
            364 CACHE
            366 CACHE
            368 CACHE
            370 CACHE
            372 CACHE
            374 CACHE
            376 CACHE
            378 CACHE
            380 UNPACK_SEQUENCE          0
            384 CALL                     0
            392 CACHE
            394 LOAD_FAST                0 (self)
            396 STORE_ATTR              15 (welcome)

248         406 LOAD_CONST              11 ('PREAUTH')
            408 LOAD_FAST                0 (self)
            410 LOAD_ATTR               16 (_cmd_log_len)
            430 CACHE
            432 CACHE
            434 CACHE
            436 CACHE
            438 JUMP_FORWARD            43 (to 526)

250         440 LOAD_CONST              13 ('OK')
            442 LOAD_FAST                0 (self)
            444 LOAD_ATTR               16 (_cmd_log_len)
            464 CACHE
            466 CACHE
            468 CACHE
            470 CACHE
            472 JUMP_FORWARD            26 (to 526)

253         474 LOAD_FAST                0 (self)
            476 STORE_SUBSCR
            480 CACHE
            482 CACHE
            484 CACHE
            486 CACHE
            488 CACHE
            490 CACHE
            492 CACHE
            494 CACHE
            496 CACHE
            498 LOAD_FAST                0 (self)
            500 LOAD_ATTR               15 (NULL|self + tagre)
            520 CACHE
            522 CACHE
            524 RAISE_VARARGS            1

255     >>  526 LOAD_FAST                0 (self)
            528 STORE_SUBSCR
            532 CACHE
            534 CACHE
            536 CACHE
            538 CACHE
            540 CACHE
            542 CACHE
            544 CACHE
            546 CACHE
            548 CACHE
            550 UNPACK_SEQUENCE          0
            554 CALL                     0
            562 CACHE
            564 POP_TOP

256         566 NOP

257         568 LOAD_FAST                0 (self)
            570 LOAD_ATTR               11 (NULL|self + compile)

258         590 LOAD_FAST                0 (self)
            592 STORE_SUBSCR
            596 CACHE
            598 CACHE
            600 CACHE
            602 CACHE
            604 CACHE
            606 CACHE
            608 CACHE
            610 CACHE
            612 CACHE
            614 LOAD_CONST              16 ('CAPABILITIES: ')
            616 LOAD_FAST                0 (self)
            618 LOAD_ATTR               20 (_cmd_log)
            638 CACHE
            640 CACHE
            642 CACHE
            644 CACHE
            646 POP_TOP

260         648 LOAD_GLOBAL             42 (AllowedVersions)
            658 CACHE
            660 GET_ITER
            662 FOR_ITER                21 (to 708)

261         666 LOAD_FAST                1 (version)
            668 LOAD_FAST                0 (self)
            670 LOAD_ATTR               20 (_cmd_log)
            690 STORE_ATTR              22 (PROTOCOL_VERSION)

264         700 POP_TOP
            702 LOAD_CONST               0 (None)
            704 RETURN_VALUE

266         706 LOAD_FAST                0 (self)
        >>  708 STORE_SUBSCR
            712 CACHE
            714 CACHE
            716 CACHE
            718 CACHE
            720 CACHE
            722 CACHE
            724 CACHE
            726 CACHE
            728 CACHE
            730 LOAD_CONST              17 ('server not IMAP4 compliant')
            732 UNPACK_SEQUENCE          1
            736 CALL                     1
            744 CACHE
            746 RAISE_VARARGS            1

Disassembly of <code object __getattr__ at 0x000001B2A8A09570, file "imaplib.py", line 269>:
269           0 RESUME                   0

271           2 LOAD_FAST                1 (attr)
              4 LOAD_GLOBAL              0 (Commands)
             14 CACHE
             16 CONTAINS_OP              0
             18 POP_JUMP_IF_FALSE       34 (to 88)

272          20 LOAD_GLOBAL              3 (NULL + getattr)
             30 CACHE
             32 LOAD_FAST                0 (self)
             34 LOAD_FAST                1 (attr)
             36 STORE_SUBSCR
             40 CACHE
             42 CACHE
             44 CACHE
             46 CACHE
             48 CACHE
             50 CACHE
             52 CACHE
             54 CACHE
             56 CACHE
             58 UNPACK_SEQUENCE          0
             62 CALL                     0
             70 CACHE
             72 UNPACK_SEQUENCE          2
             76 CALL                     2
             84 CACHE
             86 RETURN_VALUE

273     >>   88 LOAD_GLOBAL              7 (NULL + AttributeError)
             98 CACHE
            100 LOAD_CONST               1 ("Unknown IMAP4 command: '%s'")
            102 LOAD_FAST                1 (attr)
            104 BINARY_OP                6 (%)
            108 UNPACK_SEQUENCE          1
            112 CALL                     1
            120 CACHE
            122 RAISE_VARARGS            1

Disassembly of <code object __enter__ at 0x000001B2A8A11070, file "imaplib.py", line 275>:
275           0 RESUME                   0

276           2 LOAD_FAST                0 (self)
              4 RETURN_VALUE

Disassembly of <code object __exit__ at 0x000001B2A76B5CB0, file "imaplib.py", line 278>:
278           0 RESUME                   0

279           2 LOAD_FAST                0 (self)
              4 LOAD_ATTR                0 (state)

280          24 LOAD_CONST               0 (None)
             26 RETURN_VALUE

282          28 NOP

283          30 LOAD_FAST                0 (self)
             32 STORE_SUBSCR
             36 CACHE
             38 CACHE
             40 CACHE
             42 CACHE
             44 CACHE
             46 CACHE
             48 CACHE
             50 CACHE
             52 CACHE
             54 UNPACK_SEQUENCE          0
             58 CALL                     0
             66 CACHE
             68 POP_TOP
             70 LOAD_CONST               0 (None)
             72 RETURN_VALUE
        >>   74 PUSH_EXC_INFO

284          76 LOAD_GLOBAL              4 (OSError)
             86 CACHE
             88 CHECK_EXC_MATCH
             90 POP_JUMP_IF_FALSE        4 (to 100)
             92 POP_TOP

285          94 POP_EXCEPT
             96 LOAD_CONST               0 (None)
             98 RETURN_VALUE

284     >>  100 RERAISE                  0
        >>  102 COPY                     3
            104 POP_EXCEPT
            106 RERAISE                  1
ExceptionTable:
  30 to 68 -> 74 [0]
  74 to 92 -> 102 [1] lasti
  100 to 100 -> 102 [1] lasti

Disassembly of <code object _create_socket at 0x000001B2A722EA30, file "imaplib.py", line 291>:
291           0 RESUME                   0

295           2 LOAD_FAST                1 (timeout)
              4 POP_JUMP_IF_NONE        17 (to 40)
              6 LOAD_FAST                1 (timeout)
              8 POP_JUMP_IF_TRUE        15 (to 40)

296          10 LOAD_GLOBAL              1 (NULL + ValueError)
             20 CACHE
             22 LOAD_CONST               1 ('Non-blocking socket (timeout=0) is not supported')
             24 UNPACK_SEQUENCE          1
             28 CALL                     1
             36 CACHE
             38 RAISE_VARARGS            1

297     >>   40 LOAD_FAST                0 (self)
             42 LOAD_ATTR                1 (NULL|self + ValueError)
             62 CACHE
             64 CACHE
             66 CACHE
             68 CACHE
             70 STORE_FAST               2 (host)

298          72 LOAD_GLOBAL              5 (NULL + sys)
             82 CACHE
             84 LOAD_ATTR                3 (NULL|self + host)
            104 CACHE
            106 CACHE
            108 CACHE
            110 LOAD_FAST                0 (self)
            112 LOAD_ATTR                4 (sys)
            132 CACHE
            134 CACHE
            136 POP_TOP

299         138 LOAD_FAST                2 (host)
            140 LOAD_FAST                0 (self)
            142 LOAD_ATTR                4 (sys)
            162 CACHE
            164 CACHE
            166 CACHE
            168 CACHE
            170 CACHE
            172 LOAD_ATTR                6 (audit)
            192 CACHE
            194 CACHE
            196 CACHE
            198 CACHE
            200 RETURN_VALUE

302         202 LOAD_GLOBAL             11 (NULL + socket)
            212 CACHE
            214 LOAD_ATTR                6 (audit)
            234 CACHE
            236 CACHE
            238 CACHE
            240 RETURN_VALUE

Disassembly of <code object open at 0x000001B2A71D3B50, file "imaplib.py", line 304>:
304           0 RESUME                   0

310           2 LOAD_FAST                1 (host)
              4 LOAD_FAST                0 (self)
              6 STORE_ATTR               0 (host)

311          16 LOAD_FAST                2 (port)
             18 LOAD_FAST                0 (self)
             20 STORE_ATTR               1 (port)

312          30 LOAD_FAST                0 (self)
             32 STORE_SUBSCR
             36 CACHE
             38 CACHE
             40 CACHE
             42 CACHE
             44 CACHE
             46 CACHE
             48 CACHE
             50 CACHE
             52 CACHE
             54 LOAD_FAST                3 (timeout)
             56 UNPACK_SEQUENCE          1
             60 CALL                     1
             68 CACHE
             70 LOAD_FAST                0 (self)
             72 STORE_ATTR               3 (sock)

313          82 LOAD_FAST                0 (self)
             84 LOAD_ATTR                3 (NULL|self + port)
            104 CACHE
            106 CACHE
            108 CACHE
            110 CACHE
            112 CACHE
            114 CACHE
            116 LOAD_CONST               1 ('rb')
            118 UNPACK_SEQUENCE          1
            122 CALL                     1
            130 CACHE
            132 LOAD_FAST                0 (self)
            134 STORE_ATTR               5 (file)
            144 LOAD_CONST               2 (None)
            146 RETURN_VALUE

Disassembly of <code object read at 0x000001B2A8A0CF30, file "imaplib.py", line 316>:
316           0 RESUME                   0

318           2 LOAD_FAST                0 (self)
              4 LOAD_ATTR                0 (file)
             24 CACHE
             26 CACHE
             28 CACHE
             30 CACHE
             32 CACHE
             34 CACHE
             36 LOAD_FAST                1 (size)
             38 UNPACK_SEQUENCE          1
             42 CALL                     1
             50 CACHE
             52 RETURN_VALUE

Disassembly of <code object readline at 0x000001B2A72116B0, file "imaplib.py", line 321>:
321           0 RESUME                   0

323           2 LOAD_FAST                0 (self)
              4 LOAD_ATTR                0 (file)
             24 CACHE
             26 CACHE
             28 CACHE
             30 CACHE
             32 CACHE
             34 CACHE
             36 LOAD_GLOBAL              4 (_MAXLINE)
             46 CACHE
             48 LOAD_CONST               1 (1)
             50 BINARY_OP                0 (+)
             54 UNPACK_SEQUENCE          1
             58 CALL                     1
             66 CACHE
             68 STORE_FAST               1 (line)

324          70 LOAD_GLOBAL              7 (NULL + len)
             80 CACHE
             82 LOAD_FAST                1 (line)
             84 UNPACK_SEQUENCE          1
             88 CALL                     1
             96 CACHE
             98 LOAD_GLOBAL              4 (_MAXLINE)
            108 CACHE
            110 COMPARE_OP               4 (<)
            114 CACHE
            116 POP_JUMP_IF_FALSE       29 (to 176)

325         118 LOAD_FAST                0 (self)
            120 STORE_SUBSCR
            124 CACHE
            126 CACHE
            128 CACHE
            130 CACHE
            132 CACHE
            134 CACHE
            136 CACHE
            138 CACHE
            140 CACHE
            142 LOAD_CONST               2 ('got more than %d bytes')
            144 LOAD_GLOBAL              4 (_MAXLINE)
            154 CACHE
            156 BINARY_OP                6 (%)
            160 UNPACK_SEQUENCE          1
            164 CALL                     1
            172 CACHE
            174 RAISE_VARARGS            1

326     >>  176 LOAD_FAST                1 (line)
            178 RETURN_VALUE

Disassembly of <code object send at 0x000001B2A76B63D0, file "imaplib.py", line 329>:
329           0 RESUME                   0

331           2 LOAD_GLOBAL              1 (NULL + sys)
             12 CACHE
             14 LOAD_ATTR                1 (NULL|self + sys)
             34 CALL                     3
             42 CACHE
             44 POP_TOP

332          46 LOAD_FAST                0 (self)
             48 LOAD_ATTR                2 (audit)
             68 CACHE
             70 CACHE
             72 CACHE
             74 CACHE
             76 CACHE
             78 CACHE
             80 LOAD_FAST                1 (data)
             82 UNPACK_SEQUENCE          1
             86 CALL                     1
             94 CACHE
             96 POP_TOP
             98 LOAD_CONST               2 (None)
            100 RETURN_VALUE

Disassembly of <code object shutdown at 0x000001B2A75E2900, file "imaplib.py", line 335>:
335           0 RESUME                   0

337           2 LOAD_FAST                0 (self)
              4 LOAD_ATTR                0 (file)
             24 CACHE
             26 CACHE
             28 CACHE
             30 CACHE
             32 CACHE
             34 CACHE
             36 UNPACK_SEQUENCE          0
             40 CALL                     0
             48 CACHE
             50 POP_TOP

338          52 NOP

339          54 LOAD_FAST                0 (self)
             56 LOAD_ATTR                2 (close)
             76 CACHE
             78 CACHE
             80 CACHE
             82 CACHE
             84 CACHE
             86 CACHE
             88 LOAD_GLOBAL              8 (socket)
             98 CACHE
            100 LOAD_ATTR                5 (NULL|self + sock)
            120 CACHE
            122 CACHE
            124 POP_TOP
            126 JUMP_FORWARD            66 (to 260)
        >>  128 PUSH_EXC_INFO

340         130 LOAD_GLOBAL             12 (OSError)
            140 CACHE
            142 CHECK_EXC_MATCH
            144 POP_JUMP_IF_FALSE       53 (to 252)
            146 STORE_FAST               1 (exc)

344         148 LOAD_FAST                1 (exc)
            150 LOAD_ATTR                7 (NULL|self + shutdown)
            170 CACHE
            172 LOAD_ATTR                8 (socket)
            192 CACHE
            194 CACHE
            196 CACHE
            198 CACHE
            200 CACHE
            202 LOAD_FAST                1 (exc)
            204 LOAD_CONST               1 ('winerror')
            206 LOAD_CONST               2 (0)
            208 UNPACK_SEQUENCE          3
            212 CALL                     3
            220 CACHE
            222 LOAD_CONST               3 (10022)
            224 COMPARE_OP               3 (<)
            228 CACHE
            230 POP_JUMP_IF_FALSE        1 (to 234)

346         232 RAISE_VARARGS            0
        >>  234 POP_EXCEPT
            236 LOAD_CONST               4 (None)
            238 STORE_FAST               1 (exc)
            240 DELETE_FAST              1 (exc)
            242 JUMP_FORWARD             8 (to 260)
        >>  244 LOAD_CONST               4 (None)
            246 STORE_FAST               1 (exc)
            248 DELETE_FAST              1 (exc)
            250 RERAISE                  1

340     >>  252 RERAISE                  0
        >>  254 COPY                     3
            256 POP_EXCEPT
            258 RERAISE                  1

348     >>  260 LOAD_FAST                0 (self)
            262 LOAD_ATTR                2 (close)
            282 CACHE
            284 CACHE
            286 CACHE
            288 CACHE
            290 CACHE
            292 CACHE
            294 UNPACK_SEQUENCE          0
            298 CALL                     0
            306 CACHE
            308 POP_TOP
            310 LOAD_CONST               4 (None)
            312 RETURN_VALUE
        >>  314 PUSH_EXC_INFO
            316 LOAD_FAST                0 (self)
            318 LOAD_ATTR                2 (close)
            338 CACHE
            340 CACHE
            342 CACHE
            344 CACHE
            346 CACHE
            348 CACHE
            350 UNPACK_SEQUENCE          0
            354 CALL                     0
            362 CACHE
            364 POP_TOP
            366 RERAISE                  0
        >>  368 COPY                     3
            370 POP_EXCEPT
            372 RERAISE                  1
ExceptionTable:
  54 to 124 -> 128 [0]
  126 to 126 -> 314 [0]
  128 to 146 -> 254 [1] lasti
  148 to 232 -> 244 [1] lasti
  234 to 242 -> 314 [0]
  244 to 252 -> 254 [1] lasti
  254 to 258 -> 314 [0]
  314 to 366 -> 368 [1] lasti

Disassembly of <code object socket at 0x000001B2A8A10030, file "imaplib.py", line 351>:
351           0 RESUME                   0

356           2 LOAD_FAST                0 (self)
              4 LOAD_ATTR                0 (sock)

Disassembly of <code object recent at 0x000001B2A728FB20, file "imaplib.py", line 363>:
363           0 RESUME                   0

372           2 LOAD_CONST               1 ('RECENT')
              4 STORE_FAST               1 (name)

373           6 LOAD_FAST                0 (self)
              8 STORE_SUBSCR
             12 CACHE
             14 CACHE
             16 CACHE
             18 CACHE
             20 CACHE
             22 CACHE
             24 CACHE
             26 CACHE
             28 CACHE
             30 LOAD_CONST               2 ('OK')
             32 LOAD_CONST               3 (None)
             34 BUILD_LIST               1
             36 LOAD_FAST                1 (name)
             38 UNPACK_SEQUENCE          3
             42 CALL                     3
             50 CACHE
             52 UNPACK_SEQUENCE          2
             56 STORE_FAST               2 (typ)
             58 STORE_FAST               3 (dat)

374          60 LOAD_FAST                3 (dat)
             62 LOAD_CONST               4 (-1)
             64 BINARY_SUBSCR
             68 CACHE
             70 CACHE
             72 CACHE
             74 POP_JUMP_IF_FALSE        4 (to 84)

375          76 LOAD_FAST                2 (typ)
             78 LOAD_FAST                3 (dat)
             80 BUILD_TUPLE              2
             82 RETURN_VALUE

376     >>   84 LOAD_FAST                0 (self)
             86 STORE_SUBSCR
             90 CACHE
             92 CACHE
             94 CACHE
             96 CACHE
             98 CACHE
            100 CACHE
            102 CACHE
            104 CACHE
            106 CACHE
            108 UNPACK_SEQUENCE          0
            112 CALL                     0
            120 CACHE
            122 UNPACK_SEQUENCE          2
            126 STORE_FAST               2 (typ)
            128 STORE_FAST               3 (dat)

377         130 LOAD_FAST                0 (self)
            132 STORE_SUBSCR
            136 CACHE
            138 CACHE
            140 CACHE
            142 CACHE
            144 CACHE
            146 CACHE
            148 CACHE
            150 CACHE
            152 CACHE
            154 LOAD_FAST                2 (typ)
            156 LOAD_FAST                3 (dat)
            158 LOAD_FAST                1 (name)
            160 UNPACK_SEQUENCE          3
            164 CALL                     3
            172 CACHE
            174 RETURN_VALUE

Disassembly of <code object response at 0x000001B2A8A4C810, file "imaplib.py", line 380>:
380           0 RESUME                   0

387           2 LOAD_FAST                0 (self)
              4 STORE_SUBSCR
              8 CACHE
             10 CACHE
             12 CACHE
             14 CACHE
             16 CACHE
             18 CACHE
             20 CACHE
             22 CACHE
             24 CACHE
             26 LOAD_FAST                1 (code)
             28 LOAD_CONST               1 (None)
             30 BUILD_LIST               1
             32 LOAD_FAST                1 (code)
             34 STORE_SUBSCR
             38 CACHE
             40 CACHE
             42 CACHE
             44 CACHE
             46 CACHE
             48 CACHE
             50 CACHE
             52 CACHE
             54 CACHE
             56 UNPACK_SEQUENCE          0
             60 CALL                     0
             68 CACHE
             70 UNPACK_SEQUENCE          3
             74 CALL                     3
             82 CACHE
             84 RETURN_VALUE

Disassembly of <code object append at 0x000001B2A71AE810, file "imaplib.py", line 394>:
394           0 RESUME                   0

401           2 LOAD_CONST               1 ('APPEND')
              4 STORE_FAST               5 (name)

402           6 LOAD_FAST                1 (mailbox)
              8 POP_JUMP_IF_TRUE         2 (to 14)

403          10 LOAD_CONST               2 ('INBOX')
             12 STORE_FAST               1 (mailbox)

404     >>   14 LOAD_FAST                2 (flags)
             16 POP_JUMP_IF_FALSE       26 (to 70)

405          18 LOAD_FAST                2 (flags)
             20 LOAD_CONST               3 (0)
             22 BINARY_SUBSCR
             26 CACHE
             28 CACHE
             30 CACHE
             32 LOAD_FAST                2 (flags)
             34 LOAD_CONST               4 (-1)
             36 BINARY_SUBSCR
             40 CACHE
             42 CACHE
             44 CACHE
             46 BUILD_TUPLE              2
             48 LOAD_CONST               5 (('(', ')'))
             50 COMPARE_OP               3 (<)
             54 CACHE
             56 POP_JUMP_IF_FALSE        5 (to 68)

406          58 LOAD_CONST               6 ('(%s)')
             60 LOAD_FAST                2 (flags)
             62 BINARY_OP                6 (%)
             66 STORE_FAST               2 (flags)
        >>   68 JUMP_FORWARD             2 (to 74)

408     >>   70 LOAD_CONST               7 (None)
             72 STORE_FAST               2 (flags)

409     >>   74 LOAD_FAST                3 (date_time)
             76 POP_JUMP_IF_FALSE       16 (to 110)

410          78 LOAD_GLOBAL              1 (NULL + Time2Internaldate)
             88 CACHE
             90 LOAD_FAST                3 (date_time)
             92 UNPACK_SEQUENCE          1
             96 CALL                     1
            104 CACHE
            106 STORE_FAST               3 (date_time)
            108 JUMP_FORWARD             2 (to 114)

412     >>  110 LOAD_CONST               7 (None)
            112 STORE_FAST               3 (date_time)

413     >>  114 LOAD_GLOBAL              2 (MapCRLF)
            124 CACHE
            126 STORE_SUBSCR
            130 CACHE
            132 CACHE
            134 CACHE
            136 CACHE
            138 CACHE
            140 CACHE
            142 CACHE
            144 CACHE
            146 CACHE
            148 LOAD_GLOBAL              6 (CRLF)
            158 CACHE
            160 LOAD_FAST                4 (message)
            162 UNPACK_SEQUENCE          2
            166 CALL                     2
            174 CACHE
            176 STORE_FAST               6 (literal)

414         178 LOAD_FAST                0 (self)
            180 LOAD_ATTR                4 (sub)
            200 LOAD_CONST               9 (b')')
            202 BINARY_OP                0 (+)
            206 STORE_FAST               6 (literal)

416         208 LOAD_FAST                6 (literal)
            210 LOAD_FAST                0 (self)
            212 STORE_ATTR               5 (literal)

417         222 LOAD_FAST                0 (self)
            224 STORE_SUBSCR
            228 CACHE
            230 CACHE
            232 CACHE
            234 CACHE
            236 CACHE
            238 CACHE
            240 CACHE
            242 CACHE
            244 CACHE
            246 LOAD_FAST                5 (name)
            248 LOAD_FAST                1 (mailbox)
            250 LOAD_FAST                2 (flags)
            252 LOAD_FAST                3 (date_time)
            254 UNPACK_SEQUENCE          4
            258 CALL                     4
            266 CACHE
            268 RETURN_VALUE

Disassembly of <code object authenticate at 0x000001B2A71AF860, file "imaplib.py", line 420>:
420           0 RESUME                   0

436           2 LOAD_FAST                1 (mechanism)
              4 STORE_SUBSCR
              8 CACHE
             10 CACHE
             12 CACHE
             14 CACHE
             16 CACHE
             18 CACHE
             20 CACHE
             22 CACHE
             24 CACHE
             26 UNPACK_SEQUENCE          0
             30 CALL                     0
             38 CACHE
             40 STORE_FAST               3 (mech)

441          42 LOAD_GLOBAL              3 (NULL + _Authenticator)
             52 CACHE
             54 LOAD_FAST                2 (authobject)
             56 UNPACK_SEQUENCE          1
             60 CALL                     1
             68 CACHE
             70 LOAD_ATTR                2 (_Authenticator)
             90 CACHE

442          92 LOAD_FAST                0 (self)
             94 STORE_SUBSCR
             98 CACHE
            100 CACHE
            102 CACHE
            104 CACHE
            106 CACHE
            108 CACHE
            110 CACHE
            112 CACHE
            114 CACHE
            116 LOAD_CONST               1 ('AUTHENTICATE')
            118 LOAD_FAST                3 (mech)
            120 UNPACK_SEQUENCE          2
            124 CALL                     2
            132 CACHE
            134 UNPACK_SEQUENCE          2
            138 STORE_FAST               4 (typ)
            140 STORE_FAST               5 (dat)

443         142 LOAD_FAST                4 (typ)
            144 LOAD_CONST               2 ('OK')
            146 COMPARE_OP               3 (<)
            150 CACHE
            152 POP_JUMP_IF_FALSE       47 (to 248)

444         154 LOAD_FAST                0 (self)
            156 STORE_SUBSCR
            160 CACHE
            162 CACHE
            164 CACHE
            166 CACHE
            168 CACHE
            170 CACHE
            172 CACHE
            174 CACHE
            176 CACHE
            178 LOAD_FAST                5 (dat)
            180 LOAD_CONST               3 (-1)
            182 BINARY_SUBSCR
            186 CACHE
            188 CACHE
            190 CACHE
            192 STORE_SUBSCR
            196 CACHE
            198 CACHE
            200 CACHE
            202 CACHE
            204 CACHE
            206 CACHE
            208 CACHE
            210 CACHE
            212 CACHE
            214 LOAD_CONST               4 ('utf-8')
            216 LOAD_CONST               5 ('replace')
            218 UNPACK_SEQUENCE          2
            222 CALL                     2
            230 CACHE
            232 UNPACK_SEQUENCE          1
            236 CALL                     1
            244 CACHE
            246 RAISE_VARARGS            1

445     >>  248 LOAD_CONST               6 ('AUTH')
            250 LOAD_FAST                0 (self)
            252 STORE_ATTR               7 (state)

446         262 LOAD_FAST                4 (typ)
            264 LOAD_FAST                5 (dat)
            266 BUILD_TUPLE              2
            268 RETURN_VALUE

Disassembly of <code object capability at 0x000001B2A76B6890, file "imaplib.py", line 449>:
449           0 RESUME                   0

453           2 LOAD_CONST               1 ('CAPABILITY')
              4 STORE_FAST               1 (name)

454           6 LOAD_FAST                0 (self)
              8 STORE_SUBSCR
             12 CACHE
             14 CACHE
             16 CACHE
             18 CACHE
             20 CACHE
             22 CACHE
             24 CACHE
             26 CACHE
             28 CACHE
             30 LOAD_FAST                1 (name)
             32 UNPACK_SEQUENCE          1
             36 CALL                     1
             44 CACHE
             46 UNPACK_SEQUENCE          2
             50 STORE_FAST               2 (typ)
             52 STORE_FAST               3 (dat)

455          54 LOAD_FAST                0 (self)
             56 STORE_SUBSCR
             60 CACHE
             62 CACHE
             64 CACHE
             66 CACHE
             68 CACHE
             70 CACHE
             72 CACHE
             74 CACHE
             76 CACHE
             78 LOAD_FAST                2 (typ)
             80 LOAD_FAST                3 (dat)
             82 LOAD_FAST                1 (name)
             84 UNPACK_SEQUENCE          3
             88 CALL                     3
             96 CACHE
             98 RETURN_VALUE

Disassembly of <code object check at 0x000001B2A8A483F0, file "imaplib.py", line 458>:
458           0 RESUME                   0

463           2 LOAD_FAST                0 (self)
              4 STORE_SUBSCR
              8 CACHE
             10 CACHE
             12 CACHE
             14 CACHE
             16 CACHE
             18 CACHE
             20 CACHE
             22 CACHE
             24 CACHE
             26 LOAD_CONST               1 ('CHECK')
             28 UNPACK_SEQUENCE          1
             32 CALL                     1
             40 CACHE
             42 RETURN_VALUE

Disassembly of <code object close at 0x000001B2A76B6AF0, file "imaplib.py", line 466>:
466           0 RESUME                   0

474           2 NOP

475           4 LOAD_FAST                0 (self)
              6 STORE_SUBSCR
             10 CACHE
             12 CACHE
             14 CACHE
             16 CACHE
             18 CACHE
             20 CACHE
             22 CACHE
             24 CACHE
             26 CACHE
             28 LOAD_CONST               1 ('CLOSE')
             30 UNPACK_SEQUENCE          1
             34 CALL                     1
             42 CACHE
             44 UNPACK_SEQUENCE          2
             48 STORE_FAST               1 (typ)
             50 STORE_FAST               2 (dat)

477          52 LOAD_CONST               2 ('AUTH')
             54 LOAD_FAST                0 (self)
             56 STORE_ATTR               1 (state)
             66 JUMP_FORWARD            12 (to 92)
        >>   68 PUSH_EXC_INFO
             70 LOAD_CONST               2 ('AUTH')
             72 LOAD_FAST                0 (self)
             74 STORE_ATTR               1 (state)
             84 RERAISE                  0
        >>   86 COPY                     3
             88 POP_EXCEPT
             90 RERAISE                  1

478     >>   92 LOAD_FAST                1 (typ)
             94 LOAD_FAST                2 (dat)
             96 BUILD_TUPLE              2
             98 RETURN_VALUE
ExceptionTable:
  4 to 50 -> 68 [0]
  68 to 84 -> 86 [1] lasti

Disassembly of <code object copy at 0x000001B2A8A484E0, file "imaplib.py", line 481>:
481           0 RESUME                   0

486           2 LOAD_FAST                0 (self)
              4 STORE_SUBSCR
              8 CACHE
             10 CACHE
             12 CACHE
             14 CACHE
             16 CACHE
             18 CACHE
             20 CACHE
             22 CACHE
             24 CACHE
             26 LOAD_CONST               1 ('COPY')
             28 LOAD_FAST                1 (message_set)
             30 LOAD_FAST                2 (new_mailbox)
             32 UNPACK_SEQUENCE          3
             36 CALL                     3
             44 CACHE
             46 RETURN_VALUE

Disassembly of <code object create at 0x000001B2A8A485D0, file "imaplib.py", line 489>:
489           0 RESUME                   0

494           2 LOAD_FAST                0 (self)
              4 STORE_SUBSCR
              8 CACHE
             10 CACHE
             12 CACHE
             14 CACHE
             16 CACHE
             18 CACHE
             20 CACHE
             22 CACHE
             24 CACHE
             26 LOAD_CONST               1 ('CREATE')
             28 LOAD_FAST                1 (mailbox)
             30 UNPACK_SEQUENCE          2
             34 CALL                     2
             42 CACHE
             44 RETURN_VALUE

Disassembly of <code object delete at 0x000001B2A8A487B0, file "imaplib.py", line 497>:
497           0 RESUME                   0

502           2 LOAD_FAST                0 (self)
              4 STORE_SUBSCR
              8 CACHE
             10 CACHE
             12 CACHE
             14 CACHE
             16 CACHE
             18 CACHE
             20 CACHE
             22 CACHE
             24 CACHE
             26 LOAD_CONST               1 ('DELETE')
             28 LOAD_FAST                1 (mailbox)
             30 UNPACK_SEQUENCE          2
             34 CALL                     2
             42 CACHE
             44 RETURN_VALUE

Disassembly of <code object deleteacl at 0x000001B2A8A488A0, file "imaplib.py", line 504>:
504           0 RESUME                   0

509           2 LOAD_FAST                0 (self)
              4 STORE_SUBSCR
              8 CACHE
             10 CACHE
             12 CACHE
             14 CACHE
             16 CACHE
             18 CACHE
             20 CACHE
             22 CACHE
             24 CACHE
             26 LOAD_CONST               1 ('DELETEACL')
             28 LOAD_FAST                1 (mailbox)
             30 LOAD_FAST                2 (who)
             32 UNPACK_SEQUENCE          3
             36 CALL                     3
             44 CACHE
             46 RETURN_VALUE

Disassembly of <code object enable at 0x000001B2A726F2D0, file "imaplib.py", line 511>:
511           0 RESUME                   0

516           2 LOAD_CONST               1 ('ENABLE')
              4 LOAD_FAST                0 (self)
              6 LOAD_ATTR                0 (capabilities)
             26 CACHE
             28 CACHE
             30 CACHE
             32 STORE_SUBSCR
             36 CACHE
             38 CACHE
             40 CACHE
             42 CACHE
             44 CACHE
             46 CACHE
             48 CACHE
             50 CACHE
             52 CACHE
             54 LOAD_CONST               2 ('Server does not support ENABLE')
             56 UNPACK_SEQUENCE          1
             60 CALL                     1
             68 CACHE
             70 RAISE_VARARGS            1

518          72 LOAD_FAST                0 (self)
             74 STORE_SUBSCR
             78 CACHE
             80 CACHE
             82 CACHE
             84 CACHE
             86 CACHE
             88 CACHE
             90 CACHE
             92 CACHE
             94 CACHE
             96 LOAD_CONST               1 ('ENABLE')
             98 LOAD_FAST                1 (capability)
            100 UNPACK_SEQUENCE          2
            104 CALL                     2
            112 CACHE
            114 UNPACK_SEQUENCE          2
            118 STORE_FAST               2 (typ)
            120 STORE_FAST               3 (data)

519         122 LOAD_FAST                2 (typ)
            124 LOAD_CONST               3 ('OK')
            126 COMPARE_OP               2 (<)
            130 CACHE
            132 POP_JUMP_IF_FALSE       42 (to 218)
            134 LOAD_CONST               4 ('UTF8=ACCEPT')
            136 LOAD_FAST                1 (capability)
            138 STORE_SUBSCR
            142 CACHE
            144 CACHE
            146 CACHE
            148 CACHE
            150 CACHE
            152 CACHE
            154 CACHE
            156 CACHE
            158 CACHE
            160 UNPACK_SEQUENCE          0
            164 CALL                     0
            172 CACHE
            174 CONTAINS_OP              0
            176 POP_JUMP_IF_FALSE       20 (to 218)

520         178 LOAD_FAST                0 (self)
            180 STORE_SUBSCR
            184 CACHE
            186 CACHE
            188 CACHE
            190 CACHE
            192 CACHE
            194 CACHE
            196 CACHE
            198 CACHE
            200 CACHE
            202 UNPACK_SEQUENCE          0
            206 CALL                     0
            214 CACHE
            216 POP_TOP

521     >>  218 LOAD_FAST                2 (typ)
            220 LOAD_FAST                3 (data)
            222 BUILD_TUPLE              2
            224 RETURN_VALUE

Disassembly of <code object expunge at 0x000001B2A76B49B0, file "imaplib.py", line 523>:
523           0 RESUME                   0

532           2 LOAD_CONST               1 ('EXPUNGE')
              4 STORE_FAST               1 (name)

533           6 LOAD_FAST                0 (self)
              8 STORE_SUBSCR
             12 CACHE
             14 CACHE
             16 CACHE
             18 CACHE
             20 CACHE
             22 CACHE
             24 CACHE
             26 CACHE
             28 CACHE
             30 LOAD_FAST                1 (name)
             32 UNPACK_SEQUENCE          1
             36 CALL                     1
             44 CACHE
             46 UNPACK_SEQUENCE          2
             50 STORE_FAST               2 (typ)
             52 STORE_FAST               3 (dat)

534          54 LOAD_FAST                0 (self)
             56 STORE_SUBSCR
             60 CACHE
             62 CACHE
             64 CACHE
             66 CACHE
             68 CACHE
             70 CACHE
             72 CACHE
             74 CACHE
             76 CACHE
             78 LOAD_FAST                2 (typ)
             80 LOAD_FAST                3 (dat)
             82 LOAD_FAST                1 (name)
             84 UNPACK_SEQUENCE          3
             88 CALL                     3
             96 CACHE
             98 RETURN_VALUE

Disassembly of <code object fetch at 0x000001B2A76B70E0, file "imaplib.py", line 537>:
537           0 RESUME                   0

547           2 LOAD_CONST               1 ('FETCH')
              4 STORE_FAST               3 (name)

548           6 LOAD_FAST                0 (self)
              8 STORE_SUBSCR
             12 CACHE
             14 CACHE
             16 CACHE
             18 CACHE
             20 CACHE
             22 CACHE
             24 CACHE
             26 CACHE
             28 CACHE
             30 LOAD_FAST                3 (name)
             32 LOAD_FAST                1 (message_set)
             34 LOAD_FAST                2 (message_parts)
             36 UNPACK_SEQUENCE          3
             40 CALL                     3
             48 CACHE
             50 UNPACK_SEQUENCE          2
             54 STORE_FAST               4 (typ)
             56 STORE_FAST               5 (dat)

549          58 LOAD_FAST                0 (self)
             60 STORE_SUBSCR
             64 CACHE
             66 CACHE
             68 CACHE
             70 CACHE
             72 CACHE
             74 CACHE
             76 CACHE
             78 CACHE
             80 CACHE
             82 LOAD_FAST                4 (typ)
             84 LOAD_FAST                5 (dat)
             86 LOAD_FAST                3 (name)
             88 UNPACK_SEQUENCE          3
             92 CALL                     3
            100 CACHE
            102 RETURN_VALUE

Disassembly of <code object getacl at 0x000001B2A76B7210, file "imaplib.py", line 552>:
552           0 RESUME                   0

557           2 LOAD_FAST                0 (self)
              4 STORE_SUBSCR
              8 CACHE
             10 CACHE
             12 CACHE
             14 CACHE
             16 CACHE
             18 CACHE
             20 CACHE
             22 CACHE
             24 CACHE
             26 LOAD_CONST               1 ('GETACL')
             28 LOAD_FAST                1 (mailbox)
             30 UNPACK_SEQUENCE          2
             34 CALL                     2
             42 CACHE
             44 UNPACK_SEQUENCE          2
             48 STORE_FAST               2 (typ)
             50 STORE_FAST               3 (dat)

558          52 LOAD_FAST                0 (self)
             54 STORE_SUBSCR
             58 CACHE
             60 CACHE
             62 CACHE
             64 CACHE
             66 CACHE
             68 CACHE
             70 CACHE
             72 CACHE
             74 CACHE
             76 LOAD_FAST                2 (typ)
             78 LOAD_FAST                3 (dat)
             80 LOAD_CONST               2 ('ACL')
             82 UNPACK_SEQUENCE          3
             86 CALL                     3
             94 CACHE
             96 RETURN_VALUE

Disassembly of <code object getannotation at 0x000001B2A76B7340, file "imaplib.py", line 561>:
561           0 RESUME                   0

565           2 LOAD_FAST                0 (self)
              4 STORE_SUBSCR
              8 CACHE
             10 CACHE
             12 CACHE
             14 CACHE
             16 CACHE
             18 CACHE
             20 CACHE
             22 CACHE
             24 CACHE
             26 LOAD_CONST               1 ('GETANNOTATION')
             28 LOAD_FAST                1 (mailbox)
             30 LOAD_FAST                2 (entry)
             32 LOAD_FAST                3 (attribute)
             34 UNPACK_SEQUENCE          4
             38 CALL                     4
             46 CACHE
             48 UNPACK_SEQUENCE          2
             52 STORE_FAST               4 (typ)
             54 STORE_FAST               5 (dat)

566          56 LOAD_FAST                0 (self)
             58 STORE_SUBSCR
             62 CACHE
             64 CACHE
             66 CACHE
             68 CACHE
             70 CACHE
             72 CACHE
             74 CACHE
             76 CACHE
             78 CACHE
             80 LOAD_FAST                4 (typ)
             82 LOAD_FAST                5 (dat)
             84 LOAD_CONST               2 ('ANNOTATION')
             86 UNPACK_SEQUENCE          3
             90 CALL                     3
             98 CACHE
            100 RETURN_VALUE

Disassembly of <code object getquota at 0x000001B2A76B7470, file "imaplib.py", line 569>:
569           0 RESUME                   0

576           2 LOAD_FAST                0 (self)
              4 STORE_SUBSCR
              8 CACHE
             10 CACHE
             12 CACHE
             14 CACHE
             16 CACHE
             18 CACHE
             20 CACHE
             22 CACHE
             24 CACHE
             26 LOAD_CONST               1 ('GETQUOTA')
             28 LOAD_FAST                1 (root)
             30 UNPACK_SEQUENCE          2
             34 CALL                     2
             42 CACHE
             44 UNPACK_SEQUENCE          2
             48 STORE_FAST               2 (typ)
             50 STORE_FAST               3 (dat)

577          52 LOAD_FAST                0 (self)
             54 STORE_SUBSCR
             58 CACHE
             60 CACHE
             62 CACHE
             64 CACHE
             66 CACHE
             68 CACHE
             70 CACHE
             72 CACHE
             74 CACHE
             76 LOAD_FAST                2 (typ)
             78 LOAD_FAST                3 (dat)
             80 LOAD_CONST               2 ('QUOTA')
             82 UNPACK_SEQUENCE          3
             86 CALL                     3
             94 CACHE
             96 RETURN_VALUE

Disassembly of <code object getquotaroot at 0x000001B2A728F840, file "imaplib.py", line 580>:
580           0 RESUME                   0

585           2 LOAD_FAST                0 (self)
              4 STORE_SUBSCR
              8 CACHE
             10 CACHE
             12 CACHE
             14 CACHE
             16 CACHE
             18 CACHE
             20 CACHE
             22 CACHE
             24 CACHE
             26 LOAD_CONST               1 ('GETQUOTAROOT')
             28 LOAD_FAST                1 (mailbox)
             30 UNPACK_SEQUENCE          2
             34 CALL                     2
             42 CACHE
             44 UNPACK_SEQUENCE          2
             48 STORE_FAST               2 (typ)
             50 STORE_FAST               3 (dat)

586          52 LOAD_FAST                0 (self)
             54 STORE_SUBSCR
             58 CACHE
             60 CACHE
             62 CACHE
             64 CACHE
             66 CACHE
             68 CACHE
             70 CACHE
             72 CACHE
             74 CACHE
             76 LOAD_FAST                2 (typ)
             78 LOAD_FAST                3 (dat)
             80 LOAD_CONST               2 ('QUOTA')
             82 UNPACK_SEQUENCE          3
             86 CALL                     3
             94 CACHE
             96 UNPACK_SEQUENCE          2
            100 STORE_FAST               2 (typ)
            102 STORE_FAST               4 (quota)

587         104 LOAD_FAST                0 (self)
            106 STORE_SUBSCR
            110 CACHE
            112 CACHE
            114 CACHE
            116 CACHE
            118 CACHE
            120 CACHE
            122 CACHE
            124 CACHE
            126 CACHE
            128 LOAD_FAST                2 (typ)
            130 LOAD_FAST                3 (dat)
            132 LOAD_CONST               3 ('QUOTAROOT')
            134 UNPACK_SEQUENCE          3
            138 CALL                     3
            146 CACHE
            148 UNPACK_SEQUENCE          2
            152 STORE_FAST               2 (typ)
            154 STORE_FAST               5 (quotaroot)

588         156 LOAD_FAST                2 (typ)
            158 LOAD_FAST                5 (quotaroot)
            160 LOAD_FAST                4 (quota)
            162 BUILD_LIST               2
            164 BUILD_TUPLE              2
            166 RETURN_VALUE

Disassembly of <code object list at 0x000001B2A76B75A0, file "imaplib.py", line 591>:
591           0 RESUME                   0

598           2 LOAD_CONST               1 ('LIST')
              4 STORE_FAST               3 (name)

599           6 LOAD_FAST                0 (self)
              8 STORE_SUBSCR
             12 CACHE
             14 CACHE
             16 CACHE
             18 CACHE
             20 CACHE
             22 CACHE
             24 CACHE
             26 CACHE
             28 CACHE
             30 LOAD_FAST                3 (name)
             32 LOAD_FAST                1 (directory)
             34 LOAD_FAST                2 (pattern)
             36 UNPACK_SEQUENCE          3
             40 CALL                     3
             48 CACHE
             50 UNPACK_SEQUENCE          2
             54 STORE_FAST               4 (typ)
             56 STORE_FAST               5 (dat)

600          58 LOAD_FAST                0 (self)
             60 STORE_SUBSCR
             64 CACHE
             66 CACHE
             68 CACHE
             70 CACHE
             72 CACHE
             74 CACHE
             76 CACHE
             78 CACHE
             80 CACHE
             82 LOAD_FAST                4 (typ)
             84 LOAD_FAST                5 (dat)
             86 LOAD_FAST                3 (name)
             88 UNPACK_SEQUENCE          3
             92 CALL                     3
            100 CACHE
            102 RETURN_VALUE

Disassembly of <code object login at 0x000001B2A7212730, file "imaplib.py", line 603>:
603           0 RESUME                   0

610           2 LOAD_FAST                0 (self)
              4 STORE_SUBSCR
              8 CACHE
             10 CACHE
             12 CACHE
             14 CACHE
             16 CACHE
             18 CACHE
             20 CACHE
             22 CACHE
             24 CACHE
             26 LOAD_CONST               1 ('LOGIN')
             28 LOAD_FAST                1 (user)
             30 LOAD_FAST                0 (self)
             32 STORE_SUBSCR
             36 CACHE
             38 CACHE
             40 CACHE
             42 CACHE
             44 CACHE
             46 CACHE
             48 CACHE
             50 CACHE
             52 CACHE
             54 LOAD_FAST                2 (password)
             56 UNPACK_SEQUENCE          1
             60 CALL                     1
             68 CACHE
             70 UNPACK_SEQUENCE          3
             74 CALL                     3
             82 CACHE
             84 UNPACK_SEQUENCE          2
             88 STORE_FAST               3 (typ)
             90 STORE_FAST               4 (dat)

611          92 LOAD_FAST                3 (typ)
             94 LOAD_CONST               2 ('OK')
             96 COMPARE_OP               3 (<)
            100 CACHE
            102 POP_JUMP_IF_FALSE       27 (to 158)

612         104 LOAD_FAST                0 (self)
            106 STORE_SUBSCR
            110 CACHE
            112 CACHE
            114 CACHE
            116 CACHE
            118 CACHE
            120 CACHE
            122 CACHE
            124 CACHE
            126 CACHE
            128 LOAD_FAST                4 (dat)
            130 LOAD_CONST               3 (-1)
            132 BINARY_SUBSCR
            136 CACHE
            138 CACHE
            140 CACHE
            142 UNPACK_SEQUENCE          1
            146 CALL                     1
            154 CACHE
            156 RAISE_VARARGS            1

613     >>  158 LOAD_CONST               4 ('AUTH')
            160 LOAD_FAST                0 (self)
            162 STORE_ATTR               3 (state)

614         172 LOAD_FAST                3 (typ)
            174 LOAD_FAST                4 (dat)
            176 BUILD_TUPLE              2
            178 RETURN_VALUE

Disassembly of <code object login_cram_md5 at 0x000001B2A8A4CC90, file "imaplib.py", line 617>:
617           0 RESUME                   0

622           2 LOAD_FAST                1 (user)
              4 LOAD_FAST                2 (password)
              6 SWAP                     2
              8 LOAD_FAST                0 (self)
             10 STORE_ATTR               0 (user)
             20 LOAD_FAST                0 (self)
             22 STORE_ATTR               1 (password)

623          32 LOAD_FAST                0 (self)
             34 STORE_SUBSCR
             38 CACHE
             40 CACHE
             42 CACHE
             44 CACHE
             46 CACHE
             48 CACHE
             50 CACHE
             52 CACHE
             54 CACHE
             56 LOAD_CONST               1 ('CRAM-MD5')
             58 LOAD_FAST                0 (self)
             60 LOAD_ATTR                3 (NULL|self + password)
             80 CACHE
             82 CACHE
             84 RETURN_VALUE

Disassembly of <code object _CRAM_MD5_AUTH at 0x000001B2A726F120, file "imaplib.py", line 626>:
626           0 RESUME                   0

628           2 LOAD_CONST               1 (0)
              4 LOAD_CONST               2 (None)
              6 IMPORT_NAME              0 (hmac)
              8 STORE_FAST               2 (hmac)

629          10 LOAD_GLOBAL              3 (NULL + isinstance)
             20 CACHE
             22 LOAD_FAST                0 (self)
             24 LOAD_ATTR                2 (isinstance)
             44 CACHE
             46 UNPACK_SEQUENCE          2
             50 CALL                     2
             58 CACHE
             60 POP_JUMP_IF_FALSE       26 (to 114)
             62 LOAD_FAST                0 (self)
             64 LOAD_ATTR                2 (isinstance)
             84 CACHE
             86 CACHE
             88 CACHE
             90 CACHE
             92 CACHE
             94 CACHE
             96 LOAD_CONST               3 ('utf-8')
             98 UNPACK_SEQUENCE          1
            102 CALL                     1
            110 CACHE
            112 JUMP_FORWARD             6 (to 126)

630     >>  114 LOAD_FAST                0 (self)
            116 LOAD_ATTR                2 (isinstance)
            136 CACHE
            138 CACHE
            140 LOAD_CONST               4 (' ')
            142 BINARY_OP                0 (+)
            146 LOAD_FAST                2 (hmac)
            148 STORE_SUBSCR
            152 CACHE
            154 CACHE
            156 CACHE
            158 CACHE
            160 CACHE
            162 CACHE
            164 CACHE
            166 CACHE
            168 CACHE
            170 LOAD_FAST                3 (pwd)
            172 LOAD_FAST                1 (challenge)
            174 LOAD_CONST               5 ('md5')
            176 UNPACK_SEQUENCE          3
            180 CALL                     3
            188 CACHE
            190 STORE_SUBSCR
            194 CACHE
            196 CACHE
            198 CACHE
            200 CACHE
            202 CACHE
            204 CACHE
            206 CACHE
            208 CACHE
            210 CACHE
            212 UNPACK_SEQUENCE          0
            216 CALL                     0
            224 CACHE
            226 BINARY_OP                0 (+)
            230 RETURN_VALUE

Disassembly of <code object logout at 0x000001B2A76B76D0, file "imaplib.py", line 634>:
634           0 RESUME                   0

641           2 LOAD_CONST               1 ('LOGOUT')
              4 LOAD_FAST                0 (self)
              6 STORE_ATTR               0 (state)

642          16 LOAD_FAST                0 (self)
             18 STORE_SUBSCR
             22 CACHE
             24 CACHE
             26 CACHE
             28 CACHE
             30 CACHE
             32 CACHE
             34 CACHE
             36 CACHE
             38 CACHE
             40 LOAD_CONST               1 ('LOGOUT')
             42 UNPACK_SEQUENCE          1
             46 CALL                     1
             54 CACHE
             56 UNPACK_SEQUENCE          2
             60 STORE_FAST               1 (typ)
             62 STORE_FAST               2 (dat)

643          64 LOAD_FAST                0 (self)
             66 STORE_SUBSCR
             70 CACHE
             72 CACHE
             74 CACHE
             76 CACHE
             78 CACHE
             80 CACHE
             82 CACHE
             84 CACHE
             86 CACHE
             88 UNPACK_SEQUENCE          0
             92 CALL                     0
            100 CACHE
            102 POP_TOP

644         104 LOAD_FAST                1 (typ)
            106 LOAD_FAST                2 (dat)
            108 BUILD_TUPLE              2
            110 RETURN_VALUE

Disassembly of <code object lsub at 0x000001B2A76B7800, file "imaplib.py", line 647>:
647           0 RESUME                   0

654           2 LOAD_CONST               1 ('LSUB')
              4 STORE_FAST               3 (name)

655           6 LOAD_FAST                0 (self)
              8 STORE_SUBSCR
             12 CACHE
             14 CACHE
             16 CACHE
             18 CACHE
             20 CACHE
             22 CACHE
             24 CACHE
             26 CACHE
             28 CACHE
             30 LOAD_FAST                3 (name)
             32 LOAD_FAST                1 (directory)
             34 LOAD_FAST                2 (pattern)
             36 UNPACK_SEQUENCE          3
             40 CALL                     3
             48 CACHE
             50 UNPACK_SEQUENCE          2
             54 STORE_FAST               4 (typ)
             56 STORE_FAST               5 (dat)

656          58 LOAD_FAST                0 (self)
             60 STORE_SUBSCR
             64 CACHE
             66 CACHE
             68 CACHE
             70 CACHE
             72 CACHE
             74 CACHE
             76 CACHE
             78 CACHE
             80 CACHE
             82 LOAD_FAST                4 (typ)
             84 LOAD_FAST                5 (dat)
             86 LOAD_FAST                3 (name)
             88 UNPACK_SEQUENCE          3
             92 CALL                     3
            100 CACHE
            102 RETURN_VALUE

Disassembly of <code object myrights at 0x000001B2A76B7930, file "imaplib.py", line 658>:
658           0 RESUME                   0

663           2 LOAD_FAST                0 (self)
              4 STORE_SUBSCR
              8 CACHE
             10 CACHE
             12 CACHE
             14 CACHE
             16 CACHE
             18 CACHE
             20 CACHE
             22 CACHE
             24 CACHE
             26 LOAD_CONST               1 ('MYRIGHTS')
             28 LOAD_FAST                1 (mailbox)
             30 UNPACK_SEQUENCE          2
             34 CALL                     2
             42 CACHE
             44 UNPACK_SEQUENCE          2
             48 STORE_FAST               2 (typ)
             50 STORE_FAST               3 (dat)

664          52 LOAD_FAST                0 (self)
             54 STORE_SUBSCR
             58 CACHE
             60 CACHE
             62 CACHE
             64 CACHE
             66 CACHE
             68 CACHE
             70 CACHE
             72 CACHE
             74 CACHE
             76 LOAD_FAST                2 (typ)
             78 LOAD_FAST                3 (dat)
             80 LOAD_CONST               1 ('MYRIGHTS')
             82 UNPACK_SEQUENCE          3
             86 CALL                     3
             94 CACHE
             96 RETURN_VALUE

Disassembly of <code object namespace at 0x000001B2A76B7A60, file "imaplib.py", line 666>:
666           0 RESUME                   0

671           2 LOAD_CONST               1 ('NAMESPACE')
              4 STORE_FAST               1 (name)

672           6 LOAD_FAST                0 (self)
              8 STORE_SUBSCR
             12 CACHE
             14 CACHE
             16 CACHE
             18 CACHE
             20 CACHE
             22 CACHE
             24 CACHE
             26 CACHE
             28 CACHE
             30 LOAD_FAST                1 (name)
             32 UNPACK_SEQUENCE          1
             36 CALL                     1
             44 CACHE
             46 UNPACK_SEQUENCE          2
             50 STORE_FAST               2 (typ)
             52 STORE_FAST               3 (dat)

673          54 LOAD_FAST                0 (self)
             56 STORE_SUBSCR
             60 CACHE
             62 CACHE
             64 CACHE
             66 CACHE
             68 CACHE
             70 CACHE
             72 CACHE
             74 CACHE
             76 CACHE
             78 LOAD_FAST                2 (typ)
             80 LOAD_FAST                3 (dat)
             82 LOAD_FAST                1 (name)
             84 UNPACK_SEQUENCE          3
             88 CALL                     3
             96 CACHE
             98 RETURN_VALUE

Disassembly of <code object noop at 0x000001B2A8A09430, file "imaplib.py", line 676>:
676           0 RESUME                   0

681           2 NOP

682           4 LOAD_FAST                0 (self)
              6 LOAD_ATTR                0 (debug)

683          26 LOAD_FAST                0 (self)
             28 STORE_SUBSCR
             32 CACHE
             34 CACHE
             36 CACHE
             38 CACHE
             40 CACHE
             42 CACHE
             44 CACHE
             46 CACHE
             48 CACHE
             50 LOAD_FAST                0 (self)
             52 LOAD_ATTR                2 (_dump_ur)
             72 CACHE
             74 CACHE
             76 POP_TOP

684          78 LOAD_FAST                0 (self)
             80 STORE_SUBSCR
             84 CACHE
             86 CACHE
             88 CACHE
             90 CACHE
             92 CACHE
             94 CACHE
             96 CACHE
             98 CACHE
            100 CACHE
            102 LOAD_CONST               3 ('NOOP')
            104 UNPACK_SEQUENCE          1
            108 CALL                     1
            116 CACHE
            118 RETURN_VALUE

Disassembly of <code object partial at 0x000001B2A76B7B90, file "imaplib.py", line 687>:
687           0 RESUME                   0

694           2 LOAD_CONST               1 ('PARTIAL')
              4 STORE_FAST               5 (name)

695           6 LOAD_FAST                0 (self)
              8 STORE_SUBSCR
             12 CACHE
             14 CACHE
             16 CACHE
             18 CACHE
             20 CACHE
             22 CACHE
             24 CACHE
             26 CACHE
             28 CACHE
             30 LOAD_FAST                5 (name)
             32 LOAD_FAST                1 (message_num)
             34 LOAD_FAST                2 (message_part)
             36 LOAD_FAST                3 (start)
             38 LOAD_FAST                4 (length)
             40 UNPACK_SEQUENCE          5
             44 CALL                     5
             52 CACHE
             54 UNPACK_SEQUENCE          2
             58 STORE_FAST               6 (typ)
             60 STORE_FAST               7 (dat)

696          62 LOAD_FAST                0 (self)
             64 STORE_SUBSCR
             68 CACHE
             70 CACHE
             72 CACHE
             74 CACHE
             76 CACHE
             78 CACHE
             80 CACHE
             82 CACHE
             84 CACHE
             86 LOAD_FAST                6 (typ)
             88 LOAD_FAST                7 (dat)
             90 LOAD_CONST               2 ('FETCH')
             92 UNPACK_SEQUENCE          3
             96 CALL                     3
            104 CACHE
            106 RETURN_VALUE

Disassembly of <code object proxyauth at 0x000001B2A8A0D830, file "imaplib.py", line 699>:
699           0 RESUME                   0

708           2 LOAD_CONST               1 ('PROXYAUTH')
              4 STORE_FAST               2 (name)

709           6 LOAD_FAST                0 (self)
              8 STORE_SUBSCR
             12 CACHE
             14 CACHE
             16 CACHE
             18 CACHE
             20 CACHE
             22 CACHE
             24 CACHE
             26 CACHE
             28 CACHE
             30 LOAD_CONST               1 ('PROXYAUTH')
             32 LOAD_FAST                1 (user)
             34 UNPACK_SEQUENCE          2
             38 CALL                     2
             46 CACHE
             48 RETURN_VALUE

Disassembly of <code object rename at 0x000001B2A8A48210, file "imaplib.py", line 712>:
712           0 RESUME                   0

717           2 LOAD_FAST                0 (self)
              4 STORE_SUBSCR
              8 CACHE
             10 CACHE
             12 CACHE
             14 CACHE
             16 CACHE
             18 CACHE
             20 CACHE
             22 CACHE
             24 CACHE
             26 LOAD_CONST               1 ('RENAME')
             28 LOAD_FAST                1 (oldmailbox)
             30 LOAD_FAST                2 (newmailbox)
             32 UNPACK_SEQUENCE          3
             36 CALL                     3
             44 CACHE
             46 RETURN_VALUE

Disassembly of <code object search at 0x000001B2A8A2C800, file "imaplib.py", line 720>:
720           0 RESUME                   0

728           2 LOAD_CONST               1 ('SEARCH')
              4 STORE_FAST               3 (name)

729           6 LOAD_FAST                1 (charset)
              8 POP_JUMP_IF_FALSE       53 (to 116)

730          10 LOAD_FAST                0 (self)
             12 LOAD_ATTR                0 (utf8_enabled)
             32 CACHE
             34 CACHE
             36 STORE_SUBSCR
             40 CACHE
             42 CACHE
             44 CACHE
             46 CACHE
             48 CACHE
             50 CACHE
             52 CACHE
             54 CACHE
             56 CACHE
             58 LOAD_CONST               2 ('Non-None charset not valid in UTF8 mode')
             60 UNPACK_SEQUENCE          1
             64 CALL                     1
             72 CACHE
             74 RAISE_VARARGS            1

732          76 PUSH_NULL
             78 LOAD_FAST                0 (self)
             80 LOAD_ATTR                3 (NULL|self + IMAP4)
            100 LIST_EXTEND              1
            102 LOAD_ATTR                0 (utf8_enabled)
            122 CACHE
            124 CACHE
            126 CACHE
            128 CACHE
            130 LOAD_FAST                3 (name)
            132 BUILD_LIST               1
            134 LOAD_FAST                2 (criteria)
            136 LIST_EXTEND              1
            138 LOAD_ATTR                0 (utf8_enabled)
            158 CACHE
            160 CACHE
            162 CACHE
            164 CACHE
            166 CACHE
            168 CACHE
            170 CACHE
            172 CACHE
            174 LOAD_FAST                4 (typ)
            176 LOAD_FAST                5 (dat)
            178 LOAD_FAST                3 (name)
            180 UNPACK_SEQUENCE          3
            184 CALL                     3
            192 CACHE
            194 RETURN_VALUE

Disassembly of <code object select at 0x000001B2A75D7680, file "imaplib.py", line 738>:
738           0 RESUME                   0

750           2 BUILD_MAP                0
              4 LOAD_FAST                0 (self)
              6 STORE_ATTR               0 (untagged_responses)

751          16 LOAD_FAST                2 (readonly)
             18 LOAD_FAST                0 (self)
             20 STORE_ATTR               1 (is_readonly)

752          30 LOAD_FAST                2 (readonly)
             32 POP_JUMP_IF_FALSE        3 (to 40)

753          34 LOAD_CONST               1 ('EXAMINE')
             36 STORE_FAST               3 (name)
             38 JUMP_FORWARD             2 (to 44)

755     >>   40 LOAD_CONST               2 ('SELECT')
             42 STORE_FAST               3 (name)

756     >>   44 LOAD_FAST                0 (self)
             46 STORE_SUBSCR
             50 CACHE
             52 CACHE
             54 CACHE
             56 CACHE
             58 CACHE
             60 CACHE
             62 CACHE
             64 CACHE
             66 CACHE
             68 LOAD_FAST                3 (name)
             70 LOAD_FAST                1 (mailbox)
             72 UNPACK_SEQUENCE          2
             76 CALL                     2
             84 CACHE
             86 UNPACK_SEQUENCE          2
             90 STORE_FAST               4 (typ)
             92 STORE_FAST               5 (dat)

757          94 LOAD_FAST                4 (typ)
             96 LOAD_CONST               3 ('OK')
             98 COMPARE_OP               3 (<)
            102 CACHE
            104 POP_JUMP_IF_FALSE       11 (to 128)

758         106 LOAD_CONST               4 ('AUTH')
            108 LOAD_FAST                0 (self)
            110 STORE_ATTR               3 (state)

759         120 LOAD_FAST                4 (typ)
            122 LOAD_FAST                5 (dat)
            124 BUILD_TUPLE              2
            126 RETURN_VALUE

760     >>  128 LOAD_CONST               5 ('SELECTED')
            130 LOAD_FAST                0 (self)
            132 STORE_ATTR               3 (state)

761         142 LOAD_CONST               6 ('READ-ONLY')
            144 LOAD_FAST                0 (self)
            146 LOAD_ATTR                0 (untagged_responses)

764         166 LOAD_FAST                0 (self)
            168 LOAD_ATTR                4 (_simple_command)

765         188 LOAD_FAST                0 (self)
            190 STORE_SUBSCR
            194 CACHE
            196 CACHE
            198 CACHE
            200 CACHE
            202 CACHE
            204 CACHE
            206 CACHE
            208 CACHE
            210 CACHE
            212 LOAD_FAST                0 (self)
            214 LOAD_ATTR                0 (untagged_responses)
            234 CACHE
            236 CACHE
            238 POP_TOP

766         240 LOAD_FAST                0 (self)
            242 STORE_SUBSCR
            246 CACHE
            248 CACHE
            250 CACHE
            252 CACHE
            254 CACHE
            256 CACHE
            258 CACHE
            260 CACHE
            262 CACHE
            264 LOAD_CONST               9 ('%s is not writable')
            266 LOAD_FAST                1 (mailbox)
            268 BINARY_OP                6 (%)
            272 UNPACK_SEQUENCE          1
            276 CALL                     1
            284 CACHE
            286 RAISE_VARARGS            1

767         288 LOAD_FAST                4 (typ)
            290 LOAD_FAST                0 (self)
            292 LOAD_ATTR                0 (untagged_responses)
            312 CACHE
            314 CACHE
            316 CACHE
            318 CACHE
            320 CACHE
            322 CACHE
            324 LOAD_CONST              10 ('EXISTS')
            326 LOAD_CONST              11 (None)
            328 BUILD_LIST               1
            330 UNPACK_SEQUENCE          2
            334 CALL                     2
            342 CACHE
            344 BUILD_TUPLE              2
            346 RETURN_VALUE

Disassembly of <code object setacl at 0x000001B2A8A0DD30, file "imaplib.py", line 770>:
770           0 RESUME                   0

775           2 LOAD_FAST                0 (self)
              4 STORE_SUBSCR
              8 CACHE
             10 CACHE
             12 CACHE
             14 CACHE
             16 CACHE
             18 CACHE
             20 CACHE
             22 CACHE
             24 CACHE
             26 LOAD_CONST               1 ('SETACL')
             28 LOAD_FAST                1 (mailbox)
             30 LOAD_FAST                2 (who)
             32 LOAD_FAST                3 (what)
             34 UNPACK_SEQUENCE          4
             38 CALL                     4
             46 CACHE
             48 RETURN_VALUE

Disassembly of <code object setannotation at 0x000001B2A8A4CED0, file "imaplib.py", line 778>:
778           0 RESUME                   0

782           2 PUSH_NULL
              4 LOAD_FAST                0 (self)
              6 LOAD_ATTR                0 (_simple_command)
             26 CALL_FUNCTION_EX         0
             28 UNPACK_SEQUENCE          2
             32 STORE_FAST               2 (typ)
             34 STORE_FAST               3 (dat)

783          36 LOAD_FAST                0 (self)
             38 STORE_SUBSCR
             42 CACHE
             44 CACHE
             46 CACHE
             48 CACHE
             50 CACHE
             52 CACHE
             54 CACHE
             56 CACHE
             58 CACHE
             60 LOAD_FAST                2 (typ)
             62 LOAD_FAST                3 (dat)
             64 LOAD_CONST               2 ('ANNOTATION')
             66 UNPACK_SEQUENCE          3
             70 CALL                     3
             78 CACHE
             80 RETURN_VALUE

Disassembly of <code object setquota at 0x000001B2A76B7CC0, file "imaplib.py", line 786>:
786           0 RESUME                   0

791           2 LOAD_FAST                0 (self)
              4 STORE_SUBSCR
              8 CACHE
             10 CACHE
             12 CACHE
             14 CACHE
             16 CACHE
             18 CACHE
             20 CACHE
             22 CACHE
             24 CACHE
             26 LOAD_CONST               1 ('SETQUOTA')
             28 LOAD_FAST                1 (root)
             30 LOAD_FAST                2 (limits)
             32 UNPACK_SEQUENCE          3
             36 CALL                     3
             44 CACHE
             46 UNPACK_SEQUENCE          2
             50 STORE_FAST               3 (typ)
             52 STORE_FAST               4 (dat)

792          54 LOAD_FAST                0 (self)
             56 STORE_SUBSCR
             60 CACHE
             62 CACHE
             64 CACHE
             66 CACHE
             68 CACHE
             70 CACHE
             72 CACHE
             74 CACHE
             76 CACHE
             78 LOAD_FAST                3 (typ)
             80 LOAD_FAST                4 (dat)
             82 LOAD_CONST               2 ('QUOTA')
             84 UNPACK_SEQUENCE          3
             88 CALL                     3
             96 CACHE
             98 RETURN_VALUE

Disassembly of <code object sort at 0x000001B2A71C7C90, file "imaplib.py", line 795>:
795           0 RESUME                   0

800           2 LOAD_CONST               1 ('SORT')
              4 STORE_FAST               4 (name)

803           6 LOAD_FAST                1 (sort_criteria)
              8 LOAD_CONST               2 (0)
             10 BINARY_SUBSCR
             14 CACHE
             16 CACHE
             18 CACHE
             20 LOAD_FAST                1 (sort_criteria)
             22 LOAD_CONST               3 (-1)
             24 BINARY_SUBSCR
             28 CACHE
             30 CACHE
             32 CACHE
             34 BUILD_TUPLE              2
             36 LOAD_CONST               4 (('(', ')'))
             38 COMPARE_OP               3 (<)
             42 CACHE
             44 POP_JUMP_IF_FALSE        5 (to 56)

804          46 LOAD_CONST               5 ('(%s)')
             48 LOAD_FAST                1 (sort_criteria)
             50 BINARY_OP                6 (%)
             54 STORE_FAST               1 (sort_criteria)

805     >>   56 PUSH_NULL
             58 LOAD_FAST                0 (self)
             60 LOAD_ATTR                0 (_simple_command)
             80 LIST_EXTEND              1
             82 LOAD_ATTR                0 (_simple_command)
            102 CACHE
            104 CACHE
            106 CACHE
            108 CACHE
            110 CACHE
            112 CACHE
            114 CACHE
            116 CACHE
            118 LOAD_FAST                5 (typ)
            120 LOAD_FAST                6 (dat)
            122 LOAD_FAST                4 (name)
            124 UNPACK_SEQUENCE          3
            128 CALL                     3
            136 CACHE
            138 RETURN_VALUE

Disassembly of <code object starttls at 0x000001B2A7565540, file "imaplib.py", line 809>:
809           0 RESUME                   0

810           2 LOAD_CONST               1 ('STARTTLS')
              4 STORE_FAST               2 (name)

811           6 LOAD_GLOBAL              0 (HAVE_SSL)
             16 CACHE
             18 POP_JUMP_IF_TRUE        21 (to 62)

812          20 LOAD_FAST                0 (self)
             22 STORE_SUBSCR
             26 CACHE
             28 CACHE
             30 CACHE
             32 CACHE
             34 CACHE
             36 CACHE
             38 CACHE
             40 CACHE
             42 CACHE
             44 LOAD_CONST               2 ('SSL support missing')
             46 UNPACK_SEQUENCE          1
             50 CALL                     1
             58 CACHE
             60 RAISE_VARARGS            1

813     >>   62 LOAD_FAST                0 (self)
             64 LOAD_ATTR                2 (error)
             84 CACHE
             86 CACHE
             88 CACHE
             90 CACHE
             92 CACHE
             94 CACHE
             96 CACHE
             98 CACHE
            100 LOAD_CONST               3 ('TLS session already established')
            102 UNPACK_SEQUENCE          1
            106 CALL                     1
            114 CACHE
            116 RAISE_VARARGS            1

815         118 LOAD_FAST                2 (name)
            120 LOAD_FAST                0 (self)
            122 LOAD_ATTR                4 (_tls_established)
            142 CACHE
            144 CACHE
            146 CACHE
            148 CACHE
            150 CACHE
            152 CACHE
            154 CACHE
            156 CACHE
            158 CACHE
            160 LOAD_CONST               4 ('TLS not supported by server')
            162 UNPACK_SEQUENCE          1
            166 CALL                     1
            174 CACHE
            176 RAISE_VARARGS            1

818         178 LOAD_FAST                1 (ssl_context)
            180 POP_JUMP_IF_NOT_NONE    19 (to 220)

819         182 LOAD_GLOBAL             11 (NULL + ssl)
            192 CACHE
            194 LOAD_ATTR                6 (abort)
            214 CACHE
            216 CACHE
            218 STORE_FAST               1 (ssl_context)

820     >>  220 LOAD_FAST                0 (self)
            222 STORE_SUBSCR
            226 CACHE
            228 CACHE
            230 CACHE
            232 CACHE
            234 CACHE
            236 CACHE
            238 CACHE
            240 CACHE
            242 CACHE
            244 LOAD_FAST                2 (name)
            246 UNPACK_SEQUENCE          1
            250 CALL                     1
            258 CACHE
            260 UNPACK_SEQUENCE          2
            264 STORE_FAST               3 (typ)
            266 STORE_FAST               4 (dat)

821         268 LOAD_FAST                3 (typ)
            270 LOAD_CONST               5 ('OK')
            272 COMPARE_OP               2 (<)
            276 CACHE
            278 POP_JUMP_IF_FALSE       97 (to 474)

822         280 LOAD_FAST                1 (ssl_context)
            282 STORE_SUBSCR
            286 CACHE
            288 CACHE
            290 CACHE
            292 CACHE
            294 CACHE
            296 CACHE
            298 CACHE
            300 CACHE
            302 CACHE
            304 LOAD_FAST                0 (self)
            306 LOAD_ATTR                9 (NULL|self + capabilities)
            326 CACHE

822         328 KW_NAMES                 6 (('server_hostname',))
            330 UNPACK_SEQUENCE          2
            334 CALL                     2
            342 CACHE
            344 LOAD_FAST                0 (self)
            346 STORE_ATTR               9 (sock)

824         356 LOAD_FAST                0 (self)
            358 LOAD_ATTR                9 (NULL|self + capabilities)
            378 CACHE
            380 CACHE
            382 CACHE
            384 CACHE
            386 CACHE
            388 CACHE
            390 LOAD_CONST               7 ('rb')
            392 UNPACK_SEQUENCE          1
            396 CALL                     1
            404 CACHE
            406 LOAD_FAST                0 (self)
            408 STORE_ATTR              12 (file)

825         418 LOAD_CONST               8 (True)
            420 LOAD_FAST                0 (self)
            422 STORE_ATTR               2 (_tls_established)

826         432 LOAD_FAST                0 (self)
            434 STORE_SUBSCR
            438 CACHE
            440 CACHE
            442 CACHE
            444 CACHE
            446 CACHE
            448 CACHE
            450 CACHE
            452 CACHE
            454 CACHE
            456 UNPACK_SEQUENCE          0
            460 CALL                     0
            468 CACHE
            470 POP_TOP
            472 JUMP_FORWARD            21 (to 516)

828     >>  474 LOAD_FAST                0 (self)
            476 STORE_SUBSCR
            480 CACHE
            482 CACHE
            484 CACHE
            486 CACHE
            488 CACHE
            490 CACHE
            492 CACHE
            494 CACHE
            496 CACHE
            498 LOAD_CONST               9 ("Couldn't establish TLS session")
            500 UNPACK_SEQUENCE          1
            504 CALL                     1
            512 CACHE
            514 RAISE_VARARGS            1

829     >>  516 LOAD_FAST                0 (self)
            518 STORE_SUBSCR
            522 CACHE
            524 CACHE
            526 CACHE
            528 CACHE
            530 CACHE
            532 CACHE
            534 CACHE
            536 CACHE
            538 CACHE
            540 LOAD_FAST                3 (typ)
            542 LOAD_FAST                4 (dat)
            544 LOAD_FAST                2 (name)
            546 UNPACK_SEQUENCE          3
            550 CALL                     3
            558 CACHE
            560 RETURN_VALUE

Disassembly of <code object status at 0x000001B2A76B7DF0, file "imaplib.py", line 832>:
832           0 RESUME                   0

837           2 LOAD_CONST               1 ('STATUS')
              4 STORE_FAST               3 (name)

840           6 LOAD_FAST                0 (self)
              8 STORE_SUBSCR
             12 CACHE
             14 CACHE
             16 CACHE
             18 CACHE
             20 CACHE
             22 CACHE
             24 CACHE
             26 CACHE
             28 CACHE
             30 LOAD_FAST                3 (name)
             32 LOAD_FAST                1 (mailbox)
             34 LOAD_FAST                2 (names)
             36 UNPACK_SEQUENCE          3
             40 CALL                     3
             48 CACHE
             50 UNPACK_SEQUENCE          2
             54 STORE_FAST               4 (typ)
             56 STORE_FAST               5 (dat)

841          58 LOAD_FAST                0 (self)
             60 STORE_SUBSCR
             64 CACHE
             66 CACHE
             68 CACHE
             70 CACHE
             72 CACHE
             74 CACHE
             76 CACHE
             78 CACHE
             80 CACHE
             82 LOAD_FAST                4 (typ)
             84 LOAD_FAST                5 (dat)
             86 LOAD_FAST                3 (name)
             88 UNPACK_SEQUENCE          3
             92 CALL                     3
            100 CACHE
            102 RETURN_VALUE

Disassembly of <code object store at 0x000001B2A71D3CB0, file "imaplib.py", line 844>:
844           0 RESUME                   0

849           2 LOAD_FAST                3 (flags)
              4 LOAD_CONST               1 (0)
              6 BINARY_SUBSCR
             10 CACHE
             12 CACHE
             14 CACHE
             16 LOAD_FAST                3 (flags)
             18 LOAD_CONST               2 (-1)
             20 BINARY_SUBSCR
             24 CACHE
             26 CACHE
             28 CACHE
             30 BUILD_TUPLE              2
             32 LOAD_CONST               3 (('(', ')'))
             34 COMPARE_OP               3 (<)
             38 CACHE
             40 POP_JUMP_IF_FALSE        5 (to 52)

850          42 LOAD_CONST               4 ('(%s)')
             44 LOAD_FAST                3 (flags)
             46 BINARY_OP                6 (%)
             50 STORE_FAST               3 (flags)

851     >>   52 LOAD_FAST                0 (self)
             54 STORE_SUBSCR
             58 CACHE
             60 CACHE
             62 CACHE
             64 CACHE
             66 CACHE
             68 CACHE
             70 CACHE
             72 CACHE
             74 CACHE
             76 LOAD_CONST               5 ('STORE')
             78 LOAD_FAST                1 (message_set)
             80 LOAD_FAST                2 (command)
             82 LOAD_FAST                3 (flags)
             84 UNPACK_SEQUENCE          4
             88 CALL                     4
             96 CACHE
             98 UNPACK_SEQUENCE          2
            102 STORE_FAST               4 (typ)
            104 STORE_FAST               5 (dat)

852         106 LOAD_FAST                0 (self)
            108 STORE_SUBSCR
            112 CACHE
            114 CACHE
            116 CACHE
            118 CACHE
            120 CACHE
            122 CACHE
            124 CACHE
            126 CACHE
            128 CACHE
            130 LOAD_FAST                4 (typ)
            132 LOAD_FAST                5 (dat)
            134 LOAD_CONST               6 ('FETCH')
            136 UNPACK_SEQUENCE          3
            140 CALL                     3
            148 CACHE
            150 RETURN_VALUE

Disassembly of <code object subscribe at 0x000001B2A8A48C60, file "imaplib.py", line 855>:
855           0 RESUME                   0

860           2 LOAD_FAST                0 (self)
              4 STORE_SUBSCR
              8 CACHE
             10 CACHE
             12 CACHE
             14 CACHE
             16 CACHE
             18 CACHE
             20 CACHE
             22 CACHE
             24 CACHE
             26 LOAD_CONST               1 ('SUBSCRIBE')
             28 LOAD_FAST                1 (mailbox)
             30 UNPACK_SEQUENCE          2
             34 CALL                     2
             42 CACHE
             44 RETURN_VALUE

Disassembly of <code object thread at 0x000001B2A8A4CFF0, file "imaplib.py", line 863>:
863           0 RESUME                   0

868           2 LOAD_CONST               1 ('THREAD')
              4 STORE_FAST               4 (name)

869           6 PUSH_NULL
              8 LOAD_FAST                0 (self)
             10 LOAD_ATTR                0 (_simple_command)
             30 LIST_EXTEND              1
             32 LOAD_ATTR                0 (_simple_command)
             52 CACHE
             54 CACHE
             56 CACHE
             58 CACHE
             60 CACHE
             62 CACHE
             64 CACHE
             66 CACHE
             68 LOAD_FAST                5 (typ)
             70 LOAD_FAST                6 (dat)
             72 LOAD_FAST                4 (name)
             74 UNPACK_SEQUENCE          3
             78 CALL                     3
             86 CACHE
             88 RETURN_VALUE

Disassembly of <code object uid at 0x000001B2A75E3D40, file "imaplib.py", line 873>:
873           0 RESUME                   0

881           2 LOAD_FAST                1 (command)
              4 STORE_SUBSCR
              8 CACHE
             10 CACHE
             12 CACHE
             14 CACHE
             16 CACHE
             18 CACHE
             20 CACHE
             22 CACHE
             24 CACHE
             26 UNPACK_SEQUENCE          0
             30 CALL                     0
             38 CACHE
             40 STORE_FAST               1 (command)

882          42 LOAD_FAST                1 (command)
             44 LOAD_GLOBAL              2 (Commands)
             54 CACHE
             56 CONTAINS_OP              1
             58 POP_JUMP_IF_FALSE       24 (to 108)

883          60 LOAD_FAST                0 (self)
             62 STORE_SUBSCR
             66 CACHE
             68 CACHE
             70 CACHE
             72 CACHE
             74 CACHE
             76 CACHE
             78 CACHE
             80 CACHE
             82 CACHE
             84 LOAD_CONST               1 ('Unknown IMAP4 UID command: %s')
             86 LOAD_FAST                1 (command)
             88 BINARY_OP                6 (%)
             92 UNPACK_SEQUENCE          1
             96 CALL                     1
            104 CACHE
            106 RAISE_VARARGS            1

884     >>  108 LOAD_FAST                0 (self)
            110 LOAD_ATTR                3 (NULL|self + Commands)
            130 CACHE
            132 LOAD_FAST                1 (command)
            134 BINARY_SUBSCR
            138 CACHE
            140 CACHE
            142 CACHE
            144 CONTAINS_OP              1
            146 POP_JUMP_IF_FALSE       65 (to 278)

885         148 LOAD_FAST                0 (self)
            150 STORE_SUBSCR
            154 CACHE
            156 CACHE
            158 CACHE
            160 CACHE
            162 CACHE
            164 CACHE
            166 CACHE
            168 CACHE
            170 CACHE
            172 LOAD_CONST               2 ('command ')

887         174 LOAD_FAST                1 (command)
            176 FORMAT_VALUE             1 (str)
            178 LOAD_CONST               3 (' illegal in state ')
            180 LOAD_FAST                0 (self)
            182 LOAD_ATTR                3 (NULL|self + Commands)
            202 CACHE
            204 CACHE
            206 CACHE
            208 CACHE
            210 CACHE
            212 CACHE
            214 CACHE
            216 CACHE
            218 CACHE
            220 LOAD_GLOBAL              2 (Commands)
            230 CACHE
            232 LOAD_FAST                1 (command)
            234 BINARY_SUBSCR
            238 CACHE
            240 CACHE
            242 CACHE
            244 UNPACK_SEQUENCE          1
            248 CALL                     1
            256 CACHE
            258 FORMAT_VALUE             1 (str)

885         260 BUILD_STRING             6
            262 UNPACK_SEQUENCE          1
            266 CALL                     1
            274 CACHE
            276 RAISE_VARARGS            1

889     >>  278 LOAD_CONST               6 ('UID')
            280 STORE_FAST               3 (name)

890         282 PUSH_NULL
            284 LOAD_FAST                0 (self)
            286 LOAD_ATTR                5 (NULL|self + error)
            306 LOAD_ATTR                0 (upper)

892         326 LOAD_FAST                1 (command)
            328 STORE_FAST               3 (name)
            330 JUMP_FORWARD             2 (to 336)

894         332 LOAD_CONST               8 ('FETCH')
            334 STORE_FAST               3 (name)

895     >>  336 LOAD_FAST                0 (self)
            338 STORE_SUBSCR
            342 CACHE
            344 CACHE
            346 CACHE
            348 CACHE
            350 CACHE
            352 CACHE
            354 CACHE
            356 CACHE
            358 CACHE
            360 LOAD_FAST                4 (typ)
            362 LOAD_FAST                5 (dat)
            364 LOAD_FAST                3 (name)
            366 UNPACK_SEQUENCE          3
            370 CALL                     3
            378 CACHE
            380 RETURN_VALUE

Disassembly of <code object unsubscribe at 0x000001B2A8A48F30, file "imaplib.py", line 898>:
898           0 RESUME                   0

903           2 LOAD_FAST                0 (self)
              4 STORE_SUBSCR
              8 CACHE
             10 CACHE
             12 CACHE
             14 CACHE
             16 CACHE
             18 CACHE
             20 CACHE
             22 CACHE
             24 CACHE
             26 LOAD_CONST               1 ('UNSUBSCRIBE')
             28 LOAD_FAST                1 (mailbox)
             30 UNPACK_SEQUENCE          2
             34 CALL                     2
             42 CACHE
             44 RETURN_VALUE

Disassembly of <code object unselect at 0x000001B2A8A90030, file "imaplib.py", line 906>:
906           0 RESUME                   0

915           2 NOP

916           4 LOAD_FAST                0 (self)
              6 STORE_SUBSCR
             10 CACHE
             12 CACHE
             14 CACHE
             16 CACHE
             18 CACHE
             20 CACHE
             22 CACHE
             24 CACHE
             26 CACHE
             28 LOAD_CONST               1 ('UNSELECT')
             30 UNPACK_SEQUENCE          1
             34 CALL                     1
             42 CACHE
             44 UNPACK_SEQUENCE          2
             48 STORE_FAST               1 (typ)
             50 STORE_FAST               2 (data)

918          52 LOAD_CONST               2 ('AUTH')
             54 LOAD_FAST                0 (self)
             56 STORE_ATTR               1 (state)
             66 JUMP_FORWARD            12 (to 92)
        >>   68 PUSH_EXC_INFO
             70 LOAD_CONST               2 ('AUTH')
             72 LOAD_FAST                0 (self)
             74 STORE_ATTR               1 (state)
             84 RERAISE                  0
        >>   86 COPY                     3
             88 POP_EXCEPT
             90 RERAISE                  1

919     >>   92 LOAD_FAST                1 (typ)
             94 LOAD_FAST                2 (data)
             96 BUILD_TUPLE              2
             98 RETURN_VALUE
ExceptionTable:
  4 to 50 -> 68 [0]
  68 to 84 -> 86 [1] lasti

Disassembly of <code object xatom at 0x000001B2A8A09930, file "imaplib.py", line 922>:
922           0 RESUME                   0

932           2 LOAD_FAST                1 (name)
              4 STORE_SUBSCR
              8 CACHE
             10 CACHE
             12 CACHE
             14 CACHE
             16 CACHE
             18 CACHE
             20 CACHE
             22 CACHE
             24 CACHE
             26 UNPACK_SEQUENCE          0
             30 CALL                     0
             38 CACHE
             40 STORE_FAST               1 (name)

935          42 LOAD_FAST                1 (name)
             44 LOAD_GLOBAL              2 (Commands)
             54 CACHE
             56 CONTAINS_OP              1
             58 POP_JUMP_IF_FALSE       16 (to 92)

936          60 LOAD_FAST                0 (self)
             62 LOAD_ATTR                2 (Commands)
             82 CACHE
             84 CACHE
             86 LOAD_FAST                1 (name)
             88 STORE_SUBSCR

937     >>   92 PUSH_NULL
             94 LOAD_FAST                0 (self)
             96 LOAD_ATTR                3 (NULL|self + Commands)
            116 CALL_FUNCTION_EX         0
            118 RETURN_VALUE

Disassembly of <code object _append_untagged at 0x000001B2A71AF120, file "imaplib.py", line 944>:
944           0 RESUME                   0

945           2 LOAD_FAST                2 (dat)
              4 POP_JUMP_IF_NOT_NONE     2 (to 10)

946           6 LOAD_CONST               1 (b'')
              8 STORE_FAST               2 (dat)

947     >>   10 LOAD_FAST                0 (self)
             12 LOAD_ATTR                0 (untagged_responses)
             32 CACHE
             34 CACHE
             36 CACHE
             38 LOAD_CONST               3 (5)
             40 COMPARE_OP               5 (<)
             44 CACHE
             46 POP_JUMP_IF_FALSE       64 (to 176)

950          48 LOAD_FAST                0 (self)
             50 STORE_SUBSCR
             54 CACHE
             56 CACHE
             58 CACHE
             60 CACHE
             62 CACHE
             64 CACHE
             66 CACHE
             68 CACHE
             70 CACHE
             72 LOAD_CONST               4 ('untagged_responses[')

951          74 LOAD_FAST                1 (typ)
             76 FORMAT_VALUE             1 (str)
             78 LOAD_CONST               5 ('] ')
             80 LOAD_GLOBAL              7 (NULL + len)
             90 CACHE
             92 LOAD_FAST                3 (ur)
             94 STORE_SUBSCR
             98 CACHE
            100 CACHE
            102 CACHE
            104 CACHE
            106 CACHE
            108 CACHE
            110 CACHE
            112 CACHE
            114 CACHE
            116 LOAD_FAST                1 (typ)
            118 LOAD_CONST               6 ('')
            120 UNPACK_SEQUENCE          2
            124 CALL                     2
            132 CACHE
            134 UNPACK_SEQUENCE          1
            138 CALL                     1
            146 CACHE
            148 FORMAT_VALUE             1 (str)
            150 LOAD_CONST               7 (' += ["')
            152 LOAD_FAST                2 (dat)
            154 FORMAT_VALUE             2 (repr)
            156 LOAD_CONST               8 ('"]')

950         158 BUILD_STRING             7
            160 UNPACK_SEQUENCE          1
            164 CALL                     1
            172 CACHE
            174 POP_TOP

952     >>  176 LOAD_FAST                1 (typ)
            178 LOAD_FAST                3 (ur)
            180 CONTAINS_OP              0
            182 POP_JUMP_IF_FALSE       29 (to 242)

953         184 LOAD_FAST                3 (ur)
            186 LOAD_FAST                1 (typ)
            188 BINARY_SUBSCR
            192 CACHE
            194 CACHE
            196 CACHE
            198 STORE_SUBSCR
            202 CACHE
            204 CACHE
            206 CACHE
            208 CACHE
            210 CACHE
            212 CACHE
            214 CACHE
            216 CACHE
            218 CACHE
            220 LOAD_FAST                2 (dat)
            222 UNPACK_SEQUENCE          1
            226 CALL                     1
            234 CACHE
            236 POP_TOP
            238 LOAD_CONST               0 (None)
            240 RETURN_VALUE

955     >>  242 LOAD_FAST                2 (dat)
            244 BUILD_LIST               1
            246 LOAD_FAST                3 (ur)
            248 LOAD_FAST                1 (typ)
            250 STORE_SUBSCR
            254 LOAD_CONST               0 (None)
            256 RETURN_VALUE

Disassembly of <code object _check_bye at 0x000001B2A728E420, file "imaplib.py", line 958>:
958           0 RESUME                   0

959           2 LOAD_FAST                0 (self)
              4 LOAD_ATTR                0 (untagged_responses)
             24 CACHE
             26 CACHE
             28 CACHE
             30 CACHE
             32 CACHE
             34 CACHE
             36 LOAD_CONST               1 ('BYE')
             38 UNPACK_SEQUENCE          1
             42 CALL                     1
             50 CACHE
             52 STORE_FAST               1 (bye)

960          54 LOAD_FAST                1 (bye)
             56 POP_JUMP_IF_FALSE       52 (to 162)

961          58 LOAD_FAST                0 (self)
             60 STORE_SUBSCR
             64 CACHE
             66 CACHE
             68 CACHE
             70 CACHE
             72 CACHE
             74 CACHE
             76 CACHE
             78 CACHE
             80 CACHE
             82 LOAD_FAST                1 (bye)
             84 LOAD_CONST               2 (-1)
             86 BINARY_SUBSCR
             90 CACHE
             92 CACHE
             94 CACHE
             96 STORE_SUBSCR
            100 CACHE
            102 CACHE
            104 CACHE
            106 CACHE
            108 CACHE
            110 CACHE
            112 CACHE
            114 CACHE
            116 CACHE
            118 LOAD_FAST                0 (self)
            120 LOAD_ATTR                4 (abort)
            140 CACHE
            142 CACHE
            144 CACHE
            146 UNPACK_SEQUENCE          1
            150 CALL                     1
            158 CACHE
            160 RAISE_VARARGS            1

960     >>  162 LOAD_CONST               0 (None)
            164 RETURN_VALUE

Disassembly of <code object _command at 0x000001B2A74A2F20, file "imaplib.py", line 964>:
 964           0 RESUME                   0

 966           2 LOAD_FAST                0 (self)
               4 LOAD_ATTR                0 (state)
              24 CACHE
              26 LOAD_FAST                1 (name)
              28 BINARY_SUBSCR
              32 CACHE
              34 CACHE
              36 CACHE
              38 CONTAINS_OP              1
              40 POP_JUMP_IF_FALSE       72 (to 186)

 967          42 LOAD_CONST               0 (None)
              44 LOAD_FAST                0 (self)
              46 STORE_ATTR               2 (literal)

 968          56 LOAD_FAST                0 (self)
              58 STORE_SUBSCR
              62 CACHE
              64 CACHE
              66 CACHE
              68 CACHE
              70 CACHE
              72 CACHE
              74 CACHE
              76 CACHE
              78 CACHE
              80 LOAD_CONST               1 ('command ')

 970          82 LOAD_FAST                1 (name)
              84 FORMAT_VALUE             1 (str)
              86 LOAD_CONST               2 (' illegal in state ')
              88 LOAD_FAST                0 (self)
              90 LOAD_ATTR                0 (state)
             110 CACHE
             112 CACHE
             114 CACHE
             116 CACHE
             118 CACHE
             120 CACHE
             122 CACHE
             124 CACHE
             126 CACHE
             128 LOAD_GLOBAL              2 (Commands)
             138 CACHE
             140 LOAD_FAST                1 (name)
             142 BINARY_SUBSCR
             146 CACHE
             148 CACHE
             150 CACHE
             152 UNPACK_SEQUENCE          1
             156 CALL                     1
             164 CACHE
             166 FORMAT_VALUE             1 (str)

 968         168 BUILD_STRING             6
             170 UNPACK_SEQUENCE          1
             174 CALL                     1
             182 CACHE
             184 RAISE_VARARGS            1

 973     >>  186 LOAD_CONST               5 (('OK', 'NO', 'BAD'))
             188 GET_ITER
         >>  190 FOR_ITER                19 (to 232)

 974         194 LOAD_FAST                3 (typ)
             196 LOAD_FAST                0 (self)
             198 LOAD_ATTR                5 (NULL|self + literal)
             218 CACHE
             220 CACHE
             222 CACHE
             224 LOAD_FAST                3 (typ)
             226 DELETE_SUBSCR
             228 JUMP_BACKWARD           20 (to 190)

 977         230 LOAD_CONST               6 ('READ-ONLY')
         >>  232 LOAD_FAST                0 (self)
             234 LOAD_ATTR                5 (NULL|self + literal)
             254 CACHE
             256 CACHE
             258 CACHE

 977         260 POP_JUMP_IF_TRUE        21 (to 304)

 979         262 LOAD_FAST                0 (self)
             264 STORE_SUBSCR
             268 CACHE
             270 CACHE
             272 CACHE
             274 CACHE
             276 CACHE
             278 CACHE
             280 CACHE
             282 CACHE
             284 CACHE
             286 LOAD_CONST               7 ('mailbox status changed to READ-ONLY')
             288 UNPACK_SEQUENCE          1
             292 CALL                     1
             300 CACHE
             302 RAISE_VARARGS            1

 981     >>  304 LOAD_FAST                0 (self)
             306 STORE_SUBSCR
             310 CACHE
             312 CACHE
             314 CACHE
             316 CACHE
             318 CACHE
             320 CACHE
             322 CACHE
             324 CACHE
             326 CACHE
             328 UNPACK_SEQUENCE          0
             332 CALL                     0
             340 CACHE
             342 STORE_FAST               4 (tag)

 982         344 LOAD_GLOBAL             19 (NULL + bytes)
             354 CACHE
             356 LOAD_FAST                1 (name)
             358 LOAD_FAST                0 (self)
             360 LOAD_ATTR               10 (untagged_responses)
             380 CACHE
             382 CACHE
             384 STORE_FAST               1 (name)

 983         386 LOAD_FAST                4 (tag)
             388 LOAD_CONST               8 (b' ')
             390 BINARY_OP                0 (+)
             394 LOAD_FAST                1 (name)
             396 BINARY_OP                0 (+)
             400 STORE_FAST               5 (data)

 984         402 LOAD_FAST                2 (args)
             404 GET_ITER
         >>  406 FOR_ITER                55 (to 520)

 985         410 LOAD_FAST                6 (arg)
             412 POP_JUMP_IF_NOT_NONE     1 (to 416)
             414 JUMP_BACKWARD            5 (to 406)

 986     >>  416 LOAD_GLOBAL             23 (NULL + isinstance)
             426 CACHE
             428 LOAD_FAST                6 (arg)
             430 LOAD_GLOBAL             24 (str)
             440 CACHE
             442 UNPACK_SEQUENCE          2
             446 CALL                     2
             454 CACHE
             456 POP_JUMP_IF_FALSE       21 (to 500)

 987         458 LOAD_GLOBAL             19 (NULL + bytes)
             468 CACHE
             470 LOAD_FAST                6 (arg)
             472 LOAD_FAST                0 (self)
             474 LOAD_ATTR               10 (untagged_responses)
             494 CACHE
             496 CACHE
             498 STORE_FAST               6 (arg)

 988     >>  500 LOAD_FAST                5 (data)
             502 LOAD_CONST               8 (b' ')
             504 BINARY_OP                0 (+)
             508 LOAD_FAST                6 (arg)
             510 BINARY_OP                0 (+)
             514 STORE_FAST               5 (data)
             516 JUMP_BACKWARD           56 (to 406)

 990         518 LOAD_FAST                0 (self)
         >>  520 LOAD_ATTR                2 (Commands)
             540 STORE_ATTR               2 (literal)

 993         550 LOAD_GLOBAL             27 (NULL + type)
             560 CACHE
             562 LOAD_FAST                7 (literal)
             564 UNPACK_SEQUENCE          1
             568 CALL                     1
             576 CACHE
             578 LOAD_GLOBAL             27 (NULL + type)
             588 CACHE
             590 LOAD_FAST                0 (self)
             592 LOAD_ATTR               14 (readonly)
             612 CACHE
             614 CACHE
             616 IS_OP                    0
             618 POP_JUMP_IF_FALSE        3 (to 626)

 994         620 LOAD_FAST                7 (literal)
             622 STORE_FAST               8 (literator)
             624 JUMP_FORWARD            42 (to 710)

 996     >>  626 LOAD_CONST               0 (None)
             628 STORE_FAST               8 (literator)

 997         630 LOAD_FAST                5 (data)
             632 LOAD_GLOBAL             19 (NULL + bytes)
             642 CACHE
             644 LOAD_CONST               9 (' {%s}')
             646 LOAD_GLOBAL             31 (NULL + len)
             656 CACHE
             658 LOAD_FAST                7 (literal)
             660 UNPACK_SEQUENCE          1
             664 CALL                     1
             672 CACHE
             674 BINARY_OP                6 (%)
             678 LOAD_FAST                0 (self)
             680 LOAD_ATTR               10 (untagged_responses)
             700 CACHE
             702 CACHE
             704 BINARY_OP                0 (+)
             708 STORE_FAST               5 (data)

 999     >>  710 NOP

1000         712 LOAD_FAST                0 (self)
             714 LOAD_ATTR               16 (_new_tag)

1001         734 LOAD_FAST                0 (self)
             736 STORE_SUBSCR
             740 CACHE
             742 CACHE
             744 CACHE
             746 CACHE
             748 CACHE
             750 CACHE
             752 CACHE
             754 CACHE
             756 CACHE
             758 LOAD_CONST              12 ('> %r')
             760 LOAD_FAST                5 (data)
             762 BINARY_OP                6 (%)
             766 UNPACK_SEQUENCE          1
             770 CALL                     1
             778 CACHE
             780 POP_TOP
             782 JUMP_FORWARD            24 (to 832)

1003         784 LOAD_FAST                0 (self)
             786 STORE_SUBSCR
             790 CACHE
             792 CACHE
             794 CACHE
             796 CACHE
             798 CACHE
             800 CACHE
             802 CACHE
             804 CACHE
             806 CACHE
             808 LOAD_CONST              12 ('> %r')
             810 LOAD_FAST                5 (data)
             812 BINARY_OP                6 (%)
             816 UNPACK_SEQUENCE          1
             820 CALL                     1
             828 CACHE
             830 POP_TOP

1005     >>  832 NOP

1006         834 LOAD_FAST                0 (self)
             836 STORE_SUBSCR
             840 CACHE
             842 CACHE
             844 CACHE
             846 CACHE
             848 CACHE
             850 CACHE
             852 CACHE
             854 CACHE
             856 CACHE
             858 LOAD_FAST                5 (data)
             860 LOAD_GLOBAL             40 (CRLF)
             870 CACHE
             872 BINARY_OP                0 (+)
             876 UNPACK_SEQUENCE          1
             880 CALL                     1
             888 CACHE
             890 POP_TOP
             892 JUMP_FORWARD            42 (to 978)
         >>  894 PUSH_EXC_INFO

1007         896 LOAD_GLOBAL             42 (OSError)
             906 CACHE
             908 CHECK_EXC_MATCH
             910 POP_JUMP_IF_FALSE       29 (to 970)
             912 STORE_FAST               9 (val)

1008         914 LOAD_FAST                0 (self)
             916 STORE_SUBSCR
             920 CACHE
             922 CACHE
             924 CACHE
             926 CACHE
             928 CACHE
             930 CACHE
             932 CACHE
             934 CACHE
             936 CACHE
             938 LOAD_CONST              13 ('socket error: %s')
             940 LOAD_FAST                9 (val)
             942 BINARY_OP                6 (%)
             946 UNPACK_SEQUENCE          1
             950 CALL                     1
             958 CACHE
             960 RAISE_VARARGS            1
         >>  962 LOAD_CONST               0 (None)
             964 STORE_FAST               9 (val)
             966 DELETE_FAST              9 (val)
             968 RERAISE                  1

1007     >>  970 RERAISE                  0
         >>  972 COPY                     3
             974 POP_EXCEPT
             976 RERAISE                  1

1010     >>  978 LOAD_FAST                7 (literal)
             980 POP_JUMP_IF_NOT_NONE     2 (to 986)

1011         982 LOAD_FAST                4 (tag)
             984 RETURN_VALUE

1013     >>  986 NOP

1016     >>  988 LOAD_FAST                0 (self)
             990 STORE_SUBSCR
             994 CACHE
             996 CACHE
             998 CACHE
            1000 CACHE
            1002 CACHE
            1004 CACHE
            1006 CACHE
            1008 CACHE
            1010 CACHE
            1012 UNPACK_SEQUENCE          0
            1016 CALL                     0
            1024 CACHE
            1026 POP_JUMP_IF_FALSE       35 (to 1098)

1017        1028 LOAD_FAST                0 (self)
            1030 LOAD_ATTR               24 (str)
            1050 CACHE
            1052 POP_JUMP_IF_FALSE        2 (to 1058)

1018        1054 LOAD_FAST                4 (tag)
            1056 RETURN_VALUE

1016     >> 1058 LOAD_FAST                0 (self)
            1060 STORE_SUBSCR
            1064 CACHE
            1066 CACHE
            1068 CACHE
            1070 CACHE
            1072 CACHE
            1074 CACHE
            1076 CACHE
            1078 CACHE
            1080 CACHE
            1082 UNPACK_SEQUENCE          0
            1086 CALL                     0
            1094 CACHE
