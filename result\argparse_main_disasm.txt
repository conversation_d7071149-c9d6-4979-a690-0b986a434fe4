# MAIN APPLICATION CODE OBJECT
# Position: 7795986
# Filename: argparse.py
# Function: <module>
# Args: 0
# Locals: 0
# Stack size: 6
# Flags: 0

# BYTECODE DISASSEMBLY:
==================================================
   0           0 RESUME                   0

   4           2 LOAD_CONST               0 ("Command-line parsing library\n\nThis module is an optparse-inspired command-line parsing library that:\n\n    - handles both optional and positional arguments\n    - produces highly informative usage messages\n    - supports parsers that dispatch to sub-parsers\n\nThe following is a simple usage example that sums integers from the\ncommand-line and writes the result to a file::\n\n    parser = argparse.ArgumentParser(\n        description='sum the integers at the command line')\n    parser.add_argument(\n        'integers', metavar='int', nargs='+', type=int,\n        help='an integer to be summed')\n    parser.add_argument(\n        '--log', default=sys.stdout, type=argparse.FileType('w'),\n        help='the file where the sum should be written')\n    args = parser.parse_args()\n    args.log.write('%s' % sum(args.integers))\n    args.log.close()\n\nThe module contains the following public classes:\n\n    - ArgumentParser -- The main entry point for command-line parsing. As the\n        example above shows, the add_argument() method is used to populate\n        the parser with actions for optional and positional arguments. Then\n        the parse_args() method is invoked to convert the args at the\n        command-line into an object with attributes.\n\n    - ArgumentError -- The exception raised by ArgumentParser objects when\n        there are errors with the parser's actions. Errors raised while\n        parsing the command-line are caught by ArgumentParser and emitted\n        as command-line messages.\n\n    - FileType -- A factory for defining types of files to be created. As the\n        example above shows, instances of FileType are typically passed as\n        the type= argument of add_argument() calls.\n\n    - Action -- The base class for parser actions. Typically actions are\n        selected by passing strings like 'store_true' or 'append_const' to\n        the action= argument of add_argument(). However, for greater\n        customization of ArgumentParser actions, subclasses of Action may\n        be defined and passed as the action= argument.\n\n    - HelpFormatter, RawDescriptionHelpFormatter, RawTextHelpFormatter,\n        ArgumentDefaultsHelpFormatter -- Formatter classes which\n        may be passed as the formatter_class= argument to the\n        ArgumentParser constructor. HelpFormatter is the default,\n        RawDescriptionHelpFormatter and RawTextHelpFormatter tell the parser\n        not to change the formatting for help text, and\n        ArgumentDefaultsHelpFormatter adds information about argument defaults\n        to the help.\n\nAll other classes in this module are considered implementation details.\n(Also note that HelpFormatter and RawDescriptionHelpFormatter are only\nconsidered public as object names -- the API of the formatter objects is\nstill considered an implementation detail.)\n")
               4 STORE_NAME               0 (__doc__)

  65           6 LOAD_CONST               1 ('1.1')
               8 STORE_NAME               1 (__version__)

  66          10 BUILD_LIST               0
              12 LOAD_CONST               2 (('ArgumentParser', 'ArgumentError', 'ArgumentTypeError', 'BooleanOptionalAction', 'FileType', 'HelpFormatter', 'ArgumentDefaultsHelpFormatter', 'RawDescriptionHelpFormatter', 'RawTextHelpFormatter', 'MetavarTypeHelpFormatter', 'Namespace', 'Action', 'ONE_OR_MORE', 'OPTIONAL', 'PARSER', 'REMAINDER', 'SUPPRESS', 'ZERO_OR_MORE'))
              14 LIST_EXTEND              1
              16 STORE_NAME               2 (__all__)

  88          18 LOAD_CONST               3 (0)
              20 LOAD_CONST               4 (None)
              22 IMPORT_NAME              3 (os)
              24 STORE_NAME               4 (_os)

  89          26 LOAD_CONST               3 (0)
              28 LOAD_CONST               4 (None)
              30 IMPORT_NAME              5 (re)
              32 STORE_NAME               6 (_re)

  90          34 LOAD_CONST               3 (0)
              36 LOAD_CONST               4 (None)
              38 IMPORT_NAME              7 (sys)
              40 STORE_NAME               8 (_sys)

  92          42 LOAD_CONST               3 (0)
              44 LOAD_CONST               4 (None)
              46 IMPORT_NAME              9 (warnings)
              48 STORE_NAME               9 (warnings)

  94          50 LOAD_CONST               3 (0)
              52 LOAD_CONST               5 (('gettext', 'ngettext'))
              54 IMPORT_NAME             10 (gettext)
              56 IMPORT_FROM             10 (gettext)
              58 STORE_NAME              11 (_)
              60 IMPORT_FROM             12 (ngettext)
              62 STORE_NAME              12 (ngettext)
              64 POP_TOP

  96          66 LOAD_CONST               6 ('==SUPPRESS==')
              68 STORE_NAME              13 (SUPPRESS)

  98          70 LOAD_CONST               7 ('?')
              72 STORE_NAME              14 (OPTIONAL)

  99          74 LOAD_CONST               8 ('*')
              76 STORE_NAME              15 (ZERO_OR_MORE)

 100          78 LOAD_CONST               9 ('+')
              80 STORE_NAME              16 (ONE_OR_MORE)

 101          82 LOAD_CONST              10 ('A...')
              84 STORE_NAME              17 (PARSER)

 102          86 LOAD_CONST              11 ('...')
              88 STORE_NAME              18 (REMAINDER)

 103          90 LOAD_CONST              12 ('_unrecognized_args')
              92 STORE_NAME              19 (_UNRECOGNIZED_ARGS_ATTR)

 109          94 PUSH_NULL
              96 LOAD_BUILD_CLASS
              98 LOAD_CONST              13 (<code object _AttributeHolder at 0x000001E77EC37A50, file "argparse.py", line 109>)
             100 MAKE_FUNCTION            0
             102 LOAD_CONST              14 ('_AttributeHolder')
             104 LOAD_NAME               20 (object)
             106 UNPACK_SEQUENCE          3
             110 CALL                     3
             118 CACHE
             120 STORE_NAME              21 (_AttributeHolder)

 140         122 LOAD_CONST              15 (<code object _copy_items at 0x000001E77EC3A0B0, file "argparse.py", line 140>)
             124 MAKE_FUNCTION            0
             126 STORE_NAME              22 (_copy_items)

 157         128 PUSH_NULL
             130 LOAD_BUILD_CLASS
             132 LOAD_CONST              16 (<code object HelpFormatter at 0x000001E77E6DF910, file "argparse.py", line 157>)
             134 MAKE_FUNCTION            0
             136 LOAD_CONST              17 ('HelpFormatter')
             138 LOAD_NAME               20 (object)
             140 UNPACK_SEQUENCE          3
             144 CALL                     3
             152 CACHE
             154 STORE_NAME              23 (HelpFormatter)

 679         156 PUSH_NULL
             158 LOAD_BUILD_CLASS
             160 LOAD_CONST              18 (<code object RawDescriptionHelpFormatter at 0x000001E77EC5DC30, file "argparse.py", line 679>)
             162 MAKE_FUNCTION            0
             164 LOAD_CONST              19 ('RawDescriptionHelpFormatter')
             166 LOAD_NAME               23 (HelpFormatter)
             168 UNPACK_SEQUENCE          3
             172 CALL                     3
             180 CACHE
             182 STORE_NAME              24 (RawDescriptionHelpFormatter)

 690         184 PUSH_NULL
             186 LOAD_BUILD_CLASS
             188 LOAD_CONST              20 (<code object RawTextHelpFormatter at 0x000001E77EC5DD10, file "argparse.py", line 690>)
             190 MAKE_FUNCTION            0
             192 LOAD_CONST              21 ('RawTextHelpFormatter')
             194 LOAD_NAME               24 (RawDescriptionHelpFormatter)
             196 UNPACK_SEQUENCE          3
             200 CALL                     3
             208 CACHE
             210 STORE_NAME              25 (RawTextHelpFormatter)

 701         212 PUSH_NULL
             214 LOAD_BUILD_CLASS
             216 LOAD_CONST              22 (<code object ArgumentDefaultsHelpFormatter at 0x000001E77EC5DDF0, file "argparse.py", line 701>)
             218 MAKE_FUNCTION            0
             220 LOAD_CONST              23 ('ArgumentDefaultsHelpFormatter')
             222 LOAD_NAME               23 (HelpFormatter)
             224 UNPACK_SEQUENCE          3
             228 CALL                     3
             236 CACHE
             238 STORE_NAME              26 (ArgumentDefaultsHelpFormatter)

 730         240 PUSH_NULL
             242 LOAD_BUILD_CLASS
             244 LOAD_CONST              24 (<code object MetavarTypeHelpFormatter at 0x000001E77EC5E090, file "argparse.py", line 730>)
             246 MAKE_FUNCTION            0
             248 LOAD_CONST              25 ('MetavarTypeHelpFormatter')
             250 LOAD_NAME               23 (HelpFormatter)
             252 UNPACK_SEQUENCE          3
             256 CALL                     3
             264 CACHE
             266 STORE_NAME              27 (MetavarTypeHelpFormatter)

 749         268 LOAD_CONST              26 (<code object _get_action_name at 0x000001E77E73F830, file "argparse.py", line 749>)
             270 MAKE_FUNCTION            0
             272 STORE_NAME              28 (_get_action_name)

 764         274 PUSH_NULL
             276 LOAD_BUILD_CLASS
             278 LOAD_CONST              27 (<code object ArgumentError at 0x000001E77EC5E170, file "argparse.py", line 764>)
             280 MAKE_FUNCTION            0
             282 LOAD_CONST              28 ('ArgumentError')
             284 LOAD_NAME               29 (Exception)
             286 UNPACK_SEQUENCE          3
             290 CALL                     3
             298 CACHE
             300 STORE_NAME              30 (ArgumentError)

 784         302 PUSH_NULL
             304 LOAD_BUILD_CLASS
             306 LOAD_CONST              29 (<code object ArgumentTypeError at 0x000001E77EC5E250, file "argparse.py", line 784>)
             308 MAKE_FUNCTION            0
             310 LOAD_CONST              30 ('ArgumentTypeError')
             312 LOAD_NAME               29 (Exception)
             314 UNPACK_SEQUENCE          3
             318 CALL                     3
             326 CACHE
             328 STORE_NAME              31 (ArgumentTypeError)

 793         330 PUSH_NULL
             332 LOAD_BUILD_CLASS
             334 LOAD_CONST              31 (<code object Action at 0x000001E77EC79A30, file "argparse.py", line 793>)
             336 MAKE_FUNCTION            0
             338 LOAD_CONST              32 ('Action')
             340 LOAD_NAME               21 (_AttributeHolder)
             342 UNPACK_SEQUENCE          3
             346 CALL                     3
             354 CACHE
             356 STORE_NAME              32 (Action)

 888         358 PUSH_NULL
             360 LOAD_BUILD_CLASS
             362 LOAD_CONST              33 (<code object BooleanOptionalAction at 0x000001E77EC79C30, file "argparse.py", line 888>)
             364 MAKE_FUNCTION            0
             366 LOAD_CONST              34 ('BooleanOptionalAction')
             368 LOAD_NAME               32 (Action)
             370 UNPACK_SEQUENCE          3
             374 CALL                     3
             382 CACHE
             384 STORE_NAME              33 (BooleanOptionalAction)

 927         386 PUSH_NULL
             388 LOAD_BUILD_CLASS
             390 LOAD_CONST              35 (<code object _StoreAction at 0x000001E77EC79F30, file "argparse.py", line 927>)
             392 MAKE_FUNCTION            0
             394 LOAD_CONST              36 ('_StoreAction')
             396 LOAD_NAME               32 (Action)
             398 UNPACK_SEQUENCE          3
             402 CALL                     3
             410 CACHE
             412 STORE_NAME              34 (_StoreAction)

 962         414 PUSH_NULL
             416 LOAD_BUILD_CLASS
             418 LOAD_CONST              37 (<code object _StoreConstAction at 0x000001E77EC7A130, file "argparse.py", line 962>)
             420 MAKE_FUNCTION            0
             422 LOAD_CONST              38 ('_StoreConstAction')
             424 LOAD_NAME               32 (Action)
             426 UNPACK_SEQUENCE          3
             430 CALL                     3
             438 CACHE
             440 STORE_NAME              35 (_StoreConstAction)

 985         442 PUSH_NULL
             444 LOAD_BUILD_CLASS
             446 LOAD_CONST              39 (<code object _StoreTrueAction at 0x000001E77EC903F0, file "argparse.py", line 985>)
             448 MAKE_FUNCTION            0
             450 LOAD_CONST              40 ('_StoreTrueAction')
             452 LOAD_NAME               35 (_StoreConstAction)
             454 UNPACK_SEQUENCE          3
             458 CALL                     3
             466 CACHE
             468 STORE_NAME              36 (_StoreTrueAction)

1002         470 PUSH_NULL
             472 LOAD_BUILD_CLASS
             474 LOAD_CONST              41 (<code object _StoreFalseAction at 0x000001E77EC904E0, file "argparse.py", line 1002>)
             476 MAKE_FUNCTION            0
             478 LOAD_CONST              42 ('_StoreFalseAction')
             480 LOAD_NAME               35 (_StoreConstAction)
             482 UNPACK_SEQUENCE          3
             486 CALL                     3
             494 CACHE
             496 STORE_NAME              37 (_StoreFalseAction)

1019         498 PUSH_NULL
             500 LOAD_BUILD_CLASS
             502 LOAD_CONST              43 (<code object _AppendAction at 0x000001E77EC7A330, file "argparse.py", line 1019>)
             504 MAKE_FUNCTION            0
             506 LOAD_CONST              44 ('_AppendAction')
             508 LOAD_NAME               32 (Action)
             510 UNPACK_SEQUENCE          3
             514 CALL                     3
             522 CACHE
             524 STORE_NAME              38 (_AppendAction)

1057         526 PUSH_NULL
             528 LOAD_BUILD_CLASS
             530 LOAD_CONST              45 (<code object _AppendConstAction at 0x000001E77EC7A430, file "argparse.py", line 1057>)
             532 MAKE_FUNCTION            0
             534 LOAD_CONST              46 ('_AppendConstAction')
             536 LOAD_NAME               32 (Action)
             538 UNPACK_SEQUENCE          3
             542 CALL                     3
             550 CACHE
             552 STORE_NAME              39 (_AppendConstAction)

1084         554 PUSH_NULL
             556 LOAD_BUILD_CLASS
             558 LOAD_CONST              47 (<code object _CountAction at 0x000001E77EC905D0, file "argparse.py", line 1084>)
             560 MAKE_FUNCTION            0
             562 LOAD_CONST              48 ('_CountAction')
             564 LOAD_NAME               32 (Action)
             566 UNPACK_SEQUENCE          3
             570 CALL                     3
             578 CACHE
             580 STORE_NAME              40 (_CountAction)

1107         582 PUSH_NULL
             584 LOAD_BUILD_CLASS
             586 LOAD_CONST              49 (<code object _HelpAction at 0x000001E77EC906C0, file "argparse.py", line 1107>)
             588 MAKE_FUNCTION            0
             590 LOAD_CONST              50 ('_HelpAction')
             592 LOAD_NAME               32 (Action)
             594 UNPACK_SEQUENCE          3
             598 CALL                     3
             606 CACHE
             608 STORE_NAME              41 (_HelpAction)

1126         610 PUSH_NULL
             612 LOAD_BUILD_CLASS
             614 LOAD_CONST              51 (<code object _VersionAction at 0x000001E77EC907B0, file "argparse.py", line 1126>)
             616 MAKE_FUNCTION            0
             618 LOAD_CONST              52 ('_VersionAction')
             620 LOAD_NAME               32 (Action)
             622 UNPACK_SEQUENCE          3
             626 CALL                     3
             634 CACHE
             636 STORE_NAME              42 (_VersionAction)

1154         638 PUSH_NULL
             640 LOAD_BUILD_CLASS
             642 LOAD_CONST              53 (<code object _SubParsersAction at 0x000001E77EC57510, file "argparse.py", line 1154>)
             644 MAKE_FUNCTION            0
             646 LOAD_CONST              54 ('_SubParsersAction')
             648 LOAD_NAME               32 (Action)
             650 UNPACK_SEQUENCE          3
             654 CALL                     3
             662 CACHE
             664 STORE_NAME              43 (_SubParsersAction)

1254         666 PUSH_NULL
             668 LOAD_BUILD_CLASS
             670 LOAD_CONST              55 (<code object _ExtendAction at 0x000001E77EC5E870, file "argparse.py", line 1254>)
             672 MAKE_FUNCTION            0
             674 LOAD_CONST              56 ('_ExtendAction')
             676 LOAD_NAME               38 (_AppendAction)
             678 UNPACK_SEQUENCE          3
             682 CALL                     3
             690 CACHE
             692 STORE_NAME              44 (_ExtendAction)

1265         694 PUSH_NULL
             696 LOAD_BUILD_CLASS
             698 LOAD_CONST              57 (<code object FileType at 0x000001E77EC90B70, file "argparse.py", line 1265>)
             700 MAKE_FUNCTION            0
             702 LOAD_CONST              58 ('FileType')
             704 LOAD_NAME               20 (object)
             706 UNPACK_SEQUENCE          3
             710 CALL                     3
             718 CACHE
             720 STORE_NAME              45 (FileType)

1320         722 PUSH_NULL
             724 LOAD_BUILD_CLASS
             726 LOAD_CONST              59 (<code object Namespace at 0x000001E77EC90C60, file "argparse.py", line 1320>)
             728 MAKE_FUNCTION            0
             730 LOAD_CONST              60 ('Namespace')
             732 LOAD_NAME               21 (_AttributeHolder)
             734 UNPACK_SEQUENCE          3
             738 CALL                     3
             746 CACHE
             748 STORE_NAME              46 (Namespace)

1340         750 PUSH_NULL
             752 LOAD_BUILD_CLASS
             754 LOAD_CONST              61 (<code object _ActionsContainer at 0x000001E77EC4DE60, file "argparse.py", line 1340>)
             756 MAKE_FUNCTION            0
             758 LOAD_CONST              62 ('_ActionsContainer')
             760 LOAD_NAME               20 (object)
             762 UNPACK_SEQUENCE          3
             766 CALL                     3
             774 CACHE
             776 STORE_NAME              47 (_ActionsContainer)

1650         778 PUSH_NULL
             780 LOAD_BUILD_CLASS
             782 LOAD_CONST              63 (<code object _ArgumentGroup at 0x000001E77EC7AA30, file "argparse.py", line 1650>)
             784 MAKE_FUNCTION            0
             786 LOAD_CONST              64 ('_ArgumentGroup')
             788 LOAD_NAME               47 (_ActionsContainer)
             790 UNPACK_SEQUENCE          3
             794 CALL                     3
             802 CACHE
             804 STORE_NAME              48 (_ArgumentGroup)

1692         806 PUSH_NULL
             808 LOAD_BUILD_CLASS
             810 LOAD_CONST              65 (<code object _MutuallyExclusiveGroup at 0x000001E77EC7AB30, file "argparse.py", line 1692>)
             812 MAKE_FUNCTION            0
             814 LOAD_CONST              66 ('_MutuallyExclusiveGroup')
             816 LOAD_NAME               48 (_ArgumentGroup)
             818 UNPACK_SEQUENCE          3
             822 CALL                     3
             830 CACHE
             832 STORE_NAME              49 (_MutuallyExclusiveGroup)

1720         834 PUSH_NULL
             836 LOAD_BUILD_CLASS
             838 LOAD_CONST              67 (<code object ArgumentParser at 0x000001E77E73FBB0, file "argparse.py", line 1720>)
             840 MAKE_FUNCTION            0
             842 LOAD_CONST              68 ('ArgumentParser')
             844 LOAD_NAME               21 (_AttributeHolder)
             846 LOAD_NAME               47 (_ActionsContainer)
             848 UNPACK_SEQUENCE          4
             852 CALL                     4
             860 CACHE
             862 STORE_NAME              50 (ArgumentParser)
             864 LOAD_CONST               4 (None)
             866 RETURN_VALUE

Disassembly of <code object _AttributeHolder at 0x000001E77EC37A50, file "argparse.py", line 109>:
109           0 RESUME                   0
              2 LOAD_NAME                0 (__name__)
              4 STORE_NAME               1 (__module__)
              6 LOAD_CONST               0 ('_AttributeHolder')
              8 STORE_NAME               2 (__qualname__)

110          10 LOAD_CONST               1 ("Abstract base class that provides __repr__.\n\n    The __repr__ method returns a string in the format::\n        ClassName(attr=name, attr=name, ...)\n    The attributes are determined either by a class-level attribute,\n    '_kwarg_names', or by inspecting the instance __dict__.\n    ")
             12 STORE_NAME               3 (__doc__)

118          14 LOAD_CONST               2 (<code object __repr__ at 0x000001E77E9CFBB0, file "argparse.py", line 118>)
             16 MAKE_FUNCTION            0
             18 STORE_NAME               4 (__repr__)

133          20 LOAD_CONST               3 (<code object _get_kwargs at 0x000001E77EC46340, file "argparse.py", line 133>)
             22 MAKE_FUNCTION            0
             24 STORE_NAME               5 (_get_kwargs)

136          26 LOAD_CONST               4 (<code object _get_args at 0x000001E77EC21F10, file "argparse.py", line 136>)
             28 MAKE_FUNCTION            0
             30 STORE_NAME               6 (_get_args)
             32 LOAD_CONST               5 (None)
             34 RETURN_VALUE

Disassembly of <code object __repr__ at 0x000001E77E9CFBB0, file "argparse.py", line 118>:
118           0 RESUME                   0

119           2 LOAD_GLOBAL              1 (NULL + type)
             12 CACHE
             14 LOAD_FAST                0 (self)
             16 UNPACK_SEQUENCE          1
             20 CALL                     1
             28 CACHE
             30 LOAD_ATTR                1 (NULL|self + type)

122          50 LOAD_FAST                0 (self)
             52 STORE_SUBSCR
             56 CACHE
             58 CACHE
             60 CACHE
             62 CACHE
             64 CACHE
             66 CACHE
             68 CACHE
             70 CACHE
             72 CACHE
             74 UNPACK_SEQUENCE          0
             78 CALL                     0
             86 CACHE
             88 GET_ITER
        >>   90 FOR_ITER                36 (to 166)

123          94 LOAD_FAST                2 (arg_strings)
             96 STORE_SUBSCR
            100 CACHE
            102 CACHE
            104 CACHE
            106 CACHE
            108 CACHE
            110 CACHE
            112 CACHE
            114 CACHE
            116 CACHE
            118 LOAD_GLOBAL              9 (NULL + repr)
            128 CACHE
            130 LOAD_FAST                4 (arg)
            132 UNPACK_SEQUENCE          1
            136 CALL                     1
            144 CACHE
            146 UNPACK_SEQUENCE          1
            150 CALL                     1
            158 CACHE
            160 POP_TOP
            162 JUMP_BACKWARD           37 (to 90)

124         164 LOAD_FAST                0 (self)
        >>  166 STORE_SUBSCR
            170 CACHE
            172 CACHE
            174 CACHE
            176 CACHE
            178 CACHE
            180 CACHE
            182 CACHE
            184 CACHE
            186 CACHE
            188 UNPACK_SEQUENCE          0
            192 CALL                     0
            200 CACHE
            202 GET_ITER
        >>  204 FOR_ITER                57 (to 322)
            208 CACHE
            210 STORE_FAST               5 (name)
            212 STORE_FAST               6 (value)

125         214 LOAD_FAST                5 (name)
            216 STORE_SUBSCR
            220 CACHE
            222 CACHE
            224 CACHE
            226 CACHE
            228 CACHE
            230 CACHE
            232 CACHE
            234 CACHE
            236 CACHE
            238 UNPACK_SEQUENCE          0
            242 CALL                     0
            250 CACHE
            252 POP_JUMP_IF_FALSE       27 (to 308)

126         254 LOAD_FAST                2 (arg_strings)
            256 STORE_SUBSCR
            260 CACHE
            262 CACHE
            264 CACHE
            266 CACHE
            268 CACHE
            270 CACHE
            272 CACHE
            274 CACHE
            276 CACHE
            278 LOAD_FAST                5 (name)
            280 FORMAT_VALUE             1 (str)
            282 LOAD_CONST               1 ('=')
            284 LOAD_FAST                6 (value)
            286 FORMAT_VALUE             2 (repr)
            288 BUILD_STRING             3
            290 UNPACK_SEQUENCE          1
            294 CALL                     1
            302 CACHE
            304 POP_TOP
            306 JUMP_BACKWARD           52 (to 204)

128     >>  308 LOAD_FAST                6 (value)
            310 LOAD_FAST                3 (star_args)
            312 LOAD_FAST                5 (name)
            314 STORE_SUBSCR
            318 JUMP_BACKWARD           58 (to 204)

129         320 LOAD_FAST                3 (star_args)
        >>  322 POP_JUMP_IF_FALSE       37 (to 398)

130         324 LOAD_FAST                2 (arg_strings)
            326 STORE_SUBSCR
            330 CACHE
            332 CACHE
            334 CACHE
            336 CACHE
            338 CACHE
            340 CACHE
            342 CACHE
            344 CACHE
            346 CACHE
            348 LOAD_CONST               2 ('**%s')
            350 LOAD_GLOBAL              9 (NULL + repr)
            360 CACHE
            362 LOAD_FAST                3 (star_args)
            364 UNPACK_SEQUENCE          1
            368 CALL                     1
            376 CACHE
            378 BINARY_OP                6 (%)
            382 UNPACK_SEQUENCE          1
            386 CALL                     1
            394 CACHE
            396 POP_TOP

131     >>  398 LOAD_FAST                1 (type_name)
            400 FORMAT_VALUE             1 (str)
            402 LOAD_CONST               3 ('(')
            404 LOAD_CONST               4 (', ')
            406 STORE_SUBSCR
            410 CACHE
            412 CACHE
            414 CACHE
            416 CACHE
            418 CACHE
            420 CACHE
            422 CACHE
            424 CACHE
            426 CACHE
            428 LOAD_FAST                2 (arg_strings)
            430 UNPACK_SEQUENCE          1
            434 CALL                     1
            442 CACHE
            444 FORMAT_VALUE             1 (str)
            446 LOAD_CONST               5 (')')
            448 BUILD_STRING             4
            450 RETURN_VALUE

Disassembly of <code object _get_kwargs at 0x000001E77EC46340, file "argparse.py", line 133>:
133           0 RESUME                   0

134           2 LOAD_GLOBAL              1 (NULL + list)
             12 CACHE
             14 LOAD_FAST                0 (self)
             16 LOAD_ATTR                1 (NULL|self + list)
             36 CACHE
             38 CACHE
             40 CACHE
             42 CACHE
             44 CACHE
             46 CACHE
             48 UNPACK_SEQUENCE          0
             52 CALL                     0
             60 CACHE
             62 UNPACK_SEQUENCE          1
             66 CALL                     1
             74 CACHE
             76 RETURN_VALUE

Disassembly of <code object _get_args at 0x000001E77EC21F10, file "argparse.py", line 136>:
136           0 RESUME                   0

137           2 BUILD_LIST               0
              4 RETURN_VALUE

Disassembly of <code object _copy_items at 0x000001E77EC3A0B0, file "argparse.py", line 140>:
140           0 RESUME                   0

141           2 LOAD_FAST                0 (items)
              4 POP_JUMP_IF_NOT_NONE     2 (to 10)

142           6 BUILD_LIST               0
              8 RETURN_VALUE

146     >>   10 LOAD_GLOBAL              1 (NULL + type)
             20 CACHE
             22 LOAD_FAST                0 (items)
             24 UNPACK_SEQUENCE          1
             28 CALL                     1
             36 CACHE
             38 LOAD_GLOBAL              2 (list)
             48 CACHE
             50 IS_OP                    0
             52 POP_JUMP_IF_FALSE       10 (to 74)

147          54 LOAD_FAST                0 (items)
             56 LOAD_CONST               0 (None)
             58 LOAD_CONST               0 (None)
             60 BUILD_SLICE              2
             62 BINARY_SUBSCR
             66 CACHE
             68 CACHE
             70 CACHE
             72 RETURN_VALUE

148     >>   74 LOAD_CONST               1 (0)
             76 LOAD_CONST               0 (None)
             78 IMPORT_NAME              2 (copy)
             80 STORE_FAST               1 (copy)

149          82 LOAD_FAST                1 (copy)
             84 STORE_SUBSCR
             88 CACHE
             90 CACHE
             92 CACHE
             94 CACHE
             96 CACHE
             98 CACHE
            100 CACHE
            102 CACHE
            104 CACHE
            106 LOAD_FAST                0 (items)
            108 UNPACK_SEQUENCE          1
            112 CALL                     1
            120 CACHE
            122 RETURN_VALUE

Disassembly of <code object HelpFormatter at 0x000001E77E6DF910, file "argparse.py", line 157>:
157           0 RESUME                   0
              2 LOAD_NAME                0 (__name__)
              4 STORE_NAME               1 (__module__)
              6 LOAD_CONST               0 ('HelpFormatter')
              8 STORE_NAME               2 (__qualname__)

158          10 LOAD_CONST               1 ('Formatter for generating usage messages and argument help strings.\n\n    Only the name of this class is considered a public API. All the methods\n    provided by the class are considered an implementation detail.\n    ')
             12 STORE_NAME               3 (__doc__)

166          14 NOP

167          16 NOP

168          18 NOP

164          20 LOAD_CONST              33 ((2, 24, None))
             22 LOAD_CONST               5 (<code object __init__ at 0x000001E77E9F97C0, file "argparse.py", line 164>)
             24 MAKE_FUNCTION            1 (defaults)
             26 STORE_NAME               4 (__init__)

195          28 LOAD_CONST               6 (<code object _indent at 0x000001E77EC46450, file "argparse.py", line 195>)
             30 MAKE_FUNCTION            0
             32 STORE_NAME               5 (_indent)

199          34 LOAD_CONST               7 (<code object _dedent at 0x000001E77EC3A330, file "argparse.py", line 199>)
             36 MAKE_FUNCTION            0
             38 STORE_NAME               6 (_dedent)

204          40 PUSH_NULL
             42 LOAD_BUILD_CLASS
             44 LOAD_CONST               8 (<code object _Section at 0x000001E77EC5D7D0, file "argparse.py", line 204>)
             46 MAKE_FUNCTION            0
             48 LOAD_CONST               9 ('_Section')
             50 LOAD_NAME                7 (object)
             52 UNPACK_SEQUENCE          3
             56 CALL                     3
             64 CACHE
             66 STORE_NAME               8 (_Section)

236          68 LOAD_CONST              10 (<code object _add_item at 0x000001E77EC46670, file "argparse.py", line 236>)
             70 MAKE_FUNCTION            0
             72 STORE_NAME               9 (_add_item)

242          74 LOAD_CONST              11 (<code object start_section at 0x000001E77E79F110, file "argparse.py", line 242>)
             76 MAKE_FUNCTION            0
             78 STORE_NAME              10 (start_section)

248          80 LOAD_CONST              12 (<code object end_section at 0x000001E77EC46780, file "argparse.py", line 248>)
             82 MAKE_FUNCTION            0
             84 STORE_NAME              11 (end_section)

252          86 LOAD_CONST              13 (<code object add_text at 0x000001E77EC57090, file "argparse.py", line 252>)
             88 MAKE_FUNCTION            0
             90 STORE_NAME              12 (add_text)

256          92 LOAD_CONST              34 ((None,))
             94 LOAD_CONST              14 (<code object add_usage at 0x000001E77EC571B0, file "argparse.py", line 256>)
             96 MAKE_FUNCTION            1 (defaults)
             98 STORE_NAME              13 (add_usage)

261         100 LOAD_CONST              15 (<code object add_argument at 0x000001E77E857040, file "argparse.py", line 261>)
            102 MAKE_FUNCTION            0
            104 STORE_NAME              14 (add_argument)

279         106 LOAD_CONST              16 (<code object add_arguments at 0x000001E77EC79030, file "argparse.py", line 279>)
            108 MAKE_FUNCTION            0
            110 STORE_NAME              15 (add_arguments)

286         112 LOAD_CONST              17 (<code object format_help at 0x000001E77E79F280, file "argparse.py", line 286>)
            114 MAKE_FUNCTION            0
            116 STORE_NAME              16 (format_help)

293         118 LOAD_CONST              18 (<code object _join_parts at 0x000001E77EC79130, file "argparse.py", line 293>)
            120 MAKE_FUNCTION            0
            122 STORE_NAME              17 (_join_parts)

298         124 LOAD_CONST              19 (<code object _format_usage at 0x000001E77E8ADD70, file "argparse.py", line 298>)
            126 MAKE_FUNCTION            0
            128 STORE_NAME              18 (_format_usage)

394         130 LOAD_CONST              20 (<code object _format_actions_usage at 0x000001E77E8AF390, file "argparse.py", line 394>)
            132 MAKE_FUNCTION            0
            134 STORE_NAME              19 (_format_actions_usage)

509         136 LOAD_CONST              21 (<code object _format_text at 0x000001E77E723630, file "argparse.py", line 509>)
            138 MAKE_FUNCTION            0
            140 STORE_NAME              20 (_format_text)

516         142 LOAD_CONST              22 (<code object _format_action at 0x000001E77E9B8E00, file "argparse.py", line 516>)
            144 MAKE_FUNCTION            0
            146 STORE_NAME              21 (_format_action)

564         148 LOAD_CONST              23 (<code object _format_action_invocation at 0x000001E77E9F7FC0, file "argparse.py", line 564>)
            150 MAKE_FUNCTION            0
            152 STORE_NAME              22 (_format_action_invocation)

588         154 LOAD_CONST              24 (<code object _metavar_formatter at 0x000001E77EC84190, file "argparse.py", line 588>)
            156 MAKE_FUNCTION            0
            158 STORE_NAME              23 (_metavar_formatter)

604         160 LOAD_CONST              25 (<code object _format_args at 0x000001E77E9CC870, file "argparse.py", line 604>)
            162 MAKE_FUNCTION            0
            164 STORE_NAME              24 (_format_args)

632         166 LOAD_CONST              26 (<code object _expand_help at 0x000001E77E890D80, file "argparse.py", line 632>)
            168 MAKE_FUNCTION            0
            170 STORE_NAME              25 (_expand_help)

645         172 LOAD_CONST              27 (<code object _iter_indented_subactions at 0x000001E77E79F3F0, file "argparse.py", line 645>)
            174 MAKE_FUNCTION            0
            176 STORE_NAME              26 (_iter_indented_subactions)

655         178 LOAD_CONST              28 (<code object _split_lines at 0x000001E77EC4D920, file "argparse.py", line 655>)
            180 MAKE_FUNCTION            0
            182 STORE_NAME              27 (_split_lines)

662         184 LOAD_CONST              29 (<code object _fill_text at 0x000001E77EC842F0, file "argparse.py", line 662>)
            186 MAKE_FUNCTION            0
            188 STORE_NAME              28 (_fill_text)

669         190 LOAD_CONST              30 (<code object _get_help_string at 0x000001E77EC224C0, file "argparse.py", line 669>)
            192 MAKE_FUNCTION            0
            194 STORE_NAME              29 (_get_help_string)

672         196 LOAD_CONST              31 (<code object _get_default_metavar_for_optional at 0x000001E77EC79430, file "argparse.py", line 672>)
            198 MAKE_FUNCTION            0
            200 STORE_NAME              30 (_get_default_metavar_for_optional)

675         202 LOAD_CONST              32 (<code object _get_default_metavar_for_positional at 0x000001E77EC22590, file "argparse.py", line 675>)
            204 MAKE_FUNCTION            0
            206 STORE_NAME              31 (_get_default_metavar_for_positional)
            208 LOAD_CONST               4 (None)
            210 RETURN_VALUE

Disassembly of <code object __init__ at 0x000001E77E9F97C0, file "argparse.py", line 164>:
164           0 RESUME                   0

171           2 LOAD_FAST                4 (width)
              4 POP_JUMP_IF_NOT_NONE    34 (to 74)

172           6 LOAD_CONST               1 (0)
              8 LOAD_CONST               0 (None)
             10 IMPORT_NAME              0 (shutil)
             12 STORE_FAST               5 (shutil)

173          14 LOAD_FAST                5 (shutil)
             16 STORE_SUBSCR
             20 CACHE
             22 CACHE
             24 CACHE
             26 CACHE
             28 CACHE
             30 CACHE
             32 CACHE
             34 CACHE
             36 CACHE
             38 UNPACK_SEQUENCE          0
             42 CALL                     0
             50 CACHE
             52 LOAD_ATTR                2 (get_terminal_size)
             72 STORE_FAST               4 (width)

176     >>   74 LOAD_FAST                1 (prog)
             76 LOAD_FAST                0 (self)
             78 STORE_ATTR               3 (_prog)

177          88 LOAD_FAST                2 (indent_increment)
             90 LOAD_FAST                0 (self)
             92 STORE_ATTR               4 (_indent_increment)

178         102 LOAD_GLOBAL             11 (NULL + min)
            112 CACHE
            114 LOAD_FAST                3 (max_help_position)

179         116 LOAD_GLOBAL             13 (NULL + max)
            126 CACHE
            128 LOAD_FAST                4 (width)
            130 LOAD_CONST               3 (20)
            132 BINARY_OP               10 (-)
            136 LOAD_FAST                2 (indent_increment)
            138 LOAD_CONST               2 (2)
            140 BINARY_OP                5 (*)
            144 UNPACK_SEQUENCE          2
            148 CALL                     2
            156 CACHE

178         158 UNPACK_SEQUENCE          2
            162 CALL                     2
            170 CACHE
            172 LOAD_FAST                0 (self)
            174 STORE_ATTR               7 (_max_help_position)

180         184 LOAD_FAST                4 (width)
            186 LOAD_FAST                0 (self)
            188 STORE_ATTR               8 (_width)

182         198 LOAD_CONST               1 (0)
            200 LOAD_FAST                0 (self)
            202 STORE_ATTR               9 (_current_indent)

183         212 LOAD_CONST               1 (0)
            214 LOAD_FAST                0 (self)
            216 STORE_ATTR              10 (_level)

184         226 LOAD_CONST               1 (0)
            228 LOAD_FAST                0 (self)
            230 STORE_ATTR              11 (_action_max_length)

186         240 LOAD_FAST                0 (self)
            242 STORE_SUBSCR
            246 CACHE
            248 CACHE
            250 CACHE
            252 CACHE
            254 CACHE
            256 CACHE
            258 CACHE
            260 CACHE
            262 CACHE
            264 LOAD_FAST                0 (self)
            266 LOAD_CONST               0 (None)
            268 UNPACK_SEQUENCE          2
            272 CALL                     2
            280 CACHE
            282 LOAD_FAST                0 (self)
            284 STORE_ATTR              13 (_root_section)

187         294 LOAD_FAST                0 (self)
            296 LOAD_ATTR               13 (NULL|self + max)
            316 CACHE

189         318 LOAD_GLOBAL             31 (NULL + _re)
            328 CACHE
            330 LOAD_ATTR               16 (_width)
            350 CACHE
            352 CACHE
            354 LOAD_ATTR               17 (NULL|self + _width)
            374 CACHE
            376 CACHE
            378 LOAD_FAST                0 (self)
            380 STORE_ATTR              18 (_whitespace_matcher)

190         390 LOAD_GLOBAL             31 (NULL + _re)
            400 CACHE
            402 LOAD_ATTR               16 (_width)
            422 CACHE
            424 CACHE
            426 CACHE
            428 LOAD_FAST                0 (self)
            430 STORE_ATTR              19 (_long_break_matcher)
            440 LOAD_CONST               0 (None)
            442 RETURN_VALUE

Disassembly of <code object _indent at 0x000001E77EC46450, file "argparse.py", line 195>:
195           0 RESUME                   0

196           2 LOAD_FAST                0 (self)
              4 COPY                     1
              6 LOAD_ATTR                0 (_current_indent)
             26 CACHE
             28 BINARY_OP               13 (+=)
             32 SWAP                     2
             34 STORE_ATTR               0 (_current_indent)

197          44 LOAD_FAST                0 (self)
             46 COPY                     1
             48 LOAD_ATTR                2 (_indent_increment)
             68 CACHE
             70 CACHE
             72 CACHE
             74 CACHE
             76 LOAD_CONST               0 (None)
             78 RETURN_VALUE

Disassembly of <code object _dedent at 0x000001E77EC3A330, file "argparse.py", line 199>:
199           0 RESUME                   0

200           2 LOAD_FAST                0 (self)
              4 COPY                     1
              6 LOAD_ATTR                0 (_current_indent)
             26 CACHE
             28 BINARY_OP               23 (-=)
             32 SWAP                     2
             34 STORE_ATTR               0 (_current_indent)

201          44 LOAD_FAST                0 (self)
             46 LOAD_ATTR                0 (_current_indent)
             66 LOAD_ASSERTION_ERROR
             68 LOAD_CONST               2 ('Indent decreased below 0.')
             70 UNPACK_SEQUENCE          0
             74 CALL                     0
             82 CACHE
             84 RAISE_VARARGS            1

202          86 LOAD_FAST                0 (self)
             88 COPY                     1
             90 LOAD_ATTR                2 (_indent_increment)
            110 CACHE
            112 CACHE
            114 CACHE
            116 CACHE
            118 LOAD_CONST               0 (None)
            120 RETURN_VALUE

Disassembly of <code object _Section at 0x000001E77EC5D7D0, file "argparse.py", line 204>:
204           0 RESUME                   0
              2 LOAD_NAME                0 (__name__)
              4 STORE_NAME               1 (__module__)
              6 LOAD_CONST               0 ('HelpFormatter._Section')
              8 STORE_NAME               2 (__qualname__)

206          10 LOAD_CONST               4 ((None,))
             12 LOAD_CONST               2 (<code object __init__ at 0x000001E77EC78E30, file "argparse.py", line 206>)
             14 MAKE_FUNCTION            1 (defaults)
             16 STORE_NAME               3 (__init__)

212          18 LOAD_CONST               3 (<code object format_help at 0x000001E77E9F7D60, file "argparse.py", line 212>)
             20 MAKE_FUNCTION            0
             22 STORE_NAME               4 (format_help)
             24 LOAD_CONST               1 (None)
             26 RETURN_VALUE

Disassembly of <code object __init__ at 0x000001E77EC78E30, file "argparse.py", line 206>:
206           0 RESUME                   0

207           2 LOAD_FAST                1 (formatter)
              4 LOAD_FAST                0 (self)
              6 STORE_ATTR               0 (formatter)

208          16 LOAD_FAST                2 (parent)
             18 LOAD_FAST                0 (self)
             20 STORE_ATTR               1 (parent)

209          30 LOAD_FAST                3 (heading)
             32 LOAD_FAST                0 (self)
             34 STORE_ATTR               2 (heading)

210          44 BUILD_LIST               0
             46 LOAD_FAST                0 (self)
             48 STORE_ATTR               3 (items)
             58 LOAD_CONST               0 (None)
             60 RETURN_VALUE

Disassembly of <code object format_help at 0x000001E77E9F7D60, file "argparse.py", line 212>:
212           0 RESUME                   0

214           2 LOAD_FAST                0 (self)
              4 LOAD_ATTR                0 (parent)
             24 CACHE
             26 CACHE
             28 STORE_SUBSCR
             32 CACHE
             34 CACHE
             36 CACHE
             38 CACHE
             40 CACHE
             42 CACHE
             44 CACHE
             46 CACHE
             48 CACHE
             50 UNPACK_SEQUENCE          0
             54 CALL                     0
             62 CACHE
             64 POP_TOP

216          66 LOAD_FAST                0 (self)
             68 LOAD_ATTR                1 (NULL|self + parent)
             88 STORE_FAST               1 (join)

217          90 PUSH_NULL
             92 LOAD_FAST                1 (join)
             94 LOAD_CONST               1 (<code object <listcomp> at 0x000001E77EC5D6F0, file "argparse.py", line 217>)
             96 MAKE_FUNCTION            0
             98 LOAD_FAST                0 (self)
            100 LOAD_ATTR                4 (_indent)
            120 CACHE
            122 CACHE
            124 CACHE
            126 UNPACK_SEQUENCE          1
            130 CALL                     1
            138 CACHE
            140 STORE_FAST               2 (item_help)

218         142 LOAD_FAST                0 (self)
            144 LOAD_ATTR                0 (parent)
            164 CACHE
            166 CACHE
            168 STORE_SUBSCR
            172 CACHE
            174 CACHE
            176 CACHE
            178 CACHE
            180 CACHE
            182 CACHE
            184 CACHE
            186 CACHE
            188 CACHE
            190 UNPACK_SEQUENCE          0
            194 CALL                     0
            202 CACHE
            204 POP_TOP

222         206 LOAD_FAST                2 (item_help)
            208 POP_JUMP_IF_TRUE         2 (to 214)

223         210 LOAD_CONST               2 ('')
            212 RETURN_VALUE

226     >>  214 LOAD_FAST                0 (self)
            216 LOAD_ATTR                6 (_join_parts)
            236 CACHE
            238 IS_OP                    1
            240 POP_JUMP_IF_FALSE       65 (to 372)
            242 LOAD_FAST                0 (self)
            244 LOAD_ATTR                6 (_join_parts)
            264 CACHE
            266 CACHE
            268 LOAD_ATTR                8 (items)
            288 CACHE
            290 CACHE
            292 LOAD_CONST               3 ('%(heading)s:')
            294 UNPACK_SEQUENCE          1
            298 CALL                     1
            306 CACHE
            308 LOAD_GLOBAL             21 (NULL + dict)
            318 CACHE
            320 LOAD_FAST                0 (self)
            322 LOAD_ATTR                6 (_join_parts)
            342 CACHE
            344 CACHE
            346 CACHE
            348 BINARY_OP                6 (%)
            352 STORE_FAST               4 (heading_text)

229         354 LOAD_CONST               5 ('%*s%s\n')
            356 LOAD_FAST                3 (current_indent)
            358 LOAD_CONST               2 ('')
            360 LOAD_FAST                4 (heading_text)
            362 BUILD_TUPLE              3
            364 BINARY_OP                6 (%)
            368 STORE_FAST               5 (heading)
            370 JUMP_FORWARD             2 (to 376)

231     >>  372 LOAD_CONST               2 ('')
            374 STORE_FAST               5 (heading)

234     >>  376 PUSH_NULL
            378 LOAD_FAST                1 (join)
            380 LOAD_CONST               6 ('\n')
            382 LOAD_FAST                5 (heading)
            384 LOAD_FAST                2 (item_help)
            386 LOAD_CONST               6 ('\n')
            388 BUILD_LIST               4
            390 UNPACK_SEQUENCE          1
            394 CALL                     1
            402 CACHE
            404 RETURN_VALUE

Disassembly of <code object <listcomp> at 0x000001E77EC5D6F0, file "argparse.py", line 217>:
217           0 RESUME                   0
              2 BUILD_LIST               0
              4 LOAD_FAST                0 (.0)
        >>    6 FOR_ITER                10 (to 30)
             10 CACHE
             12 STORE_FAST               1 (func)
             14 STORE_FAST               2 (args)
             16 PUSH_NULL
             18 LOAD_FAST                1 (func)
             20 LOAD_FAST                2 (args)
             22 CALL_FUNCTION_EX         0
             24 LIST_APPEND              2
             26 JUMP_BACKWARD           11 (to 6)
             28 RETURN_VALUE

Disassembly of <code object _add_item at 0x000001E77EC46670, file "argparse.py", line 236>:
236           0 RESUME                   0

237           2 LOAD_FAST                0 (self)
              4 LOAD_ATTR                0 (_current_section)
             24 STORE_SUBSCR
             28 CACHE
             30 CACHE
             32 CACHE
             34 CACHE
             36 CACHE
             38 CACHE
             40 CACHE
             42 CACHE
             44 CACHE
             46 LOAD_FAST                1 (func)
             48 LOAD_FAST                2 (args)
             50 BUILD_TUPLE              2
             52 UNPACK_SEQUENCE          1
             56 CALL                     1
             64 CACHE
             66 POP_TOP
             68 LOAD_CONST               0 (None)
             70 RETURN_VALUE

Disassembly of <code object start_section at 0x000001E77E79F110, file "argparse.py", line 242>:
242           0 RESUME                   0

243           2 LOAD_FAST                0 (self)
              4 STORE_SUBSCR
              8 CACHE
             10 CACHE
             12 CACHE
             14 CACHE
             16 CACHE
             18 CACHE
             20 CACHE
             22 CACHE
             24 CACHE
             26 UNPACK_SEQUENCE          0
             30 CALL                     0
             38 CACHE
             40 POP_TOP

244          42 LOAD_FAST                0 (self)
             44 STORE_SUBSCR
             48 CACHE
             50 CACHE
             52 CACHE
             54 CACHE
             56 CACHE
             58 CACHE
             60 CACHE
             62 CACHE
             64 CACHE
             66 LOAD_FAST                0 (self)
             68 LOAD_FAST                0 (self)
             70 LOAD_ATTR                2 (_Section)
             90 CACHE
             92 CACHE
             94 CACHE
             96 STORE_FAST               2 (section)

245          98 LOAD_FAST                0 (self)
            100 STORE_SUBSCR
            104 CACHE
            106 CACHE
            108 CACHE
            110 CACHE
            112 CACHE
            114 CACHE
            116 CACHE
            118 CACHE
            120 CACHE
            122 LOAD_FAST                2 (section)
            124 LOAD_ATTR                4 (_current_section)
            144 CACHE
            146 CACHE
            148 CACHE
            150 POP_TOP

246         152 LOAD_FAST                2 (section)
            154 LOAD_FAST                0 (self)
            156 STORE_ATTR               2 (_current_section)
            166 LOAD_CONST               0 (None)
            168 RETURN_VALUE

Disassembly of <code object end_section at 0x000001E77EC46780, file "argparse.py", line 248>:
248           0 RESUME                   0

249           2 LOAD_FAST                0 (self)
              4 LOAD_ATTR                0 (_current_section)
             24 LOAD_FAST                0 (self)
             26 STORE_ATTR               0 (_current_section)

250          36 LOAD_FAST                0 (self)
             38 STORE_SUBSCR
             42 CACHE
             44 CACHE
             46 CACHE
             48 CACHE
             50 CACHE
             52 CACHE
             54 CACHE
             56 CACHE
             58 CACHE
             60 UNPACK_SEQUENCE          0
             64 CALL                     0
             72 CACHE
             74 POP_TOP
             76 LOAD_CONST               0 (None)
             78 RETURN_VALUE

Disassembly of <code object add_text at 0x000001E77EC57090, file "argparse.py", line 252>:
252           0 RESUME                   0

253           2 LOAD_FAST                1 (text)
              4 LOAD_GLOBAL              0 (SUPPRESS)
             14 CACHE
             16 IS_OP                    1
             18 POP_JUMP_IF_FALSE       32 (to 84)
             20 LOAD_FAST                1 (text)
             22 POP_JUMP_IF_NONE        32 (to 88)

254          24 LOAD_FAST                0 (self)
             26 STORE_SUBSCR
             30 CACHE
             32 CACHE
             34 CACHE
             36 CACHE
             38 CACHE
             40 CACHE
             42 CACHE
             44 CACHE
             46 CACHE
             48 LOAD_FAST                0 (self)
             50 LOAD_ATTR                2 (_add_item)
             70 CACHE
             72 CACHE
             74 CACHE
             76 CACHE
             78 POP_TOP
             80 LOAD_CONST               0 (None)
             82 RETURN_VALUE

253     >>   84 LOAD_CONST               0 (None)
             86 RETURN_VALUE
        >>   88 LOAD_CONST               0 (None)
             90 RETURN_VALUE

Disassembly of <code object add_usage at 0x000001E77EC571B0, file "argparse.py", line 256>:
256           0 RESUME                   0

257           2 LOAD_FAST                1 (usage)
              4 LOAD_GLOBAL              0 (SUPPRESS)
             14 CACHE
             16 IS_OP                    1
             18 POP_JUMP_IF_FALSE       35 (to 90)

258          20 LOAD_FAST                1 (usage)
             22 LOAD_FAST                2 (actions)
             24 LOAD_FAST                3 (groups)
             26 LOAD_FAST                4 (prefix)
             28 BUILD_TUPLE              4
             30 STORE_FAST               5 (args)

259          32 LOAD_FAST                0 (self)
             34 STORE_SUBSCR
             38 CACHE
             40 CACHE
             42 CACHE
             44 CACHE
             46 CACHE
             48 CACHE
             50 CACHE
             52 CACHE
             54 CACHE
             56 LOAD_FAST                0 (self)
             58 LOAD_ATTR                2 (_add_item)
             78 CACHE
             80 CACHE
             82 CACHE
             84 POP_TOP
             86 LOAD_CONST               0 (None)
             88 RETURN_VALUE

257     >>   90 LOAD_CONST               0 (None)
             92 RETURN_VALUE

Disassembly of <code object add_argument at 0x000001E77E857040, file "argparse.py", line 261>:
261           0 RESUME                   0

262           2 LOAD_FAST                1 (action)
              4 LOAD_ATTR                0 (help)
             24 CACHE
             26 IS_OP                    1
             28 POP_JUMP_IF_FALSE      173 (to 376)

265          30 LOAD_FAST                0 (self)
             32 LOAD_ATTR                2 (SUPPRESS)
             52 CACHE
             54 CALL                     1
             62 CACHE
             64 BUILD_LIST               1
             66 STORE_FAST               3 (invocations)

267          68 LOAD_FAST                0 (self)
             70 STORE_SUBSCR
             74 CACHE
             76 CACHE
             78 CACHE
             80 CACHE
             82 CACHE
             84 CACHE
             86 CACHE
             88 CACHE
             90 CACHE
             92 LOAD_FAST                1 (action)
             94 UNPACK_SEQUENCE          1
             98 CALL                     1
            106 CACHE
            108 GET_ITER
        >>  110 FOR_ITER                32 (to 178)

268         114 LOAD_FAST                3 (invocations)
            116 STORE_SUBSCR
            120 CACHE
            122 CACHE
            124 CACHE
            126 CACHE
            128 CACHE
            130 CACHE
            132 CACHE
            134 CACHE
            136 CACHE
            138 PUSH_NULL
            140 LOAD_FAST                2 (get_invocation)
            142 LOAD_FAST                4 (subaction)
            144 UNPACK_SEQUENCE          1
            148 CALL                     1
            156 CACHE
            158 UNPACK_SEQUENCE          1
            162 CALL                     1
            170 CACHE
            172 POP_TOP
            174 JUMP_BACKWARD           33 (to 110)

271         176 LOAD_GLOBAL             11 (NULL + max)
            186 CACHE
            188 LOAD_GLOBAL             13 (NULL + map)
            198 CACHE
            200 LOAD_GLOBAL             14 (len)
            210 CACHE
            212 LOAD_FAST                3 (invocations)
            214 UNPACK_SEQUENCE          2
            218 CALL                     2
            226 CACHE
            228 UNPACK_SEQUENCE          1
            232 CALL                     1
            240 CACHE
            242 STORE_FAST               5 (invocation_length)

272         244 LOAD_FAST                5 (invocation_length)
            246 LOAD_FAST                0 (self)
            248 LOAD_ATTR                8 (append)
            268 CACHE
            270 CACHE
            272 CACHE
            274 CACHE
            276 LOAD_FAST                0 (self)
            278 LOAD_ATTR                9 (NULL|self + append)
            298 CACHE
            300 CACHE
            302 CACHE
            304 LOAD_FAST                0 (self)
            306 STORE_ATTR               9 (_action_max_length)

277         316 LOAD_FAST                0 (self)
            318 STORE_SUBSCR
            322 CACHE
            324 CACHE
            326 CACHE
            328 CACHE
            330 CACHE
            332 CACHE
            334 CACHE
            336 CACHE
            338 CACHE
            340 LOAD_FAST                0 (self)
            342 LOAD_ATTR               11 (NULL|self + max)
            362 CACHE
            364 CACHE
            366 CACHE
            368 CACHE
            370 POP_TOP
            372 LOAD_CONST               0 (None)
            374 RETURN_VALUE

262     >>  376 LOAD_CONST               0 (None)
            378 RETURN_VALUE

Disassembly of <code object add_arguments at 0x000001E77EC79030, file "argparse.py", line 279>:
279           0 RESUME                   0

280           2 LOAD_FAST                1 (actions)
              4 GET_ITER
        >>    6 FOR_ITER                23 (to 56)

281          10 LOAD_FAST                0 (self)
             12 STORE_SUBSCR
             16 CACHE
             18 CACHE
             20 CACHE
             22 CACHE
             24 CACHE
             26 CACHE
             28 CACHE
             30 CACHE
             32 CACHE
             34 LOAD_FAST                2 (action)
             36 UNPACK_SEQUENCE          1
             40 CALL                     1
             48 CACHE
             50 POP_TOP
             52 JUMP_BACKWARD           24 (to 6)

280          54 LOAD_CONST               0 (None)
        >>   56 RETURN_VALUE

Disassembly of <code object format_help at 0x000001E77E79F280, file "argparse.py", line 286>:
286           0 RESUME                   0

287           2 LOAD_FAST                0 (self)
              4 LOAD_ATTR                0 (_root_section)
             24 CACHE
             26 CACHE
             28 CACHE
             30 CACHE
             32 CACHE
             34 CACHE
             36 UNPACK_SEQUENCE          0
             40 CALL                     0
             48 CACHE
             50 STORE_FAST               1 (help)

288          52 LOAD_FAST                1 (help)
             54 POP_JUMP_IF_FALSE       51 (to 158)

289          56 LOAD_FAST                0 (self)
             58 LOAD_ATTR                2 (format_help)
             78 CACHE
             80 CACHE
             82 CACHE
             84 CACHE
             86 CACHE
             88 CACHE
             90 LOAD_CONST               1 ('\n\n')
             92 LOAD_FAST                1 (help)
             94 UNPACK_SEQUENCE          2
             98 CALL                     2
            106 CACHE
            108 STORE_FAST               1 (help)

290         110 LOAD_FAST                1 (help)
            112 STORE_SUBSCR
            116 CACHE
            118 CACHE
            120 CACHE
            122 CACHE
            124 CACHE
            126 CACHE
            128 CACHE
            130 CACHE
            132 CACHE
            134 LOAD_CONST               2 ('\n')
            136 UNPACK_SEQUENCE          1
            140 CALL                     1
            148 CACHE
            150 LOAD_CONST               2 ('\n')
            152 BINARY_OP                0 (+)
            156 STORE_FAST               1 (help)

291     >>  158 LOAD_FAST                1 (help)
            160 RETURN_VALUE

Disassembly of <code object _join_parts at 0x000001E77EC79130, file "argparse.py", line 293>:
293           0 RESUME                   0

294           2 LOAD_CONST               1 ('')
              4 STORE_SUBSCR
              8 CACHE
             10 CACHE
             12 CACHE
             14 CACHE
             16 CACHE
             18 CACHE
             20 CACHE
             22 CACHE
             24 CACHE
             26 LOAD_CONST               2 (<code object <listcomp> at 0x000001E77EC37B40, file "argparse.py", line 294>)
             28 MAKE_FUNCTION            0

295          30 LOAD_FAST                1 (part_strings)

294          32 GET_ITER
             34 UNPACK_SEQUENCE          0
             38 CALL                     0
             46 CACHE
             48 UNPACK_SEQUENCE          1
             52 CALL                     1
             60 CACHE
             62 RETURN_VALUE

Disassembly of <code object <listcomp> at 0x000001E77EC37B40, file "argparse.py", line 294>:
294           0 RESUME                   0
              2 BUILD_LIST               0
              4 LOAD_FAST                0 (.0)
        >>    6 FOR_ITER                15 (to 40)

296          10 LOAD_FAST                1 (part)

294          12 POP_JUMP_IF_FALSE       11 (to 36)

296          14 LOAD_FAST                1 (part)
             16 LOAD_GLOBAL              0 (SUPPRESS)
             26 CACHE
             28 IS_OP                    1
