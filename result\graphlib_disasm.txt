# Code object from position 10265716
# Filename: graphlib.py
# Name: <module>
# Args: 0
# Locals: 0
# Stack size: 5
# Flags: 0

  0           0 RESUME                   0

  1           2 LOAD_CONST               0 (0)
              4 LOAD_CONST               1 (('GenericAlia<PERSON>',))
              6 IMPORT_NAME              0 (types)
              8 IMPORT_FROM              1 (GenericAlias)
             10 STORE_NAME               1 (GenericAlias)
             12 POP_TOP

  3          14 LOAD_CONST               2 ('TopologicalSorter')
             16 LOAD_CONST               3 ('CycleError')
             18 BUILD_LIST               2
             20 STORE_NAME               2 (__all__)

  5          22 LOAD_CONST               4 (-1)
             24 STORE_NAME               3 (_NODE_OUT)

  6          26 LOAD_CONST               5 (-2)
             28 STORE_NAME               4 (_NODE_DONE)

  9          30 PUSH_NULL
             32 LOAD_BUILD_CLASS
             34 LOAD_CONST               6 (<code object _NodeInfo at 0x000001B2A7689530, file "graphlib.py", line 9>)
             36 MAKE_FUNCTION            0
             38 LOAD_CONST               7 ('_NodeInfo')
             40 UNPACK_SEQUENCE          2
             44 CALL                     2
             52 CACHE
             54 STORE_NAME               5 (_NodeInfo)

 26          56 PUSH_NULL
             58 LOAD_BUILD_CLASS
             60 LOAD_CONST               8 (<code object CycleError at 0x000001B2A768A4F0, file "graphlib.py", line 26>)
             62 MAKE_FUNCTION            0
             64 LOAD_CONST               3 ('CycleError')
             66 LOAD_NAME                6 (ValueError)
             68 UNPACK_SEQUENCE          3
             72 CALL                     3
             80 CACHE
             82 STORE_NAME               7 (CycleError)

 41          84 PUSH_NULL
             86 LOAD_BUILD_CLASS
             88 LOAD_CONST               9 (<code object TopologicalSorter at 0x000001B2A76B5590, file "graphlib.py", line 41>)
             90 MAKE_FUNCTION            0
             92 LOAD_CONST               2 ('TopologicalSorter')
             94 UNPACK_SEQUENCE          2
             98 CALL                     2
            106 CACHE
            108 STORE_NAME               8 (TopologicalSorter)
            110 LOAD_CONST              10 (None)
            112 RETURN_VALUE

Disassembly of <code object _NodeInfo at 0x000001B2A7689530, file "graphlib.py", line 9>:
  9           0 RESUME                   0
              2 LOAD_NAME                0 (__name__)
              4 STORE_NAME               1 (__module__)
              6 LOAD_CONST               0 ('_NodeInfo')
              8 STORE_NAME               2 (__qualname__)

 10          10 LOAD_CONST               1 (('node', 'npredecessors', 'successors'))
             12 STORE_NAME               3 (__slots__)

 12          14 LOAD_CONST               2 (<code object __init__ at 0x000001B2A767F870, file "graphlib.py", line 12>)
             16 MAKE_FUNCTION            0
             18 STORE_NAME               4 (__init__)
             20 LOAD_CONST               3 (None)
             22 RETURN_VALUE

Disassembly of <code object __init__ at 0x000001B2A767F870, file "graphlib.py", line 12>:
 12           0 RESUME                   0

 14           2 LOAD_FAST                1 (node)
              4 LOAD_FAST                0 (self)
              6 STORE_ATTR               0 (node)

 19          16 LOAD_CONST               1 (0)
             18 LOAD_FAST                0 (self)
             20 STORE_ATTR               1 (npredecessors)

 23          30 BUILD_LIST               0
             32 LOAD_FAST                0 (self)
             34 STORE_ATTR               2 (successors)
             44 LOAD_CONST               0 (None)
             46 RETURN_VALUE

Disassembly of <code object CycleError at 0x000001B2A768A4F0, file "graphlib.py", line 26>:
 26           0 RESUME                   0
              2 LOAD_NAME                0 (__name__)
              4 STORE_NAME               1 (__module__)
              6 LOAD_CONST               0 ('CycleError')
              8 STORE_NAME               2 (__qualname__)

 27          10 LOAD_CONST               1 ('Subclass of ValueError raised by TopologicalSorter.prepare if cycles\n    exist in the working graph.\n\n    If multiple cycles exist, only one undefined choice among them will be reported\n    and included in the exception. The detected cycle can be accessed via the second\n    element in the *args* attribute of the exception instance and consists in a list\n    of nodes, such that each node is, in the graph, an immediate predecessor of the\n    next node in the list. In the reported list, the first and the last node will be\n    the same, to make it clear that it is cyclic.\n    ')
             12 STORE_NAME               3 (__doc__)

 38          14 LOAD_CONST               2 (None)
             16 RETURN_VALUE

Disassembly of <code object TopologicalSorter at 0x000001B2A76B5590, file "graphlib.py", line 41>:
 41           0 RESUME                   0
              2 LOAD_NAME                0 (__name__)
              4 STORE_NAME               1 (__module__)
              6 LOAD_CONST               0 ('TopologicalSorter')
              8 STORE_NAME               2 (__qualname__)

 42          10 LOAD_CONST               1 ('Provides functionality to topologically sort a graph of hashable nodes')
             12 STORE_NAME               3 (__doc__)

 44          14 LOAD_CONST              13 ((None,))
             16 LOAD_CONST               3 (<code object __init__ at 0x000001B2A71D35D0, file "graphlib.py", line 44>)
             18 MAKE_FUNCTION            1 (defaults)
             20 STORE_NAME               4 (__init__)

 54          22 LOAD_CONST               4 (<code object _get_nodeinfo at 0x000001B2A76B5200, file "graphlib.py", line 54>)
             24 MAKE_FUNCTION            0
             26 STORE_NAME               5 (_get_nodeinfo)

 59          28 LOAD_CONST               5 (<code object add at 0x000001B2A722E870, file "graphlib.py", line 59>)
             30 MAKE_FUNCTION            0
             32 STORE_NAME               6 (add)

 86          34 LOAD_CONST               6 (<code object prepare at 0x000001B2A761F550, file "graphlib.py", line 86>)
             36 MAKE_FUNCTION            0
             38 STORE_NAME               7 (prepare)

108          40 LOAD_CONST               7 (<code object get_ready at 0x000001B2A71AE640, file "graphlib.py", line 108>)
             42 MAKE_FUNCTION            0
             44 STORE_NAME               8 (get_ready)

134          46 LOAD_CONST               8 (<code object is_active at 0x000001B2A8A087B0, file "graphlib.py", line 134>)
             48 MAKE_FUNCTION            0
             50 STORE_NAME               9 (is_active)

148          52 LOAD_CONST               9 (<code object __bool__ at 0x000001B2A767F1E0, file "graphlib.py", line 148>)
             54 MAKE_FUNCTION            0
             56 STORE_NAME              10 (__bool__)

151          58 LOAD_CONST              10 (<code object done at 0x000001B2A75C2180, file "graphlib.py", line 151>)
             60 MAKE_FUNCTION            0
             62 STORE_NAME              11 (done)

198          64 LOAD_CONST              11 (<code object _find_cycle at 0x000001B2A74A2660, file "graphlib.py", line 198>)
             66 MAKE_FUNCTION            0
             68 STORE_NAME              12 (_find_cycle)

235          70 LOAD_CONST              12 (<code object static_order at 0x000001B2A71CE590, file "graphlib.py", line 235>)
             72 MAKE_FUNCTION            0
             74 STORE_NAME              13 (static_order)

250          76 PUSH_NULL
             78 LOAD_NAME               14 (classmethod)
             80 LOAD_NAME               15 (GenericAlias)
             82 UNPACK_SEQUENCE          1
             86 CALL                     1
             94 CACHE
             96 STORE_NAME              16 (__class_getitem__)
             98 LOAD_CONST               2 (None)
            100 RETURN_VALUE

Disassembly of <code object __init__ at 0x000001B2A71D35D0, file "graphlib.py", line 44>:
 44           0 RESUME                   0

 45           2 BUILD_MAP                0
              4 LOAD_FAST                0 (self)
              6 STORE_ATTR               0 (_node2info)

 46          16 LOAD_CONST               0 (None)
             18 LOAD_FAST                0 (self)
             20 STORE_ATTR               1 (_ready_nodes)

 47          30 LOAD_CONST               1 (0)
             32 LOAD_FAST                0 (self)
             34 STORE_ATTR               2 (_npassedout)

 48          44 LOAD_CONST               1 (0)
             46 LOAD_FAST                0 (self)
             48 STORE_ATTR               3 (_nfinished)

 50          58 LOAD_FAST                1 (graph)
             60 POP_JUMP_IF_NONE        40 (to 142)

 51          62 LOAD_FAST                1 (graph)
             64 STORE_SUBSCR
             68 CACHE
             70 CACHE
             72 CACHE
             74 CACHE
             76 CACHE
             78 CACHE
             80 CACHE
             82 CACHE
             84 CACHE
             86 UNPACK_SEQUENCE          0
             90 CALL                     0
             98 CACHE
            100 GET_ITER
        >>  102 FOR_ITER                21 (to 148)
            106 CACHE
            108 STORE_FAST               2 (node)
            110 STORE_FAST               3 (predecessors)

 52         112 PUSH_NULL
            114 LOAD_FAST                0 (self)
            116 LOAD_ATTR                5 (NULL|self + _npassedout)
            136 CALL_FUNCTION_EX         0
            138 POP_TOP
            140 JUMP_BACKWARD           20 (to 102)

 50     >>  142 LOAD_CONST               0 (None)
            144 RETURN_VALUE

 51         146 LOAD_CONST               0 (None)
        >>  148 RETURN_VALUE

Disassembly of <code object _get_nodeinfo at 0x000001B2A76B5200, file "graphlib.py", line 54>:
 54           0 RESUME                   0

 55           2 LOAD_FAST                0 (self)
              4 LOAD_ATTR                0 (_node2info)
             24 CACHE
             26 CACHE
             28 CACHE
             30 CACHE
             32 CACHE
             34 CACHE
             36 LOAD_FAST                1 (node)
             38 UNPACK_SEQUENCE          1
             42 CALL                     1
             50 CACHE
             52 COPY                     1
             54 STORE_FAST               2 (result)
             56 POP_JUMP_IF_NOT_NONE    25 (to 108)

 56          58 LOAD_GLOBAL              5 (NULL + _NodeInfo)
             68 CACHE
             70 LOAD_FAST                1 (node)
             72 UNPACK_SEQUENCE          1
             76 CALL                     1
             84 CACHE
             86 COPY                     1
             88 LOAD_FAST                0 (self)
             90 LOAD_ATTR                0 (_node2info)
            110 RETURN_VALUE

Disassembly of <code object add at 0x000001B2A722E870, file "graphlib.py", line 59>:
 59           0 RESUME                   0

 74           2 LOAD_FAST                0 (self)
              4 LOAD_ATTR                0 (_ready_nodes)
             24 CACHE
             26 CACHE
             28 LOAD_CONST               2 ('Nodes cannot be added after a call to prepare()')
             30 UNPACK_SEQUENCE          1
             34 CALL                     1
             42 CACHE
             44 RAISE_VARARGS            1

 78          46 LOAD_FAST                0 (self)
             48 STORE_SUBSCR
             52 CACHE
             54 CACHE
             56 CACHE
             58 CACHE
             60 CACHE
             62 CACHE
             64 CACHE
             66 CACHE
             68 CACHE
             70 LOAD_FAST                1 (node)
             72 UNPACK_SEQUENCE          1
             76 CALL                     1
             84 CACHE
             86 STORE_FAST               3 (nodeinfo)

 79          88 LOAD_FAST                3 (nodeinfo)
             90 COPY                     1
             92 LOAD_ATTR                3 (NULL|self + ValueError)
            112 CACHE
            114 LOAD_FAST                2 (predecessors)
            116 UNPACK_SEQUENCE          1
            120 CALL                     1
            128 CACHE
            130 BINARY_OP               13 (+=)
            134 SWAP                     2
            136 STORE_ATTR               3 (npredecessors)

 82         146 LOAD_FAST                2 (predecessors)
            148 GET_ITER
        >>  150 FOR_ITER                49 (to 252)

 83         154 LOAD_FAST                0 (self)
            156 STORE_SUBSCR
            160 CACHE
            162 CACHE
            164 CACHE
            166 CACHE
            168 CACHE
            170 CACHE
            172 CACHE
            174 CACHE
            176 CACHE
            178 LOAD_FAST                4 (pred)
            180 UNPACK_SEQUENCE          1
            184 CALL                     1
            192 CACHE
            194 STORE_FAST               5 (pred_info)

 84         196 LOAD_FAST                5 (pred_info)
            198 LOAD_ATTR                5 (NULL|self + _get_nodeinfo)
            218 CACHE
            220 CACHE
            222 CACHE
            224 CACHE
            226 CACHE
            228 CACHE
            230 LOAD_FAST                1 (node)
            232 UNPACK_SEQUENCE          1
            236 CALL                     1
            244 CACHE
            246 POP_TOP
            248 JUMP_BACKWARD           50 (to 150)

 82         250 LOAD_CONST               1 (None)
        >>  252 RETURN_VALUE

Disassembly of <code object prepare at 0x000001B2A761F550, file "graphlib.py", line 86>:
 86           0 RESUME                   0

 94           2 LOAD_FAST                0 (self)
              4 LOAD_ATTR                0 (_ready_nodes)
             24 CACHE
             26 CACHE
             28 LOAD_CONST               2 ('cannot prepare() more than once')
             30 UNPACK_SEQUENCE          1
             34 CALL                     1
             42 CACHE
             44 RAISE_VARARGS            1

 97          46 LOAD_CONST               3 (<code object <listcomp> at 0x000001B2A7671C30, file "graphlib.py", line 97>)
             48 MAKE_FUNCTION            0

 98          50 LOAD_FAST                0 (self)
             52 LOAD_ATTR                2 (ValueError)
             72 CACHE
             74 CACHE
             76 CACHE
             78 CACHE
             80 CACHE
             82 CACHE
             84 UNPACK_SEQUENCE          0
             88 CALL                     0
             96 CACHE

 97          98 GET_ITER
            100 UNPACK_SEQUENCE          0
            104 CALL                     0
            112 CACHE
            114 LOAD_FAST                0 (self)
            116 STORE_ATTR               0 (_ready_nodes)

104         126 LOAD_FAST                0 (self)
            128 STORE_SUBSCR
            132 CACHE
            134 CACHE
            136 CACHE
            138 CACHE
            140 CACHE
            142 CACHE
            144 CACHE
            146 CACHE
            148 CACHE
            150 UNPACK_SEQUENCE          0
            154 CALL                     0
            162 CACHE
            164 STORE_FAST               1 (cycle)

105         166 LOAD_FAST                1 (cycle)
            168 POP_JUMP_IF_FALSE       16 (to 202)

106         170 LOAD_GLOBAL             11 (NULL + CycleError)
            180 CACHE
            182 LOAD_CONST               4 ('nodes are in a cycle')
            184 LOAD_FAST                1 (cycle)
            186 UNPACK_SEQUENCE          2
            190 CALL                     2
            198 CACHE
            200 RAISE_VARARGS            1

105     >>  202 LOAD_CONST               1 (None)
            204 RETURN_VALUE

Disassembly of <code object <listcomp> at 0x000001B2A7671C30, file "graphlib.py", line 97>:
 97           0 RESUME                   0
              2 BUILD_LIST               0
              4 LOAD_FAST                0 (.0)
              6 FOR_ITER                20 (to 50)
             10 LOAD_FAST                1 (i)
             12 LOAD_ATTR                0 (npredecessors)
             32 LOAD_FAST                1 (i)
             34 LOAD_ATTR                1 (NULL|self + npredecessors)

Disassembly of <code object get_ready at 0x000001B2A71AE640, file "graphlib.py", line 108>:
108           0 RESUME                   0

118           2 LOAD_FAST                0 (self)
              4 LOAD_ATTR                0 (_ready_nodes)
             24 CACHE
             26 CACHE
             28 LOAD_CONST               2 ('prepare() must be called first')
             30 UNPACK_SEQUENCE          1
             34 CALL                     1
             42 CACHE
             44 RAISE_VARARGS            1

122          46 LOAD_GLOBAL              5 (NULL + tuple)
             56 CACHE
             58 LOAD_FAST                0 (self)
             60 LOAD_ATTR                0 (_ready_nodes)
             80 CACHE
             82 CACHE
             84 STORE_FAST               1 (result)

123          86 LOAD_FAST                0 (self)
             88 LOAD_ATTR                3 (NULL|self + ValueError)

125         108 LOAD_GLOBAL              8 (_NODE_OUT)
            118 CACHE
            120 LOAD_FAST                2 (n2i)
            122 LOAD_FAST                3 (node)
            124 BINARY_SUBSCR
            128 CACHE
            130 CACHE
            132 CACHE
            134 STORE_ATTR               5 (npredecessors)
            144 JUMP_BACKWARD           21 (to 104)

129         146 LOAD_FAST                0 (self)
            148 LOAD_ATTR                0 (_ready_nodes)
            168 CACHE
            170 CACHE
            172 CACHE
            174 CACHE
            176 CACHE
            178 CACHE
            180 UNPACK_SEQUENCE          0
            184 CALL                     0
            192 CACHE
            194 POP_TOP

130         196 LOAD_FAST                0 (self)
            198 COPY                     1
            200 LOAD_ATTR                7 (NULL|self + _node2info)
            220 CACHE
            222 LOAD_FAST                1 (result)
            224 UNPACK_SEQUENCE          1
            228 CALL                     1
            236 CACHE
            238 BINARY_OP               13 (+=)
            242 SWAP                     2
            244 STORE_ATTR               7 (_npassedout)

132         254 LOAD_FAST                1 (result)
            256 RETURN_VALUE

Disassembly of <code object is_active at 0x000001B2A8A087B0, file "graphlib.py", line 134>:
134           0 RESUME                   0

144           2 LOAD_FAST                0 (self)
              4 LOAD_ATTR                0 (_ready_nodes)
             24 CACHE
             26 CACHE
             28 LOAD_CONST               2 ('prepare() must be called first')
             30 UNPACK_SEQUENCE          1
             34 CALL                     1
             42 CACHE
             44 RAISE_VARARGS            1

146          46 LOAD_FAST                0 (self)
             48 LOAD_ATTR                2 (ValueError)
             68 CACHE
             70 COMPARE_OP               0 (<)
             74 CACHE
