#!/usr/bin/env python3
"""
Clean test script for normal map generation functions
This version avoids module conflicts by being in a separate directory
"""

import cv2
import numpy as np
import os
import sys
import time
from typing import List, Tuple, Optional


def print_elapsed_time(func):
    """Print the elapsed time of the decorated function"""
    def wrapper(*args, **kwargs):
        start_time = time.time()
        print(f"imgproc: Started - {func.__name__}")
        result = func(*args, **kwargs)
        elapsed = time.time() - start_time
        hours = int(elapsed // 3600)
        minutes = int((elapsed % 3600) // 60)
        seconds = elapsed % 60
        print(f"imgproc: Complete - elapsed time: {hours:d}:{minutes:02d}:{seconds:0.2f}")
        return result
    return wrapper


def normalize_intensity(image: np.ndarray) -> np.ndarray:
    """Normalize image intensity to [0, 1] range"""
    if image.dtype == np.uint8:
        normalized = image.astype(np.float32) / 255.0
        print(f"Normalized 8-bit image: min={np.min(normalized):.3f}, max={np.max(normalized):.3f}")
        return normalized
    elif image.dtype == np.uint16:
        normalized = image.astype(np.float32) / 65535.0
        print(f"Normalized 16-bit image: min={np.min(normalized):.3f}, max={np.max(normalized):.3f}")
        return normalized
    else:
        print(f"Image is not 8bit or 16bit (dtype: {image.dtype})")
        return None


def create_test_images():
    """Create test images for normal map generation"""
    print("Creating test images...")
    
    # Create test directory
    test_dir = "test_images"
    os.makedirs(test_dir, exist_ok=True)
    
    # Create a simple height map (16-bit)
    height, width = 256, 256
    
    # Create a height map with some geometric features
    height_map = np.zeros((height, width), dtype=np.uint16)
    
    # Add a pyramid in the center
    center_x, center_y = width // 2, height // 2
    for y in range(height):
        for x in range(width):
            dist_from_center = np.sqrt((x - center_x)**2 + (y - center_y)**2)
            pyramid_height = max(0, 1.0 - dist_from_center / 80.0)
            height_map[y, x] = int(pyramid_height * 65535)
    
    # Add some noise for texture
    noise = np.random.normal(0, 0.02, (height, width))
    height_map = np.clip(height_map.astype(np.float32) + noise * 65535, 0, 65535).astype(np.uint16)
    
    # Save height map
    height_map_path = os.path.join(test_dir, "test_height_map.tif")
    cv2.imwrite(height_map_path, height_map)
    print(f"Created height map: {height_map_path}")
    
    # Create 6 directional lighting images for object normal map test
    directional_paths = []
    
    # Light directions
    light_directions = [
        (-1.0, 0.0, 1.0),   # Left
        (1.0, 0.0, 1.0),    # Right
        (0.0, -1.0, 1.0),   # Top
        (0.0, 1.0, 1.0),    # Bottom
        (0.0, 0.0, 1.0),    # Front
        (0.0, 0.0, -1.0)    # Back
    ]
    
    # Create a simple 3D sphere for testing
    sphere_radius = 80
    for i, light_dir in enumerate(light_directions):
        # Normalize light direction
        light_dir = np.array(light_dir)
        light_dir = light_dir / np.linalg.norm(light_dir)
        
        # Create lit sphere image
        lit_image = np.zeros((height, width), dtype=np.uint8)
        
        for y in range(height):
            for x in range(width):
                # Calculate if point is on sphere
                dx = x - center_x
                dy = y - center_y
                dist_sq = dx*dx + dy*dy
                
                if dist_sq <= sphere_radius*sphere_radius:
                    # Calculate surface normal for sphere
                    dz = np.sqrt(sphere_radius*sphere_radius - dist_sq)
                    surface_normal = np.array([dx, dy, dz])
                    surface_normal = surface_normal / np.linalg.norm(surface_normal)
                    
                    # Calculate lighting (Lambertian)
                    intensity = max(0, np.dot(surface_normal, light_dir))
                    lit_image[y, x] = int(intensity * 255)
        
        # Save directional image
        dir_path = os.path.join(test_dir, f"directional_{i+1}.png")
        cv2.imwrite(dir_path, lit_image)
        directional_paths.append(dir_path)
        print(f"Created directional image {i+1}: {dir_path}")
    
    return height_map_path, directional_paths


@print_elapsed_time
def test_tangent_normal_map(height_map_path: str):
    """Test tangent normal map generation"""
    print("Testing tangent normal map generation...")
    
    # Load height map
    image = cv2.imread(height_map_path, cv2.IMREAD_UNCHANGED)
    if image is None:
        print(f"Failed to load height map: {height_map_path}")
        return False
    
    print(f"Loaded height map: shape={image.shape}, dtype={image.dtype}")
    
    # Convert to grayscale if needed
    if len(image.shape) == 3:
        height_map = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
    else:
        height_map = image.copy()
    
    # Normalize
    normalized = normalize_intensity(height_map)
    if normalized is None:
        return False
    
    # Apply Gaussian blur
    blur_size = 5
    sigma = 1.0
    kernel_size = blur_size if blur_size % 2 == 1 else blur_size + 1
    blurred = cv2.GaussianBlur(normalized, (kernel_size, kernel_size), sigma)
    
    # Compute gradients
    grad_x = cv2.Sobel(blurred, cv2.CV_32F, 1, 0, ksize=3)
    grad_y = cv2.Sobel(blurred, cv2.CV_32F, 0, 1, ksize=3)
    
    print(f"Computed gradients: X range=[{np.min(grad_x):.3f}, {np.max(grad_x):.3f}]")
    print(f"                   Y range=[{np.min(grad_y):.3f}, {np.max(grad_y):.3f}]")
    
    # Create normal map
    height, width = blurred.shape
    normal_map = np.zeros((height, width, 3), dtype=np.float32)
    
    normal_map[:, :, 0] = -grad_x  # X component
    normal_map[:, :, 1] = -grad_y  # Y component
    normal_map[:, :, 2] = 1.0      # Z component
    
    # Normalize vectors
    for y in range(height):
        for x in range(width):
            normal = normal_map[y, x]
            length = np.linalg.norm(normal)
            if length > 0:
                normal_map[y, x] = normal / length
            else:
                normal_map[y, x] = [0.0, 0.0, 1.0]
    
    # Convert to display format
    display_normal = (normal_map + 1.0) * 0.5 * 255.0
    display_normal = display_normal.astype(np.uint8)
    
    # Save result
    output_path = "test_images/tangent_normal_test.png"
    cv2.imwrite(output_path, display_normal)
    print(f"Saved tangent normal map: {output_path}")
    
    return True


@print_elapsed_time
def test_object_normal_map(directional_paths: List[str]):
    """Test object normal map generation"""
    print("Testing object normal map generation...")
    
    # Load images
    images = []
    for i, path in enumerate(directional_paths):
        img = cv2.imread(path, cv2.IMREAD_UNCHANGED)
        if img is None:
            print(f"Failed to load image: {path}")
            return False
        
        # Convert to grayscale
        if len(img.shape) == 3:
            gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
        else:
            gray = img.copy()
        
        # Normalize
        normalized = normalize_intensity(gray)
        if normalized is None:
            return False
        
        images.append(normalized)
        print(f"Loaded directional image {i+1}: {path}")
    
    # Define light directions
    light_directions = np.array([
        [-1.0, 0.0, 1.0],   # Left
        [1.0, 0.0, 1.0],    # Right
        [0.0, -1.0, 1.0],   # Top
        [0.0, 1.0, 1.0],    # Bottom
        [0.0, 0.0, 1.0],    # Front
        [0.0, 0.0, -1.0]    # Back
    ])
    
    # Normalize light directions
    for i in range(len(light_directions)):
        light_directions[i] = light_directions[i] / np.linalg.norm(light_directions[i])
    
    # Create normal map
    height, width = images[0].shape
    normal_map = np.zeros((height, width, 3), dtype=np.float32)
    
    print("Computing normal map using photometric stereo...")
    for y in range(height):
        if y % 50 == 0:
            print(f"Processing row {y}/{height}")
        
        for x in range(width):
            # Get intensities for this pixel
            intensities = np.array([img[y, x] for img in images])
            
            # Skip if any intensity is too low
            if np.any(intensities < 0.01):
                normal_map[y, x] = [0.0, 0.0, 1.0]
                continue
            
            # Solve for normal
            try:
                normal = np.linalg.lstsq(light_directions, intensities, rcond=None)[0]
                norm_length = np.linalg.norm(normal)
                if norm_length > 0:
                    normal = normal / norm_length
                else:
                    normal = np.array([0.0, 0.0, 1.0])
                
                if normal[2] < 0:
                    normal = -normal
                
                normal_map[y, x] = normal
                
            except np.linalg.LinAlgError:
                normal_map[y, x] = [0.0, 0.0, 1.0]
    
    # Convert to BGR format
    display_normal = (normal_map + 1.0) * 0.5 * 255.0
    display_normal = display_normal.astype(np.uint8)
    display_normal = display_normal[:, :, [2, 1, 0]]  # RGB to BGR
    
    # Save result
    output_path = "test_images/object_normal_test.png"
    cv2.imwrite(output_path, display_normal)
    print(f"Saved object normal map: {output_path}")
    
    return True


def main():
    """Main test function"""
    print("Clean Normal Map Test Suite")
    print("=" * 40)
    
    try:
        # Create test images
        height_map_path, directional_paths = create_test_images()
        
        # Test tangent normal map
        print("\n" + "=" * 40)
        if not test_tangent_normal_map(height_map_path):
            print("✗ Tangent normal map test failed")
            return False
        
        # Test object normal map
        print("\n" + "=" * 40)
        if not test_object_normal_map(directional_paths):
            print("✗ Object normal map test failed")
            return False
        
        print("\n" + "=" * 40)
        print("✓ All tests completed successfully!")
        print("Check the 'test_images' directory for output files.")
        
        return True
        
    except Exception as e:
        print(f"\nTest failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
