# Code object from position 7309824
# Filename: _compat_pickle.py
# Name: <module>
# Args: 0
# Locals: 0

  0           0 RESUME                   0

  8           2 BUILD_MAP                0

  9           4 LOAD_CONST               0 ('__builtin__')
              6 LOAD_CONST               1 ('builtins')

  8           8 MAP_ADD                  1

 10          10 LOAD_CONST               2 ('copy_reg')
             12 LOAD_CONST               3 ('copyreg')

  8          14 MAP_ADD                  1

 11          16 LOAD_CONST               4 ('Queue')
             18 LOAD_CONST               5 ('queue')

  8          20 MAP_ADD                  1

 12          22 LOAD_CONST               6 ('SocketServer')
             24 LOAD_CONST               7 ('socketserver')

  8          26 MAP_ADD                  1

 13          28 LOAD_CONST               8 ('ConfigParser')
             30 LOAD_CONST               9 ('configparser')

  8          32 MAP_ADD                  1

 14          34 LOAD_CONST              10 ('repr')
             36 LOAD_CONST              11 ('reprlib')

  8          38 MAP_ADD                  1

 15          40 LOAD_CONST              12 ('tkFileDialog')
             42 LOAD_CONST              13 ('tkinter.filedialog')

  8          44 MAP_ADD                  1

 16          46 LOAD_CONST              14 ('tkSimpleDialog')
             48 LOAD_CONST              15 ('tkinter.simpledialog')

  8          50 MAP_ADD                  1

 17          52 LOAD_CONST              16 ('tkColorChooser')
             54 LOAD_CONST              17 ('tkinter.colorchooser')

  8          56 MAP_ADD                  1

 18          58 LOAD_CONST              18 ('tkCommonDialog')
             60 LOAD_CONST              19 ('tkinter.commondialog')

  8          62 MAP_ADD                  1

 19          64 LOAD_CONST              20 ('Dialog')
             66 LOAD_CONST              21 ('tkinter.dialog')

  8          68 MAP_ADD                  1

 20          70 LOAD_CONST              22 ('Tkdnd')
             72 LOAD_CONST              23 ('tkinter.dnd')

  8          74 MAP_ADD                  1

 21          76 LOAD_CONST              24 ('tkFont')
             78 LOAD_CONST              25 ('tkinter.font')

  8          80 MAP_ADD                  1

 22          82 LOAD_CONST              26 ('tkMessageBox')
             84 LOAD_CONST              27 ('tkinter.messagebox')

  8          86 MAP_ADD                  1

 23          88 LOAD_CONST              28 ('ScrolledText')
             90 LOAD_CONST              29 ('tkinter.scrolledtext')

  8          92 MAP_ADD                  1

 24          94 LOAD_CONST              30 ('Tkconstants')
             96 LOAD_CONST              31 ('tkinter.constants')

  8          98 MAP_ADD                  1

 25         100 LOAD_CONST              32 ('Tix')
            102 LOAD_CONST              33 ('tkinter.tix')

  8         104 MAP_ADD                  1
            106 BUILD_MAP                0

 26         108 LOAD_CONST              34 ('ttk')
            110 LOAD_CONST              35 ('tkinter.ttk')

  8         112 MAP_ADD                  1

 27         114 LOAD_CONST              36 ('Tkinter')
            116 LOAD_CONST              37 ('tkinter')

  8         118 MAP_ADD                  1

 28         120 LOAD_CONST              38 ('markupbase')
            122 LOAD_CONST              39 ('_markupbase')

  8         124 MAP_ADD                  1

 29         126 LOAD_CONST              40 ('_winreg')
            128 LOAD_CONST              41 ('winreg')

  8         130 MAP_ADD                  1

 30         132 LOAD_CONST              42 ('thread')
            134 LOAD_CONST              43 ('_thread')

  8         136 MAP_ADD                  1

 31         138 LOAD_CONST              44 ('dummy_thread')
            140 LOAD_CONST              45 ('_dummy_thread')

  8         142 MAP_ADD                  1

 32         144 LOAD_CONST              46 ('dbhash')
            146 LOAD_CONST              47 ('dbm.bsd')

  8         148 MAP_ADD                  1

 33         150 LOAD_CONST              48 ('dumbdbm')
            152 LOAD_CONST              49 ('dbm.dumb')

  8         154 MAP_ADD                  1

 34         156 LOAD_CONST              50 ('dbm')
            158 LOAD_CONST              51 ('dbm.ndbm')

  8         160 MAP_ADD                  1

 35         162 LOAD_CONST              52 ('gdbm')
            164 LOAD_CONST              53 ('dbm.gnu')

  8         166 MAP_ADD                  1

 36         168 LOAD_CONST              54 ('xmlrpclib')
            170 LOAD_CONST              55 ('xmlrpc.client')

  8         172 MAP_ADD                  1

 37         174 LOAD_CONST              56 ('SimpleXMLRPCServer')
            176 LOAD_CONST              57 ('xmlrpc.server')

  8         178 MAP_ADD                  1

 38         180 LOAD_CONST              58 ('httplib')
            182 LOAD_CONST              59 ('http.client')

  8         184 MAP_ADD                  1

 39         186 LOAD_CONST              60 ('htmlentitydefs')
            188 LOAD_CONST              61 ('html.entities')

  8         190 MAP_ADD                  1

 40         192 LOAD_CONST              62 ('HTMLParser')
            194 LOAD_CONST              63 ('html.parser')

  8         196 MAP_ADD                  1

 41         198 LOAD_CONST              64 ('Cookie')
            200 LOAD_CONST              65 ('http.cookies')

  8         202 MAP_ADD                  1

 42         204 LOAD_CONST              66 ('cookielib')
            206 LOAD_CONST              67 ('http.cookiejar')

  8         208 MAP_ADD                  1
            210 DICT_UPDATE              1

 43         212 LOAD_CONST              68 ('http.server')

 44         214 LOAD_CONST              69 ('test.support')

 45         216 LOAD_CONST              70 ('subprocess')

 46         218 LOAD_CONST              71 ('urllib.parse')

 47         220 LOAD_CONST              72 ('urllib.robotparser')

 48         222 LOAD_CONST              73 ('urllib.request')

 49         224 LOAD_CONST              50 ('dbm')

 50         226 LOAD_CONST              74 ('collections.abc')

  8         228 LOAD_CONST              75 (('BaseHTTPServer', 'test.test_support', 'commands', 'urlparse', 'robotparser', 'urllib2', 'anydbm', '_abcoll'))
            230 BUILD_CONST_KEY_MAP      8
            232 DICT_UPDATE              1
            234 STORE_NAME               0 (IMPORT_MAPPING)

 57         236 BUILD_MAP                0

 58         238 LOAD_CONST              76 (('__builtin__', 'xrange'))
            240 LOAD_CONST              77 (('builtins', 'range'))

 57         242 MAP_ADD                  1

 59         244 LOAD_CONST              78 (('__builtin__', 'reduce'))
            246 LOAD_CONST              79 (('functools', 'reduce'))

 57         248 MAP_ADD                  1

 60         250 LOAD_CONST              80 (('__builtin__', 'intern'))
            252 LOAD_CONST              81 (('sys', 'intern'))

 57         254 MAP_ADD                  1

 61         256 LOAD_CONST              82 (('__builtin__', 'unichr'))
            258 LOAD_CONST              83 (('builtins', 'chr'))

 57         260 MAP_ADD                  1

 62         262 LOAD_CONST              84 (('__builtin__', 'unicode'))
            264 LOAD_CONST              85 (('builtins', 'str'))

 57         266 MAP_ADD                  1

 63         268 LOAD_CONST              86 (('__builtin__', 'long'))
            270 LOAD_CONST              87 (('builtins', 'int'))

 57         272 MAP_ADD                  1

 64         274 LOAD_CONST              88 (('itertools', 'izip'))
            276 LOAD_CONST              89 (('builtins', 'zip'))

 57         278 MAP_ADD                  1

 65         280 LOAD_CONST              90 (('itertools', 'imap'))
            282 LOAD_CONST              91 (('builtins', 'map'))

 57         284 MAP_ADD                  1

 66         286 LOAD_CONST              92 (('itertools', 'ifilter'))
            288 LOAD_CONST              93 (('builtins', 'filter'))

 57         290 MAP_ADD                  1

 67         292 LOAD_CONST              94 (('itertools', 'ifilterfalse'))
            294 LOAD_CONST              95 (('itertools', 'filterfalse'))

 57         296 MAP_ADD                  1

 68         298 LOAD_CONST              96 (('itertools', 'izip_longest'))
            300 LOAD_CONST              97 (('itertools', 'zip_longest'))

 57         302 MAP_ADD                  1

 69         304 LOAD_CONST              98 (('UserDict', 'IterableUserDict'))
            306 LOAD_CONST              99 (('collections', 'UserDict'))

 57         308 MAP_ADD                  1

 70         310 LOAD_CONST             100 (('UserList', 'UserList'))
            312 LOAD_CONST             101 (('collections', 'UserList'))

 57         314 MAP_ADD                  1

 71         316 LOAD_CONST             102 (('UserString', 'UserString'))
            318 LOAD_CONST             103 (('collections', 'UserString'))

 57         320 MAP_ADD                  1

 72         322 LOAD_CONST             104 (('whichdb', 'whichdb'))
            324 LOAD_CONST             105 (('dbm', 'whichdb'))

 57         326 MAP_ADD                  1

 73         328 LOAD_CONST             106 (('_socket', 'fromfd'))
            330 LOAD_CONST             107 (('socket', 'fromfd'))

 57         332 MAP_ADD                  1

 74         334 LOAD_CONST             108 (('_multiprocessing', 'Connection'))
            336 LOAD_CONST             109 (('multiprocessing.connection', 'Connection'))

 57         338 MAP_ADD                  1
            340 BUILD_MAP                0

 75         342 LOAD_CONST             110 (('multiprocessing.process', 'Process'))
            344 LOAD_CONST             111 (('multiprocessing.context', 'Process'))

 57         346 MAP_ADD                  1

 76         348 LOAD_CONST             112 (('multiprocessing.forking', 'Popen'))
            350 LOAD_CONST             113 (('multiprocessing.popen_fork', 'Popen'))

 57         352 MAP_ADD                  1

 77         354 LOAD_CONST             114 (('urllib', 'ContentTooShortError'))
            356 LOAD_CONST             115 (('urllib.error', 'ContentTooShortError'))

 57         358 MAP_ADD                  1

 78         360 LOAD_CONST             116 (('urllib', 'getproxies'))
            362 LOAD_CONST             117 (('urllib.request', 'getproxies'))

 57         364 MAP_ADD                  1

 79         366 LOAD_CONST             118 (('urllib', 'pathname2url'))
            368 LOAD_CONST             119 (('urllib.request', 'pathname2url'))

 57         370 MAP_ADD                  1

 80         372 LOAD_CONST             120 (('urllib', 'quote_plus'))
            374 LOAD_CONST             121 (('urllib.parse', 'quote_plus'))

 57         376 MAP_ADD                  1

 81         378 LOAD_CONST             122 (('urllib', 'quote'))
            380 LOAD_CONST             123 (('urllib.parse', 'quote'))

 57         382 MAP_ADD                  1

 82         384 LOAD_CONST             124 (('urllib', 'unquote_plus'))
            386 LOAD_CONST             125 (('urllib.parse', 'unquote_plus'))

 57         388 MAP_ADD                  1

 83         390 LOAD_CONST             126 (('urllib', 'unquote'))
            392 LOAD_CONST             127 (('urllib.parse', 'unquote'))

 57         394 MAP_ADD                  1

 84         396 LOAD_CONST             128 (('urllib', 'url2pathname'))
            398 LOAD_CONST             129 (('urllib.request', 'url2pathname'))

 57         400 MAP_ADD                  1

 85         402 LOAD_CONST             130 (('urllib', 'urlcleanup'))
            404 LOAD_CONST             131 (('urllib.request', 'urlcleanup'))

 57         406 MAP_ADD                  1

 86         408 LOAD_CONST             132 (('urllib', 'urlencode'))
            410 LOAD_CONST             133 (('urllib.parse', 'urlencode'))

 57         412 MAP_ADD                  1

 87         414 LOAD_CONST             134 (('urllib', 'urlopen'))
            416 LOAD_CONST             135 (('urllib.request', 'urlopen'))

 57         418 MAP_ADD                  1

 88         420 LOAD_CONST             136 (('urllib', 'urlretrieve'))
            422 LOAD_CONST             137 (('urllib.request', 'urlretrieve'))

 57         424 MAP_ADD                  1

 89         426 LOAD_CONST             138 (('urllib2', 'HTTPError'))
            428 LOAD_CONST             139 (('urllib.error', 'HTTPError'))

 57         430 MAP_ADD                  1

 90         432 LOAD_CONST             140 (('urllib2', 'URLError'))
            434 LOAD_CONST             141 (('urllib.error', 'URLError'))

 57         436 MAP_ADD                  1
            438 DICT_UPDATE              1
            440 STORE_NAME               1 (NAME_MAPPING)

 93         442 LOAD_CONST             142 (('ArithmeticError', 'AssertionError', 'AttributeError', 'BaseException', 'BufferError', 'BytesWarning', 'DeprecationWarning', 'EOFError', 'EnvironmentError', 'Exception', 'FloatingPointError', 'FutureWarning', 'GeneratorExit', 'IOError', 'ImportError', 'ImportWarning', 'IndentationError', 'IndexError', 'KeyError', 'KeyboardInterrupt', 'LookupError', 'MemoryError', 'NameError', 'NotImplementedError', 'OSError', 'OverflowError', 'PendingDeprecationWarning', 'ReferenceError', 'RuntimeError', 'RuntimeWarning', 'StopIteration', 'SyntaxError', 'SyntaxWarning', 'SystemError', 'SystemExit', 'TabError', 'TypeError', 'UnboundLocalError', 'UnicodeDecodeError', 'UnicodeEncodeError', 'UnicodeError', 'UnicodeTranslateError', 'UnicodeWarning', 'UserWarning', 'ValueError', 'Warning', 'ZeroDivisionError'))
            444 STORE_NAME               2 (PYTHON2_EXCEPTIONS)

144         446 NOP

145         448 LOAD_NAME                3 (WindowsError)
            450 POP_TOP

149         452 LOAD_NAME                2 (PYTHON2_EXCEPTIONS)
            454 LOAD_CONST             143 (('WindowsError',))
            456 BINARY_OP               13 (+=)
            460 STORE_NAME               2 (PYTHON2_EXCEPTIONS)
            462 JUMP_FORWARD            11 (to 486)
        >>  464 PUSH_EXC_INFO

146         466 LOAD_NAME                4 (NameError)
            468 CHECK_EXC_MATCH
            470 POP_JUMP_IF_FALSE        3 (to 478)
            472 POP_TOP

147         474 POP_EXCEPT
            476 JUMP_FORWARD             4 (to 486)

146     >>  478 RERAISE                  0
        >>  480 COPY                     3
            482 POP_EXCEPT
            484 RERAISE                  1

151     >>  486 LOAD_NAME                2 (PYTHON2_EXCEPTIONS)
            488 GET_ITER
        >>  490 FOR_ITER                11 (to 516)

152         494 LOAD_CONST               1 ('builtins')
            496 LOAD_NAME                5 (excname)
            498 BUILD_TUPLE              2
            500 LOAD_NAME                1 (NAME_MAPPING)
            502 LOAD_CONST             144 ('exceptions')
            504 LOAD_NAME                5 (excname)
            506 BUILD_TUPLE              2
            508 STORE_SUBSCR
            512 JUMP_BACKWARD           12 (to 490)

154         514 LOAD_CONST             145 (('AuthenticationError', 'BufferTooShort', 'ProcessError', 'TimeoutError'))
        >>  516 STORE_NAME               6 (MULTIPROCESSING_EXCEPTIONS)

161         518 LOAD_NAME                6 (MULTIPROCESSING_EXCEPTIONS)
            520 GET_ITER
        >>  522 FOR_ITER                11 (to 548)

162         526 LOAD_CONST             146 ('multiprocessing.context')
            528 LOAD_NAME                5 (excname)
            530 BUILD_TUPLE              2
            532 LOAD_NAME                1 (NAME_MAPPING)
            534 LOAD_CONST             147 ('multiprocessing')
            536 LOAD_NAME                5 (excname)
            538 BUILD_TUPLE              2
            540 STORE_SUBSCR
            544 JUMP_BACKWARD           12 (to 522)

165         546 PUSH_NULL
        >>  548 LOAD_NAME                7 (dict)
            550 LOAD_CONST             148 (<code object <genexpr> at 0x000001A2D0115E30, file "_compat_pickle.py", line 165>)
            552 MAKE_FUNCTION            0
            554 LOAD_NAME                0 (IMPORT_MAPPING)
            556 STORE_SUBSCR
            560 CACHE
            562 CACHE
            564 CACHE
            566 CACHE
            568 CACHE
            570 CACHE
            572 CACHE
            574 CACHE
            576 CACHE
            578 UNPACK_SEQUENCE          0
            582 CALL                     0
            590 CACHE
            592 GET_ITER
            594 UNPACK_SEQUENCE          0
            598 CALL                     0
            606 CACHE
            608 UNPACK_SEQUENCE          1
            612 CALL                     1
            620 CACHE
            622 STORE_NAME               9 (REVERSE_IMPORT_MAPPING)

166         624 PUSH_NULL
            626 LOAD_NAME               10 (len)
            628 LOAD_NAME                9 (REVERSE_IMPORT_MAPPING)
            630 UNPACK_SEQUENCE          1
            634 CALL                     1
            642 CACHE
            644 PUSH_NULL
            646 LOAD_NAME               10 (len)
            648 LOAD_NAME                0 (IMPORT_MAPPING)
            650 UNPACK_SEQUENCE          1
            654 CALL                     1
            662 CACHE
            664 COMPARE_OP               2 (<)
            668 CACHE
            670 POP_JUMP_IF_TRUE         2 (to 676)
            672 LOAD_ASSERTION_ERROR
            674 RAISE_VARARGS            1

167     >>  676 PUSH_NULL
            678 LOAD_NAME                7 (dict)
            680 LOAD_CONST             149 (<code object <genexpr> at 0x000001A2D0115C50, file "_compat_pickle.py", line 167>)
            682 MAKE_FUNCTION            0
            684 LOAD_NAME                1 (NAME_MAPPING)
            686 STORE_SUBSCR
            690 CACHE
            692 CACHE
            694 CACHE
            696 CACHE
            698 CACHE
            700 CACHE
            702 CACHE
            704 CACHE
            706 CACHE
            708 UNPACK_SEQUENCE          0
            712 CALL                     0
            720 CACHE
            722 GET_ITER
            724 UNPACK_SEQUENCE          0
            728 CALL                     0
            736 CACHE
            738 UNPACK_SEQUENCE          1
            742 CALL                     1
            750 CACHE
            752 STORE_NAME              11 (REVERSE_NAME_MAPPING)

168         754 PUSH_NULL
            756 LOAD_NAME               10 (len)
            758 LOAD_NAME               11 (REVERSE_NAME_MAPPING)
            760 UNPACK_SEQUENCE          1
            764 CALL                     1
            772 CACHE
            774 PUSH_NULL
            776 LOAD_NAME               10 (len)
            778 LOAD_NAME                1 (NAME_MAPPING)
            780 UNPACK_SEQUENCE          1
            784 CALL                     1
            792 CACHE
            794 COMPARE_OP               2 (<)
            798 CACHE
            800 POP_JUMP_IF_TRUE         2 (to 806)
            802 LOAD_ASSERTION_ERROR
            804 RAISE_VARARGS            1

172     >>  806 LOAD_NAME                0 (IMPORT_MAPPING)
            808 STORE_SUBSCR
            812 CACHE
            814 CACHE
            816 CACHE
            818 CACHE
            820 CACHE
            822 CACHE
            824 CACHE
            826 CACHE
            828 CACHE

173         830 LOAD_CONST             150 ('pickle')

174         832 LOAD_CONST             151 ('xml.etree.ElementTree')

175         834 LOAD_CONST              13 ('tkinter.filedialog')

176         836 LOAD_CONST              15 ('tkinter.simpledialog')

177         838 LOAD_CONST              57 ('xmlrpc.server')

178         840 LOAD_CONST              68 ('http.server')

179         842 LOAD_CONST              68 ('http.server')

181         844 LOAD_CONST             152 ('collections')

182         846 LOAD_CONST             152 ('collections')

183         848 LOAD_CONST             152 ('collections')

184         850 LOAD_CONST              50 ('dbm')

185         852 LOAD_CONST             153 ('io')

186         854 LOAD_CONST             153 ('io')

172         856 LOAD_CONST             154 (('cPickle', '_elementtree', 'FileDialog', 'SimpleDialog', 'DocXMLRPCServer', 'SimpleHTTPServer', 'CGIHTTPServer', 'UserDict', 'UserList', 'UserString', 'whichdb', 'StringIO', 'cStringIO'))
            858 BUILD_CONST_KEY_MAP     13
            860 UNPACK_SEQUENCE          1
            864 CALL                     1
            872 CACHE
            874 POP_TOP

189         876 LOAD_NAME                9 (REVERSE_IMPORT_MAPPING)
            878 STORE_SUBSCR
            882 CACHE
            884 CACHE
            886 CACHE
            888 CACHE
            890 CACHE
            892 CACHE
            894 CACHE
            896 CACHE
            898 CACHE

190         900 LOAD_CONST             155 ('bz2')

191         902 LOAD_CONST              50 ('dbm')

192         904 LOAD_CONST             156 ('functools')

193         906 LOAD_CONST              52 ('gdbm')

194         908 LOAD_CONST             150 ('pickle')

189         910 LOAD_CONST             157 (('_bz2', '_dbm', '_functools', '_gdbm', '_pickle'))
            912 BUILD_CONST_KEY_MAP      5
            914 UNPACK_SEQUENCE          1
            918 CALL                     1
            926 CACHE
            928 POP_TOP

197         930 LOAD_NAME                1 (NAME_MAPPING)
            932 STORE_SUBSCR
            936 CACHE
            938 CACHE
            940 CACHE
            942 CACHE
            944 CACHE
            946 CACHE
            948 CACHE
            950 CACHE
            952 CACHE

198         954 LOAD_CONST              85 (('builtins', 'str'))

199         956 LOAD_CONST             158 (('builtins', 'Exception'))

200         958 LOAD_CONST              99 (('collections', 'UserDict'))

201         960 LOAD_CONST             159 (('socket', 'SocketType'))

197         962 LOAD_CONST             160 ((('__builtin__', 'basestring'), ('exceptions', 'StandardError'), ('UserDict', 'UserDict'), ('socket', '_socketobject')))
            964 BUILD_CONST_KEY_MAP      4
            966 UNPACK_SEQUENCE          1
            970 CALL                     1
            978 CACHE
            980 POP_TOP

204         982 LOAD_NAME               11 (REVERSE_NAME_MAPPING)
            984 STORE_SUBSCR
            988 CACHE
            990 CACHE
            992 CACHE
            994 CACHE
            996 CACHE
            998 CACHE
           1000 CACHE
           1002 CACHE
           1004 CACHE

205        1006 LOAD_CONST              78 (('__builtin__', 'reduce'))

206        1008 LOAD_CONST             161 (('FileDialog', 'FileDialog'))

207        1010 LOAD_CONST             162 (('FileDialog', 'LoadFileDialog'))

208        1012 LOAD_CONST             163 (('FileDialog', 'SaveFileDialog'))

209        1014 LOAD_CONST             164 (('SimpleDialog', 'SimpleDialog'))

210        1016 LOAD_CONST             165 (('DocXMLRPCServer', 'ServerHTMLDoc'))

212        1018 LOAD_CONST             166 (('DocXMLRPCServer', 'XMLRPCDocGenerator'))

214        1020 LOAD_CONST             167 (('DocXMLRPCServer', 'DocXMLRPCRequestHandler'))

216        1022 LOAD_CONST             168 (('DocXMLRPCServer', 'DocXMLRPCServer'))

218        1024 LOAD_CONST             169 (('DocXMLRPCServer', 'DocCGIXMLRPCRequestHandler'))

220        1026 LOAD_CONST             170 (('SimpleHTTPServer', 'SimpleHTTPRequestHandler'))

222        1028 LOAD_CONST             171 (('CGIHTTPServer', 'CGIHTTPRequestHandler'))

223        1030 LOAD_CONST             172 (('socket', '_socketobject'))

204        1032 LOAD_CONST             173 ((('_functools', 'reduce'), ('tkinter.filedialog', 'FileDialog'), ('tkinter.filedialog', 'LoadFileDialog'), ('tkinter.filedialog', 'SaveFileDialog'), ('tkinter.simpledialog', 'SimpleDialog'), ('xmlrpc.server', 'ServerHTMLDoc'), ('xmlrpc.server', 'XMLRPCDocGenerator'), ('xmlrpc.server', 'DocXMLRPCRequestHandler'), ('xmlrpc.server', 'DocXMLRPCServer'), ('xmlrpc.server', 'DocCGIXMLRPCRequestHandler'), ('http.server', 'SimpleHTTPRequestHandler'), ('http.server', 'CGIHTTPRequestHandler'), ('_socket', 'socket')))
           1034 BUILD_CONST_KEY_MAP     13
           1036 UNPACK_SEQUENCE          1
           1040 CALL                     1
           1048 CACHE
           1050 POP_TOP

226        1052 LOAD_CONST             174 (('BrokenPipeError', 'ChildProcessError', 'ConnectionAbortedError', 'ConnectionError', 'ConnectionRefusedError', 'ConnectionResetError', 'FileExistsError', 'FileNotFoundError', 'InterruptedError', 'IsADirectoryError', 'NotADirectoryError', 'PermissionError', 'ProcessLookupError', 'TimeoutError'))
           1054 STORE_NAME              13 (PYTHON3_OSERROR_EXCEPTIONS)

243        1056 LOAD_NAME               13 (PYTHON3_OSERROR_EXCEPTIONS)
           1058 GET_ITER
        >> 1060 FOR_ITER                 9 (to 1082)

244        1064 LOAD_CONST             175 (('exceptions', 'OSError'))
           1066 LOAD_NAME               11 (REVERSE_NAME_MAPPING)
           1068 LOAD_CONST               1 ('builtins')
           1070 LOAD_NAME                5 (excname)
           1072 BUILD_TUPLE              2
           1074 STORE_SUBSCR
           1078 JUMP_BACKWARD           10 (to 1060)

246        1080 LOAD_CONST             176 (('ModuleNotFoundError',))
        >> 1082 STORE_NAME              14 (PYTHON3_IMPORTERROR_EXCEPTIONS)

250        1084 LOAD_NAME               14 (PYTHON3_IMPORTERROR_EXCEPTIONS)
           1086 GET_ITER
        >> 1088 FOR_ITER                 9 (to 1110)

251        1092 LOAD_CONST             177 (('exceptions', 'ImportError'))
           1094 LOAD_NAME               11 (REVERSE_NAME_MAPPING)
           1096 LOAD_CONST               1 ('builtins')
           1098 LOAD_NAME                5 (excname)
           1100 BUILD_TUPLE              2
           1102 STORE_SUBSCR
           1106 JUMP_BACKWARD           10 (to 1088)

252        1108 DELETE_NAME              5 (excname)
        >> 1110 LOAD_CONST             178 (None)
           1112 RETURN_VALUE
ExceptionTable:
  448 to 450 -> 464 [0]
  464 to 472 -> 480 [1] lasti
  478 to 478 -> 480 [1] lasti

Disassembly of <code object <genexpr> at 0x000001A2D0115E30, file "_compat_pickle.py", line 165>:
165           0 RETURN_GENERATOR
              2 POP_TOP
              4 RESUME                   0
              6 LOAD_FAST                0 (.0)
        >>    8 FOR_ITER                11 (to 34)
             12 CACHE
             14 STORE_FAST               1 (k)
             16 STORE_FAST               2 (v)
             18 LOAD_FAST                2 (v)
             20 LOAD_FAST                1 (k)
             22 BUILD_TUPLE              2
             24 LOAD_FAST                0 (.0)
             26 RESUME                   1
             28 POP_TOP
             30 JUMP_BACKWARD           12 (to 8)
             32 LOAD_CONST               0 (None)
        >>   34 RETURN_VALUE

Disassembly of <code object <genexpr> at 0x000001A2D0115C50, file "_compat_pickle.py", line 167>:
167           0 RETURN_GENERATOR
              2 POP_TOP
              4 RESUME                   0
              6 LOAD_FAST                0 (.0)
        >>    8 FOR_ITER                11 (to 34)
             12 CACHE
             14 STORE_FAST               1 (k)
             16 STORE_FAST               2 (v)
             18 LOAD_FAST                2 (v)
             20 LOAD_FAST                1 (k)
             22 BUILD_TUPLE              2
             24 LOAD_FAST                0 (.0)
             26 RESUME                   1
             28 POP_TOP
             30 JUMP_BACKWARD           12 (to 8)
             32 LOAD_CONST               0 (None)
        >>   34 RETURN_VALUE


# Constants:
# 0: '__builtin__'
# 1: 'builtins'
# 2: 'copy_reg'
# 3: 'copyreg'
# 4: 'Queue'
# 5: 'queue'
# 6: 'SocketServer'
# 7: 'socketserver'
# 8: 'ConfigParser'
# 9: 'configparser'
# 10: 'repr'
# 11: 'reprlib'
# 12: 'tkFileDialog'
# 13: 'tkinter.filedialog'
# 14: 'tkSimpleDialog'
# 15: 'tkinter.simpledialog'
# 16: 'tkColorChooser'
# 17: 'tkinter.colorchooser'
# 18: 'tkCommonDialog'
# 19: 'tkinter.commondialog'
# 20: 'Dialog'
# 21: 'tkinter.dialog'
# 22: 'Tkdnd'
# 23: 'tkinter.dnd'
# 24: 'tkFont'
# 25: 'tkinter.font'
# 26: 'tkMessageBox'
# 27: 'tkinter.messagebox'
# 28: 'ScrolledText'
# 29: 'tkinter.scrolledtext'
# 30: 'Tkconstants'
# 31: 'tkinter.constants'
# 32: 'Tix'
# 33: 'tkinter.tix'
# 34: 'ttk'
# 35: 'tkinter.ttk'
# 36: 'Tkinter'
# 37: 'tkinter'
# 38: 'markupbase'
# 39: '_markupbase'
# 40: '_winreg'
# 41: 'winreg'
# 42: 'thread'
# 43: '_thread'
# 44: 'dummy_thread'
# 45: '_dummy_thread'
# 46: 'dbhash'
# 47: 'dbm.bsd'
# 48: 'dumbdbm'
# 49: 'dbm.dumb'
# 50: 'dbm'
# 51: 'dbm.ndbm'
# 52: 'gdbm'
# 53: 'dbm.gnu'
# 54: 'xmlrpclib'
# 55: 'xmlrpc.client'
# 56: 'SimpleXMLRPCServer'
# 57: 'xmlrpc.server'
# 58: 'httplib'
# 59: 'http.client'
# 60: 'htmlentitydefs'
# 61: 'html.entities'
# 62: 'HTMLParser'
# 63: 'html.parser'
# 64: 'Cookie'
# 65: 'http.cookies'
# 66: 'cookielib'
# 67: 'http.cookiejar'
# 68: 'http.server'
# 69: 'test.support'
# 70: 'subprocess'
# 71: 'urllib.parse'
# 72: 'urllib.robotparser'
# 73: 'urllib.request'
# 74: 'collections.abc'
# 75: tuple
# 76: tuple
# 77: tuple
# 78: tuple
# 79: tuple
# 80: tuple
# 81: tuple
# 82: tuple
# 83: tuple
# 84: tuple
# 85: tuple
# 86: tuple
# 87: tuple
# 88: tuple
# 89: tuple
# 90: tuple
# 91: tuple
# 92: tuple
# 93: tuple
# 94: tuple
# 95: tuple
# 96: tuple
# 97: tuple
# 98: tuple
# 99: tuple
# 100: tuple
# 101: tuple
# 102: tuple
# 103: tuple
# 104: tuple
# 105: tuple
# 106: tuple
# 107: tuple
# 108: tuple
# 109: tuple
# 110: tuple
# 111: tuple
# 112: tuple
# 113: tuple
# 114: tuple
# 115: tuple
# 116: tuple
# 117: tuple
# 118: tuple
# 119: tuple
# 120: tuple
# 121: tuple
# 122: tuple
# 123: tuple
# 124: tuple
# 125: tuple
# 126: tuple
# 127: tuple
# 128: tuple
# 129: tuple
# 130: tuple
# 131: tuple
# 132: tuple
# 133: tuple
# 134: tuple
# 135: tuple
# 136: tuple
# 137: tuple
# 138: tuple
# 139: tuple
# 140: tuple
# 141: tuple
# 142: tuple
# 143: tuple
# 144: 'exceptions'
# 145: tuple
# 146: 'multiprocessing.context'
# 147: 'multiprocessing'
# 148: code
# 149: code
# 150: 'pickle'
# 151: 'xml.etree.ElementTree'
# 152: 'collections'
# 153: 'io'
# 154: tuple
# 155: 'bz2'
# 156: 'functools'
# 157: tuple
# 158: tuple
# 159: tuple
# 160: tuple
# 161: tuple
# 162: tuple
# 163: tuple
# 164: tuple
# 165: tuple
# 166: tuple
# 167: tuple
# 168: tuple
# 169: tuple
# 170: tuple
# 171: tuple
# 172: tuple
# 173: tuple
# 174: tuple
# 175: tuple
# 176: tuple
# 177: tuple
# 178: None


# Names:
# 0: 'IMPORT_MAPPING'
# 1: 'NAME_MAPPING'
# 2: 'PYTHON2_EXCEPTIONS'
# 3: 'WindowsError'
# 4: 'NameError'
# 5: 'excname'
# 6: 'MULTIPROCESSING_EXCEPTIONS'
# 7: 'dict'
# 8: 'items'
# 9: 'REVERSE_IMPORT_MAPPING'
# 10: 'len'
# 11: 'REVERSE_NAME_MAPPING'
# 12: 'update'
# 13: 'PYTHON3_OSERROR_EXCEPTIONS'
# 14: 'PYTHON3_IMPORTERROR_EXCEPTIONS'


# Variable names:
