# MAIN APPLICATION CODE OBJECT
# Position: 8175157
# Filename: chunk.py
# Function: <module>
# Args: 0
# Locals: 0
# Stack size: 4
# Flags: 0

# BYTECODE DISASSEMBLY:
==================================================
  0           0 RESUME                   0

  1           2 LOAD_CONST               0 ('Simple class to read IFF chunks.\n\nAn IFF chunk (used in formats such as AIFF, TIFF, RMFF (RealMedia File\nFormat)) has the following structure:\n\n+----------------+\n| ID (4 bytes)   |\n+----------------+\n| size (4 bytes) |\n+----------------+\n| data           |\n| ...            |\n+----------------+\n\nThe ID is a 4-byte string which identifies the type of chunk.\n\nThe size field (a 32-bit value, encoded using big-endian byte order)\ngives the size of the whole chunk, including the 8-byte header.\n\nUsually an IFF-type file consists of one or more chunks.  The proposed\nusage of the Chunk class defined here is to instantiate an instance at\nthe start of each chunk and read from the instance until it reaches\nthe end, after which a new instance can be instantiated.  At the end\nof the file, creating a new instance will fail with an EOFError\nexception.\n\nUsage:\nwhile True:\n    try:\n        chunk = Chunk(file)\n    except EOFError:\n        break\n    chunktype = chunk.getname()\n    while True:\n        data = chunk.read(nbytes)\n        if not data:\n            pass\n        # do something with data\n\nThe interface is file-like.  The implemented methods are:\nread, close, seek, tell, isatty.\nExtra methods are: skip() (called by close, skips to the end of the chunk),\ngetname() (returns the name (ID) of the chunk)\n\nThe __init__ method has one required argument, a file-like object\n(including a chunk instance), and one optional argument, a flag which\nspecifies whether or not chunks are aligned on 2-byte boundaries.  The\ndefault is 1, i.e. aligned.\n')
              4 STORE_NAME               0 (__doc__)

 51           6 LOAD_CONST               1 (0)
              8 LOAD_CONST               2 (None)
             10 IMPORT_NAME              1 (warnings)
             12 STORE_NAME               1 (warnings)

 53          14 PUSH_NULL
             16 LOAD_NAME                1 (warnings)
             18 LOAD_ATTR                2 (warnings)
             38 CALL                     2
             46 CACHE
             48 POP_TOP

 55          50 PUSH_NULL
             52 LOAD_BUILD_CLASS
             54 LOAD_CONST               5 (<code object Chunk at 0x000001E77ECF2450, file "chunk.py", line 55>)
             56 MAKE_FUNCTION            0
             58 LOAD_CONST               6 ('Chunk')
             60 UNPACK_SEQUENCE          2
             64 CALL                     2
             72 CACHE
             74 STORE_NAME               4 (Chunk)
             76 LOAD_CONST               2 (None)
             78 RETURN_VALUE

Disassembly of <code object Chunk at 0x000001E77ECF2450, file "chunk.py", line 55>:
 55           0 RESUME                   0
              2 LOAD_NAME                0 (__name__)
              4 STORE_NAME               1 (__module__)
              6 LOAD_CONST               0 ('Chunk')
              8 STORE_NAME               2 (__qualname__)

 56          10 LOAD_CONST              15 ((True, True, False))
             12 LOAD_CONST               3 (<code object __init__ at 0x000001E77E9E65E0, file "chunk.py", line 56>)
             14 MAKE_FUNCTION            1 (defaults)
             16 STORE_NAME               3 (__init__)

 82          18 LOAD_CONST               4 (<code object getname at 0x000001E77ED105E0, file "chunk.py", line 82>)
             20 MAKE_FUNCTION            0
             22 STORE_NAME               4 (getname)

 86          24 LOAD_CONST               5 (<code object getsize at 0x000001E77ED106B0, file "chunk.py", line 86>)
             26 MAKE_FUNCTION            0
             28 STORE_NAME               5 (getsize)

 90          30 LOAD_CONST               6 (<code object close at 0x000001E77ECBE500, file "chunk.py", line 90>)
             32 MAKE_FUNCTION            0
             34 STORE_NAME               6 (close)

 97          36 LOAD_CONST               7 (<code object isatty at 0x000001E77ECD9E30, file "chunk.py", line 97>)
             38 MAKE_FUNCTION            0
             40 STORE_NAME               7 (isatty)

102          42 LOAD_CONST              16 ((0,))
             44 LOAD_CONST               9 (<code object seek at 0x000001E77E762CC0, file "chunk.py", line 102>)
             46 MAKE_FUNCTION            1 (defaults)
             48 STORE_NAME               8 (seek)

121          50 LOAD_CONST              10 (<code object tell at 0x000001E77ECD9F30, file "chunk.py", line 121>)
             52 MAKE_FUNCTION            0
             54 STORE_NAME               9 (tell)

126          56 LOAD_CONST              17 ((-1,))
             58 LOAD_CONST              12 (<code object read at 0x000001E77EF25930, file "chunk.py", line 126>)
             60 MAKE_FUNCTION            1 (defaults)
             62 STORE_NAME              10 (read)

149          64 LOAD_CONST              13 (<code object skip at 0x000001E77E9F9040, file "chunk.py", line 149>)
             66 MAKE_FUNCTION            0
             68 STORE_NAME              11 (skip)
             70 LOAD_CONST              14 (None)
             72 RETURN_VALUE

Disassembly of <code object __init__ at 0x000001E77E9E65E0, file "chunk.py", line 56>:
 56           0 RESUME                   0

 57           2 LOAD_CONST               1 (0)
              4 LOAD_CONST               0 (None)
              6 IMPORT_NAME              0 (struct)
              8 STORE_FAST               5 (struct)

 58          10 LOAD_CONST               2 (False)
             12 LOAD_FAST                0 (self)
             14 STORE_ATTR               1 (closed)

 59          24 LOAD_FAST                2 (align)
             26 LOAD_FAST                0 (self)
             28 STORE_ATTR               2 (align)

 60          38 LOAD_FAST                3 (bigendian)
             40 POP_JUMP_IF_FALSE        3 (to 48)

 61          42 LOAD_CONST               3 ('>')
             44 STORE_FAST               6 (strflag)
             46 JUMP_FORWARD             2 (to 52)

 63     >>   48 LOAD_CONST               4 ('<')
             50 STORE_FAST               6 (strflag)

 64     >>   52 LOAD_FAST                1 (file)
             54 LOAD_FAST                0 (self)
             56 STORE_ATTR               3 (file)

 65          66 LOAD_FAST                1 (file)
             68 STORE_SUBSCR
             72 CACHE
             74 CACHE
             76 CACHE
             78 CACHE
             80 CACHE
             82 CACHE
             84 CACHE
             86 CACHE
             88 CACHE
             90 LOAD_CONST               5 (4)
             92 UNPACK_SEQUENCE          1
             96 CALL                     1
            104 CACHE
            106 LOAD_FAST                0 (self)
            108 STORE_ATTR               5 (chunkname)

 66         118 LOAD_GLOBAL             13 (NULL + len)
            128 CACHE
            130 LOAD_FAST                0 (self)
            132 LOAD_ATTR                5 (NULL|self + align)
            152 CACHE
            154 CACHE
            156 LOAD_CONST               5 (4)
            158 COMPARE_OP               0 (<)
            162 CACHE
            164 POP_JUMP_IF_FALSE        7 (to 180)

 67         166 LOAD_GLOBAL             14 (EOFError)
            176 CACHE
            178 RAISE_VARARGS            1

 68     >>  180 NOP

 69         182 LOAD_FAST                5 (struct)
            184 STORE_SUBSCR
            188 CACHE
            190 CACHE
            192 CACHE
            194 CACHE
            196 CACHE
            198 CACHE
            200 CACHE
            202 CACHE
            204 CACHE
            206 LOAD_FAST                6 (strflag)
            208 LOAD_CONST               6 ('L')
            210 BINARY_OP                0 (+)
            214 LOAD_FAST                1 (file)
            216 STORE_SUBSCR
            220 CACHE
            222 CACHE
            224 CACHE
            226 CACHE
            228 CACHE
            230 CACHE
            232 CACHE
            234 CACHE
            236 CACHE
            238 LOAD_CONST               5 (4)
            240 UNPACK_SEQUENCE          1
            244 CALL                     1
            252 CACHE
            254 UNPACK_SEQUENCE          2
            258 CALL                     2
            266 CACHE
            268 LOAD_CONST               1 (0)
            270 BINARY_SUBSCR
            274 CACHE
            276 CACHE
            278 CACHE
            280 LOAD_FAST                0 (self)
            282 STORE_ATTR               9 (chunksize)
            292 JUMP_FORWARD            22 (to 338)
        >>  294 PUSH_EXC_INFO

 70         296 LOAD_FAST                5 (struct)
            298 LOAD_ATTR               10 (chunkname)
            318 CACHE
            320 CACHE
            322 CACHE
            324 CACHE
            326 LOAD_CONST               0 (None)
            328 RAISE_VARARGS            2

 70         330 RERAISE                  0
        >>  332 COPY                     3
            334 POP_EXCEPT
            336 RERAISE                  1

 72     >>  338 LOAD_FAST                4 (inclheader)
            340 POP_JUMP_IF_FALSE       15 (to 372)

 73         342 LOAD_FAST                0 (self)
            344 LOAD_ATTR                9 (NULL|self + read)
            364 CACHE
            366 CACHE
            368 CACHE
            370 CACHE

 74     >>  372 LOAD_CONST               1 (0)
            374 LOAD_FAST                0 (self)
            376 STORE_ATTR              11 (size_read)

 75         386 NOP

 76         388 LOAD_FAST                0 (self)
            390 LOAD_ATTR                3 (NULL|self + closed)
            410 CACHE
            412 CACHE
            414 CACHE
            416 CACHE
            418 CACHE
            420 CACHE
            422 UNPACK_SEQUENCE          0
            426 CALL                     0
            434 CACHE
            436 LOAD_FAST                0 (self)
            438 STORE_ATTR              13 (offset)

 80         448 LOAD_CONST               8 (True)
            450 LOAD_FAST                0 (self)
            452 STORE_ATTR              14 (seekable)
            462 LOAD_CONST               0 (None)
            464 RETURN_VALUE
        >>  466 PUSH_EXC_INFO

 77         468 LOAD_GLOBAL             30 (AttributeError)
            478 CACHE
            480 LOAD_GLOBAL             32 (OSError)
            490 CACHE
            492 BUILD_TUPLE              2
            494 CHECK_EXC_MATCH
            496 POP_JUMP_IF_FALSE       11 (to 520)
            498 POP_TOP

 78         500 LOAD_CONST               2 (False)
            502 LOAD_FAST                0 (self)
            504 STORE_ATTR              14 (seekable)
            514 POP_EXCEPT
            516 LOAD_CONST               0 (None)
            518 RETURN_VALUE

 77     >>  520 RERAISE                  0
        >>  522 COPY                     3
            524 POP_EXCEPT
            526 RERAISE                  1
ExceptionTable:
  182 to 290 -> 294 [0]
  294 to 330 -> 332 [1] lasti
  388 to 446 -> 466 [0]
  466 to 512 -> 522 [1] lasti
  520 to 520 -> 522 [1] lasti

Disassembly of <code object getname at 0x000001E77ED105E0, file "chunk.py", line 82>:
 82           0 RESUME                   0

 84           2 LOAD_FAST                0 (self)
              4 LOAD_ATTR                0 (chunkname)

Disassembly of <code object getsize at 0x000001E77ED106B0, file "chunk.py", line 86>:
 86           0 RESUME                   0

 88           2 LOAD_FAST                0 (self)
              4 LOAD_ATTR                0 (chunksize)

Disassembly of <code object close at 0x000001E77ECBE500, file "chunk.py", line 90>:
 90           0 RESUME                   0

 91           2 LOAD_FAST                0 (self)
              4 LOAD_ATTR                0 (closed)
             24 CACHE
             26 CACHE
             28 CACHE
             30 CACHE
             32 CACHE
             34 CACHE
             36 CACHE
             38 CACHE
             40 CACHE
             42 UNPACK_SEQUENCE          0
             46 CALL                     0
             54 CACHE
             56 POP_TOP

 95          58 LOAD_CONST               1 (True)
             60 LOAD_FAST                0 (self)
             62 STORE_ATTR               0 (closed)
             72 LOAD_CONST               0 (None)
             74 RETURN_VALUE
        >>   76 PUSH_EXC_INFO
             78 LOAD_CONST               1 (True)
             80 LOAD_FAST                0 (self)
             82 STORE_ATTR               0 (closed)
             92 RERAISE                  0
        >>   94 COPY                     3
             96 POP_EXCEPT
             98 RERAISE                  1

 91         100 LOAD_CONST               0 (None)
            102 RETURN_VALUE
ExceptionTable:
  18 to 56 -> 76 [0]
  76 to 92 -> 94 [1] lasti

Disassembly of <code object isatty at 0x000001E77ECD9E30, file "chunk.py", line 97>:
 97           0 RESUME                   0

 98           2 LOAD_FAST                0 (self)
              4 LOAD_ATTR                0 (closed)
             24 CACHE
             26 CACHE
             28 LOAD_CONST               1 ('I/O operation on closed file')
             30 UNPACK_SEQUENCE          1
             34 CALL                     1
             42 CACHE
             44 RAISE_VARARGS            1

100          46 LOAD_CONST               2 (False)
             48 RETURN_VALUE

Disassembly of <code object seek at 0x000001E77E762CC0, file "chunk.py", line 102>:
102           0 RESUME                   0

108           2 LOAD_FAST                0 (self)
              4 LOAD_ATTR                0 (closed)
             24 CACHE
             26 CACHE
             28 LOAD_CONST               1 ('I/O operation on closed file')
             30 UNPACK_SEQUENCE          1
             34 CALL                     1
             42 CACHE
             44 RAISE_VARARGS            1

110          46 LOAD_FAST                0 (self)
             48 LOAD_ATTR                2 (ValueError)
             68 CACHE
             70 CACHE
             72 LOAD_CONST               2 ('cannot seek')
             74 UNPACK_SEQUENCE          1
             78 CALL                     1
             86 CACHE
             88 RAISE_VARARGS            1

112          90 LOAD_FAST                2 (whence)
             92 LOAD_CONST               3 (1)
             94 COMPARE_OP               2 (<)
             98 CACHE
            100 POP_JUMP_IF_FALSE       11 (to 124)

113         102 LOAD_FAST                1 (pos)
            104 LOAD_FAST                0 (self)
            106 LOAD_ATTR                4 (seekable)
            126 LOAD_CONST               4 (2)
            128 COMPARE_OP               2 (<)
            132 CACHE
            134 POP_JUMP_IF_FALSE       10 (to 156)

115         136 LOAD_FAST                1 (pos)
            138 LOAD_FAST                0 (self)
            140 LOAD_ATTR                5 (NULL|self + seekable)
            160 COMPARE_OP               0 (<)
            164 CACHE
            166 POP_JUMP_IF_TRUE        11 (to 190)
            168 LOAD_FAST                1 (pos)
            170 LOAD_FAST                0 (self)
            172 LOAD_ATTR                5 (NULL|self + seekable)
            192 CACHE
            194 CACHE
            196 CACHE
            198 CACHE
            200 CACHE
            202 RAISE_VARARGS            1

118         204 LOAD_FAST                0 (self)
            206 LOAD_ATTR                7 (NULL|self + OSError)
            226 CACHE
            228 CACHE
            230 CACHE
            232 CACHE
            234 CACHE
            236 CACHE
            238 LOAD_FAST                0 (self)
            240 LOAD_ATTR                9 (NULL|self + size_read)
            260 CACHE
            262 CALL                     2
            270 CACHE
            272 POP_TOP

119         274 LOAD_FAST                1 (pos)
            276 LOAD_FAST                0 (self)
            278 STORE_ATTR               4 (size_read)
            288 LOAD_CONST               6 (None)
            290 RETURN_VALUE

Disassembly of <code object tell at 0x000001E77ECD9F30, file "chunk.py", line 121>:
121           0 RESUME                   0

122           2 LOAD_FAST                0 (self)
              4 LOAD_ATTR                0 (closed)
             24 CACHE
             26 CACHE
             28 LOAD_CONST               1 ('I/O operation on closed file')
             30 UNPACK_SEQUENCE          1
             34 CALL                     1
             42 CACHE
             44 RAISE_VARARGS            1

124          46 LOAD_FAST                0 (self)
             48 LOAD_ATTR                2 (ValueError)

Disassembly of <code object read at 0x000001E77EF25930, file "chunk.py", line 126>:
126           0 RESUME                   0

132           2 LOAD_FAST                0 (self)
              4 LOAD_ATTR                0 (closed)
             24 CACHE
             26 CACHE
             28 LOAD_CONST               1 ('I/O operation on closed file')
             30 UNPACK_SEQUENCE          1
             34 CALL                     1
             42 CACHE
             44 RAISE_VARARGS            1

134          46 LOAD_FAST                0 (self)
             48 LOAD_ATTR                2 (ValueError)
             68 CACHE
             70 COMPARE_OP               5 (<)
             74 CACHE
             76 POP_JUMP_IF_FALSE        2 (to 82)

135          78 LOAD_CONST               2 (b'')
             80 RETURN_VALUE

136     >>   82 LOAD_FAST                1 (size)
             84 LOAD_CONST               3 (0)
             86 COMPARE_OP               0 (<)
             90 CACHE
             92 POP_JUMP_IF_FALSE       15 (to 124)

137          94 LOAD_FAST                0 (self)
             96 LOAD_ATTR                3 (NULL|self + ValueError)
            116 CACHE
            118 BINARY_OP               10 (-)
            122 STORE_FAST               1 (size)

138     >>  124 LOAD_FAST                1 (size)
            126 LOAD_FAST                0 (self)
            128 LOAD_ATTR                3 (NULL|self + ValueError)
            148 CACHE
            150 BINARY_OP               10 (-)
            154 COMPARE_OP               4 (<)
            158 CACHE
            160 POP_JUMP_IF_FALSE       15 (to 192)

139         162 LOAD_FAST                0 (self)
            164 LOAD_ATTR                3 (NULL|self + ValueError)
            184 CACHE
            186 BINARY_OP               10 (-)
            190 STORE_FAST               1 (size)

140     >>  192 LOAD_FAST                0 (self)
            194 LOAD_ATTR                4 (size_read)
            214 CACHE
            216 CACHE
            218 CACHE
            220 CACHE
            222 CACHE
            224 CACHE
            226 LOAD_FAST                1 (size)
            228 UNPACK_SEQUENCE          1
            232 CALL                     1
            240 CACHE
            242 STORE_FAST               2 (data)

141         244 LOAD_FAST                0 (self)
            246 LOAD_ATTR                2 (ValueError)
            266 CACHE
            268 LOAD_FAST                2 (data)
            270 UNPACK_SEQUENCE          1
            274 CALL                     1
            282 CACHE
            284 BINARY_OP                0 (+)
            288 LOAD_FAST                0 (self)
            290 STORE_ATTR               2 (size_read)

142         300 LOAD_FAST                0 (self)
            302 LOAD_ATTR                2 (ValueError)
            322 CACHE
            324 COMPARE_OP               2 (<)
            328 CACHE
            330 POP_JUMP_IF_FALSE       71 (to 474)

143         332 LOAD_FAST                0 (self)
            334 LOAD_ATTR                7 (NULL|self + chunksize)
            354 CACHE
            356 CACHE
            358 LOAD_CONST               4 (1)
            360 BINARY_OP                1 (&)

142         364 POP_JUMP_IF_FALSE       54 (to 474)

145         366 LOAD_FAST                0 (self)
            368 LOAD_ATTR                4 (size_read)
            388 CACHE
            390 CACHE
            392 CACHE
            394 CACHE
            396 CACHE
            398 CACHE
            400 LOAD_CONST               4 (1)
            402 UNPACK_SEQUENCE          1
            406 CALL                     1
            414 CACHE
            416 STORE_FAST               3 (dummy)

146         418 LOAD_FAST                0 (self)
            420 LOAD_ATTR                2 (ValueError)
            440 CACHE
            442 LOAD_FAST                3 (dummy)
            444 UNPACK_SEQUENCE          1
            448 CALL                     1
            456 CACHE
            458 BINARY_OP                0 (+)
            462 LOAD_FAST                0 (self)
            464 STORE_ATTR               2 (size_read)

147     >>  474 LOAD_FAST                2 (data)
            476 RETURN_VALUE

Disassembly of <code object skip at 0x000001E77E9F9040, file "chunk.py", line 149>:
149           0 RESUME                   0

156           2 LOAD_FAST                0 (self)
              4 LOAD_ATTR                0 (closed)
             24 CACHE
             26 CACHE
             28 LOAD_CONST               1 ('I/O operation on closed file')
             30 UNPACK_SEQUENCE          1
             34 CALL                     1
             42 CACHE
             44 RAISE_VARARGS            1

158          46 LOAD_FAST                0 (self)
             48 LOAD_ATTR                2 (ValueError)
             68 CACHE
             70 CACHE
             72 CACHE
             74 LOAD_FAST                0 (self)
             76 LOAD_ATTR                4 (seekable)
             96 CACHE
             98 CACHE
            100 CACHE
            102 CACHE
            104 POP_JUMP_IF_FALSE       15 (to 136)
            106 LOAD_FAST                0 (self)
            108 LOAD_ATTR                3 (NULL|self + ValueError)
            128 LOAD_CONST               2 (1)
            130 BINARY_OP                0 (+)
            134 STORE_FAST               1 (n)

164     >>  136 LOAD_FAST                0 (self)
            138 LOAD_ATTR                6 (chunksize)
            158 CACHE
            160 CACHE
            162 CACHE
            164 CACHE
            166 CACHE
            168 CACHE
            170 LOAD_FAST                1 (n)
            172 LOAD_CONST               2 (1)
            174 UNPACK_SEQUENCE          2
            178 CALL                     2
            186 CACHE
            188 POP_TOP

165         190 LOAD_FAST                0 (self)
            192 LOAD_ATTR                4 (seekable)
            212 CACHE
            214 CACHE
            216 CACHE
            218 CACHE

166         220 LOAD_CONST               3 (None)
            222 RETURN_VALUE
        >>  224 PUSH_EXC_INFO

167         226 LOAD_GLOBAL             16 (OSError)
            236 CACHE
            238 CHECK_EXC_MATCH
            240 POP_JUMP_IF_FALSE        3 (to 248)
            242 POP_TOP

168         244 POP_EXCEPT
            246 JUMP_FORWARD             4 (to 256)

167     >>  248 RERAISE                  0
        >>  250 COPY                     3
            252 POP_EXCEPT
            254 RERAISE                  1

169     >>  256 LOAD_FAST                0 (self)
            258 LOAD_ATTR                4 (seekable)
            278 CACHE
            280 COMPARE_OP               0 (<)
            284 CACHE
            286 POP_JUMP_IF_FALSE       77 (to 442)

170         288 LOAD_GLOBAL             19 (NULL + min)
            298 CACHE
            300 LOAD_CONST               4 (8192)
            302 LOAD_FAST                0 (self)
            304 LOAD_ATTR                3 (NULL|self + ValueError)
            324 CACHE
            326 BINARY_OP               10 (-)
            330 UNPACK_SEQUENCE          2
            334 CALL                     2
            342 CACHE
            344 STORE_FAST               1 (n)

171         346 LOAD_FAST                0 (self)
            348 STORE_SUBSCR
            352 CACHE
            354 CACHE
            356 CACHE
            358 CACHE
            360 CACHE
            362 CACHE
            364 CACHE
            366 CACHE
            368 CACHE
            370 LOAD_FAST                1 (n)
            372 UNPACK_SEQUENCE          1
            376 CALL                     1
            384 CACHE
            386 STORE_FAST               2 (dummy)

172         388 LOAD_FAST                2 (dummy)
            390 POP_JUMP_IF_TRUE         7 (to 406)

173         392 LOAD_GLOBAL             22 (EOFError)
            402 CACHE
            404 RAISE_VARARGS            1

169     >>  406 LOAD_FAST                0 (self)
            408 LOAD_ATTR                4 (seekable)
            428 CACHE
            430 COMPARE_OP               0 (<)
            434 CACHE
