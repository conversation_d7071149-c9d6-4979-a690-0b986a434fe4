# Code object from position 10112798
# Filename: ftplib.py
# Name: <module>
# Args: 0
# Locals: 0
# Stack size: 5
# Flags: 0

  0           0 RESUME                   0

  1           2 LOAD_CONST               0 ("An FTP client class and some helper functions.\n\nBased on RFC 959: File Transfer Protocol (FTP), by <PERSON><PERSON> and <PERSON><PERSON>\n\nExample:\n\n>>> from ftplib import FTP\n>>> ftp = FTP('ftp.python.org') # connect to host, default port\n>>> ftp.login() # default, i.e.: user anonymous, passwd anonymous@\n'230 Guest login ok, access restrictions apply.'\n>>> ftp.retrlines('LIST') # list directory contents\ntotal 9\ndrwxr-xr-x   8 <USER>     <GROUP>        1024 Jan  3  1994 .\ndrwxr-xr-x   8 <USER>     <GROUP>        1024 Jan  3  1994 ..\ndrwxr-xr-x   2 <USER>     <GROUP>        1024 Jan  3  1994 bin\ndrwxr-xr-x   2 <USER>     <GROUP>        1024 Jan  3  1994 etc\nd-wxrwxr-x   2 <USER>      <GROUP>        1024 Sep  5 13:43 incoming\ndrwxr-xr-x   2 <USER>     <GROUP>        1024 Nov 17  1993 lib\ndrwxr-xr-x   6 <USER>     <GROUP>        1024 Sep 13 19:07 pub\ndrwxr-xr-x   3 <USER>     <GROUP>        1024 Jan  3  1994 usr\n-rw-r--r--   1 <USER>     <GROUP>          312 Aug  1  1994 welcome.msg\n'226 Transfer complete.'\n>>> ftp.quit()\n'221 Goodbye.'\n>>>\n\nA nice test that reveals some of the network dialogue would be:\npython ftplib.py -d localhost -l -p -l\n")
              4 STORE_NAME               0 (__doc__)

 39           6 LOAD_CONST               1 (0)
              8 LOAD_CONST               2 (None)
             10 IMPORT_NAME              1 (sys)
             12 STORE_NAME               1 (sys)

 40          14 LOAD_CONST               1 (0)
             16 LOAD_CONST               2 (None)
             18 IMPORT_NAME              2 (socket)
             20 STORE_NAME               2 (socket)

 41          22 LOAD_CONST               1 (0)
             24 LOAD_CONST               3 (('_GLOBAL_DEFAULT_TIMEOUT',))
             26 IMPORT_NAME              2 (socket)
             28 IMPORT_FROM              3 (_GLOBAL_DEFAULT_TIMEOUT)
             30 STORE_NAME               3 (_GLOBAL_DEFAULT_TIMEOUT)
             32 POP_TOP

 43          34 BUILD_LIST               0
             36 LOAD_CONST               4 (('FTP', 'error_reply', 'error_temp', 'error_perm', 'error_proto', 'all_errors'))
             38 LIST_EXTEND              1
             40 STORE_NAME               4 (__all__)

 47          42 LOAD_CONST               5 (1)
             44 STORE_NAME               5 (MSG_OOB)

 51          46 LOAD_CONST               6 (21)
             48 STORE_NAME               6 (FTP_PORT)

 53          50 LOAD_CONST               7 (8192)
             52 STORE_NAME               7 (MAXLINE)

 57          54 PUSH_NULL
             56 LOAD_BUILD_CLASS
             58 LOAD_CONST               8 (<code object Error at 0x000001B2A8A10780, file "ftplib.py", line 57>)
             60 MAKE_FUNCTION            0
             62 LOAD_CONST               9 ('Error')
             64 LOAD_NAME                8 (Exception)
             66 UNPACK_SEQUENCE          3
             70 CALL                     3
             78 CACHE
             80 STORE_NAME               9 (Error)

 58          82 PUSH_NULL
             84 LOAD_BUILD_CLASS
             86 LOAD_CONST              10 (<code object error_reply at 0x000001B2A8A10850, file "ftplib.py", line 58>)
             88 MAKE_FUNCTION            0
             90 LOAD_CONST              11 ('error_reply')
             92 LOAD_NAME                9 (Error)
             94 UNPACK_SEQUENCE          3
             98 CALL                     3
            106 CACHE
            108 STORE_NAME              10 (error_reply)

 59         110 PUSH_NULL
            112 LOAD_BUILD_CLASS
            114 LOAD_CONST              12 (<code object error_temp at 0x000001B2A8A10920, file "ftplib.py", line 59>)
            116 MAKE_FUNCTION            0
            118 LOAD_CONST              13 ('error_temp')
            120 LOAD_NAME                9 (Error)
            122 UNPACK_SEQUENCE          3
            126 CALL                     3
            134 CACHE
            136 STORE_NAME              11 (error_temp)

 60         138 PUSH_NULL
            140 LOAD_BUILD_CLASS
            142 LOAD_CONST              14 (<code object error_perm at 0x000001B2A8A10AC0, file "ftplib.py", line 60>)
            144 MAKE_FUNCTION            0
            146 LOAD_CONST              15 ('error_perm')
            148 LOAD_NAME                9 (Error)
            150 UNPACK_SEQUENCE          3
            154 CALL                     3
            162 CACHE
            164 STORE_NAME              12 (error_perm)

 61         166 PUSH_NULL
            168 LOAD_BUILD_CLASS
            170 LOAD_CONST              16 (<code object error_proto at 0x000001B2A8A10B90, file "ftplib.py", line 61>)
            172 MAKE_FUNCTION            0
            174 LOAD_CONST              17 ('error_proto')
            176 LOAD_NAME                9 (Error)
            178 UNPACK_SEQUENCE          3
            182 CALL                     3
            190 CACHE
            192 STORE_NAME              13 (error_proto)

 66         194 LOAD_NAME                9 (Error)
            196 LOAD_NAME               14 (OSError)
            198 LOAD_NAME               15 (EOFError)
            200 BUILD_TUPLE              3
            202 STORE_NAME              16 (all_errors)

 70         204 LOAD_CONST              18 ('\r\n')
            206 STORE_NAME              17 (CRLF)

 71         208 LOAD_CONST              19 (b'\r\n')
            210 STORE_NAME              18 (B_CRLF)

 74         212 PUSH_NULL
            214 LOAD_BUILD_CLASS
            216 LOAD_CONST              20 (<code object FTP at 0x000001B2A75D5480, file "ftplib.py", line 74>)
            218 MAKE_FUNCTION            0
            220 LOAD_CONST              21 ('FTP')
            222 UNPACK_SEQUENCE          2
            226 CALL                     2
            234 CACHE
            236 STORE_NAME              19 (FTP)

676         238 NOP

677         240 LOAD_CONST               1 (0)
            242 LOAD_CONST               2 (None)
            244 IMPORT_NAME             20 (ssl)
            246 STORE_NAME              20 (ssl)

681         248 LOAD_NAME               20 (ssl)
            250 LOAD_ATTR               21 (NULL|self + error_reply)
            270 LOAD_CONST              23 ('FTP_TLS')
            272 LOAD_NAME               19 (FTP)
            274 UNPACK_SEQUENCE          3
            278 CALL                     3
            286 CACHE
            288 STORE_NAME              23 (FTP_TLS)

808         290 LOAD_NAME                4 (__all__)
            292 STORE_SUBSCR
            296 CACHE
            298 CACHE
            300 CACHE
            302 CACHE
            304 CACHE
            306 CACHE
            308 CACHE
            310 CACHE
            312 CACHE
            314 LOAD_CONST              23 ('FTP_TLS')
            316 UNPACK_SEQUENCE          1
            320 CALL                     1
            328 CACHE
            330 POP_TOP

809         332 LOAD_NAME                9 (Error)
            334 LOAD_NAME               14 (OSError)
            336 LOAD_NAME               15 (EOFError)
            338 LOAD_NAME               20 (ssl)
            340 LOAD_ATTR               25 (NULL|self + error_perm)
            360 CHECK_EXC_MATCH
            362 POP_JUMP_IF_FALSE        5 (to 374)
            364 POP_TOP

679         366 LOAD_CONST               2 (None)
            368 STORE_NAME              22 (_SSLSocket)
            370 POP_EXCEPT
            372 JUMP_FORWARD             4 (to 382)

678     >>  374 RERAISE                  0
        >>  376 COPY                     3
            378 POP_EXCEPT
            380 RERAISE                  1

812     >>  382 LOAD_CONST               2 (None)
            384 STORE_GLOBAL            27 (_150_re)

814         386 LOAD_CONST              24 (<code object parse150 at 0x000001B2A721A5B0, file "ftplib.py", line 814>)
            388 MAKE_FUNCTION            0
            390 STORE_NAME              28 (parse150)

832         392 LOAD_CONST               2 (None)
            394 STORE_GLOBAL            29 (_227_re)

834         396 LOAD_CONST              25 (<code object parse227 at 0x000001B2A74A4890, file "ftplib.py", line 834>)
            398 MAKE_FUNCTION            0
            400 STORE_NAME              30 (parse227)

853         402 LOAD_CONST              26 (<code object parse229 at 0x000001B2A75C1040, file "ftplib.py", line 853>)
            404 MAKE_FUNCTION            0
            406 STORE_NAME              31 (parse229)

874         408 LOAD_CONST              27 (<code object parse257 at 0x000001B2A722EF70, file "ftplib.py", line 874>)
            410 MAKE_FUNCTION            0
            412 STORE_NAME              32 (parse257)

896         414 LOAD_CONST              28 (<code object print_line at 0x000001B2A767FE10, file "ftplib.py", line 896>)
            416 MAKE_FUNCTION            0
            418 STORE_NAME              33 (print_line)

901         420 LOAD_CONST              34 (('', 'I'))
            422 LOAD_CONST              31 (<code object ftpcp at 0x000001B2A74A5C50, file "ftplib.py", line 901>)
            424 MAKE_FUNCTION            1 (defaults)
            426 STORE_NAME              34 (ftpcp)

923         428 LOAD_CONST              32 (<code object test at 0x000001B2A74A6220, file "ftplib.py", line 923>)
            430 MAKE_FUNCTION            0
            432 STORE_NAME              35 (test)

987         434 LOAD_NAME               36 (__name__)
            436 LOAD_CONST              33 ('__main__')
            438 COMPARE_OP               2 (<)
            442 CACHE
            444 POP_JUMP_IF_FALSE       12 (to 470)

988         446 PUSH_NULL
            448 LOAD_NAME               35 (test)
            450 UNPACK_SEQUENCE          0
            454 CALL                     0
            462 CACHE
            464 POP_TOP
            466 LOAD_CONST               2 (None)
            468 RETURN_VALUE

987     >>  470 LOAD_CONST               2 (None)
            472 RETURN_VALUE
ExceptionTable:
  240 to 246 -> 356 [0]
  356 to 368 -> 376 [1] lasti
  374 to 374 -> 376 [1] lasti

Disassembly of <code object Error at 0x000001B2A8A10780, file "ftplib.py", line 57>:
 57           0 RESUME                   0
              2 LOAD_NAME                0 (__name__)
              4 STORE_NAME               1 (__module__)
              6 LOAD_CONST               0 ('Error')
              8 STORE_NAME               2 (__qualname__)
             10 LOAD_CONST               1 (None)
             12 RETURN_VALUE

Disassembly of <code object error_reply at 0x000001B2A8A10850, file "ftplib.py", line 58>:
 58           0 RESUME                   0
              2 LOAD_NAME                0 (__name__)
              4 STORE_NAME               1 (__module__)
              6 LOAD_CONST               0 ('error_reply')
              8 STORE_NAME               2 (__qualname__)
             10 LOAD_CONST               1 (None)
             12 RETURN_VALUE

Disassembly of <code object error_temp at 0x000001B2A8A10920, file "ftplib.py", line 59>:
 59           0 RESUME                   0
              2 LOAD_NAME                0 (__name__)
              4 STORE_NAME               1 (__module__)
              6 LOAD_CONST               0 ('error_temp')
              8 STORE_NAME               2 (__qualname__)
             10 LOAD_CONST               1 (None)
             12 RETURN_VALUE

Disassembly of <code object error_perm at 0x000001B2A8A10AC0, file "ftplib.py", line 60>:
 60           0 RESUME                   0
              2 LOAD_NAME                0 (__name__)
              4 STORE_NAME               1 (__module__)
              6 LOAD_CONST               0 ('error_perm')
              8 STORE_NAME               2 (__qualname__)
             10 LOAD_CONST               1 (None)
             12 RETURN_VALUE

Disassembly of <code object error_proto at 0x000001B2A8A10B90, file "ftplib.py", line 61>:
 61           0 RESUME                   0
              2 LOAD_NAME                0 (__name__)
              4 STORE_NAME               1 (__module__)
              6 LOAD_CONST               0 ('error_proto')
              8 STORE_NAME               2 (__qualname__)
             10 LOAD_CONST               1 (None)
             12 RETURN_VALUE

Disassembly of <code object FTP at 0x000001B2A75D5480, file "ftplib.py", line 74>:
 74           0 RESUME                   0
              2 LOAD_NAME                0 (__name__)
              4 STORE_NAME               1 (__module__)
              6 LOAD_CONST               0 ('FTP')
              8 STORE_NAME               2 (__qualname__)

 75          10 LOAD_CONST               1 ("An FTP client class.\n\n    To create a connection, call the class using these arguments:\n            host, user, passwd, acct, timeout, source_address, encoding\n\n    The first four arguments are all strings, and have default value ''.\n    The parameter ´timeout´ must be numeric and defaults to None if not\n    passed, meaning that no timeout will be set on any ftp socket(s).\n    If a timeout is passed, then this is now the default timeout for all ftp\n    socket operations for this instance.\n    The last parameter is the encoding of filenames, which defaults to utf-8.\n\n    Then use self.connect() with optional host and port argument.\n\n    To download a file, use ftp.retrlines('RETR ' + filename),\n    or ftp.retrbinary() with slightly different arguments.\n    To upload a file, use ftp.storlines() or ftp.storbinary(),\n    which have an open file as argument (see their definitions\n    below for details).\n    The download/upload functions first issue appropriate TYPE\n    and PORT or PASV commands.\n    ")
             12 STORE_NAME               3 (__doc__)

 98          14 LOAD_CONST               2 (0)
             16 STORE_NAME               4 (debugging)

 99          18 LOAD_CONST               3 ('')
             20 STORE_NAME               5 (host)

100          22 LOAD_NAME                6 (FTP_PORT)
             24 STORE_NAME               7 (port)

101          26 LOAD_NAME                8 (MAXLINE)
             28 STORE_NAME               9 (maxline)

102          30 LOAD_CONST               4 (None)
             32 STORE_NAME              10 (sock)

103          34 LOAD_CONST               4 (None)
             36 STORE_NAME              11 (file)

104          38 LOAD_CONST               4 (None)
             40 STORE_NAME              12 (welcome)

105          42 LOAD_CONST               5 (True)
             44 STORE_NAME              13 (passiveserver)

107          46 LOAD_CONST               6 (False)
             48 STORE_NAME              14 (trust_server_pasv_ipv4_address)

109          50 LOAD_CONST               3 ('')
             52 LOAD_CONST               3 ('')
             54 LOAD_CONST               3 ('')
             56 LOAD_CONST               3 ('')

110          58 LOAD_NAME               15 (_GLOBAL_DEFAULT_TIMEOUT)
             60 LOAD_CONST               4 (None)

109          62 BUILD_TUPLE              6

111          64 LOAD_CONST               7 ('utf-8')

109          66 LOAD_CONST               8 (('encoding',))
             68 BUILD_CONST_KEY_MAP      1
             70 LOAD_CONST               9 (<code object __init__ at 0x000001B2A71D31B0, file "ftplib.py", line 109>)
             72 MAKE_FUNCTION            3 (defaults, kwdefaults)
             74 STORE_NAME              16 (__init__)

125          76 LOAD_CONST              10 (<code object __enter__ at 0x000001B2A8A10C60, file "ftplib.py", line 125>)
             78 MAKE_FUNCTION            0
             80 STORE_NAME              17 (__enter__)

129          82 LOAD_CONST              11 (<code object __exit__ at 0x000001B2A726D980, file "ftplib.py", line 129>)
             84 MAKE_FUNCTION            0
             86 STORE_NAME              18 (__exit__)

139          88 LOAD_CONST              52 (('', 0, -999, None))
             90 LOAD_CONST              13 (<code object connect at 0x000001B2A74D2C00, file "ftplib.py", line 139>)
             92 MAKE_FUNCTION            1 (defaults)
             94 STORE_NAME              19 (connect)

165          96 LOAD_CONST              14 (<code object getwelcome at 0x000001B2A76B5A50, file "ftplib.py", line 165>)
             98 MAKE_FUNCTION            0
            100 STORE_NAME              20 (getwelcome)

172         102 LOAD_CONST              15 (<code object set_debuglevel at 0x000001B2A768BBB0, file "ftplib.py", line 172>)
            104 MAKE_FUNCTION            0
            106 STORE_NAME              21 (set_debuglevel)

179         108 LOAD_NAME               21 (set_debuglevel)
            110 STORE_NAME              22 (debug)

181         112 LOAD_CONST              16 (<code object set_pasv at 0x000001B2A768BAD0, file "ftplib.py", line 181>)
            114 MAKE_FUNCTION            0
            116 STORE_NAME              23 (set_pasv)

188         118 LOAD_CONST              17 (<code object sanitize at 0x000001B2A7212430, file "ftplib.py", line 188>)
            120 MAKE_FUNCTION            0
            122 STORE_NAME              24 (sanitize)

195         124 LOAD_CONST              18 (<code object putline at 0x000001B2A71BB630, file "ftplib.py", line 195>)
            126 MAKE_FUNCTION            0
            128 STORE_NAME              25 (putline)

205         130 LOAD_CONST              19 (<code object putcmd at 0x000001B2A71C6E20, file "ftplib.py", line 205>)
            132 MAKE_FUNCTION            0
            134 STORE_NAME              26 (putcmd)

211         136 LOAD_CONST              20 (<code object getline at 0x000001B2A75B82C0, file "ftplib.py", line 211>)
            138 MAKE_FUNCTION            0
            140 STORE_NAME              27 (getline)

229         142 LOAD_CONST              21 (<code object getmultiline at 0x000001B2A71CDD70, file "ftplib.py", line 229>)
            144 MAKE_FUNCTION            0
            146 STORE_NAME              28 (getmultiline)

243         148 LOAD_CONST              22 (<code object getresp at 0x000001B2A7251960, file "ftplib.py", line 243>)
            150 MAKE_FUNCTION            0
            152 STORE_NAME              29 (getresp)

257         154 LOAD_CONST              23 (<code object voidresp at 0x000001B2A76B5DE0, file "ftplib.py", line 257>)
            156 MAKE_FUNCTION            0
            158 STORE_NAME              30 (voidresp)

264         160 LOAD_CONST              24 (<code object abort at 0x000001B2A7219C50, file "ftplib.py", line 264>)
            162 MAKE_FUNCTION            0
            164 STORE_NAME              31 (abort)

278         166 LOAD_CONST              25 (<code object sendcmd at 0x000001B2A76DB870, file "ftplib.py", line 278>)
            168 MAKE_FUNCTION            0
            170 STORE_NAME              32 (sendcmd)

283         172 LOAD_CONST              26 (<code object voidcmd at 0x000001B2A76DBAB0, file "ftplib.py", line 283>)
            174 MAKE_FUNCTION            0
            176 STORE_NAME              33 (voidcmd)

288         178 LOAD_CONST              27 (<code object sendport at 0x000001B2A71CEDB0, file "ftplib.py", line 288>)
            180 MAKE_FUNCTION            0
            182 STORE_NAME              34 (sendport)

298         184 LOAD_CONST              28 (<code object sendeprt at 0x000001B2A7250BD0, file "ftplib.py", line 298>)
            186 MAKE_FUNCTION            0
            188 STORE_NAME              35 (sendeprt)

311         190 LOAD_CONST              29 (<code object makeport at 0x000001B2A75B8510, file "ftplib.py", line 311>)
            192 MAKE_FUNCTION            0
            194 STORE_NAME              36 (makeport)

324         196 LOAD_CONST              30 (<code object makepasv at 0x000001B2A508AA90, file "ftplib.py", line 324>)
            198 MAKE_FUNCTION            0
            200 STORE_NAME              37 (makepasv)

336         202 LOAD_CONST              53 ((None,))
            204 LOAD_CONST              31 (<code object ntransfercmd at 0x000001B2A75B4140, file "ftplib.py", line 336>)
            206 MAKE_FUNCTION            1 (defaults)
            208 STORE_NAME              38 (ntransfercmd)

391         210 LOAD_CONST              53 ((None,))
            212 LOAD_CONST              32 (<code object transfercmd at 0x000001B2A8A0DB30, file "ftplib.py", line 391>)
            214 MAKE_FUNCTION            1 (defaults)
            216 STORE_NAME              39 (transfercmd)

395         218 LOAD_CONST              54 (('', '', ''))
            220 LOAD_CONST              33 (<code object login at 0x000001B2A8A1C030, file "ftplib.py", line 395>)
            222 MAKE_FUNCTION            1 (defaults)
            224 STORE_NAME              40 (login)

421         226 LOAD_CONST              55 ((8192, None))
            228 LOAD_CONST              35 (<code object retrbinary at 0x000001B2A75D5260, file "ftplib.py", line 421>)
            230 MAKE_FUNCTION            1 (defaults)
            232 STORE_NAME              41 (retrbinary)

447         234 LOAD_CONST              53 ((None,))
            236 LOAD_CONST              36 (<code object retrlines at 0x000001B2A74A3670, file "ftplib.py", line 447>)
            238 MAKE_FUNCTION            1 (defaults)
            240 STORE_NAME              42 (retrlines)

482         242 LOAD_CONST              56 ((8192, None, None))
            244 LOAD_CONST              37 (<code object storbinary at 0x000001B2A75B8760, file "ftplib.py", line 482>)
            246 MAKE_FUNCTION            1 (defaults)
            248 STORE_NAME              43 (storbinary)

511         250 LOAD_CONST              53 ((None,))
            252 LOAD_CONST              38 (<code object storlines at 0x000001B2A74A3CE0, file "ftplib.py", line 511>)
            254 MAKE_FUNCTION            1 (defaults)
            256 STORE_NAME              44 (storlines)

542         258 LOAD_CONST              39 (<code object acct at 0x000001B2A8A0CD30, file "ftplib.py", line 542>)
            260 MAKE_FUNCTION            0
            262 STORE_NAME              45 (acct)

547         264 LOAD_CONST              40 (<code object nlst at 0x000001B2A76DB1B0, file "ftplib.py", line 547>)
            266 MAKE_FUNCTION            0
            268 STORE_NAME              46 (nlst)

556         270 LOAD_CONST              41 (<code object dir at 0x000001B2A71CEC10, file "ftplib.py", line 556>)
            272 MAKE_FUNCTION            0
            274 STORE_NAME              47 (dir)

571         276 LOAD_CONST               3 ('')
            278 BUILD_LIST               0
            280 BUILD_TUPLE              2
            282 LOAD_CONST              42 (<code object mlsd at 0x000001B2A74A4290, file "ftplib.py", line 571>)
            284 MAKE_FUNCTION            1 (defaults)
            286 STORE_NAME              48 (mlsd)

599         288 LOAD_CONST              43 (<code object rename at 0x000001B2A71D3730, file "ftplib.py", line 599>)
            290 MAKE_FUNCTION            0
            292 STORE_NAME              49 (rename)

606         294 LOAD_CONST              44 (<code object delete at 0x000001B2A76B5B80, file "ftplib.py", line 606>)
            296 MAKE_FUNCTION            0
            298 STORE_NAME              50 (delete)

614         300 LOAD_CONST              45 (<code object cwd at 0x000001B2A71CDBD0, file "ftplib.py", line 614>)
            302 MAKE_FUNCTION            0
            304 STORE_NAME              51 (cwd)

627         306 LOAD_CONST              46 (<code object size at 0x000001B2A728F6D0, file "ftplib.py", line 627>)
            308 MAKE_FUNCTION            0
            310 STORE_NAME              52 (size)

635         312 LOAD_CONST              47 (<code object mkd at 0x000001B2A8A08CB0, file "ftplib.py", line 635>)
            314 MAKE_FUNCTION            0
            316 STORE_NAME              53 (mkd)

644         318 LOAD_CONST              48 (<code object rmd at 0x000001B2A8A0D930, file "ftplib.py", line 644>)
            320 MAKE_FUNCTION            0
            322 STORE_NAME              54 (rmd)

648         324 LOAD_CONST              49 (<code object pwd at 0x000001B2A8A08F30, file "ftplib.py", line 648>)
            326 MAKE_FUNCTION            0
            328 STORE_NAME              55 (pwd)

657         330 LOAD_CONST              50 (<code object quit at 0x000001B2A76D9FB0, file "ftplib.py", line 657>)
            332 MAKE_FUNCTION            0
            334 STORE_NAME              56 (quit)

663         336 LOAD_CONST              51 (<code object close at 0x000001B2A726EC10, file "ftplib.py", line 663>)
            338 MAKE_FUNCTION            0
            340 STORE_NAME              57 (close)
            342 LOAD_CONST               4 (None)
            344 RETURN_VALUE

Disassembly of <code object __init__ at 0x000001B2A71D31B0, file "ftplib.py", line 109>:
109           0 RESUME                   0

117           2 LOAD_FAST                7 (encoding)
              4 LOAD_FAST                0 (self)
              6 STORE_ATTR               0 (encoding)

118          16 LOAD_FAST                6 (source_address)
             18 LOAD_FAST                0 (self)
             20 STORE_ATTR               1 (source_address)

119          30 LOAD_FAST                5 (timeout)
             32 LOAD_FAST                0 (self)
             34 STORE_ATTR               2 (timeout)

120          44 LOAD_FAST                1 (host)
             46 POP_JUMP_IF_FALSE       48 (to 144)

121          48 LOAD_FAST                0 (self)
             50 STORE_SUBSCR
             54 CACHE
             56 CACHE
             58 CACHE
             60 CACHE
             62 CACHE
             64 CACHE
             66 CACHE
             68 CACHE
             70 CACHE
             72 LOAD_FAST                1 (host)
             74 UNPACK_SEQUENCE          1
             78 CALL                     1
             86 CACHE
             88 POP_TOP

122          90 LOAD_FAST                2 (user)
             92 POP_JUMP_IF_FALSE       27 (to 148)

123          94 LOAD_FAST                0 (self)
             96 STORE_SUBSCR
            100 CACHE
            102 CACHE
            104 CACHE
            106 CACHE
            108 CACHE
            110 CACHE
            112 CACHE
            114 CACHE
            116 CACHE
            118 LOAD_FAST                2 (user)
            120 LOAD_FAST                3 (passwd)
            122 LOAD_FAST                4 (acct)
            124 UNPACK_SEQUENCE          3
            128 CALL                     3
            136 CACHE
            138 POP_TOP
            140 LOAD_CONST               1 (None)
            142 RETURN_VALUE

120     >>  144 LOAD_CONST               1 (None)
            146 RETURN_VALUE

122     >>  148 LOAD_CONST               1 (None)
            150 RETURN_VALUE

Disassembly of <code object __enter__ at 0x000001B2A8A10C60, file "ftplib.py", line 125>:
125           0 RESUME                   0

126           2 LOAD_FAST                0 (self)
              4 RETURN_VALUE

Disassembly of <code object __exit__ at 0x000001B2A726D980, file "ftplib.py", line 129>:
129           0 RESUME                   0

130           2 LOAD_FAST                0 (self)
              4 LOAD_ATTR                0 (sock)
             24 CACHE
             26 CACHE
             28 CACHE
             30 CACHE
             32 CACHE
             34 CACHE
             36 CACHE
             38 CACHE
             40 CACHE
             42 UNPACK_SEQUENCE          0
             46 CALL                     0
             54 CACHE
             56 POP_TOP
             58 JUMP_FORWARD            23 (to 106)
        >>   60 PUSH_EXC_INFO

133          62 LOAD_GLOBAL              4 (OSError)
             72 CACHE
             74 LOAD_GLOBAL              6 (EOFError)
             84 CACHE
             86 BUILD_TUPLE              2
             88 CHECK_EXC_MATCH
             90 POP_JUMP_IF_FALSE        3 (to 98)
             92 POP_TOP

134          94 POP_EXCEPT
             96 JUMP_FORWARD             4 (to 106)

133     >>   98 RERAISE                  0
        >>  100 COPY                     3
            102 POP_EXCEPT
            104 RERAISE                  1

136     >>  106 LOAD_FAST                0 (self)
            108 LOAD_ATTR                0 (sock)
            128 CACHE
            130 CACHE
            132 CACHE
            134 CACHE
            136 CACHE
            138 CACHE
            140 CACHE
            142 CACHE
            144 UNPACK_SEQUENCE          0
            148 CALL                     0
            156 CACHE
            158 POP_TOP
            160 LOAD_CONST               0 (None)
            162 RETURN_VALUE

136         164 LOAD_CONST               0 (None)
            166 RETURN_VALUE
        >>  168 PUSH_EXC_INFO
            170 LOAD_FAST                0 (self)
            172 LOAD_ATTR                0 (sock)
            192 CACHE
            194 CACHE
            196 CACHE
            198 CACHE
            200 CACHE
            202 CACHE
            204 CACHE
            206 CACHE
            208 UNPACK_SEQUENCE          0
            212 CALL                     0
            220 CACHE
            222 POP_TOP
            224 RERAISE                  0

136         226 RERAISE                  0
        >>  228 COPY                     3
            230 POP_EXCEPT
            232 RERAISE                  1

130         234 LOAD_CONST               0 (None)
            236 RETURN_VALUE
ExceptionTable:
  18 to 56 -> 60 [0]
  58 to 58 -> 168 [0]
  60 to 92 -> 100 [1] lasti
  94 to 96 -> 168 [0]
  98 to 98 -> 100 [1] lasti
  100 to 104 -> 168 [0]
  168 to 226 -> 228 [1] lasti

Disassembly of <code object connect at 0x000001B2A74D2C00, file "ftplib.py", line 139>:
139           0 RESUME                   0

147           2 LOAD_FAST                1 (host)
              4 LOAD_CONST               1 ('')
              6 COMPARE_OP               3 (<)
             10 CACHE
             12 POP_JUMP_IF_FALSE        7 (to 28)

148          14 LOAD_FAST                1 (host)
             16 LOAD_FAST                0 (self)
             18 STORE_ATTR               0 (host)

149     >>   28 LOAD_FAST                2 (port)
             30 LOAD_CONST               2 (0)
             32 COMPARE_OP               4 (<)
             36 CACHE
             38 POP_JUMP_IF_FALSE        7 (to 54)

150          40 LOAD_FAST                2 (port)
             42 LOAD_FAST                0 (self)
             44 STORE_ATTR               1 (port)

151     >>   54 LOAD_FAST                3 (timeout)
             56 LOAD_CONST               3 (-999)
             58 COMPARE_OP               3 (<)
             62 CACHE
             64 POP_JUMP_IF_FALSE        7 (to 80)

152          66 LOAD_FAST                3 (timeout)
             68 LOAD_FAST                0 (self)
             70 STORE_ATTR               2 (timeout)

153     >>   80 LOAD_FAST                0 (self)
             82 LOAD_ATTR                2 (port)
            102 CACHE
            104 CACHE
            106 POP_JUMP_IF_TRUE        15 (to 138)

154         108 LOAD_GLOBAL              7 (NULL + ValueError)
            118 CACHE
            120 LOAD_CONST               5 ('Non-blocking socket (timeout=0) is not supported')
            122 UNPACK_SEQUENCE          1
            126 CALL                     1
            134 CACHE
            136 RAISE_VARARGS            1

155     >>  138 LOAD_FAST                4 (source_address)
            140 POP_JUMP_IF_NONE         7 (to 156)

156         142 LOAD_FAST                4 (source_address)
            144 LOAD_FAST                0 (self)
            146 STORE_ATTR               4 (source_address)

157     >>  156 LOAD_GLOBAL             11 (NULL + sys)
            166 CACHE
            168 LOAD_ATTR                6 (ValueError)
            188 CACHE
            190 CACHE
            192 CACHE
            194 LOAD_FAST                0 (self)
            196 LOAD_ATTR                1 (NULL|self + host)
            216 CACHE
            218 CACHE
            220 POP_TOP

158         222 LOAD_GLOBAL             15 (NULL + socket)
            232 CACHE
            234 LOAD_ATTR                8 (source_address)
            254 CACHE
            256 LOAD_FAST                0 (self)
            258 LOAD_ATTR                1 (NULL|self + host)
            278 CACHE
            280 CACHE

159         282 LOAD_FAST                0 (self)
            284 LOAD_ATTR                4 (timeout)
            304 CACHE
            306 CACHE
            308 CACHE
            310 LOAD_FAST                0 (self)
            312 STORE_ATTR               9 (sock)

160         322 LOAD_FAST                0 (self)
            324 LOAD_ATTR                9 (NULL|self + source_address)
            344 LOAD_FAST                0 (self)
            346 STORE_ATTR              11 (af)

161         356 LOAD_FAST                0 (self)
            358 LOAD_ATTR                9 (NULL|self + source_address)
            378 CACHE
            380 CACHE
            382 CACHE
            384 CACHE
            386 CACHE
            388 CACHE
            390 LOAD_CONST               8 ('r')
            392 LOAD_FAST                0 (self)
            394 LOAD_ATTR               13 (NULL|self + audit)
            414 CACHE
            416 CACHE
            418 CACHE
            420 LOAD_FAST                0 (self)
            422 STORE_ATTR              14 (file)

162         432 LOAD_FAST                0 (self)
            434 STORE_SUBSCR
            438 CACHE
            440 CACHE
            442 CACHE
            444 CACHE
            446 CACHE
            448 CACHE
            450 CACHE
            452 CACHE
            454 CACHE
            456 UNPACK_SEQUENCE          0
            460 CALL                     0
            468 CACHE
            470 LOAD_FAST                0 (self)
            472 STORE_ATTR              16 (welcome)

163         482 LOAD_FAST                0 (self)
            484 LOAD_ATTR               16 (create_connection)

Disassembly of <code object getwelcome at 0x000001B2A76B5A50, file "ftplib.py", line 165>:
165           0 RESUME                   0

168           2 LOAD_FAST                0 (self)
              4 LOAD_ATTR                0 (debugging)
             24 CACHE
             26 CACHE
             28 LOAD_CONST               1 ('*welcome*')
             30 LOAD_FAST                0 (self)
             32 STORE_SUBSCR
             36 CACHE
             38 CACHE
             40 CACHE
             42 CACHE
             44 CACHE
             46 CACHE
             48 CACHE
             50 CACHE
             52 CACHE
             54 LOAD_FAST                0 (self)
             56 LOAD_ATTR                3 (NULL|self + print)
             76 CACHE
             78 CACHE
             80 UNPACK_SEQUENCE          2
             84 CALL                     2
             92 CACHE
             94 POP_TOP

170          96 LOAD_FAST                0 (self)
             98 LOAD_ATTR                3 (NULL|self + print)

Disassembly of <code object set_debuglevel at 0x000001B2A768BBB0, file "ftplib.py", line 172>:
172           0 RESUME                   0

178           2 LOAD_FAST                1 (level)
              4 LOAD_FAST                0 (self)
              6 STORE_ATTR               0 (debugging)
             16 LOAD_CONST               1 (None)
             18 RETURN_VALUE

Disassembly of <code object set_pasv at 0x000001B2A768BAD0, file "ftplib.py", line 181>:
181           0 RESUME                   0

185           2 LOAD_FAST                1 (val)
              4 LOAD_FAST                0 (self)
              6 STORE_ATTR               0 (passiveserver)
             16 LOAD_CONST               1 (None)
             18 RETURN_VALUE

Disassembly of <code object sanitize at 0x000001B2A7212430, file "ftplib.py", line 188>:
188           0 RESUME                   0

189           2 LOAD_FAST                1 (s)
              4 LOAD_CONST               0 (None)
              6 LOAD_CONST               1 (5)
              8 BUILD_SLICE              2
             10 BINARY_SUBSCR
             14 CACHE
             16 CACHE
             18 CACHE
             20 LOAD_CONST               2 (frozenset({'pass ', 'PASS '}))
             22 CONTAINS_OP              0
             24 POP_JUMP_IF_FALSE       64 (to 154)

190          26 LOAD_GLOBAL              1 (NULL + len)
             36 CACHE
             38 LOAD_FAST                1 (s)
             40 STORE_SUBSCR
             44 CACHE
             46 CACHE
             48 CACHE
             50 CACHE
             52 CACHE
             54 CACHE
             56 CACHE
             58 CACHE
             60 CACHE
             62 LOAD_CONST               3 ('\r\n')
             64 UNPACK_SEQUENCE          1
             68 CALL                     1
             76 CACHE
             78 UNPACK_SEQUENCE          1
             82 CALL                     1
             90 CACHE
             92 STORE_FAST               2 (i)

191          94 LOAD_FAST                1 (s)
             96 LOAD_CONST               0 (None)
             98 LOAD_CONST               1 (5)
            100 BUILD_SLICE              2
            102 BINARY_SUBSCR
            106 CACHE
            108 CACHE
            110 CACHE
            112 LOAD_CONST               4 ('*')
            114 LOAD_FAST                2 (i)
            116 LOAD_CONST               1 (5)
            118 BINARY_OP               10 (-)
            122 BINARY_OP                5 (*)
            126 BINARY_OP                0 (+)
            130 LOAD_FAST                1 (s)
            132 LOAD_FAST                2 (i)
            134 LOAD_CONST               0 (None)
            136 BUILD_SLICE              2
            138 BINARY_SUBSCR
            142 CACHE
            144 CACHE
            146 CACHE
            148 BINARY_OP                0 (+)
            152 STORE_FAST               1 (s)

192     >>  154 LOAD_GLOBAL              5 (NULL + repr)
            164 CACHE
            166 LOAD_FAST                1 (s)
            168 UNPACK_SEQUENCE          1
            172 CALL                     1
            180 CACHE
            182 RETURN_VALUE

Disassembly of <code object putline at 0x000001B2A71BB630, file "ftplib.py", line 195>:
195           0 RESUME                   0

196           2 LOAD_CONST               1 ('\r')
              4 LOAD_FAST                1 (line)
              6 CONTAINS_OP              0
              8 POP_JUMP_IF_TRUE         4 (to 18)
             10 LOAD_CONST               2 ('\n')
             12 LOAD_FAST                1 (line)
             14 CONTAINS_OP              0
             16 POP_JUMP_IF_FALSE       15 (to 48)

197     >>   18 LOAD_GLOBAL              1 (NULL + ValueError)
             28 CACHE
             30 LOAD_CONST               3 ('an illegal newline character should not be contained')
             32 UNPACK_SEQUENCE          1
             36 CALL                     1
             44 CACHE
             46 RAISE_VARARGS            1

198     >>   48 LOAD_GLOBAL              3 (NULL + sys)
             58 CACHE
             60 LOAD_ATTR                2 (sys)
             80 CALL                     3
             88 CACHE
             90 POP_TOP

199          92 LOAD_FAST                1 (line)
             94 LOAD_GLOBAL              6 (CRLF)
            104 CACHE
            106 BINARY_OP                0 (+)
            110 STORE_FAST               1 (line)

200         112 LOAD_FAST                0 (self)
            114 LOAD_ATTR                4 (audit)

201         134 LOAD_GLOBAL             11 (NULL + print)
            144 CACHE
            146 LOAD_CONST               6 ('*put*')
            148 LOAD_FAST                0 (self)
            150 STORE_SUBSCR
            154 CACHE
            156 CACHE
            158 CACHE
            160 CACHE
            162 CACHE
            164 CACHE
            166 CACHE
            168 CACHE
            170 CACHE
            172 LOAD_FAST                1 (line)
            174 UNPACK_SEQUENCE          1
            178 CALL                     1
            186 CACHE
            188 UNPACK_SEQUENCE          2
            192 CALL                     2
            200 CACHE
            202 POP_TOP

202         204 LOAD_FAST                0 (self)
            206 LOAD_ATTR                7 (NULL|self + CRLF)
            226 CACHE
            228 CACHE
            230 CACHE
            232 CACHE
            234 CACHE
            236 CACHE
            238 LOAD_FAST                1 (line)
            240 STORE_SUBSCR
            244 CACHE
            246 CACHE
            248 CACHE
            250 CACHE
            252 CACHE
            254 CACHE
            256 CACHE
            258 CACHE
            260 CACHE
            262 LOAD_FAST                0 (self)
            264 LOAD_ATTR               10 (print)
            284 CACHE
            286 CACHE
            288 UNPACK_SEQUENCE          1
            292 CALL                     1
            300 CACHE
            302 POP_TOP
            304 LOAD_CONST               0 (None)
            306 RETURN_VALUE

Disassembly of <code object putcmd at 0x000001B2A71C6E20, file "ftplib.py", line 205>:
205           0 RESUME                   0

206           2 LOAD_FAST                0 (self)
              4 LOAD_ATTR                0 (debugging)
             24 CACHE
             26 CACHE
             28 LOAD_CONST               1 ('*cmd*')
             30 LOAD_FAST                0 (self)
             32 STORE_SUBSCR
             36 CACHE
             38 CACHE
             40 CACHE
             42 CACHE
             44 CACHE
             46 CACHE
             48 CACHE
             50 CACHE
             52 CACHE
             54 LOAD_FAST                1 (line)
             56 UNPACK_SEQUENCE          1
             60 CALL                     1
             68 CACHE
             70 UNPACK_SEQUENCE          2
             74 CALL                     2
             82 CACHE
             84 POP_TOP

207          86 LOAD_FAST                0 (self)
             88 STORE_SUBSCR
             92 CACHE
             94 CACHE
             96 CACHE
             98 CACHE
            100 CACHE
            102 CACHE
            104 CACHE
            106 CACHE
            108 CACHE
            110 LOAD_FAST                1 (line)
            112 UNPACK_SEQUENCE          1
            116 CALL                     1
            124 CACHE
            126 POP_TOP
            128 LOAD_CONST               0 (None)
            130 RETURN_VALUE

Disassembly of <code object getline at 0x000001B2A75B82C0, file "ftplib.py", line 211>:
211           0 RESUME                   0

212           2 LOAD_FAST                0 (self)
              4 LOAD_ATTR                0 (file)
             24 CACHE
             26 CACHE
             28 CACHE
             30 CACHE
             32 CACHE
             34 CACHE
             36 LOAD_FAST                0 (self)
             38 LOAD_ATTR                2 (readline)
             58 CALL                     1
             66 CACHE
             68 STORE_FAST               1 (line)

213          70 LOAD_GLOBAL              7 (NULL + len)
             80 CACHE
             82 LOAD_FAST                1 (line)
             84 UNPACK_SEQUENCE          1
             88 CALL                     1
             96 CACHE
             98 LOAD_FAST                0 (self)
            100 LOAD_ATTR                2 (readline)
            120 CACHE
            122 CACHE
            124 CACHE
            126 CACHE
            128 CACHE
            130 LOAD_CONST               2 ('got more than %d bytes')
            132 LOAD_FAST                0 (self)
            134 LOAD_ATTR                2 (readline)
            154 CACHE
            156 CACHE
            158 CACHE
            160 CACHE
            162 RAISE_VARARGS            1

215         164 LOAD_FAST                0 (self)
            166 LOAD_ATTR                5 (NULL|self + maxline)

216         186 LOAD_GLOBAL             13 (NULL + print)
            196 CACHE
            198 LOAD_CONST               3 ('*get*')
            200 LOAD_FAST                0 (self)
            202 STORE_SUBSCR
            206 CACHE
            208 CACHE
            210 CACHE
            212 CACHE
            214 CACHE
            216 CACHE
            218 CACHE
            220 CACHE
            222 CACHE
            224 LOAD_FAST                1 (line)
            226 UNPACK_SEQUENCE          1
            230 CALL                     1
            238 CACHE
            240 UNPACK_SEQUENCE          2
            244 CALL                     2
            252 CACHE
            254 POP_TOP

217         256 LOAD_FAST                1 (line)
            258 POP_JUMP_IF_TRUE         7 (to 274)

218         260 LOAD_GLOBAL             16 (EOFError)
            270 CACHE
            272 RAISE_VARARGS            1

219     >>  274 LOAD_FAST                1 (line)
            276 LOAD_CONST               4 (-2)
            278 LOAD_CONST               0 (None)
            280 BUILD_SLICE              2
            282 BINARY_SUBSCR
            286 CACHE
            288 CACHE
            290 CACHE
            292 LOAD_GLOBAL             18 (CRLF)
            302 CACHE
            304 COMPARE_OP               2 (<)
            308 CACHE
            310 POP_JUMP_IF_FALSE       11 (to 334)

220         312 LOAD_FAST                1 (line)
            314 LOAD_CONST               0 (None)
            316 LOAD_CONST               4 (-2)
            318 BUILD_SLICE              2
            320 BINARY_SUBSCR
            324 CACHE
            326 CACHE
            328 CACHE
            330 STORE_FAST               1 (line)
            332 JUMP_FORWARD            27 (to 388)

221     >>  334 LOAD_FAST                1 (line)
            336 LOAD_CONST               5 (-1)
            338 LOAD_CONST               0 (None)
            340 BUILD_SLICE              2
            342 BINARY_SUBSCR
            346 CACHE
            348 CACHE
            350 CACHE
            352 LOAD_GLOBAL             18 (CRLF)
            362 CACHE
            364 CONTAINS_OP              0
            366 POP_JUMP_IF_FALSE       10 (to 388)

222         368 LOAD_FAST                1 (line)
            370 LOAD_CONST               0 (None)
            372 LOAD_CONST               5 (-1)
            374 BUILD_SLICE              2
            376 BINARY_SUBSCR
            380 CACHE
            382 CACHE
            384 CACHE
            386 STORE_FAST               1 (line)

223     >>  388 LOAD_FAST                1 (line)
            390 RETURN_VALUE

Disassembly of <code object getmultiline at 0x000001B2A71CDD70, file "ftplib.py", line 229>:
229           0 RESUME                   0

230           2 LOAD_FAST                0 (self)
              4 STORE_SUBSCR
              8 CACHE
             10 CACHE
             12 CACHE
             14 CACHE
             16 CACHE
             18 CACHE
             20 CACHE
             22 CACHE
             24 CACHE
             26 UNPACK_SEQUENCE          0
             30 CALL                     0
             38 CACHE
             40 STORE_FAST               1 (line)

231          42 LOAD_FAST                1 (line)
             44 LOAD_CONST               1 (3)
             46 LOAD_CONST               2 (4)
             48 BUILD_SLICE              2
             50 BINARY_SUBSCR
             54 CACHE
             56 CACHE
             58 CACHE
             60 LOAD_CONST               3 ('-')
             62 COMPARE_OP               2 (<)
             66 CACHE
             68 POP_JUMP_IF_FALSE       69 (to 208)

232          70 LOAD_FAST                1 (line)
             72 LOAD_CONST               0 (None)
             74 LOAD_CONST               1 (3)
             76 BUILD_SLICE              2
             78 BINARY_SUBSCR
             82 CACHE
             84 CACHE
             86 CACHE
             88 STORE_FAST               2 (code)

233          90 NOP

234     >>   92 LOAD_FAST                0 (self)
             94 STORE_SUBSCR
             98 CACHE
            100 CACHE
            102 CACHE
            104 CACHE
            106 CACHE
            108 CACHE
            110 CACHE
            112 CACHE
            114 CACHE
            116 UNPACK_SEQUENCE          0
            120 CALL                     0
            128 CACHE
            130 STORE_FAST               3 (nextline)

235         132 LOAD_FAST                1 (line)
            134 LOAD_CONST               5 ('\n')
            136 LOAD_FAST                3 (nextline)
            138 BINARY_OP                0 (+)
            142 BINARY_OP                0 (+)
            146 STORE_FAST               1 (line)

236         148 LOAD_FAST                3 (nextline)
            150 LOAD_CONST               0 (None)
            152 LOAD_CONST               1 (3)
            154 BUILD_SLICE              2
            156 BINARY_SUBSCR
            160 CACHE
            162 CACHE
            164 CACHE
            166 LOAD_FAST                2 (code)
            168 COMPARE_OP               2 (<)
            172 CACHE
            174 POP_JUMP_IF_FALSE       15 (to 206)

237         176 LOAD_FAST                3 (nextline)
            178 LOAD_CONST               1 (3)
            180 LOAD_CONST               2 (4)
            182 BUILD_SLICE              2
            184 BINARY_SUBSCR
            188 CACHE
            190 CACHE
            192 CACHE
            194 LOAD_CONST               3 ('-')
            196 COMPARE_OP               3 (<)
            200 CACHE
            202 POP_JUMP_IF_FALSE        1 (to 206)

238         204 JUMP_FORWARD             1 (to 208)

233     >>  206 JUMP_BACKWARD           58 (to 92)

239     >>  208 LOAD_FAST                1 (line)
            210 RETURN_VALUE

Disassembly of <code object getresp at 0x000001B2A7251960, file "ftplib.py", line 243>:
243           0 RESUME                   0

244           2 LOAD_FAST                0 (self)
              4 STORE_SUBSCR
              8 CACHE
             10 CACHE
             12 CACHE
             14 CACHE
             16 CACHE
             18 CACHE
             20 CACHE
             22 CACHE
             24 CACHE
             26 UNPACK_SEQUENCE          0
             30 CALL                     0
             38 CACHE
             40 STORE_FAST               1 (resp)

245          42 LOAD_FAST                0 (self)
             44 LOAD_ATTR                1 (NULL|self + getmultiline)
             64 CACHE
             66 CACHE
             68 LOAD_CONST               1 ('*resp*')
             70 LOAD_FAST                0 (self)
             72 STORE_SUBSCR
             76 CACHE
             78 CACHE
             80 CACHE
             82 CACHE
             84 CACHE
             86 CACHE
             88 CACHE
             90 CACHE
             92 CACHE
             94 LOAD_FAST                1 (resp)
             96 UNPACK_SEQUENCE          1
            100 CALL                     1
            108 CACHE
            110 UNPACK_SEQUENCE          2
            114 CALL                     2
            122 CACHE
            124 POP_TOP

247         126 LOAD_FAST                1 (resp)
            128 LOAD_CONST               0 (None)
            130 LOAD_CONST               2 (3)
            132 BUILD_SLICE              2
            134 BINARY_SUBSCR
            138 CACHE
            140 CACHE
            142 CACHE
            144 LOAD_FAST                0 (self)
            146 STORE_ATTR               4 (lastresp)

248         156 LOAD_FAST                1 (resp)
            158 LOAD_CONST               0 (None)
            160 LOAD_CONST               3 (1)
            162 BUILD_SLICE              2
            164 BINARY_SUBSCR
            168 CACHE
            170 CACHE
            172 CACHE
            174 STORE_FAST               2 (c)

249         176 LOAD_FAST                2 (c)
            178 LOAD_CONST               4 (frozenset({'3', '1', '2'}))
            180 CONTAINS_OP              0
            182 POP_JUMP_IF_FALSE        2 (to 188)

250         184 LOAD_FAST                1 (resp)
            186 RETURN_VALUE

251     >>  188 LOAD_FAST                2 (c)
            190 LOAD_CONST               5 ('4')
            192 COMPARE_OP               2 (<)
            196 CACHE
            198 POP_JUMP_IF_FALSE       15 (to 230)

252         200 LOAD_GLOBAL             11 (NULL + error_temp)
            210 CACHE
            212 LOAD_FAST                1 (resp)
            214 UNPACK_SEQUENCE          1
            218 CALL                     1
            226 CACHE
            228 RAISE_VARARGS            1

253     >>  230 LOAD_FAST                2 (c)
            232 LOAD_CONST               6 ('5')
            234 COMPARE_OP               2 (<)
            238 CACHE
            240 POP_JUMP_IF_FALSE       15 (to 272)

254         242 LOAD_GLOBAL             13 (NULL + error_perm)
            252 CACHE
            254 LOAD_FAST                1 (resp)
            256 UNPACK_SEQUENCE          1
            260 CALL                     1
            268 CACHE
            270 RAISE_VARARGS            1

255     >>  272 LOAD_GLOBAL             15 (NULL + error_proto)
            282 CACHE
            284 LOAD_FAST                1 (resp)
            286 UNPACK_SEQUENCE          1
            290 CALL                     1
            298 CACHE
            300 RAISE_VARARGS            1

Disassembly of <code object voidresp at 0x000001B2A76B5DE0, file "ftplib.py", line 257>:
257           0 RESUME                   0

259           2 LOAD_FAST                0 (self)
              4 STORE_SUBSCR
              8 CACHE
             10 CACHE
             12 CACHE
             14 CACHE
             16 CACHE
             18 CACHE
             20 CACHE
             22 CACHE
             24 CACHE
             26 UNPACK_SEQUENCE          0
             30 CALL                     0
             38 CACHE
             40 STORE_FAST               1 (resp)

260          42 LOAD_FAST                1 (resp)
             44 LOAD_CONST               1 (None)
             46 LOAD_CONST               2 (1)
             48 BUILD_SLICE              2
             50 BINARY_SUBSCR
             54 CACHE
             56 CACHE
             58 CACHE
             60 LOAD_CONST               3 ('2')
             62 COMPARE_OP               3 (<)
             66 CACHE
             68 POP_JUMP_IF_FALSE       15 (to 100)

261          70 LOAD_GLOBAL              3 (NULL + error_reply)
             80 CACHE
             82 LOAD_FAST                1 (resp)
             84 UNPACK_SEQUENCE          1
             88 CALL                     1
             96 CACHE
             98 RAISE_VARARGS            1

262     >>  100 LOAD_FAST                1 (resp)
            102 RETURN_VALUE

Disassembly of <code object abort at 0x000001B2A7219C50, file "ftplib.py", line 264>:
264           0 RESUME                   0

269           2 LOAD_CONST               1 (b'ABOR')
              4 LOAD_GLOBAL              0 (B_CRLF)
             14 CACHE
             16 BINARY_OP                0 (+)
             20 STORE_FAST               1 (line)

270          22 LOAD_FAST                0 (self)
             24 LOAD_ATTR                1 (NULL|self + B_CRLF)

271          44 LOAD_GLOBAL              5 (NULL + print)
             54 CACHE
             56 LOAD_CONST               3 ('*put urgent*')
             58 LOAD_FAST                0 (self)
             60 STORE_SUBSCR
             64 CACHE
             66 CACHE
             68 CACHE
             70 CACHE
             72 CACHE
             74 CACHE
             76 CACHE
             78 CACHE
             80 CACHE
             82 LOAD_FAST                1 (line)
             84 UNPACK_SEQUENCE          1
             88 CALL                     1
             96 CACHE
             98 UNPACK_SEQUENCE          2
            102 CALL                     2
            110 CACHE
            112 POP_TOP

272         114 LOAD_FAST                0 (self)
            116 LOAD_ATTR                4 (print)
            136 CACHE
            138 CACHE
            140 CACHE
            142 CACHE
            144 CACHE
            146 CACHE
            148 LOAD_FAST                1 (line)
            150 LOAD_GLOBAL             12 (MSG_OOB)
            160 CACHE
            162 UNPACK_SEQUENCE          2
            166 CALL                     2
            174 CACHE
            176 POP_TOP

273         178 LOAD_FAST                0 (self)
            180 STORE_SUBSCR
            184 CACHE
            186 CACHE
            188 CACHE
            190 CACHE
            192 CACHE
            194 CACHE
            196 CACHE
            198 CACHE
            200 CACHE
            202 UNPACK_SEQUENCE          0
            206 CALL                     0
            214 CACHE
            216 STORE_FAST               2 (resp)

274         218 LOAD_FAST                2 (resp)
            220 LOAD_CONST               4 (None)
            222 LOAD_CONST               5 (3)
            224 BUILD_SLICE              2
            226 BINARY_SUBSCR
            230 CACHE
            232 CACHE
            234 CACHE
            236 LOAD_CONST               6 (frozenset({'426', '226', '225'}))
            238 CONTAINS_OP              1
            240 POP_JUMP_IF_FALSE       15 (to 272)

275         242 LOAD_GLOBAL             17 (NULL + error_proto)
            252 CACHE
            254 LOAD_FAST                2 (resp)
            256 UNPACK_SEQUENCE          1
            260 CALL                     1
            268 CACHE
            270 RAISE_VARARGS            1

276     >>  272 LOAD_FAST                2 (resp)
            274 RETURN_VALUE

Disassembly of <code object sendcmd at 0x000001B2A76DB870, file "ftplib.py", line 278>:
278           0 RESUME                   0

280           2 LOAD_FAST                0 (self)
              4 STORE_SUBSCR
              8 CACHE
             10 CACHE
             12 CACHE
             14 CACHE
             16 CACHE
             18 CACHE
             20 CACHE
             22 CACHE
             24 CACHE
             26 LOAD_FAST                1 (cmd)
             28 UNPACK_SEQUENCE          1
             32 CALL                     1
             40 CACHE
             42 POP_TOP

281          44 LOAD_FAST                0 (self)
             46 STORE_SUBSCR
             50 CACHE
             52 CACHE
             54 CACHE
             56 CACHE
             58 CACHE
             60 CACHE
             62 CACHE
             64 CACHE
             66 CACHE
             68 UNPACK_SEQUENCE          0
             72 CALL                     0
             80 CACHE
             82 RETURN_VALUE

Disassembly of <code object voidcmd at 0x000001B2A76DBAB0, file "ftplib.py", line 283>:
283           0 RESUME                   0

285           2 LOAD_FAST                0 (self)
              4 STORE_SUBSCR
              8 CACHE
             10 CACHE
             12 CACHE
             14 CACHE
             16 CACHE
             18 CACHE
             20 CACHE
             22 CACHE
             24 CACHE
             26 LOAD_FAST                1 (cmd)
             28 UNPACK_SEQUENCE          1
             32 CALL                     1
             40 CACHE
             42 POP_TOP

286          44 LOAD_FAST                0 (self)
             46 STORE_SUBSCR
             50 CACHE
             52 CACHE
             54 CACHE
             56 CACHE
             58 CACHE
             60 CACHE
             62 CACHE
             64 CACHE
             66 CACHE
             68 UNPACK_SEQUENCE          0
             72 CALL                     0
             80 CACHE
             82 RETURN_VALUE

Disassembly of <code object sendport at 0x000001B2A71CEDB0, file "ftplib.py", line 288>:
288           0 RESUME                   0

292           2 LOAD_FAST                1 (host)
              4 STORE_SUBSCR
              8 CACHE
             10 CACHE
             12 CACHE
             14 CACHE
             16 CACHE
             18 CACHE
             20 CACHE
             22 CACHE
             24 CACHE
             26 LOAD_CONST               1 ('.')
             28 UNPACK_SEQUENCE          1
             32 CALL                     1
             40 CACHE
             42 STORE_FAST               3 (hbytes)

293          44 LOAD_GLOBAL              3 (NULL + repr)
             54 CACHE
             56 LOAD_FAST                2 (port)
             58 LOAD_CONST               2 (256)
             60 BINARY_OP                2 (//)
             64 UNPACK_SEQUENCE          1
             68 CALL                     1
             76 CACHE
             78 LOAD_GLOBAL              3 (NULL + repr)
             88 CACHE
             90 LOAD_FAST                2 (port)
             92 LOAD_CONST               2 (256)
             94 BINARY_OP                6 (%)
             98 UNPACK_SEQUENCE          1
            102 CALL                     1
            110 CACHE
            112 BUILD_LIST               2
            114 STORE_FAST               4 (pbytes)

294         116 LOAD_FAST                3 (hbytes)
            118 LOAD_FAST                4 (pbytes)
            120 BINARY_OP                0 (+)
            124 STORE_FAST               5 (bytes)

295         126 LOAD_CONST               3 ('PORT ')
            128 LOAD_CONST               4 (',')
            130 STORE_SUBSCR
            134 CACHE
            136 CACHE
            138 CACHE
            140 CACHE
            142 CACHE
            144 CACHE
            146 CACHE
            148 CACHE
            150 CACHE
            152 LOAD_FAST                5 (bytes)
            154 UNPACK_SEQUENCE          1
            158 CALL                     1
            166 CACHE
            168 BINARY_OP                0 (+)
            172 STORE_FAST               6 (cmd)

296         174 LOAD_FAST                0 (self)
            176 STORE_SUBSCR
            180 CACHE
            182 CACHE
            184 CACHE
            186 CACHE
            188 CACHE
            190 CACHE
            192 CACHE
            194 CACHE
            196 CACHE
            198 LOAD_FAST                6 (cmd)
            200 UNPACK_SEQUENCE          1
            204 CALL                     1
            212 CACHE
            214 RETURN_VALUE

Disassembly of <code object sendeprt at 0x000001B2A7250BD0, file "ftplib.py", line 298>:
298           0 RESUME                   0

300           2 LOAD_CONST               1 (0)
              4 STORE_FAST               3 (af)

301           6 LOAD_FAST                0 (self)
              8 LOAD_ATTR                0 (af)
             28 CACHE
             30 LOAD_ATTR                2 (socket)
             50 STORE_FAST               3 (af)

303          52 LOAD_FAST                0 (self)
             54 LOAD_ATTR                0 (af)
             74 CACHE
             76 LOAD_ATTR                3 (NULL|self + socket)
             96 STORE_FAST               3 (af)

305          98 LOAD_FAST                3 (af)
            100 LOAD_CONST               1 (0)
            102 COMPARE_OP               2 (<)
            106 CACHE
            108 POP_JUMP_IF_FALSE       15 (to 140)

306         110 LOAD_GLOBAL              9 (NULL + error_proto)
            120 CACHE
            122 LOAD_CONST               4 ('unsupported address family')
            124 UNPACK_SEQUENCE          1
            128 CALL                     1
            136 CACHE
            138 RAISE_VARARGS            1

307     >>  140 LOAD_CONST               5 ('')
            142 LOAD_GLOBAL             11 (NULL + repr)
            152 CACHE
            154 LOAD_FAST                3 (af)
            156 UNPACK_SEQUENCE          1
            160 CALL                     1
            168 CACHE
            170 LOAD_FAST                1 (host)
            172 LOAD_GLOBAL             11 (NULL + repr)
            182 CACHE
            184 LOAD_FAST                2 (port)
            186 UNPACK_SEQUENCE          1
            190 CALL                     1
            198 CACHE
            200 LOAD_CONST               5 ('')
            202 BUILD_LIST               5
            204 STORE_FAST               4 (fields)

308         206 LOAD_CONST               6 ('EPRT ')
            208 LOAD_CONST               7 ('|')
            210 STORE_SUBSCR
            214 CACHE
            216 CACHE
            218 CACHE
            220 CACHE
            222 CACHE
            224 CACHE
            226 CACHE
            228 CACHE
            230 CACHE
            232 LOAD_FAST                4 (fields)
            234 UNPACK_SEQUENCE          1
            238 CALL                     1
            246 CACHE
            248 BINARY_OP                0 (+)
            252 STORE_FAST               5 (cmd)

309         254 LOAD_FAST                0 (self)
            256 STORE_SUBSCR
            260 CACHE
            262 CACHE
            264 CACHE
            266 CACHE
            268 CACHE
            270 CACHE
            272 CACHE
            274 CACHE
            276 CACHE
            278 LOAD_FAST                5 (cmd)
            280 UNPACK_SEQUENCE          1
            284 CALL                     1
            292 CACHE
            294 RETURN_VALUE

Disassembly of <code object makeport at 0x000001B2A75B8510, file "ftplib.py", line 311>:
311           0 RESUME                   0

313           2 LOAD_GLOBAL              1 (NULL + socket)
             12 CACHE
             14 LOAD_ATTR                1 (NULL|self + socket)
             34 CACHE
             36 CACHE
             38 LOAD_CONST               2 (1)
             40 KW_NAMES                 3 (('family', 'backlog'))
             42 UNPACK_SEQUENCE          3
             46 CALL                     3
             54 CACHE
             56 STORE_FAST               1 (sock)

314          58 LOAD_FAST                1 (sock)
             60 STORE_SUBSCR
             64 CACHE
             66 CACHE
             68 CACHE
             70 CACHE
             72 CACHE
             74 CACHE
             76 CACHE
             78 CACHE
             80 CACHE
             82 UNPACK_SEQUENCE          0
             86 CALL                     0
             94 CACHE
             96 LOAD_CONST               2 (1)
             98 BINARY_SUBSCR
            102 CACHE
            104 CACHE
            106 CACHE
            108 STORE_FAST               2 (port)

315         110 LOAD_FAST                0 (self)
            112 LOAD_ATTR                4 (af)
            132 CACHE
            134 CACHE
            136 CACHE
            138 CACHE
            140 CACHE
            142 CACHE
            144 UNPACK_SEQUENCE          0
            148 CALL                     0
            156 CACHE
            158 LOAD_CONST               4 (0)
            160 BINARY_SUBSCR
            164 CACHE
            166 CACHE
            168 CACHE
            170 STORE_FAST               3 (host)

316         172 LOAD_FAST                0 (self)
            174 LOAD_ATTR                2 (create_server)
            194 CACHE
            196 LOAD_ATTR                5 (NULL|self + af)
            216 STORE_SUBSCR
            220 CACHE
            222 CACHE
            224 CACHE
            226 CACHE
            228 CACHE
            230 CACHE
            232 CACHE
            234 CACHE
            236 CACHE
            238 LOAD_FAST                3 (host)
            240 LOAD_FAST                2 (port)
            242 UNPACK_SEQUENCE          2
            246 CALL                     2
            254 CACHE
            256 STORE_FAST               4 (resp)
            258 JUMP_FORWARD            22 (to 304)

319         260 LOAD_FAST                0 (self)
            262 STORE_SUBSCR
            266 CACHE
            268 CACHE
            270 CACHE
            272 CACHE
            274 CACHE
            276 CACHE
            278 CACHE
            280 CACHE
            282 CACHE
            284 LOAD_FAST                3 (host)
            286 LOAD_FAST                2 (port)
            288 UNPACK_SEQUENCE          2
            292 CALL                     2
            300 CACHE
            302 STORE_FAST               4 (resp)

320     >>  304 LOAD_FAST                0 (self)
            306 LOAD_ATTR                8 (sock)
            326 CACHE
            328 IS_OP                    1
            330 POP_JUMP_IF_FALSE       26 (to 384)

321         332 LOAD_FAST                1 (sock)
            334 STORE_SUBSCR
            338 CACHE
            340 CACHE
            342 CACHE
            344 CACHE
            346 CACHE
            348 CACHE
            350 CACHE
            352 CACHE
            354 CACHE
            356 LOAD_FAST                0 (self)
            358 LOAD_ATTR                8 (sock)
            378 CACHE
            380 CACHE
            382 POP_TOP

322     >>  384 LOAD_FAST                1 (sock)
            386 RETURN_VALUE

Disassembly of <code object makepasv at 0x000001B2A508AA90, file "ftplib.py", line 324>:
324           0 RESUME                   0

326           2 LOAD_FAST                0 (self)
              4 LOAD_ATTR                0 (af)
             24 CACHE
             26 LOAD_ATTR                2 (socket)
             46 CACHE
             48 CACHE
             50 CACHE
             52 CACHE
             54 CACHE
             56 LOAD_FAST                0 (self)
             58 STORE_SUBSCR
             62 CACHE
             64 CACHE
             66 CACHE
             68 CACHE
             70 CACHE
             72 CACHE
             74 CACHE
             76 CACHE
             78 CACHE
             80 LOAD_CONST               1 ('PASV')
             82 UNPACK_SEQUENCE          1
             86 CALL                     1
             94 CACHE
             96 UNPACK_SEQUENCE          1
            100 CALL                     1
            108 CACHE
            110 UNPACK_SEQUENCE          2
            114 STORE_FAST               1 (untrusted_host)
            116 STORE_FAST               2 (port)

328         118 LOAD_FAST                0 (self)
            120 LOAD_ATTR                5 (NULL|self + AF_INET)
            140 LOAD_ATTR                6 (parse227)
            160 CACHE
            162 CACHE
            164 CACHE
            166 CACHE
            168 CACHE
            170 CACHE
            172 UNPACK_SEQUENCE          0
            176 CALL                     0
            184 CACHE
            186 LOAD_CONST               2 (0)
            188 BINARY_SUBSCR
            192 CACHE
            194 CACHE
            196 CACHE
            198 STORE_FAST               3 (host)
            200 JUMP_FORWARD            61 (to 324)

333         202 LOAD_GLOBAL             17 (NULL + parse229)
            212 CACHE
            214 LOAD_FAST                0 (self)
            216 STORE_SUBSCR
            220 CACHE
            222 CACHE
            224 CACHE
            226 CACHE
            228 CACHE
            230 CACHE
            232 CACHE
            234 CACHE
            236 CACHE
            238 LOAD_CONST               3 ('EPSV')
            240 UNPACK_SEQUENCE          1
            244 CALL                     1
            252 CACHE
            254 LOAD_FAST                0 (self)
            256 LOAD_ATTR                6 (parse227)
            276 CACHE
            278 CACHE
            280 CACHE
            282 CACHE
            284 CACHE
            286 CACHE
            288 UNPACK_SEQUENCE          0
            292 CALL                     0
            300 CACHE
            302 UNPACK_SEQUENCE          2
            306 CALL                     2
            314 CACHE
            316 UNPACK_SEQUENCE          2
            320 STORE_FAST               3 (host)
            322 STORE_FAST               2 (port)

334     >>  324 LOAD_FAST                3 (host)
            326 LOAD_FAST                2 (port)
            328 BUILD_TUPLE              2
            330 RETURN_VALUE

Disassembly of <code object ntransfercmd at 0x000001B2A75B4140, file "ftplib.py", line 336>:
336           0 RESUME                   0

351           2 LOAD_CONST               1 (None)
              4 STORE_FAST               3 (size)

352           6 LOAD_FAST                0 (self)
              8 LOAD_ATTR                0 (passiveserver)
             28 CACHE
             30 CACHE
             32 CACHE
             34 CACHE
             36 CACHE
             38 CACHE
             40 CACHE
             42 CACHE
             44 UNPACK_SEQUENCE          0
             48 CALL                     0
             56 CACHE
             58 UNPACK_SEQUENCE          2
             62 STORE_FAST               4 (host)
             64 STORE_FAST               5 (port)

354          66 LOAD_GLOBAL              5 (NULL + socket)
             76 CACHE
             78 LOAD_ATTR                3 (NULL|self + makepasv)
             98 CACHE
            100 CACHE
            102 CACHE
            104 CACHE

355         106 LOAD_FAST                0 (self)
            108 LOAD_ATTR                5 (NULL|self + socket)
            128 CACHE
            130 CACHE
            132 CACHE
            134 STORE_FAST               6 (conn)

356         136 NOP

357         138 LOAD_FAST                2 (rest)
            140 POP_JUMP_IF_NONE        24 (to 190)

358         142 LOAD_FAST                0 (self)
            144 STORE_SUBSCR
            148 CACHE
            150 CACHE
            152 CACHE
            154 CACHE
            156 CACHE
            158 CACHE
            160 CACHE
            162 CACHE
            164 CACHE
            166 LOAD_CONST               3 ('REST %s')
            168 LOAD_FAST                2 (rest)
            170 BINARY_OP                6 (%)
            174 UNPACK_SEQUENCE          1
            178 CALL                     1
            186 CACHE
            188 POP_TOP

359     >>  190 LOAD_FAST                0 (self)
            192 STORE_SUBSCR
            196 CACHE
            198 CACHE
            200 CACHE
            202 CACHE
            204 CACHE
            206 CACHE
            208 CACHE
            210 CACHE
            212 CACHE
            214 LOAD_FAST                1 (cmd)
            216 UNPACK_SEQUENCE          1
            220 CALL                     1
            228 CACHE
            230 STORE_FAST               7 (resp)

366         232 LOAD_FAST                7 (resp)
            234 LOAD_CONST               4 (0)
            236 BINARY_SUBSCR
            240 CACHE
            242 CACHE
            244 CACHE
            246 LOAD_CONST               5 ('2')
            248 COMPARE_OP               2 (<)
            252 CACHE
            254 POP_JUMP_IF_FALSE       20 (to 296)

367         256 LOAD_FAST                0 (self)
            258 STORE_SUBSCR
            262 CACHE
            264 CACHE
            266 CACHE
            268 CACHE
            270 CACHE
            272 CACHE
            274 CACHE
            276 CACHE
            278 CACHE
            280 UNPACK_SEQUENCE          0
            284 CALL                     0
            292 CACHE
            294 STORE_FAST               7 (resp)

368     >>  296 LOAD_FAST                7 (resp)
            298 LOAD_CONST               4 (0)
            300 BINARY_SUBSCR
            304 CACHE
            306 CACHE
            308 CACHE
            310 LOAD_CONST               6 ('1')
            312 COMPARE_OP               3 (<)
            316 CACHE
            318 POP_JUMP_IF_FALSE       15 (to 350)

369         320 LOAD_GLOBAL             17 (NULL + error_reply)
            330 CACHE
            332 LOAD_FAST                7 (resp)
            334 UNPACK_SEQUENCE          1
            338 CALL                     1
            346 CACHE
            348 RAISE_VARARGS            1

368     >>  350 JUMP_FORWARD           239 (to 830)
        >>  352 PUSH_EXC_INFO

370         354 POP_TOP

371         356 LOAD_FAST                6 (conn)
            358 STORE_SUBSCR
            362 CACHE
            364 CACHE
            366 CACHE
            368 CACHE
            370 CACHE
            372 CACHE
            374 CACHE
            376 CACHE
            378 CACHE
            380 UNPACK_SEQUENCE          0
            384 CALL                     0
            392 CACHE
            394 POP_TOP

372         396 RAISE_VARARGS            0
        >>  398 COPY                     3
            400 POP_EXCEPT
            402 RERAISE                  1

374         404 LOAD_FAST                0 (self)
            406 STORE_SUBSCR
            410 CACHE
            412 CACHE
            414 CACHE
            416 CACHE
            418 CACHE
            420 CACHE
            422 CACHE
            424 CACHE
            426 CACHE
            428 UNPACK_SEQUENCE          0
            432 CALL                     0
            440 CACHE
            442 BEFORE_WITH
            444 STORE_FAST               8 (sock)

375         446 LOAD_FAST                2 (rest)
            448 POP_JUMP_IF_NONE        24 (to 498)

376         450 LOAD_FAST                0 (self)
            452 STORE_SUBSCR
            456 CACHE
            458 CACHE
            460 CACHE
            462 CACHE
            464 CACHE
            466 CACHE
            468 CACHE
            470 CACHE
            472 CACHE
            474 LOAD_CONST               3 ('REST %s')
            476 LOAD_FAST                2 (rest)
            478 BINARY_OP                6 (%)
            482 UNPACK_SEQUENCE          1
            486 CALL                     1
            494 CACHE
            496 POP_TOP

377     >>  498 LOAD_FAST                0 (self)
            500 STORE_SUBSCR
            504 CACHE
            506 CACHE
            508 CACHE
            510 CACHE
            512 CACHE
            514 CACHE
            516 CACHE
            518 CACHE
            520 CACHE
            522 LOAD_FAST                1 (cmd)
            524 UNPACK_SEQUENCE          1
            528 CALL                     1
            536 CACHE
            538 STORE_FAST               7 (resp)

379         540 LOAD_FAST                7 (resp)
            542 LOAD_CONST               4 (0)
            544 BINARY_SUBSCR
            548 CACHE
            550 CACHE
            552 CACHE
            554 LOAD_CONST               5 ('2')
            556 COMPARE_OP               2 (<)
            560 CACHE
            562 POP_JUMP_IF_FALSE       20 (to 604)

380         564 LOAD_FAST                0 (self)
            566 STORE_SUBSCR
            570 CACHE
            572 CACHE
            574 CACHE
            576 CACHE
            578 CACHE
            580 CACHE
            582 CACHE
            584 CACHE
            586 CACHE
            588 UNPACK_SEQUENCE          0
            592 CALL                     0
            600 CACHE
            602 STORE_FAST               7 (resp)

381     >>  604 LOAD_FAST                7 (resp)
            606 LOAD_CONST               4 (0)
            608 BINARY_SUBSCR
            612 CACHE
            614 CACHE
            616 CACHE
            618 LOAD_CONST               6 ('1')
            620 COMPARE_OP               3 (<)
            624 CACHE
            626 POP_JUMP_IF_FALSE       15 (to 658)

382         628 LOAD_GLOBAL             17 (NULL + error_reply)
            638 CACHE
            640 LOAD_FAST                7 (resp)
            642 UNPACK_SEQUENCE          1
            646 CALL                     1
            654 CACHE
            656 RAISE_VARARGS            1

383     >>  658 LOAD_FAST                8 (sock)
            660 STORE_SUBSCR
            664 CACHE
            666 CACHE
            668 CACHE
            670 CACHE
            672 CACHE
            674 CACHE
            676 CACHE
            678 CACHE
            680 CACHE
            682 UNPACK_SEQUENCE          0
            686 CALL                     0
            694 CACHE
            696 UNPACK_SEQUENCE          2
            700 STORE_FAST               6 (conn)
            702 STORE_FAST               9 (sockaddr)

384         704 LOAD_FAST                0 (self)
            706 LOAD_ATTR                4 (socket)
            726 CACHE
            728 IS_OP                    1
            730 POP_JUMP_IF_FALSE       26 (to 784)

385         732 LOAD_FAST                6 (conn)
            734 STORE_SUBSCR
            738 CACHE
            740 CACHE
            742 CACHE
            744 CACHE
            746 CACHE
            748 CACHE
            750 CACHE
            752 CACHE
            754 CACHE
            756 LOAD_FAST                0 (self)
            758 LOAD_ATTR                4 (socket)
            778 CACHE
            780 CACHE
            782 POP_TOP

374     >>  784 LOAD_CONST               1 (None)
            786 LOAD_CONST               1 (None)
            788 LOAD_CONST               1 (None)
            790 UNPACK_SEQUENCE          2
            794 CALL                     2
            802 CACHE
            804 POP_TOP
            806 JUMP_FORWARD            11 (to 830)
        >>  808 PUSH_EXC_INFO
            810 WITH_EXCEPT_START
            812 POP_JUMP_IF_TRUE         4 (to 822)
            814 RERAISE                  2
        >>  816 COPY                     3
            818 POP_EXCEPT
            820 RERAISE                  1
        >>  822 POP_TOP
            824 POP_EXCEPT
            826 POP_TOP
            828 POP_TOP

386     >>  830 LOAD_FAST                7 (resp)
            832 LOAD_CONST               1 (None)
            834 LOAD_CONST               7 (3)
            836 BUILD_SLICE              2
            838 BINARY_SUBSCR
            842 CACHE
            844 CACHE
            846 CACHE
            848 LOAD_CONST               8 ('150')
            850 COMPARE_OP               2 (<)
            854 CACHE
            856 POP_JUMP_IF_FALSE       15 (to 888)

388         858 LOAD_GLOBAL             29 (NULL + parse150)
            868 CACHE
            870 LOAD_FAST                7 (resp)
            872 UNPACK_SEQUENCE          1
            876 CALL                     1
            884 CACHE
            886 STORE_FAST               3 (size)

389     >>  888 LOAD_FAST                6 (conn)
            890 LOAD_FAST                3 (size)
            892 BUILD_TUPLE              2
            894 RETURN_VALUE
ExceptionTable:
  138 to 348 -> 352 [0]
  352 to 396 -> 398 [1] lasti
  444 to 782 -> 808 [1] lasti
  808 to 814 -> 816 [3] lasti
  822 to 822 -> 816 [3] lasti

Disassembly of <code object transfercmd at 0x000001B2A8A0DB30, file "ftplib.py", line 391>:
391           0 RESUME                   0

393           2 LOAD_FAST                0 (self)
              4 STORE_SUBSCR
              8 CACHE
             10 CACHE
             12 CACHE
             14 CACHE
             16 CACHE
             18 CACHE
             20 CACHE
             22 CACHE
             24 CACHE
             26 LOAD_FAST                1 (cmd)
             28 LOAD_FAST                2 (rest)
             30 UNPACK_SEQUENCE          2
             34 CALL                     2
             42 CACHE
             44 LOAD_CONST               1 (0)
             46 BINARY_SUBSCR
             50 CACHE
             52 CACHE
             54 CACHE
             56 RETURN_VALUE

Disassembly of <code object login at 0x000001B2A8A1C030, file "ftplib.py", line 395>:
395           0 RESUME                   0

397           2 LOAD_FAST                1 (user)
              4 POP_JUMP_IF_TRUE         2 (to 10)

398           6 LOAD_CONST               1 ('anonymous')
              8 STORE_FAST               1 (user)

399     >>   10 LOAD_FAST                2 (passwd)
             12 POP_JUMP_IF_TRUE         2 (to 18)

400          14 LOAD_CONST               2 ('')
             16 STORE_FAST               2 (passwd)

401     >>   18 LOAD_FAST                3 (acct)
             20 POP_JUMP_IF_TRUE         2 (to 26)

402          22 LOAD_CONST               2 ('')
             24 STORE_FAST               3 (acct)

403     >>   26 LOAD_FAST                1 (user)
             28 LOAD_CONST               1 ('anonymous')
             30 COMPARE_OP               2 (<)
             34 CACHE
             36 POP_JUMP_IF_FALSE        9 (to 56)
             38 LOAD_FAST                2 (passwd)
             40 LOAD_CONST               3 (frozenset({'', '-'}))
             42 CONTAINS_OP              0
             44 POP_JUMP_IF_FALSE        5 (to 56)

411          46 LOAD_FAST                2 (passwd)
             48 LOAD_CONST               4 ('anonymous@')
             50 BINARY_OP                0 (+)
             54 STORE_FAST               2 (passwd)

412     >>   56 LOAD_FAST                0 (self)
             58 STORE_SUBSCR
             62 CACHE
             64 CACHE
             66 CACHE
             68 CACHE
             70 CACHE
             72 CACHE
             74 CACHE
             76 CACHE
             78 CACHE
             80 LOAD_CONST               5 ('USER ')
             82 LOAD_FAST                1 (user)
             84 BINARY_OP                0 (+)
             88 UNPACK_SEQUENCE          1
             92 CALL                     1
            100 CACHE
            102 STORE_FAST               4 (resp)

413         104 LOAD_FAST                4 (resp)
            106 LOAD_CONST               6 (0)
            108 BINARY_SUBSCR
            112 CACHE
            114 CACHE
            116 CACHE
            118 LOAD_CONST               7 ('3')
            120 COMPARE_OP               2 (<)
            124 CACHE
            126 POP_JUMP_IF_FALSE       24 (to 176)

414         128 LOAD_FAST                0 (self)
            130 STORE_SUBSCR
            134 CACHE
            136 CACHE
            138 CACHE
            140 CACHE
            142 CACHE
            144 CACHE
            146 CACHE
            148 CACHE
            150 CACHE
            152 LOAD_CONST               8 ('PASS ')
            154 LOAD_FAST                2 (passwd)
            156 BINARY_OP                0 (+)
            160 UNPACK_SEQUENCE          1
            164 CALL                     1
            172 CACHE
            174 STORE_FAST               4 (resp)

415     >>  176 LOAD_FAST                4 (resp)
            178 LOAD_CONST               6 (0)
            180 BINARY_SUBSCR
            184 CACHE
            186 CACHE
            188 CACHE
            190 LOAD_CONST               7 ('3')
            192 COMPARE_OP               2 (<)
            196 CACHE
            198 POP_JUMP_IF_FALSE       24 (to 248)

416         200 LOAD_FAST                0 (self)
            202 STORE_SUBSCR
            206 CACHE
            208 CACHE
            210 CACHE
            212 CACHE
            214 CACHE
            216 CACHE
            218 CACHE
            220 CACHE
            222 CACHE
            224 LOAD_CONST               9 ('ACCT ')
            226 LOAD_FAST                3 (acct)
            228 BINARY_OP                0 (+)
            232 UNPACK_SEQUENCE          1
            236 CALL                     1
            244 CACHE
            246 STORE_FAST               4 (resp)

417     >>  248 LOAD_FAST                4 (resp)
            250 LOAD_CONST               6 (0)
            252 BINARY_SUBSCR
            256 CACHE
            258 CACHE
            260 CACHE
            262 LOAD_CONST              10 ('2')
            264 COMPARE_OP               3 (<)
            268 CACHE
            270 POP_JUMP_IF_FALSE       15 (to 302)

418         272 LOAD_GLOBAL              3 (NULL + error_reply)
            282 CACHE
            284 LOAD_FAST                4 (resp)
            286 UNPACK_SEQUENCE          1
            290 CALL                     1
            298 CACHE
            300 RAISE_VARARGS            1

419     >>  302 LOAD_FAST                4 (resp)
            304 RETURN_VALUE

Disassembly of <code object retrbinary at 0x000001B2A75D5260, file "ftplib.py", line 421>:
421           0 RESUME                   0

435           2 LOAD_FAST                0 (self)
              4 STORE_SUBSCR
              8 CACHE
             10 CACHE
             12 CACHE
             14 CACHE
             16 CACHE
             18 CACHE
             20 CACHE
             22 CACHE
             24 CACHE
             26 LOAD_CONST               1 ('TYPE I')
             28 UNPACK_SEQUENCE          1
             32 CALL                     1
             40 CACHE
             42 POP_TOP

436          44 LOAD_FAST                0 (self)
             46 STORE_SUBSCR
             50 CACHE
             52 CACHE
             54 CACHE
             56 CACHE
             58 CACHE
             60 CACHE
             62 CACHE
             64 CACHE
             66 CACHE
             68 LOAD_FAST                1 (cmd)
             70 LOAD_FAST                4 (rest)
             72 UNPACK_SEQUENCE          2
             76 CALL                     2
             84 CACHE
             86 BEFORE_WITH
             88 STORE_FAST               5 (conn)

437          90 NOP

438     >>   92 LOAD_FAST                5 (conn)
             94 STORE_SUBSCR
             98 CACHE
            100 CACHE
            102 CACHE
            104 CACHE
            106 CACHE
            108 CACHE
            110 CACHE
            112 CACHE
            114 CACHE
            116 LOAD_FAST                3 (blocksize)
            118 UNPACK_SEQUENCE          1
            122 CALL                     1
            130 CACHE
            132 STORE_FAST               6 (data)

439         134 LOAD_FAST                6 (data)
            136 POP_JUMP_IF_TRUE         1 (to 140)

440         138 JUMP_FORWARD            12 (to 164)

441     >>  140 PUSH_NULL
            142 LOAD_FAST                2 (callback)
            144 LOAD_FAST                6 (data)
            146 UNPACK_SEQUENCE          1
            150 CALL                     1
            158 CACHE
            160 POP_TOP

437         162 JUMP_BACKWARD           36 (to 92)

443     >>  164 LOAD_GLOBAL              6 (_SSLSocket)
            174 CACHE
            176 POP_JUMP_IF_NONE        41 (to 260)
            178 LOAD_GLOBAL              9 (NULL + isinstance)
            188 CACHE
            190 LOAD_FAST                5 (conn)
            192 LOAD_GLOBAL              6 (_SSLSocket)
            202 CACHE
            204 UNPACK_SEQUENCE          2
            208 CALL                     2
            216 CACHE
            218 POP_JUMP_IF_FALSE       20 (to 260)

444         220 LOAD_FAST                5 (conn)
            222 STORE_SUBSCR
            226 CACHE
            228 CACHE
            230 CACHE
            232 CACHE
            234 CACHE
            236 CACHE
            238 CACHE
            240 CACHE
            242 CACHE
            244 UNPACK_SEQUENCE          0
            248 CALL                     0
            256 CACHE
            258 POP_TOP

436     >>  260 LOAD_CONST               3 (None)
            262 LOAD_CONST               3 (None)
            264 LOAD_CONST               3 (None)
            266 UNPACK_SEQUENCE          2
            270 CALL                     2
            278 CACHE
            280 POP_TOP
            282 JUMP_FORWARD            11 (to 306)
        >>  284 PUSH_EXC_INFO
            286 WITH_EXCEPT_START
            288 POP_JUMP_IF_TRUE         4 (to 298)
            290 RERAISE                  2
        >>  292 COPY                     3
            294 POP_EXCEPT
            296 RERAISE                  1
        >>  298 POP_TOP
            300 POP_EXCEPT
            302 POP_TOP
            304 POP_TOP

445     >>  306 LOAD_FAST                0 (self)
            308 STORE_SUBSCR
            312 CACHE
            314 CACHE
            316 CACHE
            318 CACHE
            320 CACHE
            322 CACHE
            324 CACHE
            326 CACHE
            328 CACHE
            330 UNPACK_SEQUENCE          0
            334 CALL                     0
            342 CACHE
            344 RETURN_VALUE
ExceptionTable:
  88 to 258 -> 284 [1] lasti
  284 to 290 -> 292 [3] lasti
  298 to 298 -> 292 [3] lasti

Disassembly of <code object retrlines at 0x000001B2A74A3670, file "ftplib.py", line 447>:
447           0 RESUME                   0

459           2 LOAD_FAST                2 (callback)
              4 POP_JUMP_IF_NOT_NONE     7 (to 20)

460           6 LOAD_GLOBAL              0 (print_line)
             16 CACHE
             18 STORE_FAST               2 (callback)

461     >>   20 LOAD_FAST                0 (self)
             22 STORE_SUBSCR
             26 CACHE
             28 CACHE
             30 CACHE
             32 CACHE
             34 CACHE
             36 CACHE
             38 CACHE
             40 CACHE
             42 CACHE
             44 LOAD_CONST               2 ('TYPE A')
             46 UNPACK_SEQUENCE          1
             50 CALL                     1
             58 CACHE
             60 STORE_FAST               3 (resp)

462          62 LOAD_FAST                0 (self)
             64 STORE_SUBSCR
             68 CACHE
             70 CACHE
             72 CACHE
             74 CACHE
             76 CACHE
             78 CACHE
             80 CACHE
             82 CACHE
             84 CACHE
             86 LOAD_FAST                1 (cmd)
             88 UNPACK_SEQUENCE          1
             92 CALL                     1
            100 CACHE
            102 BEFORE_WITH
            104 STORE_FAST               4 (conn)

463         106 LOAD_FAST                4 (conn)
            108 STORE_SUBSCR
            112 CACHE
            114 CACHE
            116 CACHE
            118 CACHE
            120 CACHE
            122 CACHE
            124 CACHE
            126 CACHE
            128 CACHE
            130 LOAD_CONST               3 ('r')
            132 LOAD_FAST                0 (self)
            134 LOAD_ATTR                4 (transfercmd)
            154 CACHE
            156 CACHE
            158 CACHE

462         160 BEFORE_WITH

463         162 STORE_FAST               5 (fp)

464         164 NOP

465     >>  166 LOAD_FAST                5 (fp)
            168 STORE_SUBSCR
            172 CACHE
            174 CACHE
            176 CACHE
            178 CACHE
            180 CACHE
            182 CACHE
            184 CACHE
            186 CACHE
            188 CACHE
            190 LOAD_FAST                0 (self)
            192 LOAD_ATTR                6 (makefile)
            212 CALL                     1
            220 CACHE
            222 STORE_FAST               6 (line)

466         224 LOAD_GLOBAL             15 (NULL + len)
            234 CACHE
            236 LOAD_FAST                6 (line)
            238 UNPACK_SEQUENCE          1
            242 CALL                     1
            250 CACHE
            252 LOAD_FAST                0 (self)
            254 LOAD_ATTR                6 (makefile)
            274 CACHE
            276 CACHE
            278 CACHE
            280 CACHE
            282 CACHE
            284 LOAD_CONST               6 ('got more than %d bytes')
            286 LOAD_FAST                0 (self)
            288 LOAD_ATTR                6 (makefile)
            308 CACHE
            310 CACHE
            312 CACHE
            314 CACHE
            316 RAISE_VARARGS            1

468         318 LOAD_FAST                0 (self)
            320 LOAD_ATTR                9 (NULL|self + encoding)

469         340 LOAD_GLOBAL             21 (NULL + print)
            350 CACHE
            352 LOAD_CONST               8 ('*retr*')
            354 LOAD_GLOBAL             23 (NULL + repr)
            364 CACHE
            366 LOAD_FAST                6 (line)
            368 UNPACK_SEQUENCE          1
            372 CALL                     1
            380 CACHE
            382 UNPACK_SEQUENCE          2
            386 CALL                     2
            394 CACHE
            396 POP_TOP

470         398 LOAD_FAST                6 (line)
            400 POP_JUMP_IF_TRUE         1 (to 404)

471         402 JUMP_FORWARD            66 (to 536)

472     >>  404 LOAD_FAST                6 (line)
            406 LOAD_CONST               9 (-2)
            408 LOAD_CONST               1 (None)
            410 BUILD_SLICE              2
            412 BINARY_SUBSCR
            416 CACHE
            418 CACHE
            420 CACHE
            422 LOAD_GLOBAL             24 (CRLF)
            432 CACHE
            434 COMPARE_OP               2 (<)
            438 CACHE
            440 POP_JUMP_IF_FALSE       11 (to 464)

473         442 LOAD_FAST                6 (line)
            444 LOAD_CONST               1 (None)
            446 LOAD_CONST               9 (-2)
            448 BUILD_SLICE              2
            450 BINARY_SUBSCR
            454 CACHE
            456 CACHE
            458 CACHE
            460 STORE_FAST               6 (line)
            462 JUMP_FORWARD            24 (to 512)

474     >>  464 LOAD_FAST                6 (line)
            466 LOAD_CONST              10 (-1)
            468 LOAD_CONST               1 (None)
            470 BUILD_SLICE              2
            472 BINARY_SUBSCR
            476 CACHE
            478 CACHE
            480 CACHE
            482 LOAD_CONST              11 ('\n')
            484 COMPARE_OP               2 (<)
            488 CACHE
            490 POP_JUMP_IF_FALSE       10 (to 512)

475         492 LOAD_FAST                6 (line)
            494 LOAD_CONST               1 (None)
            496 LOAD_CONST              10 (-1)
            498 BUILD_SLICE              2
            500 BINARY_SUBSCR
            504 CACHE
            506 CACHE
            508 CACHE
            510 STORE_FAST               6 (line)

476     >>  512 PUSH_NULL
            514 LOAD_FAST                2 (callback)
            516 LOAD_FAST                6 (line)
            518 UNPACK_SEQUENCE          1
            522 CALL                     1
            530 CACHE
            532 POP_TOP

464         534 JUMP_BACKWARD          185 (to 166)

478     >>  536 LOAD_GLOBAL             26 (_SSLSocket)
            546 CACHE
            548 POP_JUMP_IF_NONE        41 (to 632)
            550 LOAD_GLOBAL             29 (NULL + isinstance)
            560 CACHE
            562 LOAD_FAST                4 (conn)
            564 LOAD_GLOBAL             26 (_SSLSocket)
            574 CACHE
            576 UNPACK_SEQUENCE          2
            580 CALL                     2
            588 CACHE
            590 POP_JUMP_IF_FALSE       20 (to 632)

479         592 LOAD_FAST                4 (conn)
            594 STORE_SUBSCR
            598 CACHE
            600 CACHE
            602 CACHE
            604 CACHE
            606 CACHE
            608 CACHE
            610 CACHE
            612 CACHE
            614 CACHE
            616 UNPACK_SEQUENCE          0
            620 CALL                     0
            628 CACHE
            630 POP_TOP

462     >>  632 LOAD_CONST               1 (None)
            634 LOAD_CONST               1 (None)
            636 LOAD_CONST               1 (None)
            638 UNPACK_SEQUENCE          2
            642 CALL                     2
            650 CACHE
            652 POP_TOP
            654 JUMP_FORWARD            11 (to 678)
        >>  656 PUSH_EXC_INFO
            658 WITH_EXCEPT_START
            660 POP_JUMP_IF_TRUE         4 (to 670)
            662 RERAISE                  2
        >>  664 COPY                     3
            666 POP_EXCEPT
            668 RERAISE                  1
        >>  670 POP_TOP
            672 POP_EXCEPT
            674 POP_TOP
            676 POP_TOP
        >>  678 LOAD_CONST               1 (None)
            680 LOAD_CONST               1 (None)
            682 LOAD_CONST               1 (None)
            684 UNPACK_SEQUENCE          2
            688 CALL                     2
            696 CACHE
            698 POP_TOP
            700 JUMP_FORWARD            11 (to 724)
        >>  702 PUSH_EXC_INFO
            704 WITH_EXCEPT_START
            706 POP_JUMP_IF_TRUE         4 (to 716)
            708 RERAISE                  2
        >>  710 COPY                     3
            712 POP_EXCEPT
            714 RERAISE                  1
        >>  716 POP_TOP
            718 POP_EXCEPT
            720 POP_TOP
            722 POP_TOP

480     >>  724 LOAD_FAST                0 (self)
            726 STORE_SUBSCR
            730 CACHE
            732 CACHE
            734 CACHE
            736 CACHE
            738 CACHE
            740 CACHE
            742 CACHE
            744 CACHE
            746 CACHE
            748 UNPACK_SEQUENCE          0
            752 CALL                     0
            760 CACHE
            762 RETURN_VALUE
ExceptionTable:
  104 to 160 -> 702 [1] lasti
  162 to 630 -> 656 [2] lasti
  632 to 654 -> 702 [1] lasti
  656 to 662 -> 664 [4] lasti
  664 to 668 -> 702 [1] lasti
  670 to 670 -> 664 [4] lasti
  672 to 676 -> 702 [1] lasti
  702 to 708 -> 710 [3] lasti
  716 to 716 -> 710 [3] lasti

Disassembly of <code object storbinary at 0x000001B2A75B8760, file "ftplib.py", line 482>:
482           0 RESUME                   0

497           2 LOAD_FAST                0 (self)
              4 STORE_SUBSCR
              8 CACHE
             10 CACHE
             12 CACHE
             14 CACHE
             16 CACHE
             18 CACHE
             20 CACHE
             22 CACHE
             24 CACHE
             26 LOAD_CONST               1 ('TYPE I')
             28 UNPACK_SEQUENCE          1
             32 CALL                     1
             40 CACHE
             42 POP_TOP

498          44 LOAD_FAST                0 (self)
             46 STORE_SUBSCR
             50 CACHE
             52 CACHE
             54 CACHE
             56 CACHE
             58 CACHE
             60 CACHE
             62 CACHE
             64 CACHE
             66 CACHE
             68 LOAD_FAST                1 (cmd)
             70 LOAD_FAST                5 (rest)
             72 UNPACK_SEQUENCE          2
             76 CALL                     2
             84 CACHE
             86 BEFORE_WITH
             88 STORE_FAST               6 (conn)

499          90 NOP

500     >>   92 LOAD_FAST                2 (fp)
             94 STORE_SUBSCR
             98 CACHE
            100 CACHE
            102 CACHE
            104 CACHE
            106 CACHE
            108 CACHE
            110 CACHE
            112 CACHE
            114 CACHE
            116 LOAD_FAST                3 (blocksize)
            118 UNPACK_SEQUENCE          1
            122 CALL                     1
            130 CACHE
            132 STORE_FAST               7 (buf)

501         134 LOAD_FAST                7 (buf)
            136 POP_JUMP_IF_TRUE         1 (to 140)

502         138 JUMP_FORWARD            35 (to 210)

503     >>  140 LOAD_FAST                6 (conn)
            142 STORE_SUBSCR
            146 CACHE
            148 CACHE
            150 CACHE
            152 CACHE
            154 CACHE
            156 CACHE
            158 CACHE
            160 CACHE
            162 CACHE
            164 LOAD_FAST                7 (buf)
            166 UNPACK_SEQUENCE          1
            170 CALL                     1
            178 CACHE
            180 POP_TOP

504         182 LOAD_FAST                4 (callback)
            184 POP_JUMP_IF_FALSE       11 (to 208)

505         186 PUSH_NULL
            188 LOAD_FAST                4 (callback)
            190 LOAD_FAST                7 (buf)
            192 UNPACK_SEQUENCE          1
            196 CALL                     1
            204 CACHE
            206 POP_TOP

499     >>  208 JUMP_BACKWARD           59 (to 92)

507     >>  210 LOAD_GLOBAL              8 (_SSLSocket)
            220 CACHE
            222 POP_JUMP_IF_NONE        41 (to 306)
            224 LOAD_GLOBAL             11 (NULL + isinstance)
            234 CACHE
            236 LOAD_FAST                6 (conn)
            238 LOAD_GLOBAL              8 (_SSLSocket)
            248 CACHE
            250 UNPACK_SEQUENCE          2
            254 CALL                     2
            262 CACHE
            264 POP_JUMP_IF_FALSE       20 (to 306)

508         266 LOAD_FAST                6 (conn)
            268 STORE_SUBSCR
            272 CACHE
            274 CACHE
            276 CACHE
            278 CACHE
            280 CACHE
            282 CACHE
            284 CACHE
            286 CACHE
            288 CACHE
            290 UNPACK_SEQUENCE          0
            294 CALL                     0
            302 CACHE
            304 POP_TOP

498     >>  306 LOAD_CONST               3 (None)
            308 LOAD_CONST               3 (None)
            310 LOAD_CONST               3 (None)
            312 UNPACK_SEQUENCE          2
            316 CALL                     2
            324 CACHE
            326 POP_TOP
            328 JUMP_FORWARD            11 (to 352)
        >>  330 PUSH_EXC_INFO
            332 WITH_EXCEPT_START
            334 POP_JUMP_IF_TRUE         4 (to 344)
            336 RERAISE                  2
        >>  338 COPY                     3
            340 POP_EXCEPT
            342 RERAISE                  1
        >>  344 POP_TOP
            346 POP_EXCEPT
            348 POP_TOP
            350 POP_TOP

509     >>  352 LOAD_FAST                0 (self)
            354 STORE_SUBSCR
            358 CACHE
            360 CACHE
            362 CACHE
            364 CACHE
            366 CACHE
            368 CACHE
            370 CACHE
            372 CACHE
            374 CACHE
            376 UNPACK_SEQUENCE          0
            380 CALL                     0
            388 CACHE
            390 RETURN_VALUE
ExceptionTable:
  88 to 304 -> 330 [1] lasti
  330 to 336 -> 338 [3] lasti
  344 to 344 -> 338 [3] lasti

Disassembly of <code object storlines at 0x000001B2A74A3CE0, file "ftplib.py", line 511>:
511           0 RESUME                   0

523           2 LOAD_FAST                0 (self)
              4 STORE_SUBSCR
              8 CACHE
             10 CACHE
             12 CACHE
             14 CACHE
             16 CACHE
             18 CACHE
             20 CACHE
             22 CACHE
             24 CACHE
             26 LOAD_CONST               1 ('TYPE A')
             28 UNPACK_SEQUENCE          1
             32 CALL                     1
             40 CACHE
             42 POP_TOP

524          44 LOAD_FAST                0 (self)
             46 STORE_SUBSCR
             50 CACHE
             52 CACHE
             54 CACHE
             56 CACHE
             58 CACHE
             60 CACHE
             62 CACHE
             64 CACHE
             66 CACHE
             68 LOAD_FAST                1 (cmd)
             70 UNPACK_SEQUENCE          1
             74 CALL                     1
             82 CACHE
             84 BEFORE_WITH
             86 STORE_FAST               4 (conn)

525          88 NOP

526     >>   90 LOAD_FAST                2 (fp)
             92 STORE_SUBSCR
             96 CACHE
             98 CACHE
            100 CACHE
            102 CACHE
            104 CACHE
            106 CACHE
            108 CACHE
            110 CACHE
            112 CACHE
            114 LOAD_FAST                0 (self)
            116 LOAD_ATTR                3 (NULL|self + transfercmd)
            136 CALL                     1
            144 CACHE
            146 STORE_FAST               5 (buf)

527         148 LOAD_GLOBAL              9 (NULL + len)
            158 CACHE
            160 LOAD_FAST                5 (buf)
            162 UNPACK_SEQUENCE          1
            166 CALL                     1
            174 CACHE
            176 LOAD_FAST                0 (self)
            178 LOAD_ATTR                3 (NULL|self + transfercmd)
            198 CACHE
            200 CACHE
            202 CACHE
            204 CACHE
            206 CACHE
            208 LOAD_CONST               3 ('got more than %d bytes')
            210 LOAD_FAST                0 (self)
            212 LOAD_ATTR                3 (NULL|self + transfercmd)
            232 CACHE
            234 CACHE
            236 CACHE
            238 CACHE
            240 RAISE_VARARGS            1

529         242 LOAD_FAST                5 (buf)
            244 POP_JUMP_IF_TRUE         1 (to 248)

530         246 JUMP_FORWARD            89 (to 426)

531     >>  248 LOAD_FAST                5 (buf)
            250 LOAD_CONST               4 (-2)
            252 LOAD_CONST               5 (None)
            254 BUILD_SLICE              2
            256 BINARY_SUBSCR
            260 CACHE
            262 CACHE
            264 CACHE
            266 LOAD_GLOBAL             12 (B_CRLF)
            276 CACHE
            278 COMPARE_OP               3 (<)
            282 CACHE
            284 POP_JUMP_IF_FALSE       35 (to 356)

532         286 LOAD_FAST                5 (buf)
            288 LOAD_CONST               6 (-1)
            290 BINARY_SUBSCR
            294 CACHE
            296 CACHE
            298 CACHE
            300 LOAD_GLOBAL             12 (B_CRLF)
            310 CACHE
            312 CONTAINS_OP              0
            314 POP_JUMP_IF_FALSE       10 (to 336)
            316 LOAD_FAST                5 (buf)
            318 LOAD_CONST               5 (None)
            320 LOAD_CONST               6 (-1)
            322 BUILD_SLICE              2
            324 BINARY_SUBSCR
            328 CACHE
            330 CACHE
            332 CACHE
            334 STORE_FAST               5 (buf)

533     >>  336 LOAD_FAST                5 (buf)
            338 LOAD_GLOBAL             12 (B_CRLF)
            348 CACHE
            350 BINARY_OP                0 (+)
            354 STORE_FAST               5 (buf)

534     >>  356 LOAD_FAST                4 (conn)
            358 STORE_SUBSCR
            362 CACHE
            364 CACHE
            366 CACHE
            368 CACHE
            370 CACHE
            372 CACHE
            374 CACHE
            376 CACHE
            378 CACHE
            380 LOAD_FAST                5 (buf)
            382 UNPACK_SEQUENCE          1
            386 CALL                     1
            394 CACHE
            396 POP_TOP

535         398 LOAD_FAST                3 (callback)
            400 POP_JUMP_IF_FALSE       11 (to 424)

536         402 PUSH_NULL
            404 LOAD_FAST                3 (callback)
            406 LOAD_FAST                5 (buf)
            408 UNPACK_SEQUENCE          1
            412 CALL                     1
            420 CACHE
            422 POP_TOP

525     >>  424 JUMP_BACKWARD          168 (to 90)

538     >>  426 LOAD_GLOBAL             16 (_SSLSocket)
            436 CACHE
            438 POP_JUMP_IF_NONE        41 (to 522)
            440 LOAD_GLOBAL             19 (NULL + isinstance)
            450 CACHE
            452 LOAD_FAST                4 (conn)
            454 LOAD_GLOBAL             16 (_SSLSocket)
            464 CACHE
            466 UNPACK_SEQUENCE          2
            470 CALL                     2
            478 CACHE
            480 POP_JUMP_IF_FALSE       20 (to 522)

539         482 LOAD_FAST                4 (conn)
            484 STORE_SUBSCR
            488 CACHE
            490 CACHE
            492 CACHE
            494 CACHE
            496 CACHE
            498 CACHE
            500 CACHE
            502 CACHE
            504 CACHE
            506 UNPACK_SEQUENCE          0
            510 CALL                     0
            518 CACHE
            520 POP_TOP

524     >>  522 LOAD_CONST               5 (None)
            524 LOAD_CONST               5 (None)
            526 LOAD_CONST               5 (None)
            528 UNPACK_SEQUENCE          2
            532 CALL                     2
            540 CACHE
            542 POP_TOP
            544 JUMP_FORWARD            11 (to 568)
        >>  546 PUSH_EXC_INFO
            548 WITH_EXCEPT_START
            550 POP_JUMP_IF_TRUE         4 (to 560)
            552 RERAISE                  2
        >>  554 COPY                     3
            556 POP_EXCEPT
            558 RERAISE                  1
        >>  560 POP_TOP
            562 POP_EXCEPT
            564 POP_TOP
            566 POP_TOP

540     >>  568 LOAD_FAST                0 (self)
            570 STORE_SUBSCR
            574 CACHE
            576 CACHE
            578 CACHE
            580 CACHE
            582 CACHE
            584 CACHE
            586 CACHE
            588 CACHE
            590 CACHE
            592 UNPACK_SEQUENCE          0
            596 CALL                     0
            604 CACHE
            606 RETURN_VALUE
ExceptionTable:
  86 to 520 -> 546 [1] lasti
  546 to 552 -> 554 [3] lasti
  560 to 560 -> 554 [3] lasti

Disassembly of <code object acct at 0x000001B2A8A0CD30, file "ftplib.py", line 542>:
542           0 RESUME                   0

544           2 LOAD_CONST               1 ('ACCT ')
              4 LOAD_FAST                1 (password)
              6 BINARY_OP                0 (+)
             10 STORE_FAST               2 (cmd)

545          12 LOAD_FAST                0 (self)
             14 STORE_SUBSCR
             18 CACHE
             20 CACHE
             22 CACHE
             24 CACHE
             26 CACHE
             28 CACHE
             30 CACHE
             32 CACHE
             34 CACHE
             36 LOAD_FAST                2 (cmd)
             38 UNPACK_SEQUENCE          1
             42 CALL                     1
             50 CACHE
             52 RETURN_VALUE

Disassembly of <code object nlst at 0x000001B2A76DB1B0, file "ftplib.py", line 547>:
547           0 RESUME                   0

549           2 LOAD_CONST               1 ('NLST')
              4 STORE_FAST               2 (cmd)

550           6 LOAD_FAST                1 (args)
              8 GET_ITER
        >>   10 FOR_ITER                10 (to 34)

551          14 LOAD_FAST                2 (cmd)
             16 LOAD_CONST               2 (' ')
             18 LOAD_FAST                3 (arg)
             20 BINARY_OP                0 (+)
             24 BINARY_OP                0 (+)
             28 STORE_FAST               2 (cmd)
             30 JUMP_BACKWARD           11 (to 10)

552          32 BUILD_LIST               0
        >>   34 STORE_FAST               4 (files)

553          36 LOAD_FAST                0 (self)
             38 STORE_SUBSCR
             42 CACHE
             44 CACHE
             46 CACHE
             48 CACHE
             50 CACHE
             52 CACHE
             54 CACHE
             56 CACHE
             58 CACHE
             60 LOAD_FAST                2 (cmd)
             62 LOAD_FAST                4 (files)
             64 LOAD_ATTR                1 (NULL|self + retrlines)
             84 CACHE
             86 CACHE
             88 POP_TOP

554          90 LOAD_FAST                4 (files)
             92 RETURN_VALUE

Disassembly of <code object dir at 0x000001B2A71CEC10, file "ftplib.py", line 556>:
556           0 RESUME                   0

562           2 LOAD_CONST               1 ('LIST')
              4 STORE_FAST               2 (cmd)

563           6 LOAD_CONST               2 (None)
              8 STORE_FAST               3 (func)

564          10 LOAD_FAST                1 (args)
             12 LOAD_CONST               3 (-1)
             14 LOAD_CONST               2 (None)
             16 BUILD_SLICE              2
             18 BINARY_SUBSCR
             22 CACHE
             24 CACHE
             26 CACHE
             28 POP_JUMP_IF_FALSE       56 (to 142)
             30 LOAD_GLOBAL              1 (NULL + type)
             40 CACHE
             42 LOAD_FAST                1 (args)
             44 LOAD_CONST               3 (-1)
             46 BINARY_SUBSCR
             50 CACHE
             52 CACHE
             54 CACHE
             56 UNPACK_SEQUENCE          1
             60 CALL                     1
             68 CACHE
             70 LOAD_GLOBAL              1 (NULL + type)
             80 CACHE
             82 LOAD_CONST               4 ('')
             84 UNPACK_SEQUENCE          1
             88 CALL                     1
             96 CACHE
             98 COMPARE_OP               3 (<)
            102 CACHE
            104 POP_JUMP_IF_FALSE       18 (to 142)

565         106 LOAD_FAST                1 (args)
            108 LOAD_CONST               2 (None)
            110 LOAD_CONST               3 (-1)
            112 BUILD_SLICE              2
            114 BINARY_SUBSCR
            118 CACHE
            120 CACHE
            122 CACHE
            124 LOAD_FAST                1 (args)
            126 LOAD_CONST               3 (-1)
            128 BINARY_SUBSCR
            132 CACHE
            134 CACHE
            136 CACHE
            138 STORE_FAST               3 (func)
            140 STORE_FAST               1 (args)

566     >>  142 LOAD_FAST                1 (args)
            144 GET_ITER
        >>  146 FOR_ITER                12 (to 174)

567         150 LOAD_FAST                4 (arg)
            152 POP_JUMP_IF_FALSE        8 (to 170)

568         154 LOAD_FAST                2 (cmd)
            156 LOAD_CONST               5 (' ')
            158 LOAD_FAST                4 (arg)
            160 BINARY_OP                0 (+)
            164 BINARY_OP                0 (+)
            168 STORE_FAST               2 (cmd)
        >>  170 JUMP_BACKWARD           13 (to 146)

569         172 LOAD_FAST                0 (self)
        >>  174 STORE_SUBSCR
            178 CACHE
            180 CACHE
            182 CACHE
            184 CACHE
            186 CACHE
            188 CACHE
            190 CACHE
            192 CACHE
            194 CACHE
            196 LOAD_FAST                2 (cmd)
            198 LOAD_FAST                3 (func)
            200 UNPACK_SEQUENCE          2
            204 CALL                     2
            212 CACHE
            214 POP_TOP
            216 LOAD_CONST               2 (None)
            218 RETURN_VALUE

Disassembly of <code object mlsd at 0x000001B2A74A4290, file "ftplib.py", line 571>:
571           0 RETURN_GENERATOR
              2 POP_TOP
              4 RESUME                   0

583           6 LOAD_FAST                2 (facts)
              8 POP_JUMP_IF_FALSE       46 (to 102)

584          10 LOAD_FAST                0 (self)
             12 STORE_SUBSCR
             16 CACHE
             18 CACHE
             20 CACHE
             22 CACHE
             24 CACHE
             26 CACHE
             28 CACHE
             30 CACHE
             32 CACHE
             34 LOAD_CONST               1 ('OPTS MLST ')
             36 LOAD_CONST               2 (';')
             38 STORE_SUBSCR
             42 CACHE
             44 CACHE
             46 CACHE
             48 CACHE
             50 CACHE
             52 CACHE
             54 CACHE
             56 CACHE
             58 CACHE
             60 LOAD_FAST                2 (facts)
             62 UNPACK_SEQUENCE          1
             66 CALL                     1
             74 CACHE
             76 BINARY_OP                0 (+)
             80 LOAD_CONST               2 (';')
             82 BINARY_OP                0 (+)
             86 UNPACK_SEQUENCE          1
             90 CALL                     1
             98 CACHE
            100 POP_TOP

585     >>  102 LOAD_FAST                1 (path)
            104 POP_JUMP_IF_FALSE        6 (to 118)

586         106 LOAD_CONST               3 ('MLSD %s')
            108 LOAD_FAST                1 (path)
            110 BINARY_OP                6 (%)
            114 STORE_FAST               3 (cmd)
            116 JUMP_FORWARD             2 (to 122)

588     >>  118 LOAD_CONST               4 ('MLSD')
            120 STORE_FAST               3 (cmd)

589     >>  122 BUILD_LIST               0
            124 STORE_FAST               4 (lines)

590         126 LOAD_FAST                0 (self)
            128 STORE_SUBSCR
            132 CACHE
            134 CACHE
            136 CACHE
            138 CACHE
            140 CACHE
            142 CACHE
            144 CACHE
            146 CACHE
            148 CACHE
            150 LOAD_FAST                3 (cmd)
            152 LOAD_FAST                4 (lines)
            154 LOAD_ATTR                3 (NULL|self + join)
            174 CACHE
            176 CACHE
            178 POP_TOP

591         180 LOAD_FAST                4 (lines)
            182 GET_ITER
        >>  184 FOR_ITER               139 (to 466)

592         188 LOAD_FAST                5 (line)
            190 STORE_SUBSCR
            194 CACHE
            196 CACHE
            198 CACHE
            200 CACHE
            202 CACHE
            204 CACHE
            206 CACHE
            208 CACHE
            210 CACHE
            212 LOAD_GLOBAL             10 (CRLF)
            222 CACHE
            224 UNPACK_SEQUENCE          1
            228 CALL                     1
            236 CACHE
            238 STORE_SUBSCR
            242 CACHE
            244 CACHE
            246 CACHE
            248 CACHE
            250 CACHE
            252 CACHE
            254 CACHE
            256 CACHE
            258 CACHE
            260 LOAD_CONST               5 (' ')
            262 UNPACK_SEQUENCE          1
            266 CALL                     1
            274 CACHE
            276 UNPACK_SEQUENCE          3
            280 STORE_FAST               6 (facts_found)
            282 STORE_FAST               7 (_)
            284 STORE_FAST               8 (name)

593         286 BUILD_MAP                0
            288 STORE_FAST               9 (entry)

594         290 LOAD_FAST                6 (facts_found)
            292 LOAD_CONST               6 (None)
            294 LOAD_CONST               7 (-1)
            296 BUILD_SLICE              2
            298 BINARY_SUBSCR
            302 CACHE
            304 CACHE
            306 CACHE
            308 STORE_SUBSCR
            312 CACHE
            314 CACHE
            316 CACHE
            318 CACHE
            320 CACHE
            322 CACHE
            324 CACHE
            326 CACHE
            328 CACHE
            330 LOAD_CONST               2 (';')
            332 UNPACK_SEQUENCE          1
            336 CALL                     1
            344 CACHE
            346 GET_ITER
        >>  348 FOR_ITER                50 (to 452)

595         352 LOAD_FAST               10 (fact)
            354 STORE_SUBSCR
            358 CACHE
            360 CACHE
            362 CACHE
            364 CACHE
            366 CACHE
            368 CACHE
            370 CACHE
            372 CACHE
            374 CACHE
            376 LOAD_CONST               8 ('=')
            378 UNPACK_SEQUENCE          1
            382 CALL                     1
            390 CACHE
            392 UNPACK_SEQUENCE          3
            396 STORE_FAST              11 (key)
            398 STORE_FAST               7 (_)
            400 STORE_FAST              12 (value)

596         402 LOAD_FAST               12 (value)
            404 LOAD_FAST                9 (entry)
            406 LOAD_FAST               11 (key)
            408 STORE_SUBSCR
            412 CACHE
            414 CACHE
            416 CACHE
            418 CACHE
            420 CACHE
            422 CACHE
            424 CACHE
            426 CACHE
            428 CACHE
            430 UNPACK_SEQUENCE          0
            434 CALL                     0
            442 CACHE
            444 STORE_SUBSCR
            448 JUMP_BACKWARD           51 (to 348)

597         450 LOAD_FAST                8 (name)
        >>  452 LOAD_FAST                9 (entry)
            454 BUILD_TUPLE              2
            456 LOAD_FAST                0 (self)
            458 RESUME                   1
            460 POP_TOP
            462 JUMP_BACKWARD          140 (to 184)

591         464 LOAD_CONST               6 (None)
        >>  466 RETURN_VALUE

Disassembly of <code object rename at 0x000001B2A71D3730, file "ftplib.py", line 599>:
599           0 RESUME                   0

601           2 LOAD_FAST                0 (self)
              4 STORE_SUBSCR
              8 CACHE
             10 CACHE
             12 CACHE
             14 CACHE
             16 CACHE
             18 CACHE
             20 CACHE
             22 CACHE
             24 CACHE
             26 LOAD_CONST               1 ('RNFR ')
             28 LOAD_FAST                1 (fromname)
             30 BINARY_OP                0 (+)
             34 UNPACK_SEQUENCE          1
             38 CALL                     1
             46 CACHE
             48 STORE_FAST               3 (resp)

602          50 LOAD_FAST                3 (resp)
             52 LOAD_CONST               2 (0)
             54 BINARY_SUBSCR
             58 CACHE
             60 CACHE
             62 CACHE
             64 LOAD_CONST               3 ('3')
             66 COMPARE_OP               3 (<)
             70 CACHE
             72 POP_JUMP_IF_FALSE       15 (to 104)

603          74 LOAD_GLOBAL              3 (NULL + error_reply)
             84 CACHE
             86 LOAD_FAST                3 (resp)
             88 UNPACK_SEQUENCE          1
             92 CALL                     1
            100 CACHE
            102 RAISE_VARARGS            1

604     >>  104 LOAD_FAST                0 (self)
            106 STORE_SUBSCR
            110 CACHE
            112 CACHE
            114 CACHE
            116 CACHE
            118 CACHE
            120 CACHE
            122 CACHE
            124 CACHE
            126 CACHE
            128 LOAD_CONST               4 ('RNTO ')
            130 LOAD_FAST                2 (toname)
            132 BINARY_OP                0 (+)
            136 UNPACK_SEQUENCE          1
            140 CALL                     1
            148 CACHE
            150 RETURN_VALUE

Disassembly of <code object delete at 0x000001B2A76B5B80, file "ftplib.py", line 606>:
606           0 RESUME                   0

608           2 LOAD_FAST                0 (self)
              4 STORE_SUBSCR
              8 CACHE
             10 CACHE
             12 CACHE
             14 CACHE
             16 CACHE
             18 CACHE
             20 CACHE
             22 CACHE
             24 CACHE
             26 LOAD_CONST               1 ('DELE ')
             28 LOAD_FAST                1 (filename)
             30 BINARY_OP                0 (+)
             34 UNPACK_SEQUENCE          1
             38 CALL                     1
             46 CACHE
             48 STORE_FAST               2 (resp)

609          50 LOAD_FAST                2 (resp)
             52 LOAD_CONST               2 (None)
             54 LOAD_CONST               3 (3)
             56 BUILD_SLICE              2
             58 BINARY_SUBSCR
             62 CACHE
             64 CACHE
             66 CACHE
             68 LOAD_CONST               4 (frozenset({'250', '200'}))
             70 CONTAINS_OP              0
             72 POP_JUMP_IF_FALSE        2 (to 78)

610          74 LOAD_FAST                2 (resp)
             76 RETURN_VALUE

612     >>   78 LOAD_GLOBAL              3 (NULL + error_reply)
             88 CACHE
             90 LOAD_FAST                2 (resp)
             92 UNPACK_SEQUENCE          1
             96 CALL                     1
            104 CACHE
            106 RAISE_VARARGS            1

Disassembly of <code object cwd at 0x000001B2A71CDBD0, file "ftplib.py", line 614>:
614           0 RESUME                   0

616           2 LOAD_FAST                1 (dirname)
              4 LOAD_CONST               1 ('..')
              6 COMPARE_OP               2 (<)
             10 CACHE
             12 POP_JUMP_IF_FALSE       71 (to 156)

617          14 NOP

618          16 LOAD_FAST                0 (self)
             18 STORE_SUBSCR
             22 CACHE
             24 CACHE
             26 CACHE
             28 CACHE
             30 CACHE
             32 CACHE
             34 CACHE
             36 CACHE
             38 CACHE
             40 LOAD_CONST               2 ('CDUP')
             42 UNPACK_SEQUENCE          1
             46 CALL                     1
             54 CACHE
             56 RETURN_VALUE
        >>   58 PUSH_EXC_INFO

619          60 LOAD_GLOBAL              2 (error_perm)
             70 CACHE
             72 CHECK_EXC_MATCH
             74 POP_JUMP_IF_FALSE       36 (to 148)
             76 STORE_FAST               2 (msg)

620          78 LOAD_FAST                2 (msg)
             80 LOAD_ATTR                2 (error_perm)
            100 CACHE
            102 LOAD_CONST               4 (None)
            104 LOAD_CONST               5 (3)
            106 BUILD_SLICE              2
            108 BINARY_SUBSCR
            112 CACHE
            114 CACHE
            116 CACHE
            118 LOAD_CONST               6 ('500')
            120 COMPARE_OP               3 (<)
            124 CACHE
            126 POP_JUMP_IF_FALSE        1 (to 130)

621         128 RAISE_VARARGS            0

620     >>  130 POP_EXCEPT
            132 LOAD_CONST               4 (None)
            134 STORE_FAST               2 (msg)
            136 DELETE_FAST              2 (msg)
            138 JUMP_FORWARD            16 (to 172)
        >>  140 LOAD_CONST               4 (None)
            142 STORE_FAST               2 (msg)
            144 DELETE_FAST              2 (msg)
            146 RERAISE                  1

619     >>  148 RERAISE                  0
        >>  150 COPY                     3
            152 POP_EXCEPT
            154 RERAISE                  1

622     >>  156 LOAD_FAST                1 (dirname)
            158 LOAD_CONST               7 ('')
            160 COMPARE_OP               2 (<)
            164 CACHE
            166 POP_JUMP_IF_FALSE        2 (to 172)

623         168 LOAD_CONST               8 ('.')
            170 STORE_FAST               1 (dirname)

624     >>  172 LOAD_CONST               9 ('CWD ')
            174 LOAD_FAST                1 (dirname)
            176 BINARY_OP                0 (+)
            180 STORE_FAST               3 (cmd)

625         182 LOAD_FAST                0 (self)
            184 STORE_SUBSCR
            188 CACHE
            190 CACHE
            192 CACHE
            194 CACHE
            196 CACHE
            198 CACHE
            200 CACHE
            202 CACHE
            204 CACHE
            206 LOAD_FAST                3 (cmd)
            208 UNPACK_SEQUENCE          1
            212 CALL                     1
            220 CACHE
            222 RETURN_VALUE
ExceptionTable:
  16 to 54 -> 58 [0]
  58 to 76 -> 150 [1] lasti
  78 to 128 -> 140 [1] lasti
  140 to 148 -> 150 [1] lasti

Disassembly of <code object size at 0x000001B2A728F6D0, file "ftplib.py", line 627>:
627           0 RESUME                   0

630           2 LOAD_FAST                0 (self)
              4 STORE_SUBSCR
              8 CACHE
             10 CACHE
             12 CACHE
             14 CACHE
             16 CACHE
             18 CACHE
             20 CACHE
             22 CACHE
             24 CACHE
             26 LOAD_CONST               1 ('SIZE ')
             28 LOAD_FAST                1 (filename)
             30 BINARY_OP                0 (+)
             34 UNPACK_SEQUENCE          1
             38 CALL                     1
             46 CACHE
             48 STORE_FAST               2 (resp)

631          50 LOAD_FAST                2 (resp)
             52 LOAD_CONST               2 (None)
             54 LOAD_CONST               3 (3)
             56 BUILD_SLICE              2
             58 BINARY_SUBSCR
             62 CACHE
             64 CACHE
             66 CACHE
             68 LOAD_CONST               4 ('213')
             70 COMPARE_OP               2 (<)
             74 CACHE
             76 POP_JUMP_IF_FALSE       43 (to 164)

632          78 LOAD_FAST                2 (resp)
             80 LOAD_CONST               3 (3)
             82 LOAD_CONST               2 (None)
             84 BUILD_SLICE              2
             86 BINARY_SUBSCR
             90 CACHE
             92 CACHE
             94 CACHE
             96 STORE_SUBSCR
            100 CACHE
            102 CACHE
            104 CACHE
            106 CACHE
            108 CACHE
            110 CACHE
            112 CACHE
            114 CACHE
            116 CACHE
            118 UNPACK_SEQUENCE          0
            122 CALL                     0
            130 CACHE
            132 STORE_FAST               3 (s)

633         134 LOAD_GLOBAL              5 (NULL + int)
            144 CACHE
            146 LOAD_FAST                3 (s)
            148 UNPACK_SEQUENCE          1
            152 CALL                     1
            160 CACHE
            162 RETURN_VALUE

631     >>  164 LOAD_CONST               2 (None)
            166 RETURN_VALUE

Disassembly of <code object mkd at 0x000001B2A8A08CB0, file "ftplib.py", line 635>:
635           0 RESUME                   0

637           2 LOAD_FAST                0 (self)
              4 STORE_SUBSCR
              8 CACHE
             10 CACHE
             12 CACHE
             14 CACHE
             16 CACHE
             18 CACHE
             20 CACHE
             22 CACHE
             24 CACHE
             26 LOAD_CONST               1 ('MKD ')
             28 LOAD_FAST                1 (dirname)
             30 BINARY_OP                0 (+)
             34 UNPACK_SEQUENCE          1
             38 CALL                     1
             46 CACHE
             48 STORE_FAST               2 (resp)

640          50 LOAD_FAST                2 (resp)
             52 STORE_SUBSCR
             56 CACHE
             58 CACHE
             60 CACHE
             62 CACHE
             64 CACHE
             66 CACHE
             68 CACHE
             70 CACHE
             72 CACHE
             74 LOAD_CONST               2 ('257')
             76 UNPACK_SEQUENCE          1
             80 CALL                     1
             88 CACHE
             90 POP_JUMP_IF_TRUE         2 (to 96)

641          92 LOAD_CONST               3 ('')
             94 RETURN_VALUE

642     >>   96 LOAD_GLOBAL              5 (NULL + parse257)
            106 CACHE
            108 LOAD_FAST                2 (resp)
            110 UNPACK_SEQUENCE          1
            114 CALL                     1
            122 CACHE
            124 RETURN_VALUE

Disassembly of <code object rmd at 0x000001B2A8A0D930, file "ftplib.py", line 644>:
644           0 RESUME                   0

646           2 LOAD_FAST                0 (self)
              4 STORE_SUBSCR
              8 CACHE
             10 CACHE
             12 CACHE
             14 CACHE
             16 CACHE
             18 CACHE
             20 CACHE
             22 CACHE
             24 CACHE
             26 LOAD_CONST               1 ('RMD ')
             28 LOAD_FAST                1 (dirname)
             30 BINARY_OP                0 (+)
             34 UNPACK_SEQUENCE          1
             38 CALL                     1
             46 CACHE
             48 RETURN_VALUE

Disassembly of <code object pwd at 0x000001B2A8A08F30, file "ftplib.py", line 648>:
648           0 RESUME                   0

650           2 LOAD_FAST                0 (self)
              4 STORE_SUBSCR
              8 CACHE
             10 CACHE
             12 CACHE
             14 CACHE
             16 CACHE
             18 CACHE
             20 CACHE
             22 CACHE
             24 CACHE
             26 LOAD_CONST               1 ('PWD')
             28 UNPACK_SEQUENCE          1
             32 CALL                     1
             40 CACHE
             42 STORE_FAST               1 (resp)

653          44 LOAD_FAST                1 (resp)
             46 STORE_SUBSCR
             50 CACHE
             52 CACHE
             54 CACHE
             56 CACHE
             58 CACHE
             60 CACHE
             62 CACHE
             64 CACHE
             66 CACHE
             68 LOAD_CONST               2 ('257')
             70 UNPACK_SEQUENCE          1
             74 CALL                     1
             82 CACHE
             84 POP_JUMP_IF_TRUE         2 (to 90)

654          86 LOAD_CONST               3 ('')
             88 RETURN_VALUE

655     >>   90 LOAD_GLOBAL              5 (NULL + parse257)
            100 CACHE
            102 LOAD_FAST                1 (resp)
            104 UNPACK_SEQUENCE          1
            108 CALL                     1
            116 CACHE
            118 RETURN_VALUE

Disassembly of <code object quit at 0x000001B2A76D9FB0, file "ftplib.py", line 657>:
657           0 RESUME                   0

659           2 LOAD_FAST                0 (self)
              4 STORE_SUBSCR
              8 CACHE
             10 CACHE
             12 CACHE
             14 CACHE
             16 CACHE
             18 CACHE
             20 CACHE
             22 CACHE
             24 CACHE
             26 LOAD_CONST               1 ('QUIT')
             28 UNPACK_SEQUENCE          1
             32 CALL                     1
             40 CACHE
             42 STORE_FAST               1 (resp)

660          44 LOAD_FAST                0 (self)
             46 STORE_SUBSCR
             50 CACHE
             52 CACHE
             54 CACHE
             56 CACHE
             58 CACHE
             60 CACHE
             62 CACHE
             64 CACHE
             66 CACHE
             68 UNPACK_SEQUENCE          0
             72 CALL                     0
             80 CACHE
             82 POP_TOP

661          84 LOAD_FAST                1 (resp)
             86 RETURN_VALUE

Disassembly of <code object close at 0x000001B2A726EC10, file "ftplib.py", line 663>:
663           0 RESUME                   0

665           2 NOP

666           4 LOAD_FAST                0 (self)
              6 LOAD_ATTR                0 (file)
             26 CACHE
             28 CACHE
             30 CACHE

668          32 LOAD_FAST                1 (file)
             34 POP_JUMP_IF_NONE        20 (to 76)

669          36 LOAD_FAST                1 (file)
             38 STORE_SUBSCR
             42 CACHE
             44 CACHE
             46 CACHE
             48 CACHE
             50 CACHE
             52 CACHE
             54 CACHE
             56 CACHE
             58 CACHE
             60 UNPACK_SEQUENCE          0
             64 CALL                     0
             72 CACHE
             74 POP_TOP

671     >>   76 LOAD_FAST                0 (self)
             78 LOAD_ATTR                2 (close)
             98 CACHE
            100 CACHE
            102 CACHE

673         104 LOAD_FAST                2 (sock)
            106 POP_JUMP_IF_NONE        22 (to 152)

674         108 LOAD_FAST                2 (sock)
            110 STORE_SUBSCR
            114 CACHE
            116 CACHE
            118 CACHE
            120 CACHE
            122 CACHE
            124 CACHE
            126 CACHE
            128 CACHE
            130 CACHE
            132 UNPACK_SEQUENCE          0
            136 CALL                     0
            144 CACHE
            146 POP_TOP
            148 LOAD_CONST               1 (None)
            150 RETURN_VALUE

673     >>  152 LOAD_CONST               1 (None)
            154 RETURN_VALUE
        >>  156 PUSH_EXC_INFO

671         158 LOAD_FAST                0 (self)
            160 LOAD_ATTR                2 (close)
            180 CACHE
            182 CACHE
            184 CACHE

673         186 LOAD_FAST                2 (sock)
            188 POP_JUMP_IF_NONE        21 (to 232)

674         190 LOAD_FAST                2 (sock)
            192 STORE_SUBSCR
            196 CACHE
            198 CACHE
            200 CACHE
            202 CACHE
            204 CACHE
            206 CACHE
            208 CACHE
            210 CACHE
            212 CACHE
            214 UNPACK_SEQUENCE          0
            218 CALL                     0
            226 CACHE
            228 POP_TOP
            230 RERAISE                  0

673     >>  232 RERAISE                  0
        >>  234 COPY                     3
            236 POP_EXCEPT
            238 RERAISE                  1
ExceptionTable:
  4 to 74 -> 156 [0]
  156 to 232 -> 234 [1] lasti

Disassembly of <code object FTP_TLS at 0x000001B2A8A08030, file "ftplib.py", line 683>:
              0 MAKE_CELL                0 (__class__)

683           2 RESUME                   0
              4 LOAD_NAME                0 (__name__)
              6 STORE_NAME               1 (__module__)
              8 LOAD_CONST               0 ('FTP_TLS')
             10 STORE_NAME               2 (__qualname__)

684          12 LOAD_CONST               1 ("A FTP subclass which adds TLS support to FTP as described\n        in RFC-4217.\n\n        Connect as usual to port 21 implicitly securing the FTP control\n        connection before authenticating.\n\n        Securing the data connection requires user to explicitly ask\n        for it by calling prot_p() method.\n\n        Usage example:\n        >>> from ftplib import FTP_TLS\n        >>> ftps = FTP_TLS('ftp.python.org')\n        >>> ftps.login()  # login anonymously previously securing control channel\n        '230 Guest login ok, access restrictions apply.'\n        >>> ftps.prot_p()  # switch to secure data connection\n        '200 Protection level set to P'\n        >>> ftps.retrlines('LIST')  # list directory content securely\n        total 9\n        drwxr-xr-x   8 <USER>     <GROUP>        1024 Jan  3  1994 .\n        drwxr-xr-x   8 <USER>     <GROUP>        1024 Jan  3  1994 ..\n        drwxr-xr-x   2 <USER>     <GROUP>        1024 Jan  3  1994 bin\n        drwxr-xr-x   2 <USER>     <GROUP>        1024 Jan  3  1994 etc\n        d-wxrwxr-x   2 <USER>      <GROUP>        1024 Sep  5 13:43 incoming\n        drwxr-xr-x   2 <USER>     <GROUP>        1024 Nov 17  1993 lib\n        drwxr-xr-x   6 <USER>     <GROUP>        1024 Sep 13 19:07 pub\n        drwxr-xr-x   3 <USER>     <GROUP>        1024 Jan  3  1994 usr\n        -rw-r--r--   1 <USER>     <GROUP>          312 Aug  1  1994 welcome.msg\n        '226 Transfer complete.'\n        >>> ftps.quit()\n        '221 Goodbye.'\n        >>>\n        ")
             14 STORE_NAME               3 (__doc__)

716          16 LOAD_NAME                4 (ssl)
             18 LOAD_ATTR                5 (NULL|self + __qualname__)

719          38 LOAD_CONST               3 (None)
             40 LOAD_CONST               3 (None)
             42 LOAD_CONST               3 (None)

720          44 LOAD_NAME                7 (_GLOBAL_DEFAULT_TIMEOUT)
             46 LOAD_CONST               3 (None)

718          48 BUILD_TUPLE              9

721          50 LOAD_CONST               4 ('utf-8')

718          52 LOAD_CONST               5 (('encoding',))
             54 BUILD_CONST_KEY_MAP      1
             56 LOAD_CLOSURE             0 (__class__)
             58 BUILD_TUPLE              1
             60 LOAD_CONST               6 (<code object __init__ at 0x000001B2A75D5D00, file "ftplib.py", line 718>)
             62 MAKE_FUNCTION           11 (defaults, kwdefaults, closure)
             64 STORE_NAME               8 (__init__)

743          66 LOAD_CONST              15 (('', '', '', True))
             68 LOAD_CLOSURE             0 (__class__)
             70 BUILD_TUPLE              1
             72 LOAD_CONST               8 (<code object login at 0x000001B2A72119B0, file "ftplib.py", line 743>)
             74 MAKE_FUNCTION            9 (defaults, closure)
             76 STORE_NAME               9 (login)

748          78 LOAD_CONST               9 (<code object auth at 0x000001B2A75B7040, file "ftplib.py", line 748>)
             80 MAKE_FUNCTION            0
             82 STORE_NAME              10 (auth)

760          84 LOAD_CONST              10 (<code object ccc at 0x000001B2A8A2C030, file "ftplib.py", line 760>)
             86 MAKE_FUNCTION            0
             88 STORE_NAME              11 (ccc)

768          90 LOAD_CONST              11 (<code object prot_p at 0x000001B2A76B4E70, file "ftplib.py", line 768>)
             92 MAKE_FUNCTION            0
             94 STORE_NAME              12 (prot_p)

784          96 LOAD_CONST              12 (<code object prot_c at 0x000001B2A8A0CA30, file "ftplib.py", line 784>)
             98 MAKE_FUNCTION            0
            100 STORE_NAME              13 (prot_c)

792         102 LOAD_CONST              16 ((None,))
            104 LOAD_CLOSURE             0 (__class__)
            106 BUILD_TUPLE              1
            108 LOAD_CONST              13 (<code object ntransfercmd at 0x000001B2A728F110, file "ftplib.py", line 792>)
            110 MAKE_FUNCTION            9 (defaults, closure)
            112 STORE_NAME              14 (ntransfercmd)

799         114 LOAD_CONST              14 (<code object abort at 0x000001B2A728F3F0, file "ftplib.py", line 799>)
            116 MAKE_FUNCTION            0
            118 STORE_NAME              15 (abort)
            120 LOAD_CLOSURE             0 (__class__)
            122 COPY                     1
            124 STORE_NAME              16 (__classcell__)
            126 RETURN_VALUE

Disassembly of <code object __init__ at 0x000001B2A75D5D00, file "ftplib.py", line 718>:
              0 COPY_FREE_VARS           1

718           2 RESUME                   0

722           4 LOAD_FAST                7 (context)
              6 POP_JUMP_IF_NONE        17 (to 42)
              8 LOAD_FAST                5 (keyfile)
             10 POP_JUMP_IF_NONE        15 (to 42)

723          12 LOAD_GLOBAL              1 (NULL + ValueError)
             22 CACHE
             24 LOAD_CONST               1 ('context and keyfile arguments are mutually exclusive')
             26 UNPACK_SEQUENCE          1
             30 CALL                     1
             38 CACHE
             40 RAISE_VARARGS            1

725     >>   42 LOAD_FAST                7 (context)
             44 POP_JUMP_IF_NONE        17 (to 80)
             46 LOAD_FAST                6 (certfile)
             48 POP_JUMP_IF_NONE        15 (to 80)

726          50 LOAD_GLOBAL              1 (NULL + ValueError)
             60 CACHE
             62 LOAD_CONST               2 ('context and certfile arguments are mutually exclusive')
             64 UNPACK_SEQUENCE          1
             68 CALL                     1
             76 CACHE
             78 RAISE_VARARGS            1

728     >>   80 LOAD_FAST                5 (keyfile)
             82 POP_JUMP_IF_NOT_NONE     2 (to 88)
             84 LOAD_FAST                6 (certfile)
             86 POP_JUMP_IF_NONE        32 (to 152)

729     >>   88 LOAD_CONST               3 (0)
             90 LOAD_CONST               0 (None)
             92 IMPORT_NAME              1 (warnings)
             94 STORE_FAST              11 (warnings)

730          96 LOAD_FAST               11 (warnings)
             98 STORE_SUBSCR
            102 CACHE
            104 CACHE
            106 CACHE
            108 CACHE
            110 CACHE
            112 CACHE
            114 CACHE
            116 CACHE
            118 CACHE
            120 LOAD_CONST               4 ('keyfile and certfile are deprecated, use a custom context instead')

731         122 LOAD_GLOBAL              6 (DeprecationWarning)
            132 CACHE
            134 LOAD_CONST               5 (2)

730         136 UNPACK_SEQUENCE          3
            140 CALL                     3
            148 CACHE
            150 POP_TOP

732     >>  152 LOAD_FAST                5 (keyfile)
            154 LOAD_FAST                0 (self)
            156 STORE_ATTR               4 (keyfile)

733         166 LOAD_FAST                6 (certfile)
            168 LOAD_FAST                0 (self)
            170 STORE_ATTR               5 (certfile)

734         180 LOAD_FAST                7 (context)
            182 POP_JUMP_IF_NOT_NONE    28 (to 240)

735         184 LOAD_GLOBAL             13 (NULL + ssl)
            194 CACHE
            196 LOAD_ATTR                7 (NULL|self + DeprecationWarning)
            216 CACHE

736         218 LOAD_FAST                6 (certfile)

737         220 LOAD_FAST                5 (keyfile)

735         222 KW_NAMES                 6 (('certfile', 'keyfile'))
            224 UNPACK_SEQUENCE          3
            228 CALL                     3
            236 CACHE
            238 STORE_FAST               7 (context)

738     >>  240 LOAD_FAST                7 (context)
            242 LOAD_FAST                0 (self)
            244 STORE_ATTR               9 (context)

739         254 LOAD_CONST               7 (False)
            256 LOAD_FAST                0 (self)
            258 STORE_ATTR              10 (_prot_p)

740         268 LOAD_GLOBAL             23 (NULL + super)
            278 CACHE
            280 UNPACK_SEQUENCE          0
            284 CALL                     0
            292 CACHE
            294 STORE_SUBSCR
            298 CACHE
            300 CACHE
            302 CACHE
            304 CACHE
            306 CACHE
            308 CACHE
            310 CACHE
            312 CACHE
            314 CACHE
            316 LOAD_FAST                1 (host)
            318 LOAD_FAST                2 (user)
            320 LOAD_FAST                3 (passwd)
            322 LOAD_FAST                4 (acct)

741         324 LOAD_FAST                8 (timeout)
            326 LOAD_FAST                9 (source_address)
            328 LOAD_FAST               10 (encoding)

740         330 KW_NAMES                 8 (('encoding',))
            332 UNPACK_SEQUENCE          7
            336 CALL                     7
            344 CACHE
            346 POP_TOP
            348 LOAD_CONST               0 (None)
            350 RETURN_VALUE

Disassembly of <code object login at 0x000001B2A72119B0, file "ftplib.py", line 743>:
              0 COPY_FREE_VARS           1

743           2 RESUME                   0

744           4 LOAD_FAST                4 (secure)
              6 POP_JUMP_IF_FALSE       51 (to 110)
              8 LOAD_GLOBAL              1 (NULL + isinstance)
             18 CACHE
             20 LOAD_FAST                0 (self)
             22 LOAD_ATTR                1 (NULL|self + isinstance)
             42 CACHE
             44 LOAD_ATTR                3 (NULL|self + sock)
             64 CACHE
             66 CACHE
             68 POP_JUMP_IF_TRUE        20 (to 110)

745          70 LOAD_FAST                0 (self)
             72 STORE_SUBSCR
             76 CACHE
             78 CACHE
             80 CACHE
             82 CACHE
             84 CACHE
             86 CACHE
             88 CACHE
             90 CACHE
             92 CACHE
             94 UNPACK_SEQUENCE          0
             98 CALL                     0
            106 CACHE
            108 POP_TOP

746     >>  110 LOAD_GLOBAL             11 (NULL + super)
            120 CACHE
            122 UNPACK_SEQUENCE          0
            126 CALL                     0
            134 CACHE
            136 STORE_SUBSCR
            140 CACHE
            142 CACHE
            144 CACHE
            146 CACHE
            148 CACHE
            150 CACHE
            152 CACHE
            154 CACHE
            156 CACHE
            158 LOAD_FAST                1 (user)
            160 LOAD_FAST                2 (passwd)
            162 LOAD_FAST                3 (acct)
            164 UNPACK_SEQUENCE          3
            168 CALL                     3
            176 CACHE
            178 RETURN_VALUE

Disassembly of <code object auth at 0x000001B2A75B7040, file "ftplib.py", line 748>:
748           0 RESUME                   0

750           2 LOAD_GLOBAL              1 (NULL + isinstance)
             12 CACHE
             14 LOAD_FAST                0 (self)
             16 LOAD_ATTR                1 (NULL|self + isinstance)
             36 CACHE
             38 LOAD_ATTR                3 (NULL|self + sock)
             58 CACHE
             60 CACHE
             62 POP_JUMP_IF_FALSE       15 (to 94)

751          64 LOAD_GLOBAL              9 (NULL + ValueError)
             74 CACHE
             76 LOAD_CONST               1 ('Already using TLS')
             78 UNPACK_SEQUENCE          1
             82 CALL                     1
             90 CACHE
             92 RAISE_VARARGS            1

752     >>   94 LOAD_FAST                0 (self)
             96 LOAD_ATTR                5 (NULL|self + ssl)
            116 CACHE
            118 LOAD_ATTR                6 (SSLSocket)
            138 STORE_SUBSCR
            142 CACHE
            144 CACHE
            146 CACHE
            148 CACHE
            150 CACHE
            152 CACHE
            154 CACHE
            156 CACHE
            158 CACHE
            160 LOAD_CONST               2 ('AUTH TLS')
            162 UNPACK_SEQUENCE          1
            166 CALL                     1
            174 CACHE
            176 STORE_FAST               1 (resp)
            178 JUMP_FORWARD            21 (to 222)

755         180 LOAD_FAST                0 (self)
            182 STORE_SUBSCR
            186 CACHE
            188 CACHE
            190 CACHE
            192 CACHE
            194 CACHE
            196 CACHE
            198 CACHE
            200 CACHE
            202 CACHE
            204 LOAD_CONST               3 ('AUTH SSL')
            206 UNPACK_SEQUENCE          1
            210 CALL                     1
            218 CACHE
            220 STORE_FAST               1 (resp)

756     >>  222 LOAD_FAST                0 (self)
            224 LOAD_ATTR                8 (ValueError)
            244 CACHE
            246 CACHE
            248 CACHE
            250 CACHE
            252 CACHE
            254 CACHE
            256 LOAD_FAST                0 (self)
            258 LOAD_ATTR                1 (NULL|self + isinstance)
            278 CACHE
            280 KW_NAMES                 4 (('server_hostname',))
            282 UNPACK_SEQUENCE          2
            286 CALL                     2
            294 CACHE
            296 LOAD_FAST                0 (self)
            298 STORE_ATTR               1 (sock)

757         308 LOAD_FAST                0 (self)
            310 LOAD_ATTR                1 (NULL|self + isinstance)
            330 CACHE
            332 CACHE
            334 CACHE
            336 CACHE
            338 CACHE
            340 CACHE
            342 LOAD_CONST               5 ('r')
            344 LOAD_FAST                0 (self)
            346 LOAD_ATTR               12 (PROTOCOL_TLS)
            366 CACHE
            368 CACHE
            370 CACHE
            372 LOAD_FAST                0 (self)
            374 STORE_ATTR              13 (file)

758         384 LOAD_FAST                1 (resp)
            386 RETURN_VALUE

Disassembly of <code object ccc at 0x000001B2A8A2C030, file "ftplib.py", line 760>:
760           0 RESUME                   0

762           2 LOAD_GLOBAL              1 (NULL + isinstance)
             12 CACHE
             14 LOAD_FAST                0 (self)
             16 LOAD_ATTR                1 (NULL|self + isinstance)
             36 CACHE
             38 LOAD_ATTR                3 (NULL|self + sock)
             58 CACHE
             60 CACHE
             62 POP_JUMP_IF_TRUE        15 (to 94)

763          64 LOAD_GLOBAL              9 (NULL + ValueError)
             74 CACHE
             76 LOAD_CONST               1 ('not using TLS')
             78 UNPACK_SEQUENCE          1
             82 CALL                     1
             90 CACHE
             92 RAISE_VARARGS            1

764     >>   94 LOAD_FAST                0 (self)
             96 STORE_SUBSCR
            100 CACHE
            102 CACHE
            104 CACHE
            106 CACHE
            108 CACHE
            110 CACHE
            112 CACHE
            114 CACHE
            116 CACHE
            118 LOAD_CONST               2 ('CCC')
            120 UNPACK_SEQUENCE          1
            124 CALL                     1
            132 CACHE
            134 STORE_FAST               1 (resp)

765         136 LOAD_FAST                0 (self)
            138 LOAD_ATTR                1 (NULL|self + isinstance)
            158 CACHE
            160 CACHE
            162 CACHE
            164 CACHE
            166 CACHE
            168 CACHE
            170 UNPACK_SEQUENCE          0
            174 CALL                     0
            182 CACHE
            184 LOAD_FAST                0 (self)
            186 STORE_ATTR               1 (sock)

766         196 LOAD_FAST                1 (resp)
            198 RETURN_VALUE

Disassembly of <code object prot_p at 0x000001B2A76B4E70, file "ftplib.py", line 768>:
768           0 RESUME                   0

779           2 LOAD_FAST                0 (self)
              4 STORE_SUBSCR
              8 CACHE
             10 CACHE
             12 CACHE
             14 CACHE
             16 CACHE
             18 CACHE
             20 CACHE
             22 CACHE
             24 CACHE
             26 LOAD_CONST               1 ('PBSZ 0')
             28 UNPACK_SEQUENCE          1
             32 CALL                     1
             40 CACHE
             42 POP_TOP

780          44 LOAD_FAST                0 (self)
             46 STORE_SUBSCR
             50 CACHE
             52 CACHE
             54 CACHE
             56 CACHE
             58 CACHE
             60 CACHE
             62 CACHE
             64 CACHE
             66 CACHE
             68 LOAD_CONST               2 ('PROT P')
             70 UNPACK_SEQUENCE          1
             74 CALL                     1
             82 CACHE
             84 STORE_FAST               1 (resp)

781          86 LOAD_CONST               3 (True)
             88 LOAD_FAST                0 (self)
             90 STORE_ATTR               1 (_prot_p)

782         100 LOAD_FAST                1 (resp)
            102 RETURN_VALUE

Disassembly of <code object prot_c at 0x000001B2A8A0CA30, file "ftplib.py", line 784>:
784           0 RESUME                   0

786           2 LOAD_FAST                0 (self)
              4 STORE_SUBSCR
              8 CACHE
             10 CACHE
             12 CACHE
             14 CACHE
             16 CACHE
             18 CACHE
             20 CACHE
             22 CACHE
             24 CACHE
             26 LOAD_CONST               1 ('PROT C')
             28 UNPACK_SEQUENCE          1
             32 CALL                     1
             40 CACHE
             42 STORE_FAST               1 (resp)

787          44 LOAD_CONST               2 (False)
             46 LOAD_FAST                0 (self)
             48 STORE_ATTR               1 (_prot_p)

788          58 LOAD_FAST                1 (resp)
             60 RETURN_VALUE

Disassembly of <code object ntransfercmd at 0x000001B2A728F110, file "ftplib.py", line 792>:
              0 COPY_FREE_VARS           1

792           2 RESUME                   0

793           4 LOAD_GLOBAL              1 (NULL + super)
             14 CACHE
             16 UNPACK_SEQUENCE          0
             20 CALL                     0
             28 CACHE
             30 STORE_SUBSCR
             34 CACHE
             36 CACHE
             38 CACHE
             40 CACHE
             42 CACHE
             44 CACHE
             46 CACHE
             48 CACHE
             50 CACHE
             52 LOAD_FAST                1 (cmd)
             54 LOAD_FAST                2 (rest)
             56 UNPACK_SEQUENCE          2
             60 CALL                     2
             68 CACHE
             70 UNPACK_SEQUENCE          2
             74 STORE_FAST               3 (conn)
             76 STORE_FAST               4 (size)

794          78 LOAD_FAST                0 (self)
             80 LOAD_ATTR                2 (ntransfercmd)
            100 CACHE
            102 CACHE
            104 STORE_SUBSCR
            108 CACHE
            110 CACHE
            112 CACHE
            114 CACHE
            116 CACHE
            118 CACHE
            120 CACHE
            122 CACHE
            124 CACHE
            126 LOAD_FAST                3 (conn)

796         128 LOAD_FAST                0 (self)
            130 LOAD_ATTR                5 (NULL|self + _prot_p)
            150 CACHE
            152 CACHE
            154 CACHE
            156 STORE_FAST               3 (conn)

797         158 LOAD_FAST                3 (conn)
            160 LOAD_FAST                4 (size)
            162 BUILD_TUPLE              2
            164 RETURN_VALUE

Disassembly of <code object abort at 0x000001B2A728F3F0, file "ftplib.py", line 799>:
799           0 RESUME                   0

801           2 LOAD_CONST               1 (b'ABOR')
              4 LOAD_GLOBAL              0 (B_CRLF)
             14 CACHE
             16 BINARY_OP                0 (+)
             20 STORE_FAST               1 (line)

802          22 LOAD_FAST                0 (self)
             24 LOAD_ATTR                1 (NULL|self + B_CRLF)
             44 CACHE
             46 CACHE
             48 CACHE
             50 CACHE
             52 CACHE
             54 CACHE
             56 LOAD_FAST                1 (line)
             58 UNPACK_SEQUENCE          1
             62 CALL                     1
             70 CACHE
             72 POP_TOP

803          74 LOAD_FAST                0 (self)
             76 STORE_SUBSCR
             80 CACHE
             82 CACHE
             84 CACHE
             86 CACHE
             88 CACHE
             90 CACHE
             92 CACHE
             94 CACHE
             96 CACHE
             98 UNPACK_SEQUENCE          0
            102 CALL                     0
            110 CACHE
            112 STORE_FAST               2 (resp)

804         114 LOAD_FAST                2 (resp)
            116 LOAD_CONST               0 (None)
            118 LOAD_CONST               2 (3)
            120 BUILD_SLICE              2
            122 BINARY_SUBSCR
            126 CACHE
            128 CACHE
            130 CACHE
            132 LOAD_CONST               3 (frozenset({'426', '226', '225'}))
            134 CONTAINS_OP              1
            136 POP_JUMP_IF_FALSE       15 (to 168)

805         138 LOAD_GLOBAL              9 (NULL + error_proto)
            148 CACHE
            150 LOAD_FAST                2 (resp)
            152 UNPACK_SEQUENCE          1
            156 CALL                     1
            164 CACHE
            166 RAISE_VARARGS            1

806     >>  168 LOAD_FAST                2 (resp)
            170 RETURN_VALUE

Disassembly of <code object parse150 at 0x000001B2A721A5B0, file "ftplib.py", line 814>:
814           0 RESUME                   0

819           2 LOAD_FAST                0 (resp)
              4 LOAD_CONST               1 (None)
              6 LOAD_CONST               2 (3)
              8 BUILD_SLICE              2
             10 BINARY_SUBSCR
             14 CACHE
             16 CACHE
             18 CACHE
             20 LOAD_CONST               3 ('150')
             22 COMPARE_OP               3 (<)
             26 CACHE
             28 POP_JUMP_IF_FALSE       15 (to 60)

820          30 LOAD_GLOBAL              1 (NULL + error_reply)
             40 CACHE
             42 LOAD_FAST                0 (resp)
             44 UNPACK_SEQUENCE          1
             48 CALL                     1
             56 CACHE
             58 RAISE_VARARGS            1

822     >>   60 LOAD_GLOBAL              2 (_150_re)
             70 CACHE
             72 POP_JUMP_IF_NOT_NONE    39 (to 152)

823          74 LOAD_CONST               4 (0)
             76 LOAD_CONST               1 (None)
             78 IMPORT_NAME              2 (re)
             80 STORE_FAST               1 (re)

824          82 LOAD_FAST                1 (re)
             84 STORE_SUBSCR
             88 CACHE
             90 CACHE
             92 CACHE
             94 CACHE
             96 CACHE
             98 CACHE
            100 CACHE
            102 CACHE
            104 CACHE

825         106 LOAD_CONST               5 ('150 .* \\((\\d+) bytes\\)')
            108 LOAD_FAST                1 (re)
            110 LOAD_ATTR                4 (re)
            130 CACHE
            132 BINARY_OP                7 (|)

824         136 UNPACK_SEQUENCE          2
            140 CALL                     2
            148 CACHE
            150 STORE_GLOBAL             1 (_150_re)

826     >>  152 LOAD_GLOBAL              2 (_150_re)
            162 CACHE
            164 STORE_SUBSCR
            168 CACHE
            170 CACHE
            172 CACHE
            174 CACHE
            176 CACHE
            178 CACHE
            180 CACHE
            182 CACHE
            184 CACHE
            186 LOAD_FAST                0 (resp)
            188 UNPACK_SEQUENCE          1
            192 CALL                     1
            200 CACHE
            202 STORE_FAST               2 (m)

827         204 LOAD_FAST                2 (m)
            206 POP_JUMP_IF_TRUE         2 (to 212)

828         208 LOAD_CONST               1 (None)
            210 RETURN_VALUE

829     >>  212 LOAD_GLOBAL             15 (NULL + int)
            222 CACHE
            224 LOAD_FAST                2 (m)
            226 STORE_SUBSCR
            230 CACHE
            232 CACHE
            234 CACHE
            236 CACHE
            238 CACHE
            240 CACHE
            242 CACHE
            244 CACHE
            246 CACHE
            248 LOAD_CONST               6 (1)
            250 UNPACK_SEQUENCE          1
            254 CALL                     1
            262 CACHE
            264 UNPACK_SEQUENCE          1
            268 CALL                     1
            276 CACHE
            278 RETURN_VALUE

Disassembly of <code object parse227 at 0x000001B2A74A4890, file "ftplib.py", line 834>:
834           0 RESUME                   0

838           2 LOAD_FAST                0 (resp)
              4 LOAD_CONST               1 (None)
              6 LOAD_CONST               2 (3)
              8 BUILD_SLICE              2
             10 BINARY_SUBSCR
             14 CACHE
             16 CACHE
             18 CACHE
             20 LOAD_CONST               3 ('227')
             22 COMPARE_OP               3 (<)
             26 CACHE
             28 POP_JUMP_IF_FALSE       15 (to 60)

839          30 LOAD_GLOBAL              1 (NULL + error_reply)
             40 CACHE
             42 LOAD_FAST                0 (resp)
             44 UNPACK_SEQUENCE          1
             48 CALL                     1
             56 CACHE
             58 RAISE_VARARGS            1

841     >>   60 LOAD_GLOBAL              2 (_227_re)
             70 CACHE
             72 POP_JUMP_IF_NOT_NONE    31 (to 136)

842          74 LOAD_CONST               4 (0)
             76 LOAD_CONST               1 (None)
             78 IMPORT_NAME              2 (re)
             80 STORE_FAST               1 (re)

843          82 LOAD_FAST                1 (re)
             84 STORE_SUBSCR
             88 CACHE
             90 CACHE
             92 CACHE
             94 CACHE
             96 CACHE
             98 CACHE
            100 CACHE
            102 CACHE
            104 CACHE
            106 LOAD_CONST               5 ('(\\d+),(\\d+),(\\d+),(\\d+),(\\d+),(\\d+)')
            108 LOAD_FAST                1 (re)
            110 LOAD_ATTR                4 (re)
            130 CACHE
            132 CACHE
            134 STORE_GLOBAL             1 (_227_re)

844     >>  136 LOAD_GLOBAL              2 (_227_re)
            146 CACHE
            148 STORE_SUBSCR
            152 CACHE
            154 CACHE
            156 CACHE
            158 CACHE
            160 CACHE
            162 CACHE
            164 CACHE
            166 CACHE
            168 CACHE
            170 LOAD_FAST                0 (resp)
            172 UNPACK_SEQUENCE          1
            176 CALL                     1
            184 CACHE
            186 STORE_FAST               2 (m)

845         188 LOAD_FAST                2 (m)
            190 POP_JUMP_IF_TRUE        15 (to 222)

846         192 LOAD_GLOBAL             13 (NULL + error_proto)
            202 CACHE
            204 LOAD_FAST                0 (resp)
            206 UNPACK_SEQUENCE          1
            210 CALL                     1
            218 CACHE
            220 RAISE_VARARGS            1

847     >>  222 LOAD_FAST                2 (m)
            224 STORE_SUBSCR
            228 CACHE
            230 CACHE
            232 CACHE
            234 CACHE
            236 CACHE
            238 CACHE
            240 CACHE
            242 CACHE
            244 CACHE
            246 UNPACK_SEQUENCE          0
            250 CALL                     0
            258 CACHE
            260 STORE_FAST               3 (numbers)

848         262 LOAD_CONST               6 ('.')
            264 STORE_SUBSCR
            268 CACHE
            270 CACHE
            272 CACHE
            274 CACHE
            276 CACHE
            278 CACHE
            280 CACHE
            282 CACHE
            284 CACHE
            286 LOAD_FAST                3 (numbers)
            288 LOAD_CONST               1 (None)
            290 LOAD_CONST               7 (4)
            292 BUILD_SLICE              2
            294 BINARY_SUBSCR
            298 CACHE
            300 CACHE
            302 CACHE
            304 UNPACK_SEQUENCE          1
            308 CALL                     1
            316 CACHE
            318 STORE_FAST               4 (host)

849         320 LOAD_GLOBAL             19 (NULL + int)
            330 CACHE
            332 LOAD_FAST                3 (numbers)
            334 LOAD_CONST               7 (4)
            336 BINARY_SUBSCR
            340 CACHE
            342 CACHE
            344 CACHE
            346 UNPACK_SEQUENCE          1
            350 CALL                     1
            358 CACHE
            360 LOAD_CONST               8 (8)
            362 BINARY_OP                3 (<<)
            366 LOAD_GLOBAL             19 (NULL + int)
            376 CACHE
            378 LOAD_FAST                3 (numbers)
            380 LOAD_CONST               9 (5)
            382 BINARY_SUBSCR
            386 CACHE
            388 CACHE
            390 CACHE
            392 UNPACK_SEQUENCE          1
            396 CALL                     1
            404 CACHE
            406 BINARY_OP                0 (+)
            410 STORE_FAST               5 (port)

850         412 LOAD_FAST                4 (host)
            414 LOAD_FAST                5 (port)
            416 BUILD_TUPLE              2
            418 RETURN_VALUE

Disassembly of <code object parse229 at 0x000001B2A75C1040, file "ftplib.py", line 853>:
853           0 RESUME                   0

857           2 LOAD_FAST                0 (resp)
              4 LOAD_CONST               1 (None)
              6 LOAD_CONST               2 (3)
              8 BUILD_SLICE              2
             10 BINARY_SUBSCR
             14 CACHE
             16 CACHE
             18 CACHE
             20 LOAD_CONST               3 ('229')
             22 COMPARE_OP               3 (<)
             26 CACHE
             28 POP_JUMP_IF_FALSE       15 (to 60)

858          30 LOAD_GLOBAL              1 (NULL + error_reply)
             40 CACHE
             42 LOAD_FAST                0 (resp)
             44 UNPACK_SEQUENCE          1
             48 CALL                     1
             56 CACHE
             58 RAISE_VARARGS            1

859     >>   60 LOAD_FAST                0 (resp)
             62 STORE_SUBSCR
             66 CACHE
             68 CACHE
             70 CACHE
             72 CACHE
             74 CACHE
             76 CACHE
             78 CACHE
             80 CACHE
             82 CACHE
             84 LOAD_CONST               4 ('(')
             86 UNPACK_SEQUENCE          1
             90 CALL                     1
             98 CACHE
            100 STORE_FAST               2 (left)

860         102 LOAD_FAST                2 (left)
            104 LOAD_CONST               5 (0)
            106 COMPARE_OP               0 (<)
            110 CACHE
            112 POP_JUMP_IF_FALSE       15 (to 144)
            114 LOAD_GLOBAL              5 (NULL + error_proto)
            124 CACHE
            126 LOAD_FAST                0 (resp)
            128 UNPACK_SEQUENCE          1
            132 CALL                     1
            140 CACHE
            142 RAISE_VARARGS            1

861     >>  144 LOAD_FAST                0 (resp)
            146 STORE_SUBSCR
            150 CACHE
            152 CACHE
            154 CACHE
            156 CACHE
            158 CACHE
            160 CACHE
            162 CACHE
            164 CACHE
            166 CACHE
            168 LOAD_CONST               6 (')')
            170 LOAD_FAST                2 (left)
            172 LOAD_CONST               7 (1)
            174 BINARY_OP                0 (+)
            178 UNPACK_SEQUENCE          2
            182 CALL                     2
            190 CACHE
            192 STORE_FAST               3 (right)

862         194 LOAD_FAST                3 (right)
            196 LOAD_CONST               5 (0)
            198 COMPARE_OP               0 (<)
            202 CACHE
            204 POP_JUMP_IF_FALSE       15 (to 236)

863         206 LOAD_GLOBAL              5 (NULL + error_proto)
            216 CACHE
            218 LOAD_FAST                0 (resp)
            220 UNPACK_SEQUENCE          1
            224 CALL                     1
            232 CACHE
            234 RAISE_VARARGS            1

864     >>  236 LOAD_FAST                0 (resp)
            238 LOAD_FAST                2 (left)
            240 LOAD_CONST               7 (1)
            242 BINARY_OP                0 (+)
            246 BINARY_SUBSCR
            250 CACHE
            252 CACHE
            254 CACHE
            256 LOAD_FAST                0 (resp)
            258 LOAD_FAST                3 (right)
            260 LOAD_CONST               7 (1)
            262 BINARY_OP               10 (-)
            266 BINARY_SUBSCR
            270 CACHE
            272 CACHE
            274 CACHE
            276 COMPARE_OP               3 (<)
            280 CACHE
            282 POP_JUMP_IF_FALSE       15 (to 314)

865         284 LOAD_GLOBAL              5 (NULL + error_proto)
            294 CACHE
            296 LOAD_FAST                0 (resp)
            298 UNPACK_SEQUENCE          1
            302 CALL                     1
            310 CACHE
            312 RAISE_VARARGS            1

866     >>  314 LOAD_FAST                0 (resp)
            316 LOAD_FAST                2 (left)
            318 LOAD_CONST               7 (1)
            320 BINARY_OP                0 (+)
            324 LOAD_FAST                3 (right)
            326 BUILD_SLICE              2
            328 BINARY_SUBSCR
            332 CACHE
            334 CACHE
            336 CACHE
            338 STORE_SUBSCR
            342 CACHE
            344 CACHE
            346 CACHE
            348 CACHE
            350 CACHE
            352 CACHE
            354 CACHE
            356 CACHE
            358 CACHE
            360 LOAD_FAST                0 (resp)
            362 LOAD_FAST                2 (left)
            364 LOAD_CONST               7 (1)
            366 BINARY_OP                0 (+)
            370 BINARY_SUBSCR
            374 CACHE
            376 CACHE
            378 CACHE
            380 UNPACK_SEQUENCE          1
            384 CALL                     1
            392 CACHE
            394 STORE_FAST               4 (parts)

867         396 LOAD_GLOBAL              9 (NULL + len)
            406 CACHE
            408 LOAD_FAST                4 (parts)
            410 UNPACK_SEQUENCE          1
            414 CALL                     1
            422 CACHE
            424 LOAD_CONST               8 (5)
            426 COMPARE_OP               3 (<)
            430 CACHE
            432 POP_JUMP_IF_FALSE       15 (to 464)

868         434 LOAD_GLOBAL              5 (NULL + error_proto)
            444 CACHE
            446 LOAD_FAST                0 (resp)
            448 UNPACK_SEQUENCE          1
            452 CALL                     1
            460 CACHE
            462 RAISE_VARARGS            1

869     >>  464 LOAD_FAST                1 (peer)
            466 LOAD_CONST               5 (0)
            468 BINARY_SUBSCR
            472 CACHE
            474 CACHE
            476 CACHE
            478 STORE_FAST               5 (host)

870         480 LOAD_GLOBAL             11 (NULL + int)
            490 CACHE
            492 LOAD_FAST                4 (parts)
            494 LOAD_CONST               2 (3)
            496 BINARY_SUBSCR
            500 CACHE
            502 CACHE
            504 CACHE
            506 UNPACK_SEQUENCE          1
            510 CALL                     1
            518 CACHE
            520 STORE_FAST               6 (port)

871         522 LOAD_FAST                5 (host)
            524 LOAD_FAST                6 (port)
            526 BUILD_TUPLE              2
            528 RETURN_VALUE

Disassembly of <code object parse257 at 0x000001B2A722EF70, file "ftplib.py", line 874>:
874           0 RESUME                   0

878           2 LOAD_FAST                0 (resp)
              4 LOAD_CONST               1 (None)
              6 LOAD_CONST               2 (3)
              8 BUILD_SLICE              2
             10 BINARY_SUBSCR
             14 CACHE
             16 CACHE
             18 CACHE
             20 LOAD_CONST               3 ('257')
             22 COMPARE_OP               3 (<)
             26 CACHE
             28 POP_JUMP_IF_FALSE       15 (to 60)

879          30 LOAD_GLOBAL              1 (NULL + error_reply)
             40 CACHE
             42 LOAD_FAST                0 (resp)
             44 UNPACK_SEQUENCE          1
             48 CALL                     1
             56 CACHE
             58 RAISE_VARARGS            1

880     >>   60 LOAD_FAST                0 (resp)
             62 LOAD_CONST               2 (3)
             64 LOAD_CONST               4 (5)
             66 BUILD_SLICE              2
             68 BINARY_SUBSCR
             72 CACHE
             74 CACHE
             76 CACHE
             78 LOAD_CONST               5 (' "')
             80 COMPARE_OP               3 (<)
             84 CACHE
             86 POP_JUMP_IF_FALSE        2 (to 92)

881          88 LOAD_CONST               6 ('')
             90 RETURN_VALUE

882     >>   92 LOAD_CONST               6 ('')
             94 STORE_FAST               1 (dirname)

883          96 LOAD_CONST               4 (5)
             98 STORE_FAST               2 (i)

884         100 LOAD_GLOBAL              3 (NULL + len)
            110 CACHE
            112 LOAD_FAST                0 (resp)
            114 UNPACK_SEQUENCE          1
            118 CALL                     1
            126 CACHE
            128 STORE_FAST               3 (n)

885         130 LOAD_FAST                2 (i)
            132 LOAD_FAST                3 (n)
            134 COMPARE_OP               0 (<)
            138 CACHE
            140 POP_JUMP_IF_FALSE       54 (to 250)

886         142 LOAD_FAST                0 (resp)
            144 LOAD_FAST                2 (i)
            146 BINARY_SUBSCR
            150 CACHE
            152 CACHE
            154 CACHE
            156 STORE_FAST               4 (c)

887         158 LOAD_FAST                2 (i)
            160 LOAD_CONST               7 (1)
            162 BINARY_OP                0 (+)
            166 STORE_FAST               2 (i)

888         168 LOAD_FAST                4 (c)
            170 LOAD_CONST               8 ('"')
            172 COMPARE_OP               2 (<)
            176 CACHE
            178 POP_JUMP_IF_FALSE       24 (to 228)

889         180 LOAD_FAST                2 (i)
            182 LOAD_FAST                3 (n)
            184 COMPARE_OP               5 (<)
            188 CACHE
            190 POP_JUMP_IF_TRUE        12 (to 216)
            192 LOAD_FAST                0 (resp)
            194 LOAD_FAST                2 (i)
            196 BINARY_SUBSCR
            200 CACHE
            202 CACHE
            204 CACHE
            206 LOAD_CONST               8 ('"')
            208 COMPARE_OP               3 (<)
            212 CACHE
            214 POP_JUMP_IF_FALSE        1 (to 218)

890     >>  216 JUMP_FORWARD            16 (to 250)

891     >>  218 LOAD_FAST                2 (i)
            220 LOAD_CONST               7 (1)
            222 BINARY_OP                0 (+)
            226 STORE_FAST               2 (i)

892     >>  228 LOAD_FAST                1 (dirname)
            230 LOAD_FAST                4 (c)
            232 BINARY_OP                0 (+)
            236 STORE_FAST               1 (dirname)

885         238 LOAD_FAST                2 (i)
            240 LOAD_FAST                3 (n)
            242 COMPARE_OP               0 (<)
            246 CACHE
