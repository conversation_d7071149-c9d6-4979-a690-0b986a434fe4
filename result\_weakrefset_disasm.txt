# Code object from position 7773873
# Filename: _weakrefset.py
# Name: <module>
# Args: 0
# Locals: 0

  0           0 RESUME                   0

  5           2 LOAD_CONST               0 (0)
              4 LOAD_CONST               1 (('ref',))
              6 IMPORT_NAME              0 (_weakref)
              8 IMPORT_FROM              1 (ref)
             10 STORE_NAME               1 (ref)
             12 POP_TOP

  6          14 LOAD_CONST               0 (0)
             16 LOAD_CONST               2 (('<PERSON><PERSON><PERSON><PERSON><PERSON>',))
             18 IMPORT_NAME              2 (types)
             20 IMPORT_FROM              3 (GenericAlias)
             22 STORE_NAME               3 (GenericAlias)
             24 POP_TOP

  8          26 LOAD_CONST               3 ('WeakSet')
             28 BUILD_LIST               1
             30 STORE_NAME               4 (__all__)

 11          32 PUSH_NULL
             34 LOAD_BUILD_CLASS
             36 LOAD_CONST               4 (<code object _IterationGuard at 0x000001A2D06A8C70, file "_weakrefset.py", line 11>)
             38 MAKE_FUNCTION            0
             40 LOAD_CONST               5 ('_IterationGuard')
             42 UNPACK_SEQUENCE          2
             46 CALL                     2
             54 CACHE
             56 STORE_NAME               5 (_IterationGuard)

 36          58 PUSH_NULL
             60 LOAD_BUILD_CLASS
             62 LOAD_CONST               6 (<code object WeakSet at 0x000001A2D016F2F0, file "_weakrefset.py", line 36>)
             64 MAKE_FUNCTION            0
             66 LOAD_CONST               3 ('WeakSet')
             68 UNPACK_SEQUENCE          2
             72 CALL                     2
             80 CACHE
             82 STORE_NAME               6 (WeakSet)
             84 LOAD_CONST               7 (None)
             86 RETURN_VALUE

Disassembly of <code object _IterationGuard at 0x000001A2D06A8C70, file "_weakrefset.py", line 11>:
 11           0 RESUME                   0
              2 LOAD_NAME                0 (__name__)
              4 STORE_NAME               1 (__module__)
              6 LOAD_CONST               0 ('_IterationGuard')
              8 STORE_NAME               2 (__qualname__)

 17          10 LOAD_CONST               1 (<code object __init__ at 0x000001A2D0632880, file "_weakrefset.py", line 17>)
             12 MAKE_FUNCTION            0
             14 STORE_NAME               3 (__init__)

 21          16 LOAD_CONST               2 (<code object __enter__ at 0x000001A2D062A500, file "_weakrefset.py", line 21>)
             18 MAKE_FUNCTION            0
             20 STORE_NAME               4 (__enter__)

 27          22 LOAD_CONST               3 (<code object __exit__ at 0x000001A2D0113470, file "_weakrefset.py", line 27>)
             24 MAKE_FUNCTION            0
             26 STORE_NAME               5 (__exit__)
             28 LOAD_CONST               4 (None)
             30 RETURN_VALUE

Disassembly of <code object __init__ at 0x000001A2D0632880, file "_weakrefset.py", line 17>:
 17           0 RESUME                   0

 19           2 LOAD_GLOBAL              1 (NULL + ref)
             12 CACHE
             14 LOAD_FAST                1 (weakcontainer)
             16 UNPACK_SEQUENCE          1
             20 CALL                     1
             28 CACHE
             30 LOAD_FAST                0 (self)
             32 STORE_ATTR               1 (weakcontainer)
             42 LOAD_CONST               0 (None)
             44 RETURN_VALUE

Disassembly of <code object __enter__ at 0x000001A2D062A500, file "_weakrefset.py", line 21>:
 21           0 RESUME                   0

 22           2 LOAD_FAST                0 (self)
              4 STORE_SUBSCR
              8 CACHE
             10 CACHE
             12 CACHE
             14 CACHE
             16 CACHE
             18 CACHE
             20 CACHE
             22 CACHE
             24 CACHE
             26 UNPACK_SEQUENCE          0
             30 CALL                     0
             38 CACHE
             40 STORE_FAST               1 (w)

 23          42 LOAD_FAST                1 (w)
             44 POP_JUMP_IF_NONE        26 (to 98)

 24          46 LOAD_FAST                1 (w)
             48 LOAD_ATTR                1 (NULL|self + weakcontainer)
             68 CACHE
             70 CACHE
             72 CACHE
             74 CACHE
             76 CACHE
             78 CACHE
             80 LOAD_FAST                0 (self)
             82 UNPACK_SEQUENCE          1
             86 CALL                     1
             94 CACHE
             96 POP_TOP

 25     >>   98 LOAD_FAST                0 (self)
            100 RETURN_VALUE

Disassembly of <code object __exit__ at 0x000001A2D0113470, file "_weakrefset.py", line 27>:
 27           0 RESUME                   0

 28           2 LOAD_FAST                0 (self)
              4 STORE_SUBSCR
              8 CACHE
             10 CACHE
             12 CACHE
             14 CACHE
             16 CACHE
             18 CACHE
             20 CACHE
             22 CACHE
             24 CACHE
             26 UNPACK_SEQUENCE          0
             30 CALL                     0
             38 CACHE
             40 STORE_FAST               4 (w)

 29          42 LOAD_FAST                4 (w)
             44 POP_JUMP_IF_NONE        52 (to 150)

 30          46 LOAD_FAST                4 (w)
             48 LOAD_ATTR                1 (NULL|self + weakcontainer)
             68 CACHE
             70 CACHE
             72 CACHE
             74 CACHE
             76 CACHE
             78 CACHE
             80 CACHE
             82 CACHE
             84 LOAD_FAST                0 (self)
             86 UNPACK_SEQUENCE          1
             90 CALL                     1
             98 CACHE
            100 POP_TOP

 32         102 LOAD_FAST                5 (s)
            104 POP_JUMP_IF_TRUE        24 (to 154)

 33         106 LOAD_FAST                4 (w)
            108 STORE_SUBSCR
            112 CACHE
            114 CACHE
            116 CACHE
            118 CACHE
            120 CACHE
            122 CACHE
            124 CACHE
            126 CACHE
            128 CACHE
            130 UNPACK_SEQUENCE          0
            134 CALL                     0
            142 CACHE
            144 POP_TOP
            146 LOAD_CONST               0 (None)
            148 RETURN_VALUE

 29     >>  150 LOAD_CONST               0 (None)
            152 RETURN_VALUE

 32     >>  154 LOAD_CONST               0 (None)
            156 RETURN_VALUE

Disassembly of <code object WeakSet at 0x000001A2D016F2F0, file "_weakrefset.py", line 36>:
 36           0 RESUME                   0
              2 LOAD_NAME                0 (__name__)
              4 STORE_NAME               1 (__module__)
              6 LOAD_CONST               0 ('WeakSet')
              8 STORE_NAME               2 (__qualname__)

 37          10 LOAD_CONST              33 ((None,))
             12 LOAD_CONST               2 (<code object __init__ at 0x000001A2D06C8B20, file "_weakrefset.py", line 37>)
             14 MAKE_FUNCTION            1 (defaults)
             16 STORE_NAME               3 (__init__)

 53          18 LOAD_CONST               3 (<code object _commit_removals at 0x000001A2D064CFF0, file "_weakrefset.py", line 53>)
             20 MAKE_FUNCTION            0
             22 STORE_NAME               4 (_commit_removals)

 63          24 LOAD_CONST               4 (<code object __iter__ at 0x000001A2D064D140, file "_weakrefset.py", line 63>)
             26 MAKE_FUNCTION            0
             28 STORE_NAME               5 (__iter__)

 72          30 LOAD_CONST               5 (<code object __len__ at 0x000001A2D06A6310, file "_weakrefset.py", line 72>)
             32 MAKE_FUNCTION            0
             34 STORE_NAME               6 (__len__)

 75          36 LOAD_CONST               6 (<code object __contains__ at 0x000001A2D06A6430, file "_weakrefset.py", line 75>)
             38 MAKE_FUNCTION            0
             40 STORE_NAME               7 (__contains__)

 82          42 LOAD_CONST               7 (<code object __reduce__ at 0x000001A2D06A6550, file "_weakrefset.py", line 82>)
             44 MAKE_FUNCTION            0
             46 STORE_NAME               8 (__reduce__)

 85          48 LOAD_CONST               8 (<code object add at 0x000001A2D06D0030, file "_weakrefset.py", line 85>)
             50 MAKE_FUNCTION            0
             52 STORE_NAME               9 (add)

 90          54 LOAD_CONST               9 (<code object clear at 0x000001A2D062A630, file "_weakrefset.py", line 90>)
             56 MAKE_FUNCTION            0
             58 STORE_NAME              10 (clear)

 95          60 LOAD_CONST              10 (<code object copy at 0x000001A2D0632C40, file "_weakrefset.py", line 95>)
             62 MAKE_FUNCTION            0
             64 STORE_NAME              11 (copy)

 98          66 LOAD_CONST              11 (<code object pop at 0x000001A2D06C8FD0, file "_weakrefset.py", line 98>)
             68 MAKE_FUNCTION            0
             70 STORE_NAME              12 (pop)

110          72 LOAD_CONST              12 (<code object remove at 0x000001A2D064D290, file "_weakrefset.py", line 110>)
             74 MAKE_FUNCTION            0
             76 STORE_NAME              13 (remove)

115          78 LOAD_CONST              13 (<code object discard at 0x000001A2D064D3E0, file "_weakrefset.py", line 115>)
             80 MAKE_FUNCTION            0
             82 STORE_NAME              14 (discard)

120          84 LOAD_CONST              14 (<code object update at 0x000001A2D062A760, file "_weakrefset.py", line 120>)
             86 MAKE_FUNCTION            0
             88 STORE_NAME              15 (update)

126          90 LOAD_CONST              15 (<code object __ior__ at 0x000001A2D0632E20, file "_weakrefset.py", line 126>)
             92 MAKE_FUNCTION            0
             94 STORE_NAME              16 (__ior__)

130          96 LOAD_CONST              16 (<code object difference at 0x000001A2D06A6670, file "_weakrefset.py", line 130>)
             98 MAKE_FUNCTION            0
            100 STORE_NAME              17 (difference)

134         102 LOAD_NAME               17 (difference)
            104 STORE_NAME              18 (__sub__)

136         106 LOAD_CONST              17 (<code object difference_update at 0x000001A2D0632F10, file "_weakrefset.py", line 136>)
            108 MAKE_FUNCTION            0
            110 STORE_NAME              19 (difference_update)

138         112 LOAD_CONST              18 (<code object __isub__ at 0x000001A2D0153330, file "_weakrefset.py", line 138>)
            114 MAKE_FUNCTION            0
            116 STORE_NAME              20 (__isub__)

147         118 LOAD_CONST              19 (<code object intersection at 0x000001A2D06418A0, file "_weakrefset.py", line 147>)
            120 MAKE_FUNCTION            0
            122 STORE_NAME              21 (intersection)

149         124 LOAD_NAME               21 (intersection)
            126 STORE_NAME              22 (__and__)

151         128 LOAD_CONST              20 (<code object intersection_update at 0x000001A2D06331E0, file "_weakrefset.py", line 151>)
            130 MAKE_FUNCTION            0
            132 STORE_NAME              23 (intersection_update)

153         134 LOAD_CONST              21 (<code object __iand__ at 0x000001A2D064D530, file "_weakrefset.py", line 153>)
            136 MAKE_FUNCTION            0
            138 STORE_NAME              24 (__iand__)

159         140 LOAD_CONST              22 (<code object issubset at 0x000001A2D06419B0, file "_weakrefset.py", line 159>)
            142 MAKE_FUNCTION            0
            144 STORE_NAME              25 (issubset)

161         146 LOAD_NAME               25 (issubset)
            148 STORE_NAME              26 (__le__)

163         150 LOAD_CONST              23 (<code object __lt__ at 0x000001A2D06A6790, file "_weakrefset.py", line 163>)
            152 MAKE_FUNCTION            0
            154 STORE_NAME              27 (__lt__)

166         156 LOAD_CONST              24 (<code object issuperset at 0x000001A2D0641AC0, file "_weakrefset.py", line 166>)
            158 MAKE_FUNCTION            0
            160 STORE_NAME              28 (issuperset)

168         162 LOAD_NAME               28 (issuperset)
            164 STORE_NAME              29 (__ge__)

170         166 LOAD_CONST              25 (<code object __gt__ at 0x000001A2D06A68B0, file "_weakrefset.py", line 170>)
            168 MAKE_FUNCTION            0
            170 STORE_NAME              30 (__gt__)

173         172 LOAD_CONST              26 (<code object __eq__ at 0x000001A2D064D680, file "_weakrefset.py", line 173>)
            174 MAKE_FUNCTION            0
            176 STORE_NAME              31 (__eq__)

178         178 LOAD_CONST              27 (<code object symmetric_difference at 0x000001A2D06A69D0, file "_weakrefset.py", line 178>)
            180 MAKE_FUNCTION            0
            182 STORE_NAME              32 (symmetric_difference)

182         184 LOAD_NAME               32 (symmetric_difference)
            186 STORE_NAME              33 (__xor__)

184         188 LOAD_CONST              28 (<code object symmetric_difference_update at 0x000001A2D06332D0, file "_weakrefset.py", line 184>)
            190 MAKE_FUNCTION            0
            192 STORE_NAME              34 (symmetric_difference_update)

186         194 LOAD_CONST              29 (<code object __ixor__ at 0x000001A2D06C9160, file "_weakrefset.py", line 186>)
            196 MAKE_FUNCTION            0
            198 STORE_NAME              35 (__ixor__)

195         200 LOAD_CONST              30 (<code object union at 0x000001A2D0641F00, file "_weakrefset.py", line 195>)
            202 MAKE_FUNCTION            0
            204 STORE_NAME              36 (union)

197         206 LOAD_NAME               36 (union)
            208 STORE_NAME              37 (__or__)

199         210 LOAD_CONST              31 (<code object isdisjoint at 0x000001A2D0642010, file "_weakrefset.py", line 199>)
            212 MAKE_FUNCTION            0
            214 STORE_NAME              38 (isdisjoint)

202         216 LOAD_CONST              32 (<code object __repr__ at 0x000001A2D06335A0, file "_weakrefset.py", line 202>)
            218 MAKE_FUNCTION            0
            220 STORE_NAME              39 (__repr__)

205         222 PUSH_NULL
            224 LOAD_NAME               40 (classmethod)
            226 LOAD_NAME               41 (GenericAlias)
            228 UNPACK_SEQUENCE          1
            232 CALL                     1
            240 CACHE
            242 STORE_NAME              42 (__class_getitem__)
            244 LOAD_CONST               1 (None)
            246 RETURN_VALUE

Disassembly of <code object __init__ at 0x000001A2D06C8B20, file "_weakrefset.py", line 37>:
 37           0 RESUME                   0

 38           2 LOAD_GLOBAL              1 (NULL + set)
             12 CACHE
             14 UNPACK_SEQUENCE          0
             18 CALL                     0
             26 CACHE
             28 LOAD_FAST                0 (self)
             30 STORE_ATTR               1 (data)

 39          40 LOAD_GLOBAL              5 (NULL + ref)
             50 CACHE
             52 LOAD_FAST                0 (self)
             54 UNPACK_SEQUENCE          1
             58 CALL                     1
             66 CACHE
             68 BUILD_TUPLE              1
             70 LOAD_CONST               1 (<code object _remove at 0x000001A2D0113E10, file "_weakrefset.py", line 39>)
             72 MAKE_FUNCTION            1 (defaults)
             74 STORE_FAST               2 (_remove)

 46          76 LOAD_FAST                2 (_remove)
             78 LOAD_FAST                0 (self)
             80 STORE_ATTR               3 (_remove)

 48          90 BUILD_LIST               0
             92 LOAD_FAST                0 (self)
             94 STORE_ATTR               4 (_pending_removals)

 49         104 LOAD_GLOBAL              1 (NULL + set)
            114 CACHE
            116 UNPACK_SEQUENCE          0
            120 CALL                     0
            128 CACHE
            130 LOAD_FAST                0 (self)
            132 STORE_ATTR               5 (_iterating)

 50         142 LOAD_FAST                1 (data)
            144 POP_JUMP_IF_NONE        23 (to 192)

 51         146 LOAD_FAST                0 (self)
            148 STORE_SUBSCR
            152 CACHE
            154 CACHE
            156 CACHE
            158 CACHE
            160 CACHE
            162 CACHE
            164 CACHE
            166 CACHE
            168 CACHE
            170 LOAD_FAST                1 (data)
            172 UNPACK_SEQUENCE          1
            176 CALL                     1
            184 CACHE
            186 POP_TOP
            188 LOAD_CONST               0 (None)
            190 RETURN_VALUE

 50     >>  192 LOAD_CONST               0 (None)
            194 RETURN_VALUE

Disassembly of <code object _remove at 0x000001A2D0113E10, file "_weakrefset.py", line 39>:
 39           0 RESUME                   0

 40           2 PUSH_NULL
              4 LOAD_FAST                1 (selfref)
              6 UNPACK_SEQUENCE          0
             10 CALL                     0
             18 CACHE
             20 STORE_FAST               2 (self)

 41          22 LOAD_FAST                2 (self)
             24 POP_JUMP_IF_NONE        63 (to 152)

 42          26 LOAD_FAST                2 (self)
             28 LOAD_ATTR                0 (_iterating)
             48 CACHE
             50 CACHE
             52 STORE_SUBSCR
             56 CACHE
             58 CACHE
             60 CACHE
             62 CACHE
             64 CACHE
             66 CACHE
             68 CACHE
             70 CACHE
             72 CACHE
             74 LOAD_FAST                0 (item)
             76 UNPACK_SEQUENCE          1
             80 CALL                     1
             88 CACHE
             90 POP_TOP
             92 LOAD_CONST               0 (None)
             94 RETURN_VALUE

 45          96 LOAD_FAST                2 (self)
             98 LOAD_ATTR                3 (NULL|self + _pending_removals)
            118 CACHE
            120 CACHE
            122 CACHE
            124 CACHE
            126 CACHE
            128 CACHE
            130 LOAD_FAST                0 (item)
            132 UNPACK_SEQUENCE          1
            136 CALL                     1
            144 CACHE
            146 POP_TOP
            148 LOAD_CONST               0 (None)
            150 RETURN_VALUE

 41     >>  152 LOAD_CONST               0 (None)
            154 RETURN_VALUE

Disassembly of <code object _commit_removals at 0x000001A2D064CFF0, file "_weakrefset.py", line 53>:
 53           0 RESUME                   0

 54           2 LOAD_FAST                0 (self)
              4 LOAD_ATTR                0 (_pending_removals)
             24 STORE_FAST               1 (pop)

 55          26 LOAD_FAST                0 (self)
             28 LOAD_ATTR                2 (pop)
             48 STORE_FAST               2 (discard)

 56          50 NOP

 57     >>   52 NOP

 58          54 PUSH_NULL
             56 LOAD_FAST                1 (pop)
             58 UNPACK_SEQUENCE          0
             62 CALL                     0
             70 CACHE
             72 STORE_FAST               3 (item)
             74 JUMP_FORWARD            17 (to 110)
        >>   76 PUSH_EXC_INFO

 59          78 LOAD_GLOBAL              8 (IndexError)
             88 CACHE
             90 CHECK_EXC_MATCH
             92 POP_JUMP_IF_FALSE        4 (to 102)
             94 POP_TOP

 60          96 POP_EXCEPT
             98 LOAD_CONST               0 (None)
            100 RETURN_VALUE

 59     >>  102 RERAISE                  0
        >>  104 COPY                     3
            106 POP_EXCEPT
            108 RERAISE                  1

 61     >>  110 PUSH_NULL
            112 LOAD_FAST                2 (discard)
            114 LOAD_FAST                3 (item)
            116 UNPACK_SEQUENCE          1
            120 CALL                     1
            128 CACHE
            130 POP_TOP

 56         132 JUMP_BACKWARD           41 (to 52)
ExceptionTable:
  54 to 72 -> 76 [0]
  76 to 94 -> 104 [1] lasti
  102 to 102 -> 104 [1] lasti

Disassembly of <code object __iter__ at 0x000001A2D064D140, file "_weakrefset.py", line 63>:
 63           0 RETURN_GENERATOR
              2 POP_TOP
              4 RESUME                   0

 64           6 LOAD_GLOBAL              1 (NULL + _IterationGuard)
             16 CACHE
             18 LOAD_FAST                0 (self)
             20 UNPACK_SEQUENCE          1
             24 CALL                     1
             32 CACHE
             34 BEFORE_WITH
             36 POP_TOP

 65          38 LOAD_FAST                0 (self)
             40 LOAD_ATTR                1 (NULL|self + _IterationGuard)
             60 UNPACK_SEQUENCE          0
             64 CALL                     0
             72 CACHE
             74 STORE_FAST               2 (item)

 67          76 LOAD_FAST                2 (item)
             78 POP_JUMP_IF_NONE         4 (to 88)

 70          80 LOAD_FAST                2 (item)
             82 LOAD_FAST                0 (self)
             84 RESUME                   1
             86 POP_TOP
        >>   88 JUMP_BACKWARD           19 (to 52)

 65          90 NOP

 64          92 LOAD_CONST               0 (None)
             94 LOAD_CONST               0 (None)
             96 LOAD_CONST               0 (None)
             98 UNPACK_SEQUENCE          2
            102 CALL                     2
            110 CACHE
            112 POP_TOP
            114 LOAD_CONST               0 (None)
            116 RETURN_VALUE
        >>  118 PUSH_EXC_INFO
            120 WITH_EXCEPT_START
            122 POP_JUMP_IF_TRUE         4 (to 132)
            124 RERAISE                  2
        >>  126 COPY                     3
            128 POP_EXCEPT
            130 RERAISE                  1
        >>  132 POP_TOP
            134 POP_EXCEPT
            136 POP_TOP
            138 POP_TOP
            140 LOAD_CONST               0 (None)
            142 RETURN_VALUE
ExceptionTable:
  36 to 88 -> 118 [1] lasti
  118 to 124 -> 126 [3] lasti
  132 to 132 -> 126 [3] lasti

Disassembly of <code object __len__ at 0x000001A2D06A6310, file "_weakrefset.py", line 72>:
 72           0 RESUME                   0

 73           2 LOAD_GLOBAL              1 (NULL + len)
             12 CACHE
             14 LOAD_FAST                0 (self)
             16 LOAD_ATTR                1 (NULL|self + len)
             36 CACHE
             38 CACHE
             40 LOAD_GLOBAL              1 (NULL + len)
             50 CACHE
             52 LOAD_FAST                0 (self)
             54 LOAD_ATTR                2 (data)
             74 CACHE
             76 CACHE
             78 BINARY_OP               10 (-)
             82 RETURN_VALUE

Disassembly of <code object __contains__ at 0x000001A2D06A6430, file "_weakrefset.py", line 75>:
 75           0 RESUME                   0

 76           2 NOP

 77           4 LOAD_GLOBAL              1 (NULL + ref)
             14 CACHE
             16 LOAD_FAST                1 (item)
             18 UNPACK_SEQUENCE          1
             22 CALL                     1
             30 CACHE
             32 STORE_FAST               2 (wr)
             34 JUMP_FORWARD            17 (to 70)
        >>   36 PUSH_EXC_INFO

 78          38 LOAD_GLOBAL              2 (TypeError)
             48 CACHE
             50 CHECK_EXC_MATCH
             52 POP_JUMP_IF_FALSE        4 (to 62)
             54 POP_TOP

 79          56 POP_EXCEPT
             58 LOAD_CONST               1 (False)
             60 RETURN_VALUE

 78     >>   62 RERAISE                  0
        >>   64 COPY                     3
             66 POP_EXCEPT
             68 RERAISE                  1

 80     >>   70 LOAD_FAST                2 (wr)
             72 LOAD_FAST                0 (self)
             74 LOAD_ATTR                2 (TypeError)
ExceptionTable:
  4 to 32 -> 36 [0]
  36 to 54 -> 64 [1] lasti
  62 to 62 -> 64 [1] lasti

Disassembly of <code object __reduce__ at 0x000001A2D06A6550, file "_weakrefset.py", line 82>:
 82           0 RESUME                   0

 83           2 LOAD_FAST                0 (self)
              4 LOAD_ATTR                0 (__class__)
             24 CACHE
             26 LOAD_FAST                0 (self)
             28 UNPACK_SEQUENCE          1
             32 CALL                     1
             40 CACHE
             42 BUILD_TUPLE              1
             44 LOAD_FAST                0 (self)
             46 STORE_SUBSCR
             50 CACHE
             52 CACHE
             54 CACHE
             56 CACHE
             58 CACHE
             60 CACHE
             62 CACHE
             64 CACHE
             66 CACHE
             68 UNPACK_SEQUENCE          0
             72 CALL                     0
             80 CACHE
             82 BUILD_TUPLE              3
             84 RETURN_VALUE

Disassembly of <code object add at 0x000001A2D06D0030, file "_weakrefset.py", line 85>:
 85           0 RESUME                   0

 86           2 LOAD_FAST                0 (self)
              4 LOAD_ATTR                0 (_pending_removals)
             24 CACHE
             26 CACHE
             28 CACHE
             30 CACHE
             32 CACHE
             34 CACHE
             36 CACHE
             38 CACHE
             40 UNPACK_SEQUENCE          0
             44 CALL                     0
             52 CACHE
             54 POP_TOP

 88          56 LOAD_FAST                0 (self)
             58 LOAD_ATTR                2 (_commit_removals)
             78 CACHE
             80 CACHE
             82 CACHE
             84 CACHE
             86 CACHE
             88 CACHE
             90 LOAD_GLOBAL              9 (NULL + ref)
            100 CACHE
            102 LOAD_FAST                1 (item)
            104 LOAD_FAST                0 (self)
            106 LOAD_ATTR                5 (NULL|self + data)
            126 CACHE
            128 CACHE
            130 UNPACK_SEQUENCE          1
            134 CALL                     1
            142 CACHE
            144 POP_TOP
            146 LOAD_CONST               0 (None)
            148 RETURN_VALUE

Disassembly of <code object clear at 0x000001A2D062A630, file "_weakrefset.py", line 90>:
 90           0 RESUME                   0

 91           2 LOAD_FAST                0 (self)
              4 LOAD_ATTR                0 (_pending_removals)
             24 CACHE
             26 CACHE
             28 CACHE
             30 CACHE
             32 CACHE
             34 CACHE
             36 CACHE
             38 CACHE
             40 UNPACK_SEQUENCE          0
             44 CALL                     0
             52 CACHE
             54 POP_TOP

 93          56 LOAD_FAST                0 (self)
             58 LOAD_ATTR                2 (_commit_removals)
             78 CACHE
             80 CACHE
             82 CACHE
             84 CACHE
             86 CACHE
             88 CACHE
             90 UNPACK_SEQUENCE          0
             94 CALL                     0
            102 CACHE
            104 POP_TOP
            106 LOAD_CONST               0 (None)
            108 RETURN_VALUE

Disassembly of <code object copy at 0x000001A2D0632C40, file "_weakrefset.py", line 95>:
 95           0 RESUME                   0

 96           2 LOAD_FAST                0 (self)
              4 STORE_SUBSCR
              8 CACHE
             10 CACHE
             12 CACHE
             14 CACHE
             16 CACHE
             18 CACHE
             20 CACHE
             22 CACHE
             24 CACHE
             26 LOAD_FAST                0 (self)
             28 UNPACK_SEQUENCE          1
             32 CALL                     1
             40 CACHE
             42 RETURN_VALUE

Disassembly of <code object pop at 0x000001A2D06C8FD0, file "_weakrefset.py", line 98>:
 98           0 RESUME                   0

 99           2 LOAD_FAST                0 (self)
              4 LOAD_ATTR                0 (_pending_removals)
             24 CACHE
             26 CACHE
             28 CACHE
             30 CACHE
             32 CACHE
             34 CACHE
             36 CACHE
             38 CACHE
             40 UNPACK_SEQUENCE          0
             44 CALL                     0
             52 CACHE
             54 POP_TOP

101          56 NOP

102     >>   58 NOP

103          60 LOAD_FAST                0 (self)
             62 LOAD_ATTR                2 (_commit_removals)
             82 CACHE
             84 CACHE
             86 CACHE
             88 CACHE
             90 CACHE
             92 CACHE
             94 UNPACK_SEQUENCE          0
             98 CALL                     0
            106 CACHE
            108 STORE_FAST               1 (itemref)
            110 JUMP_FORWARD            30 (to 172)
        >>  112 PUSH_EXC_INFO

104         114 LOAD_GLOBAL              8 (KeyError)
            124 CACHE
            126 CHECK_EXC_MATCH
            128 POP_JUMP_IF_FALSE       17 (to 164)
            130 POP_TOP

105         132 LOAD_GLOBAL              9 (NULL + KeyError)
            142 CACHE
            144 LOAD_CONST               2 ('pop from empty WeakSet')
            146 UNPACK_SEQUENCE          1
            150 CALL                     1
            158 CACHE
            160 LOAD_CONST               0 (None)
            162 RAISE_VARARGS            2

104     >>  164 RERAISE                  0
        >>  166 COPY                     3
            168 POP_EXCEPT
            170 RERAISE                  1

106     >>  172 PUSH_NULL
            174 LOAD_FAST                1 (itemref)
            176 UNPACK_SEQUENCE          0
            180 CALL                     0
            188 CACHE
            190 STORE_FAST               2 (item)

107         192 LOAD_FAST                2 (item)
            194 POP_JUMP_IF_NONE         2 (to 200)

108         196 LOAD_FAST                2 (item)
            198 RETURN_VALUE

101     >>  200 JUMP_BACKWARD           72 (to 58)
ExceptionTable:
  60 to 108 -> 112 [0]
  112 to 164 -> 166 [1] lasti

Disassembly of <code object remove at 0x000001A2D064D290, file "_weakrefset.py", line 110>:
110           0 RESUME                   0

111           2 LOAD_FAST                0 (self)
              4 LOAD_ATTR                0 (_pending_removals)
             24 CACHE
             26 CACHE
             28 CACHE
             30 CACHE
             32 CACHE
             34 CACHE
             36 CACHE
             38 CACHE
             40 UNPACK_SEQUENCE          0
             44 CALL                     0
             52 CACHE
             54 POP_TOP

113          56 LOAD_FAST                0 (self)
             58 LOAD_ATTR                2 (_commit_removals)
             78 CACHE
             80 CACHE
             82 CACHE
             84 CACHE
             86 CACHE
             88 CACHE
             90 LOAD_GLOBAL              9 (NULL + ref)
            100 CACHE
            102 LOAD_FAST                1 (item)
            104 UNPACK_SEQUENCE          1
            108 CALL                     1
            116 CACHE
            118 UNPACK_SEQUENCE          1
            122 CALL                     1
            130 CACHE
            132 POP_TOP
            134 LOAD_CONST               0 (None)
            136 RETURN_VALUE

Disassembly of <code object discard at 0x000001A2D064D3E0, file "_weakrefset.py", line 115>:
115           0 RESUME                   0

116           2 LOAD_FAST                0 (self)
              4 LOAD_ATTR                0 (_pending_removals)
             24 CACHE
             26 CACHE
             28 CACHE
             30 CACHE
             32 CACHE
             34 CACHE
             36 CACHE
             38 CACHE
             40 UNPACK_SEQUENCE          0
             44 CALL                     0
             52 CACHE
             54 POP_TOP

118          56 LOAD_FAST                0 (self)
             58 LOAD_ATTR                2 (_commit_removals)
             78 CACHE
             80 CACHE
             82 CACHE
             84 CACHE
             86 CACHE
             88 CACHE
             90 LOAD_GLOBAL              9 (NULL + ref)
            100 CACHE
            102 LOAD_FAST                1 (item)
            104 UNPACK_SEQUENCE          1
            108 CALL                     1
            116 CACHE
            118 UNPACK_SEQUENCE          1
            122 CALL                     1
            130 CACHE
            132 POP_TOP
            134 LOAD_CONST               0 (None)
            136 RETURN_VALUE

Disassembly of <code object update at 0x000001A2D062A760, file "_weakrefset.py", line 120>:
120           0 RESUME                   0

121           2 LOAD_FAST                0 (self)
              4 LOAD_ATTR                0 (_pending_removals)
             24 CACHE
             26 CACHE
             28 CACHE
             30 CACHE
             32 CACHE
             34 CACHE
             36 CACHE
             38 CACHE
             40 UNPACK_SEQUENCE          0
             44 CALL                     0
             52 CACHE
             54 POP_TOP

123          56 LOAD_FAST                1 (other)
             58 GET_ITER
        >>   60 FOR_ITER                23 (to 110)

124          64 LOAD_FAST                0 (self)
             66 STORE_SUBSCR
             70 CACHE
             72 CACHE
             74 CACHE
             76 CACHE
             78 CACHE
             80 CACHE
             82 CACHE
             84 CACHE
             86 CACHE
             88 LOAD_FAST                2 (element)
             90 UNPACK_SEQUENCE          1
             94 CALL                     1
            102 CACHE
            104 POP_TOP
            106 JUMP_BACKWARD           24 (to 60)

123         108 LOAD_CONST               0 (None)
        >>  110 RETURN_VALUE

Disassembly of <code object __ior__ at 0x000001A2D0632E20, file "_weakrefset.py", line 126>:
126           0 RESUME                   0

127           2 LOAD_FAST                0 (self)
              4 STORE_SUBSCR
              8 CACHE
             10 CACHE
             12 CACHE
             14 CACHE
             16 CACHE
             18 CACHE
             20 CACHE
             22 CACHE
             24 CACHE
             26 LOAD_FAST                1 (other)
             28 UNPACK_SEQUENCE          1
             32 CALL                     1
             40 CACHE
             42 POP_TOP

128          44 LOAD_FAST                0 (self)
             46 RETURN_VALUE

Disassembly of <code object difference at 0x000001A2D06A6670, file "_weakrefset.py", line 130>:
130           0 RESUME                   0

131           2 LOAD_FAST                0 (self)
              4 STORE_SUBSCR
              8 CACHE
             10 CACHE
             12 CACHE
             14 CACHE
             16 CACHE
             18 CACHE
             20 CACHE
             22 CACHE
             24 CACHE
             26 UNPACK_SEQUENCE          0
             30 CALL                     0
             38 CACHE
             40 STORE_FAST               2 (newset)

132          42 LOAD_FAST                2 (newset)
             44 STORE_SUBSCR
             48 CACHE
             50 CACHE
             52 CACHE
             54 CACHE
             56 CACHE
             58 CACHE
             60 CACHE
             62 CACHE
             64 CACHE
             66 LOAD_FAST                1 (other)
             68 UNPACK_SEQUENCE          1
             72 CALL                     1
             80 CACHE
             82 POP_TOP

133          84 LOAD_FAST                2 (newset)
             86 RETURN_VALUE

Disassembly of <code object difference_update at 0x000001A2D0632F10, file "_weakrefset.py", line 136>:
136           0 RESUME                   0

137           2 LOAD_FAST                0 (self)
              4 STORE_SUBSCR
              8 CACHE
             10 CACHE
             12 CACHE
             14 CACHE
             16 CACHE
             18 CACHE
             20 CACHE
             22 CACHE
             24 CACHE
             26 LOAD_FAST                1 (other)
             28 UNPACK_SEQUENCE          1
             32 CALL                     1
             40 CACHE
             42 POP_TOP
             44 LOAD_CONST               0 (None)
             46 RETURN_VALUE

Disassembly of <code object __isub__ at 0x000001A2D0153330, file "_weakrefset.py", line 138>:
138           0 RESUME                   0

139           2 LOAD_FAST                0 (self)
              4 LOAD_ATTR                0 (_pending_removals)
             24 CACHE
             26 CACHE
             28 CACHE
             30 CACHE
             32 CACHE
             34 CACHE
             36 CACHE
             38 CACHE
             40 UNPACK_SEQUENCE          0
             44 CALL                     0
             52 CACHE
             54 POP_TOP

141          56 LOAD_FAST                0 (self)
             58 LOAD_FAST                1 (other)
             60 IS_OP                    0
             62 POP_JUMP_IF_FALSE       26 (to 116)

142          64 LOAD_FAST                0 (self)
             66 LOAD_ATTR                2 (_commit_removals)
             86 CACHE
             88 CACHE
             90 CACHE
             92 CACHE
             94 CACHE
             96 CACHE
             98 UNPACK_SEQUENCE          0
            102 CALL                     0
            110 CACHE
            112 POP_TOP
            114 JUMP_FORWARD            36 (to 188)

144     >>  116 LOAD_FAST                0 (self)
            118 LOAD_ATTR                2 (_commit_removals)
            138 CACHE
            140 CACHE
            142 CACHE
            144 CACHE
            146 CACHE
            148 CACHE
            150 LOAD_CONST               1 (<code object <genexpr> at 0x000001A2D061E730, file "_weakrefset.py", line 144>)
            152 MAKE_FUNCTION            0
            154 LOAD_FAST                1 (other)
            156 GET_ITER
            158 UNPACK_SEQUENCE          0
            162 CALL                     0
            170 CACHE
            172 UNPACK_SEQUENCE          1
            176 CALL                     1
            184 CACHE
            186 POP_TOP

145     >>  188 LOAD_FAST                0 (self)
            190 RETURN_VALUE

Disassembly of <code object <genexpr> at 0x000001A2D061E730, file "_weakrefset.py", line 144>:
144           0 RETURN_GENERATOR
              2 POP_TOP
              4 RESUME                   0
              6 LOAD_FAST                0 (.0)
        >>    8 FOR_ITER                19 (to 50)
             12 LOAD_GLOBAL              1 (NULL + ref)
             22 CACHE
             24 LOAD_FAST                1 (item)
             26 UNPACK_SEQUENCE          1
             30 CALL                     1
             38 CACHE
             40 LOAD_FAST                0 (.0)
             42 RESUME                   1
             44 POP_TOP
             46 JUMP_BACKWARD           20 (to 8)
             48 LOAD_CONST               0 (None)
        >>   50 RETURN_VALUE

Disassembly of <code object intersection at 0x000001A2D06418A0, file "_weakrefset.py", line 147>:
              0 MAKE_CELL                0 (self)

147           2 RESUME                   0

148           4 LOAD_DEREF               0 (self)
              6 STORE_SUBSCR
             10 CACHE
             12 CACHE
             14 CACHE
             16 CACHE
             18 CACHE
             20 CACHE
             22 CACHE
             24 CACHE
             26 CACHE
             28 LOAD_CLOSURE             0 (self)
             30 BUILD_TUPLE              1
             32 LOAD_CONST               1 (<code object <genexpr> at 0x000001A2D06330F0, file "_weakrefset.py", line 148>)
             34 MAKE_FUNCTION            8 (closure)
             36 LOAD_FAST                1 (other)
             38 GET_ITER
             40 UNPACK_SEQUENCE          0
             44 CALL                     0
             52 CACHE
             54 UNPACK_SEQUENCE          1
             58 CALL                     1
             66 CACHE
             68 RETURN_VALUE

Disassembly of <code object <genexpr> at 0x000001A2D06330F0, file "_weakrefset.py", line 148>:
              0 COPY_FREE_VARS           1

148           2 RETURN_GENERATOR
              4 POP_TOP
              6 RESUME                   0
              8 LOAD_FAST                0 (.0)
        >>   10 FOR_ITER                10 (to 34)
             14 LOAD_FAST                1 (item)
             16 LOAD_DEREF               2 (self)
             18 CONTAINS_OP              0
