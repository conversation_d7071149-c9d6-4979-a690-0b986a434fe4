# Code object from position 10055015
# Filename: fileinput.py
# Name: <module>
# Args: 0
# Locals: 0
# Stack size: 6
# Flags: 0

  0           0 RESUME                   0

  1           2 LOAD_CONST               0 ('Helper class to quickly write a loop over all standard input files.\n\nTypical use is:\n\n    import fileinput\n    for line in fileinput.input(encoding="utf-8"):\n        process(line)\n\nThis iterates over the lines of all files listed in sys.argv[1:],\ndefaulting to sys.stdin if the list is empty.  If a filename is \'-\' it\nis also replaced by sys.stdin and the optional arguments mode and\nopenhook are ignored.  To specify an alternative list of filenames,\npass it as the argument to input().  A single file name is also allowed.\n\nFunctions filename(), lineno() return the filename and cumulative line\nnumber of the line that has just been read; filelineno() returns its\nline number in the current file; isfirstline() returns true iff the\nline just read is the first line of its file; isstdin() returns true\niff the line was read from sys.stdin.  Function nextfile() closes the\ncurrent file so that the next iteration will read the first line from\nthe next file (if any); lines not read from the file will not count\ntowards the cumulative line count; the filename is not changed until\nafter the first line of the next file has been read.  Function close()\ncloses the sequence.\n\nBefore any lines have been read, filename() returns None and both line\nnumbers are zero; nextfile() has no effect.  After all lines have been\nread, filename() and the line number functions return the values\npertaining to the last line read; nextfile() has no effect.\n\nAll files are opened in text mode by default, you can override this by\nsetting the mode parameter to input() or FileInput.__init__().\nIf an I/O error occurs during opening or reading a file, the OSError\nexception is raised.\n\nIf sys.stdin is used more than once, the second and further use will\nreturn no lines, except perhaps for interactive use, or if it has been\nexplicitly reset (e.g. using sys.stdin.seek(0)).\n\nEmpty files are opened and immediately closed; the only time their\npresence in the list of filenames is noticeable at all is when the\nlast file opened is empty.\n\nIt is possible that the last line of a file doesn\'t end in a newline\ncharacter; otherwise lines are returned including the trailing\nnewline.\n\nClass FileInput is the implementation; its methods filename(),\nlineno(), fileline(), isfirstline(), isstdin(), nextfile() and close()\ncorrespond to the functions in the module.  In addition it has a\nreadline() method which returns the next input line, and a\n__getitem__() method which implements the sequence behavior.  The\nsequence must be accessed in strictly sequential order; sequence\naccess and readline() cannot be mixed.\n\nOptional in-place filtering: if the keyword argument inplace=1 is\npassed to input() or to the FileInput constructor, the file is moved\nto a backup file and standard output is directed to the input file.\nThis makes it possible to write a filter that rewrites its input file\nin place.  If the keyword argument backup=".<some extension>" is also\ngiven, it specifies the extension for the backup file, and the backup\nfile remains around; by default, the extension is ".bak" and it is\ndeleted when the output file is closed.  In-place filtering is\ndisabled when standard input is read.  XXX The current implementation\ndoes not work for MS-DOS 8+3 filesystems.\n')
              4 STORE_NAME               0 (__doc__)

 68           6 LOAD_CONST               1 (0)
              8 LOAD_CONST               2 (None)
             10 IMPORT_NAME              1 (io)
             12 STORE_NAME               1 (io)

 69          14 LOAD_CONST               1 (0)
             16 LOAD_CONST               2 (None)
             18 IMPORT_NAME              2 (sys)
             20 STORE_NAME               2 (sys)
             22 LOAD_CONST               1 (0)
             24 LOAD_CONST               2 (None)
             26 IMPORT_NAME              3 (os)
             28 STORE_NAME               3 (os)

 70          30 LOAD_CONST               1 (0)
             32 LOAD_CONST               3 (('GenericAlias',))
             34 IMPORT_NAME              4 (types)
             36 IMPORT_FROM              5 (GenericAlias)
             38 STORE_NAME               5 (GenericAlias)
             40 POP_TOP

 72          42 BUILD_LIST               0
             44 LOAD_CONST               4 (('input', 'close', 'nextfile', 'filename', 'lineno', 'filelineno', 'fileno', 'isfirstline', 'isstdin', 'FileInput', 'hook_compressed', 'hook_encoded'))
             46 LIST_EXTEND              1
             48 STORE_NAME               6 (__all__)

 76          50 LOAD_CONST               2 (None)
             52 STORE_GLOBAL             7 (_state)

 78          54 LOAD_CONST              25 ((None, False, ''))
             56 LOAD_CONST               7 ('r')
             58 LOAD_CONST               2 (None)

 79          60 LOAD_CONST               2 (None)
             62 LOAD_CONST               2 (None)

 78          64 LOAD_CONST               8 (('mode', 'openhook', 'encoding', 'errors'))
             66 BUILD_CONST_KEY_MAP      4
             68 LOAD_CONST               9 (<code object input at 0x000001B2A8A082B0, file "fileinput.py", line 78>)
             70 MAKE_FUNCTION            3 (defaults, kwdefaults)
             72 STORE_NAME               8 (input)

 93          74 LOAD_CONST              10 (<code object close at 0x000001B2A76AE9A0, file "fileinput.py", line 93>)
             76 MAKE_FUNCTION            0
             78 STORE_NAME               9 (close)

101          80 LOAD_CONST              11 (<code object nextfile at 0x000001B2A76DB3F0, file "fileinput.py", line 101>)
             82 MAKE_FUNCTION            0
             84 STORE_NAME              10 (nextfile)

115          86 LOAD_CONST              12 (<code object filename at 0x000001B2A76DB750, file "fileinput.py", line 115>)
             88 MAKE_FUNCTION            0
             90 STORE_NAME              11 (filename)

124          92 LOAD_CONST              13 (<code object lineno at 0x000001B2A76DB2D0, file "fileinput.py", line 124>)
             94 MAKE_FUNCTION            0
             96 STORE_NAME              12 (lineno)

134          98 LOAD_CONST              14 (<code object filelineno at 0x000001B2A76DAF70, file "fileinput.py", line 134>)
            100 MAKE_FUNCTION            0
            102 STORE_NAME              13 (filelineno)

144         104 LOAD_CONST              15 (<code object fileno at 0x000001B2A76DAD30, file "fileinput.py", line 144>)
            106 MAKE_FUNCTION            0
            108 STORE_NAME              14 (fileno)

153         110 LOAD_CONST              16 (<code object isfirstline at 0x000001B2A76DB510, file "fileinput.py", line 153>)
            112 MAKE_FUNCTION            0
            114 STORE_NAME              15 (isfirstline)

162         116 LOAD_CONST              17 (<code object isstdin at 0x000001B2A76DB630, file "fileinput.py", line 162>)
            118 MAKE_FUNCTION            0
            120 STORE_NAME              16 (isstdin)

171         122 PUSH_NULL
            124 LOAD_BUILD_CLASS
            126 LOAD_CONST              18 (<code object FileInput at 0x000001B2A71D10B0, file "fileinput.py", line 171>)
            128 MAKE_FUNCTION            0
            130 LOAD_CONST              19 ('FileInput')
            132 UNPACK_SEQUENCE          2
            136 CALL                     2
            144 CACHE
            146 STORE_NAME              17 (FileInput)

401         148 LOAD_CONST               2 (None)
            150 LOAD_CONST               2 (None)
            152 LOAD_CONST              20 (('encoding', 'errors'))
            154 BUILD_CONST_KEY_MAP      2
            156 LOAD_CONST              21 (<code object hook_compressed at 0x000001B2A71BB430, file "fileinput.py", line 401>)
            158 MAKE_FUNCTION            2 (kwdefaults)
            160 STORE_NAME              18 (hook_compressed)

420         162 LOAD_CONST              26 ((None,))
            164 LOAD_CONST              22 (<code object hook_encoded at 0x000001B2A768A6B0, file "fileinput.py", line 420>)
            166 MAKE_FUNCTION            1 (defaults)
            168 STORE_NAME              19 (hook_encoded)

426         170 LOAD_CONST              23 (<code object _test at 0x000001B2A74FAC00, file "fileinput.py", line 426>)
            172 MAKE_FUNCTION            0
            174 STORE_NAME              20 (_test)

441         176 LOAD_NAME               21 (__name__)
            178 LOAD_CONST              24 ('__main__')
            180 COMPARE_OP               2 (<)
            184 CACHE
            186 POP_JUMP_IF_FALSE       12 (to 212)

442         188 PUSH_NULL
            190 LOAD_NAME               20 (_test)
            192 UNPACK_SEQUENCE          0
            196 CALL                     0
            204 CACHE
            206 POP_TOP
            208 LOAD_CONST               2 (None)
            210 RETURN_VALUE

441     >>  212 LOAD_CONST               2 (None)
            214 RETURN_VALUE

Disassembly of <code object input at 0x000001B2A8A082B0, file "fileinput.py", line 78>:
 78           0 RESUME                   0

 87           2 LOAD_GLOBAL              0 (_state)
             12 CACHE
             14 POP_JUMP_IF_FALSE       27 (to 70)
             16 LOAD_GLOBAL              0 (_state)
             26 CACHE
             28 LOAD_ATTR                1 (NULL|self + _state)
             48 CACHE
             50 CACHE
             52 LOAD_CONST               1 ('input() already active')
             54 UNPACK_SEQUENCE          1
             58 CALL                     1
             66 CACHE
             68 RAISE_VARARGS            1

 89     >>   70 LOAD_GLOBAL              7 (NULL + FileInput)
             80 CACHE
             82 LOAD_FAST                0 (files)
             84 LOAD_FAST                1 (inplace)
             86 LOAD_FAST                2 (backup)
             88 LOAD_FAST                3 (mode)
             90 LOAD_FAST                4 (openhook)

 90          92 LOAD_FAST                5 (encoding)
             94 LOAD_FAST                6 (errors)

 89          96 KW_NAMES                 2 (('mode', 'openhook', 'encoding', 'errors'))
             98 UNPACK_SEQUENCE          7
            102 CALL                     7
            110 CACHE
            112 STORE_GLOBAL             0 (_state)

 91         114 LOAD_GLOBAL              0 (_state)
            124 CACHE
            126 RETURN_VALUE

Disassembly of <code object close at 0x000001B2A76AE9A0, file "fileinput.py", line 93>:
 93           0 RESUME                   0

 96           2 LOAD_GLOBAL              0 (_state)
             12 CACHE
             14 STORE_FAST               0 (state)

 97          16 LOAD_CONST               1 (None)
             18 STORE_GLOBAL             0 (_state)

 98          20 LOAD_FAST                0 (state)
             22 POP_JUMP_IF_FALSE       22 (to 68)

 99          24 LOAD_FAST                0 (state)
             26 STORE_SUBSCR
             30 CACHE
             32 CACHE
             34 CACHE
             36 CACHE
             38 CACHE
             40 CACHE
             42 CACHE
             44 CACHE
             46 CACHE
             48 UNPACK_SEQUENCE          0
             52 CALL                     0
             60 CACHE
             62 POP_TOP
             64 LOAD_CONST               1 (None)
             66 RETURN_VALUE

 98     >>   68 LOAD_CONST               1 (None)
             70 RETURN_VALUE

Disassembly of <code object nextfile at 0x000001B2A76DB3F0, file "fileinput.py", line 101>:
101           0 RESUME                   0

111           2 LOAD_GLOBAL              0 (_state)
             12 CACHE
             14 POP_JUMP_IF_TRUE        15 (to 46)

112          16 LOAD_GLOBAL              3 (NULL + RuntimeError)
             26 CACHE
             28 LOAD_CONST               1 ('no active input()')
             30 UNPACK_SEQUENCE          1
             34 CALL                     1
             42 CACHE
             44 RAISE_VARARGS            1

113     >>   46 LOAD_GLOBAL              0 (_state)
             56 CACHE
             58 STORE_SUBSCR
             62 CACHE
             64 CACHE
             66 CACHE
             68 CACHE
             70 CACHE
             72 CACHE
             74 CACHE
             76 CACHE
             78 CACHE
             80 UNPACK_SEQUENCE          0
             84 CALL                     0
             92 CACHE
             94 RETURN_VALUE

Disassembly of <code object filename at 0x000001B2A76DB750, file "fileinput.py", line 115>:
115           0 RESUME                   0

120           2 LOAD_GLOBAL              0 (_state)
             12 CACHE
             14 POP_JUMP_IF_TRUE        15 (to 46)

121          16 LOAD_GLOBAL              3 (NULL + RuntimeError)
             26 CACHE
             28 LOAD_CONST               1 ('no active input()')
             30 UNPACK_SEQUENCE          1
             34 CALL                     1
             42 CACHE
             44 RAISE_VARARGS            1

122     >>   46 LOAD_GLOBAL              0 (_state)
             56 CACHE
             58 STORE_SUBSCR
             62 CACHE
             64 CACHE
             66 CACHE
             68 CACHE
             70 CACHE
             72 CACHE
             74 CACHE
             76 CACHE
             78 CACHE
             80 UNPACK_SEQUENCE          0
             84 CALL                     0
             92 CACHE
             94 RETURN_VALUE

Disassembly of <code object lineno at 0x000001B2A76DB2D0, file "fileinput.py", line 124>:
124           0 RESUME                   0

130           2 LOAD_GLOBAL              0 (_state)
             12 CACHE
             14 POP_JUMP_IF_TRUE        15 (to 46)

131          16 LOAD_GLOBAL              3 (NULL + RuntimeError)
             26 CACHE
             28 LOAD_CONST               1 ('no active input()')
             30 UNPACK_SEQUENCE          1
             34 CALL                     1
             42 CACHE
             44 RAISE_VARARGS            1

132     >>   46 LOAD_GLOBAL              0 (_state)
             56 CACHE
             58 STORE_SUBSCR
             62 CACHE
             64 CACHE
             66 CACHE
             68 CACHE
             70 CACHE
             72 CACHE
             74 CACHE
             76 CACHE
             78 CACHE
             80 UNPACK_SEQUENCE          0
             84 CALL                     0
             92 CACHE
             94 RETURN_VALUE

Disassembly of <code object filelineno at 0x000001B2A76DAF70, file "fileinput.py", line 134>:
134           0 RESUME                   0

140           2 LOAD_GLOBAL              0 (_state)
             12 CACHE
             14 POP_JUMP_IF_TRUE        15 (to 46)

141          16 LOAD_GLOBAL              3 (NULL + RuntimeError)
             26 CACHE
             28 LOAD_CONST               1 ('no active input()')
             30 UNPACK_SEQUENCE          1
             34 CALL                     1
             42 CACHE
             44 RAISE_VARARGS            1

142     >>   46 LOAD_GLOBAL              0 (_state)
             56 CACHE
             58 STORE_SUBSCR
             62 CACHE
             64 CACHE
             66 CACHE
             68 CACHE
             70 CACHE
             72 CACHE
             74 CACHE
             76 CACHE
             78 CACHE
             80 UNPACK_SEQUENCE          0
             84 CALL                     0
             92 CACHE
             94 RETURN_VALUE

Disassembly of <code object fileno at 0x000001B2A76DAD30, file "fileinput.py", line 144>:
144           0 RESUME                   0

149           2 LOAD_GLOBAL              0 (_state)
             12 CACHE
             14 POP_JUMP_IF_TRUE        15 (to 46)

150          16 LOAD_GLOBAL              3 (NULL + RuntimeError)
             26 CACHE
             28 LOAD_CONST               1 ('no active input()')
             30 UNPACK_SEQUENCE          1
             34 CALL                     1
             42 CACHE
             44 RAISE_VARARGS            1

151     >>   46 LOAD_GLOBAL              0 (_state)
             56 CACHE
             58 STORE_SUBSCR
             62 CACHE
             64 CACHE
             66 CACHE
             68 CACHE
             70 CACHE
             72 CACHE
             74 CACHE
             76 CACHE
             78 CACHE
             80 UNPACK_SEQUENCE          0
             84 CALL                     0
             92 CACHE
             94 RETURN_VALUE

Disassembly of <code object isfirstline at 0x000001B2A76DB510, file "fileinput.py", line 153>:
153           0 RESUME                   0

158           2 LOAD_GLOBAL              0 (_state)
             12 CACHE
             14 POP_JUMP_IF_TRUE        15 (to 46)

159          16 LOAD_GLOBAL              3 (NULL + RuntimeError)
             26 CACHE
             28 LOAD_CONST               1 ('no active input()')
             30 UNPACK_SEQUENCE          1
             34 CALL                     1
             42 CACHE
             44 RAISE_VARARGS            1

160     >>   46 LOAD_GLOBAL              0 (_state)
             56 CACHE
             58 STORE_SUBSCR
             62 CACHE
             64 CACHE
             66 CACHE
             68 CACHE
             70 CACHE
             72 CACHE
             74 CACHE
             76 CACHE
             78 CACHE
             80 UNPACK_SEQUENCE          0
             84 CALL                     0
             92 CACHE
             94 RETURN_VALUE

Disassembly of <code object isstdin at 0x000001B2A76DB630, file "fileinput.py", line 162>:
162           0 RESUME                   0

167           2 LOAD_GLOBAL              0 (_state)
             12 CACHE
             14 POP_JUMP_IF_TRUE        15 (to 46)

168          16 LOAD_GLOBAL              3 (NULL + RuntimeError)
             26 CACHE
             28 LOAD_CONST               1 ('no active input()')
             30 UNPACK_SEQUENCE          1
             34 CALL                     1
             42 CACHE
             44 RAISE_VARARGS            1

169     >>   46 LOAD_GLOBAL              0 (_state)
             56 CACHE
             58 STORE_SUBSCR
             62 CACHE
             64 CACHE
             66 CACHE
             68 CACHE
             70 CACHE
             72 CACHE
             74 CACHE
             76 CACHE
             78 CACHE
             80 UNPACK_SEQUENCE          0
             84 CALL                     0
             92 CACHE
             94 RETURN_VALUE

Disassembly of <code object FileInput at 0x000001B2A71D10B0, file "fileinput.py", line 171>:
171           0 RESUME                   0
              2 LOAD_NAME                0 (__name__)
              4 STORE_NAME               1 (__module__)
              6 LOAD_CONST               0 ('FileInput')
              8 STORE_NAME               2 (__qualname__)

172          10 LOAD_CONST               1 ('FileInput([files[, inplace[, backup]]], *, mode=None, openhook=None)\n\n    Class FileInput is the implementation of the module; its methods\n    filename(), lineno(), fileline(), isfirstline(), isstdin(), fileno(),\n    nextfile() and close() correspond to the functions of the same name\n    in the module.\n    In addition it has a readline() method which returns the next\n    input line, and a __getitem__() method which implements the\n    sequence behavior. The sequence must be accessed in strictly\n    sequential order; random access and readline() cannot be mixed.\n    ')
             12 STORE_NAME               3 (__doc__)

184          14 LOAD_CONST              23 ((None, False, ''))

185          16 LOAD_CONST               5 ('r')
             18 LOAD_CONST               2 (None)
             20 LOAD_CONST               2 (None)
             22 LOAD_CONST               2 (None)

184          24 LOAD_CONST               6 (('mode', 'openhook', 'encoding', 'errors'))
             26 BUILD_CONST_KEY_MAP      4
             28 LOAD_CONST               7 (<code object __init__ at 0x000001B2A74D35E0, file "fileinput.py", line 184>)
             30 MAKE_FUNCTION            3 (defaults, kwdefaults)
             32 STORE_NAME               4 (__init__)

231          34 LOAD_CONST               8 (<code object __del__ at 0x000001B2A767F0F0, file "fileinput.py", line 231>)
             36 MAKE_FUNCTION            0
             38 STORE_NAME               5 (__del__)

234          40 LOAD_CONST               9 (<code object close at 0x000001B2A76D98F0, file "fileinput.py", line 234>)
             42 MAKE_FUNCTION            0
             44 STORE_NAME               6 (close)

240          46 LOAD_CONST              10 (<code object __enter__ at 0x000001B2A767ADB0, file "fileinput.py", line 240>)
             48 MAKE_FUNCTION            0
             50 STORE_NAME               7 (__enter__)

243          52 LOAD_CONST              11 (<code object __exit__ at 0x000001B2A767CB70, file "fileinput.py", line 243>)
             54 MAKE_FUNCTION            0
             56 STORE_NAME               8 (__exit__)

246          58 LOAD_CONST              12 (<code object __iter__ at 0x000001B2A767AC10, file "fileinput.py", line 246>)
             60 MAKE_FUNCTION            0
             62 STORE_NAME               9 (__iter__)

249          64 LOAD_CONST              13 (<code object __next__ at 0x000001B2A71D2550, file "fileinput.py", line 249>)
             66 MAKE_FUNCTION            0
             68 STORE_NAME              10 (__next__)

260          70 LOAD_CONST              14 (<code object nextfile at 0x000001B2A75BF8C0, file "fileinput.py", line 260>)
             72 MAKE_FUNCTION            0
             74 STORE_NAME              11 (nextfile)

290          76 LOAD_CONST              15 (<code object readline at 0x000001B2A71C6CD0, file "fileinput.py", line 290>)
             78 MAKE_FUNCTION            0
             80 STORE_NAME              12 (readline)

301          82 LOAD_CONST              16 (<code object _readline at 0x000001B2A74D2430, file "fileinput.py", line 301>)
             84 MAKE_FUNCTION            0
             86 STORE_NAME              13 (_readline)

374          88 LOAD_CONST              17 (<code object filename at 0x000001B2A767BD20, file "fileinput.py", line 374>)
             90 MAKE_FUNCTION            0
             92 STORE_NAME              14 (filename)

377          94 LOAD_CONST              18 (<code object lineno at 0x000001B2A768ACD0, file "fileinput.py", line 377>)
             96 MAKE_FUNCTION            0
             98 STORE_NAME              15 (lineno)

380         100 LOAD_CONST              19 (<code object filelineno at 0x000001B2A767BDF0, file "fileinput.py", line 380>)
            102 MAKE_FUNCTION            0
            104 STORE_NAME              16 (filelineno)

383         106 LOAD_CONST              20 (<code object fileno at 0x000001B2A76B4C10, file "fileinput.py", line 383>)
            108 MAKE_FUNCTION            0
            110 STORE_NAME              17 (fileno)

392         112 LOAD_CONST              21 (<code object isfirstline at 0x000001B2A768B830, file "fileinput.py", line 392>)
            114 MAKE_FUNCTION            0
            116 STORE_NAME              18 (isfirstline)

395         118 LOAD_CONST              22 (<code object isstdin at 0x000001B2A767A4C0, file "fileinput.py", line 395>)
            120 MAKE_FUNCTION            0
            122 STORE_NAME              19 (isstdin)

398         124 PUSH_NULL
            126 LOAD_NAME               20 (classmethod)
            128 LOAD_NAME               21 (GenericAlias)
            130 UNPACK_SEQUENCE          1
            134 CALL                     1
            142 CACHE
            144 STORE_NAME              22 (__class_getitem__)
            146 LOAD_CONST               2 (None)
            148 RETURN_VALUE

Disassembly of <code object __init__ at 0x000001B2A74D35E0, file "fileinput.py", line 184>:
184           0 RESUME                   0

186           2 LOAD_GLOBAL              1 (NULL + isinstance)
             12 CACHE
             14 LOAD_FAST                1 (files)
             16 LOAD_GLOBAL              2 (str)
             26 CACHE
             28 UNPACK_SEQUENCE          2
             32 CALL                     2
             40 CACHE
             42 POP_JUMP_IF_FALSE        4 (to 52)

187          44 LOAD_FAST                1 (files)
             46 BUILD_TUPLE              1
             48 STORE_FAST               1 (files)
             50 JUMP_FORWARD            90 (to 232)

188     >>   52 LOAD_GLOBAL              1 (NULL + isinstance)
             62 CACHE
             64 LOAD_FAST                1 (files)
             66 LOAD_GLOBAL              4 (os)
             76 CACHE
             78 LOAD_ATTR                3 (NULL|self + str)
             98 CACHE
            100 CACHE
            102 POP_JUMP_IF_FALSE       22 (to 148)

189         104 LOAD_GLOBAL              5 (NULL + os)
            114 CACHE
            116 LOAD_ATTR                4 (os)
            136 CACHE
            138 CACHE
            140 CACHE
            142 BUILD_TUPLE              1
            144 STORE_FAST               1 (files)
            146 JUMP_FORWARD            42 (to 232)

191     >>  148 LOAD_FAST                1 (files)
            150 POP_JUMP_IF_NOT_NONE    20 (to 192)

192         152 LOAD_GLOBAL             10 (sys)
            162 CACHE
            164 LOAD_ATTR                6 (PathLike)
            184 CACHE
            186 CACHE
            188 CACHE
            190 STORE_FAST               1 (files)

193     >>  192 LOAD_FAST                1 (files)
            194 POP_JUMP_IF_TRUE         3 (to 202)

194         196 LOAD_CONST               2 (('-',))
            198 STORE_FAST               1 (files)
            200 JUMP_FORWARD            15 (to 232)

196     >>  202 LOAD_GLOBAL             15 (NULL + tuple)
            212 CACHE
            214 LOAD_FAST                1 (files)
            216 UNPACK_SEQUENCE          1
            220 CALL                     1
            228 CACHE
            230 STORE_FAST               1 (files)

197     >>  232 LOAD_FAST                1 (files)
            234 LOAD_FAST                0 (self)
            236 STORE_ATTR               8 (_files)

198         246 LOAD_FAST                2 (inplace)
            248 LOAD_FAST                0 (self)
            250 STORE_ATTR               9 (_inplace)

199         260 LOAD_FAST                3 (backup)
            262 LOAD_FAST                0 (self)
            264 STORE_ATTR              10 (_backup)

200         274 LOAD_CONST               0 (None)
            276 LOAD_FAST                0 (self)
            278 STORE_ATTR              11 (_savestdout)

201         288 LOAD_CONST               0 (None)
            290 LOAD_FAST                0 (self)
            292 STORE_ATTR              12 (_output)

202         302 LOAD_CONST               0 (None)
            304 LOAD_FAST                0 (self)
            306 STORE_ATTR              13 (_filename)

203         316 LOAD_CONST               3 (0)
            318 LOAD_FAST                0 (self)
            320 STORE_ATTR              14 (_startlineno)

204         330 LOAD_CONST               3 (0)
            332 LOAD_FAST                0 (self)
            334 STORE_ATTR              15 (_filelineno)

205         344 LOAD_CONST               0 (None)
            346 LOAD_FAST                0 (self)
            348 STORE_ATTR              16 (_file)

206         358 LOAD_CONST               4 (False)
            360 LOAD_FAST                0 (self)
            362 STORE_ATTR              17 (_isstdin)

207         372 LOAD_CONST               0 (None)
            374 LOAD_FAST                0 (self)
            376 STORE_ATTR              18 (_backupfilename)

208         386 LOAD_FAST                6 (encoding)
            388 LOAD_FAST                0 (self)
            390 STORE_ATTR              19 (_encoding)

209         400 LOAD_FAST                7 (errors)
            402 LOAD_FAST                0 (self)
            404 STORE_ATTR              20 (_errors)

213         414 LOAD_GLOBAL             10 (sys)
            424 CACHE
            426 LOAD_ATTR               21 (NULL|self + _backup)
            446 POP_JUMP_IF_FALSE       40 (to 528)

214         448 LOAD_CONST               5 ('b')
            450 LOAD_FAST                4 (mode)
            452 CONTAINS_OP              1
            454 POP_JUMP_IF_FALSE       36 (to 528)
            456 LOAD_FAST                6 (encoding)
            458 POP_JUMP_IF_NOT_NONE    34 (to 528)
            460 LOAD_FAST                5 (openhook)
            462 POP_JUMP_IF_NOT_NONE    32 (to 528)

215         464 LOAD_CONST               3 (0)
            466 LOAD_CONST               0 (None)
            468 IMPORT_NAME             23 (warnings)
            470 STORE_FAST               8 (warnings)

216         472 LOAD_FAST                8 (warnings)
            474 STORE_SUBSCR
            478 CACHE
            480 CACHE
            482 CACHE
            484 CACHE
            486 CACHE
            488 CACHE
            490 CACHE
            492 CACHE
            494 CACHE
            496 LOAD_CONST               6 ("'encoding' argument not specified.")

217         498 LOAD_GLOBAL             50 (EncodingWarning)
            508 CACHE
            510 LOAD_CONST               7 (2)

216         512 UNPACK_SEQUENCE          3
            516 CALL                     3
            524 CACHE
            526 POP_TOP

220     >>  528 LOAD_FAST                4 (mode)
            530 LOAD_CONST               8 (('r', 'rb'))
            532 CONTAINS_OP              1
            534 POP_JUMP_IF_FALSE       15 (to 566)

221         536 LOAD_GLOBAL             53 (NULL + ValueError)
            546 CACHE
            548 LOAD_CONST               9 ("FileInput opening mode must be 'r' or 'rb'")
            550 UNPACK_SEQUENCE          1
            554 CALL                     1
            562 CACHE
            564 RAISE_VARARGS            1

222     >>  566 LOAD_FAST                4 (mode)
            568 LOAD_FAST                0 (self)
            570 STORE_ATTR              27 (_mode)

223         580 LOAD_FAST                4 (mode)
            582 STORE_SUBSCR
            586 CACHE
            588 CACHE
            590 CACHE
            592 CACHE
            594 CACHE
            596 CACHE
            598 CACHE
            600 CACHE
            602 CACHE
            604 LOAD_CONST              10 ('r')
            606 LOAD_CONST              11 ('w')
            608 UNPACK_SEQUENCE          2
            612 CALL                     2
            620 CACHE
            622 LOAD_FAST                0 (self)
            624 STORE_ATTR              29 (_write_mode)

224         634 LOAD_FAST                5 (openhook)
            636 POP_JUMP_IF_FALSE       47 (to 732)

225         638 LOAD_FAST                2 (inplace)
            640 POP_JUMP_IF_FALSE       15 (to 672)

226         642 LOAD_GLOBAL             53 (NULL + ValueError)
            652 CACHE
            654 LOAD_CONST              12 ('FileInput cannot use an opening hook in inplace mode')
            656 UNPACK_SEQUENCE          1
            660 CALL                     1
            668 CACHE
            670 RAISE_VARARGS            1

227     >>  672 LOAD_GLOBAL             61 (NULL + callable)
            682 CACHE
            684 LOAD_FAST                5 (openhook)
            686 UNPACK_SEQUENCE          1
            690 CALL                     1
            698 CACHE
            700 POP_JUMP_IF_TRUE        15 (to 732)

228         702 LOAD_GLOBAL             53 (NULL + ValueError)
            712 CACHE
            714 LOAD_CONST              13 ('FileInput openhook must be callable')
            716 UNPACK_SEQUENCE          1
            720 CALL                     1
            728 CACHE
            730 RAISE_VARARGS            1

229     >>  732 LOAD_FAST                5 (openhook)
            734 LOAD_FAST                0 (self)
            736 STORE_ATTR              31 (_openhook)
            746 LOAD_CONST               0 (None)
            748 RETURN_VALUE

Disassembly of <code object __del__ at 0x000001B2A767F0F0, file "fileinput.py", line 231>:
231           0 RESUME                   0

232           2 LOAD_FAST                0 (self)
              4 STORE_SUBSCR
              8 CACHE
             10 CACHE
             12 CACHE
             14 CACHE
             16 CACHE
             18 CACHE
             20 CACHE
             22 CACHE
             24 CACHE
             26 UNPACK_SEQUENCE          0
             30 CALL                     0
             38 CACHE
             40 POP_TOP
             42 LOAD_CONST               0 (None)
             44 RETURN_VALUE

Disassembly of <code object close at 0x000001B2A76D98F0, file "fileinput.py", line 234>:
234           0 RESUME                   0

235           2 NOP

236           4 LOAD_FAST                0 (self)
              6 STORE_SUBSCR
             10 CACHE
             12 CACHE
             14 CACHE
             16 CACHE
             18 CACHE
             20 CACHE
             22 CACHE
             24 CACHE
             26 CACHE
             28 UNPACK_SEQUENCE          0
             32 CALL                     0
             40 CACHE
             42 POP_TOP

238          44 LOAD_CONST               1 (())
             46 LOAD_FAST                0 (self)
             48 STORE_ATTR               1 (_files)
             58 LOAD_CONST               0 (None)
             60 RETURN_VALUE
        >>   62 PUSH_EXC_INFO
             64 LOAD_CONST               1 (())
             66 LOAD_FAST                0 (self)
             68 STORE_ATTR               1 (_files)
             78 RERAISE                  0
        >>   80 COPY                     3
             82 POP_EXCEPT
             84 RERAISE                  1
ExceptionTable:
  4 to 42 -> 62 [0]
  62 to 78 -> 80 [1] lasti

Disassembly of <code object __enter__ at 0x000001B2A767ADB0, file "fileinput.py", line 240>:
240           0 RESUME                   0

241           2 LOAD_FAST                0 (self)
              4 RETURN_VALUE

Disassembly of <code object __exit__ at 0x000001B2A767CB70, file "fileinput.py", line 243>:
243           0 RESUME                   0

244           2 LOAD_FAST                0 (self)
              4 STORE_SUBSCR
              8 CACHE
             10 CACHE
             12 CACHE
             14 CACHE
             16 CACHE
             18 CACHE
             20 CACHE
             22 CACHE
             24 CACHE
             26 UNPACK_SEQUENCE          0
             30 CALL                     0
             38 CACHE
             40 POP_TOP
             42 LOAD_CONST               0 (None)
             44 RETURN_VALUE

Disassembly of <code object __iter__ at 0x000001B2A767AC10, file "fileinput.py", line 246>:
246           0 RESUME                   0

247           2 LOAD_FAST                0 (self)
              4 RETURN_VALUE

Disassembly of <code object __next__ at 0x000001B2A71D2550, file "fileinput.py", line 249>:
249           0 RESUME                   0

250           2 NOP

251     >>    4 LOAD_FAST                0 (self)
              6 STORE_SUBSCR
             10 CACHE
             12 CACHE
             14 CACHE
             16 CACHE
             18 CACHE
             20 CACHE
             22 CACHE
             24 CACHE
             26 CACHE
             28 UNPACK_SEQUENCE          0
             32 CALL                     0
             40 CACHE
             42 STORE_FAST               1 (line)

252          44 LOAD_FAST                1 (line)
             46 POP_JUMP_IF_FALSE       18 (to 84)

253          48 LOAD_FAST                0 (self)
             50 COPY                     1
             52 LOAD_ATTR                1 (NULL|self + _readline)
             72 CACHE
             74 CACHE
             76 CACHE
             78 CACHE

254          80 LOAD_FAST                1 (line)
             82 RETURN_VALUE

255     >>   84 LOAD_FAST                0 (self)
             86 LOAD_ATTR                2 (_filelineno)
            106 CACHE
            108 CACHE
            110 RAISE_VARARGS            1

257         112 LOAD_FAST                0 (self)
            114 STORE_SUBSCR
            118 CACHE
            120 CACHE
            122 CACHE
            124 CACHE
            126 CACHE
            128 CACHE
            130 CACHE
            132 CACHE
            134 CACHE
            136 UNPACK_SEQUENCE          0
            140 CALL                     0
            148 CACHE
            150 POP_TOP

250         152 JUMP_BACKWARD           75 (to 4)

Disassembly of <code object nextfile at 0x000001B2A75BF8C0, file "fileinput.py", line 260>:
260           0 RESUME                   0

261           2 LOAD_FAST                0 (self)
              4 LOAD_ATTR                0 (_savestdout)
             24 CACHE
             26 CACHE
             28 CACHE

263          30 LOAD_FAST                1 (savestdout)
             32 POP_JUMP_IF_FALSE       12 (to 58)

264          34 LOAD_FAST                1 (savestdout)
             36 LOAD_GLOBAL              2 (sys)
             46 CACHE
             48 STORE_ATTR               2 (stdout)

266     >>   58 LOAD_FAST                0 (self)
             60 LOAD_ATTR                3 (NULL|self + sys)
             80 CACHE
             82 CACHE
             84 CACHE

268          86 NOP

269          88 LOAD_FAST                2 (output)
             90 POP_JUMP_IF_FALSE       20 (to 132)

270          92 LOAD_FAST                2 (output)
             94 STORE_SUBSCR
             98 CACHE
            100 CACHE
            102 CACHE
            104 CACHE
            106 CACHE
            108 CACHE
            110 CACHE
            112 CACHE
            114 CACHE
            116 UNPACK_SEQUENCE          0
            120 CALL                     0
            128 CACHE
            130 POP_TOP

272     >>  132 LOAD_FAST                0 (self)
            134 LOAD_ATTR                5 (NULL|self + stdout)
            154 CACHE
            156 CACHE
            158 CACHE

274         160 NOP

275         162 LOAD_FAST                0 (self)
            164 DELETE_ATTR              6 (_readline)
            166 JUMP_FORWARD            16 (to 200)
        >>  168 PUSH_EXC_INFO

276         170 LOAD_GLOBAL             14 (AttributeError)
            180 CACHE
            182 CHECK_EXC_MATCH
            184 POP_JUMP_IF_FALSE        3 (to 192)
            186 POP_TOP

277         188 POP_EXCEPT
            190 JUMP_FORWARD             4 (to 200)

276     >>  192 RERAISE                  0
        >>  194 COPY                     3
            196 POP_EXCEPT
            198 RERAISE                  1

278     >>  200 NOP

279         202 LOAD_FAST                3 (file)
            204 POP_JUMP_IF_FALSE       27 (to 260)
            206 LOAD_FAST                0 (self)
            208 LOAD_ATTR                8 (close)
            228 CACHE
            230 CACHE
            232 CACHE
            234 CACHE
            236 CACHE
            238 CACHE
            240 CACHE
            242 CACHE
            244 UNPACK_SEQUENCE          0
            248 CALL                     0
            256 CACHE
            258 POP_TOP

282     >>  260 LOAD_FAST                0 (self)
            262 LOAD_ATTR                9 (NULL|self + close)
            282 CACHE
            284 CACHE
            286 CACHE

284         288 LOAD_FAST                4 (backupfilename)
            290 POP_JUMP_IF_FALSE       44 (to 380)
            292 LOAD_FAST                0 (self)
            294 LOAD_ATTR               10 (_file)
            314 CACHE
            316 CACHE
            318 LOAD_ATTR               12 (_readline)
            338 CACHE
            340 CACHE
            342 CACHE
            344 POP_TOP
            346 JUMP_FORWARD            16 (to 380)
        >>  348 PUSH_EXC_INFO

286         350 LOAD_GLOBAL             26 (OSError)
            360 CACHE
            362 CHECK_EXC_MATCH
            364 POP_JUMP_IF_FALSE        3 (to 372)
            366 POP_TOP
            368 POP_EXCEPT
            370 JUMP_FORWARD             4 (to 380)
        >>  372 RERAISE                  0
        >>  374 COPY                     3
            376 POP_EXCEPT
            378 RERAISE                  1

288     >>  380 LOAD_CONST               1 (False)
            382 LOAD_FAST                0 (self)
            384 STORE_ATTR               8 (_isstdin)
            394 LOAD_CONST               0 (None)
            396 RETURN_VALUE
        >>  398 PUSH_EXC_INFO

282         400 LOAD_FAST                0 (self)
            402 LOAD_ATTR                9 (NULL|self + close)
            422 CACHE
            424 CACHE
            426 CACHE

284         428 LOAD_FAST                4 (backupfilename)
            430 POP_JUMP_IF_FALSE       44 (to 520)
            432 LOAD_FAST                0 (self)
            434 LOAD_ATTR               10 (_file)
            454 CACHE
            456 CACHE
            458 LOAD_ATTR               12 (_readline)
            478 CACHE
            480 CACHE
            482 CACHE
            484 POP_TOP
            486 JUMP_FORWARD            16 (to 520)
        >>  488 PUSH_EXC_INFO

286         490 LOAD_GLOBAL             26 (OSError)
            500 CACHE
            502 CHECK_EXC_MATCH
            504 POP_JUMP_IF_FALSE        3 (to 512)
            506 POP_TOP
            508 POP_EXCEPT
            510 JUMP_FORWARD             4 (to 520)
        >>  512 RERAISE                  0
        >>  514 COPY                     3
            516 POP_EXCEPT
            518 RERAISE                  1

288     >>  520 LOAD_CONST               1 (False)
            522 LOAD_FAST                0 (self)
            524 STORE_ATTR               8 (_isstdin)
            534 RERAISE                  0
        >>  536 COPY                     3
            538 POP_EXCEPT
            540 RERAISE                  1
        >>  542 PUSH_EXC_INFO

272         544 LOAD_FAST                0 (self)
            546 LOAD_ATTR                5 (NULL|self + stdout)
            566 CACHE
            568 CACHE
            570 CACHE

274         572 NOP

275         574 LOAD_FAST                0 (self)
            576 DELETE_ATTR              6 (_readline)
            578 JUMP_FORWARD            16 (to 612)
        >>  580 PUSH_EXC_INFO

276         582 LOAD_GLOBAL             14 (AttributeError)
            592 CACHE
            594 CHECK_EXC_MATCH
            596 POP_JUMP_IF_FALSE        3 (to 604)
            598 POP_TOP

277         600 POP_EXCEPT
            602 JUMP_FORWARD             4 (to 612)

276     >>  604 RERAISE                  0
        >>  606 COPY                     3
            608 POP_EXCEPT
            610 RERAISE                  1

278     >>  612 NOP

279         614 LOAD_FAST                3 (file)
            616 POP_JUMP_IF_FALSE       27 (to 672)
            618 LOAD_FAST                0 (self)
            620 LOAD_ATTR                8 (close)
            640 CACHE
            642 CACHE
            644 CACHE
            646 CACHE
            648 CACHE
            650 CACHE
            652 CACHE
            654 CACHE
            656 UNPACK_SEQUENCE          0
            660 CALL                     0
            668 CACHE
            670 POP_TOP

282     >>  672 LOAD_FAST                0 (self)
            674 LOAD_ATTR                9 (NULL|self + close)
            694 CACHE
            696 CACHE
            698 CACHE

284         700 LOAD_FAST                4 (backupfilename)
            702 POP_JUMP_IF_FALSE       44 (to 792)
            704 LOAD_FAST                0 (self)
            706 LOAD_ATTR               10 (_file)
            726 CACHE
            728 CACHE
            730 LOAD_ATTR               12 (_readline)
            750 CACHE
            752 CACHE
            754 CACHE
            756 POP_TOP
            758 JUMP_FORWARD            16 (to 792)
        >>  760 PUSH_EXC_INFO

286         762 LOAD_GLOBAL             26 (OSError)
            772 CACHE
            774 CHECK_EXC_MATCH
            776 POP_JUMP_IF_FALSE        3 (to 784)
            778 POP_TOP
            780 POP_EXCEPT
            782 JUMP_FORWARD             4 (to 792)
        >>  784 RERAISE                  0
        >>  786 COPY                     3
            788 POP_EXCEPT
            790 RERAISE                  1

288     >>  792 LOAD_CONST               1 (False)
            794 LOAD_FAST                0 (self)
            796 STORE_ATTR               8 (_isstdin)
            806 JUMP_FORWARD            72 (to 952)
        >>  808 PUSH_EXC_INFO

282         810 LOAD_FAST                0 (self)
            812 LOAD_ATTR                9 (NULL|self + close)
            832 CACHE
            834 CACHE
            836 CACHE

284         838 LOAD_FAST                4 (backupfilename)
            840 POP_JUMP_IF_FALSE       44 (to 930)
            842 LOAD_FAST                0 (self)
            844 LOAD_ATTR               10 (_file)
            864 CACHE
            866 CACHE
            868 LOAD_ATTR               12 (_readline)
            888 CACHE
            890 CACHE
            892 CACHE
            894 POP_TOP
            896 JUMP_FORWARD            16 (to 930)
        >>  898 PUSH_EXC_INFO

286         900 LOAD_GLOBAL             26 (OSError)
            910 CACHE
            912 CHECK_EXC_MATCH
            914 POP_JUMP_IF_FALSE        3 (to 922)
            916 POP_TOP
            918 POP_EXCEPT
            920 JUMP_FORWARD             4 (to 930)
        >>  922 RERAISE                  0
        >>  924 COPY                     3
            926 POP_EXCEPT
            928 RERAISE                  1

288     >>  930 LOAD_CONST               1 (False)
            932 LOAD_FAST                0 (self)
            934 STORE_ATTR               8 (_isstdin)
            944 RERAISE                  0
        >>  946 COPY                     3
            948 POP_EXCEPT
            950 RERAISE                  1
        >>  952 RERAISE                  0
        >>  954 COPY                     3
            956 POP_EXCEPT
            958 RERAISE                  1
ExceptionTable:
  88 to 130 -> 542 [0]
  162 to 164 -> 168 [0]
  168 to 186 -> 194 [1] lasti
  192 to 192 -> 194 [1] lasti
  202 to 258 -> 398 [0]
  306 to 344 -> 348 [0]
  348 to 366 -> 374 [1] lasti
  372 to 372 -> 374 [1] lasti
  398 to 444 -> 536 [1] lasti
  446 to 484 -> 488 [2]
  486 to 486 -> 536 [1] lasti
  488 to 506 -> 514 [3] lasti
  508 to 510 -> 536 [1] lasti
  512 to 512 -> 514 [3] lasti
  514 to 534 -> 536 [1] lasti
  542 to 570 -> 954 [1] lasti
  574 to 576 -> 580 [2]
  578 to 578 -> 954 [1] lasti
  580 to 598 -> 606 [3] lasti
  600 to 602 -> 954 [1] lasti
  604 to 604 -> 606 [3] lasti
  606 to 610 -> 954 [1] lasti
  614 to 670 -> 808 [2]
  672 to 716 -> 954 [1] lasti
  718 to 756 -> 760 [2]
  758 to 758 -> 954 [1] lasti
  760 to 778 -> 786 [3] lasti
  780 to 782 -> 954 [1] lasti
  784 to 784 -> 786 [3] lasti
  786 to 806 -> 954 [1] lasti
  808 to 854 -> 946 [3] lasti
  856 to 894 -> 898 [4]
  896 to 896 -> 946 [3] lasti
  898 to 916 -> 924 [5] lasti
  918 to 920 -> 946 [3] lasti
  922 to 922 -> 924 [5] lasti
  924 to 944 -> 946 [3] lasti
  946 to 952 -> 954 [1] lasti

Disassembly of <code object readline at 0x000001B2A71C6CD0, file "fileinput.py", line 290>:
290           0 RESUME                   0

291           2 NOP

292     >>    4 LOAD_FAST                0 (self)
              6 STORE_SUBSCR
             10 CACHE
             12 CACHE
             14 CACHE
             16 CACHE
             18 CACHE
             20 CACHE
             22 CACHE
             24 CACHE
             26 CACHE
             28 UNPACK_SEQUENCE          0
             32 CALL                     0
             40 CACHE
             42 STORE_FAST               1 (line)

293          44 LOAD_FAST                1 (line)
             46 POP_JUMP_IF_FALSE       18 (to 84)

294          48 LOAD_FAST                0 (self)
             50 COPY                     1
             52 LOAD_ATTR                1 (NULL|self + _readline)
             72 CACHE
             74 CACHE
             76 CACHE
             78 CACHE

295          80 LOAD_FAST                1 (line)
             82 RETURN_VALUE

296     >>   84 LOAD_FAST                0 (self)
             86 LOAD_ATTR                2 (_filelineno)
            106 CACHE
            108 CACHE
            110 CACHE
            112 CACHE
            114 CACHE
            116 CACHE
            118 CACHE
            120 CACHE
            122 CACHE
            124 CACHE
            126 UNPACK_SEQUENCE          0
            130 CALL                     0
            138 CACHE
            140 POP_TOP

291         142 JUMP_BACKWARD           70 (to 4)

Disassembly of <code object _readline at 0x000001B2A74D2430, file "fileinput.py", line 301>:
301           0 RESUME                   0

302           2 LOAD_FAST                0 (self)
              4 LOAD_ATTR                0 (_files)
             24 CACHE
             26 CACHE
             28 CACHE
             30 CONTAINS_OP              0
             32 POP_JUMP_IF_FALSE        2 (to 38)

304          34 LOAD_CONST               2 (b'')
             36 RETURN_VALUE

306     >>   38 LOAD_CONST               3 ('')
             40 RETURN_VALUE

307          42 LOAD_FAST                0 (self)
             44 LOAD_ATTR                0 (_files)
             64 CACHE
             66 LOAD_FAST                0 (self)
             68 STORE_ATTR               2 (_filename)

308          78 LOAD_FAST                0 (self)
             80 LOAD_ATTR                0 (_files)
            100 CACHE
            102 CACHE
            104 CACHE
            106 LOAD_FAST                0 (self)
            108 STORE_ATTR               0 (_files)

309         118 LOAD_FAST                0 (self)
            120 STORE_SUBSCR
            124 CACHE
            126 CACHE
            128 CACHE
            130 CACHE
            132 CACHE
            134 CACHE
            136 CACHE
            138 CACHE
            140 CACHE
            142 UNPACK_SEQUENCE          0
            146 CALL                     0
            154 CACHE
            156 LOAD_FAST                0 (self)
            158 STORE_ATTR               4 (_startlineno)

310         168 LOAD_CONST               4 (0)
            170 LOAD_FAST                0 (self)
            172 STORE_ATTR               5 (_filelineno)

311         182 LOAD_CONST               0 (None)
            184 LOAD_FAST                0 (self)
            186 STORE_ATTR               6 (_file)

312         196 LOAD_CONST               6 (False)
            198 LOAD_FAST                0 (self)
            200 STORE_ATTR               7 (_isstdin)

313         210 LOAD_CONST               4 (0)
            212 LOAD_FAST                0 (self)
            214 STORE_ATTR               8 (_backupfilename)

316         224 LOAD_CONST               1 ('b')
            226 LOAD_FAST                0 (self)
            228 LOAD_ATTR                1 (NULL|self + _files)
            248 CACHE
            250 CACHE
            252 CACHE
            254 LOAD_GLOBAL              1 (NULL + _files)
            264 STORE_FAST               1 (encoding)

321         266 LOAD_FAST                0 (self)
            268 LOAD_ATTR                2 (_mode)

322         288 LOAD_CONST               9 ('<stdin>')
            290 LOAD_FAST                0 (self)
            292 STORE_ATTR               2 (_filename)

323         302 LOAD_CONST               1 ('b')
            304 LOAD_FAST                0 (self)
            306 LOAD_ATTR                1 (NULL|self + _files)
            326 CACHE
            328 CACHE
            330 CACHE
            332 LOAD_GLOBAL             22 (sys)
            342 CACHE
            344 LOAD_ATTR               12 (_file)
            364 CACHE
            366 CACHE
            368 LOAD_ATTR               12 (_file)
            388 CACHE
            390 CACHE
            392 LOAD_FAST                0 (self)
            394 STORE_ATTR               6 (_file)
            404 JUMP_FORWARD            17 (to 440)

326         406 LOAD_GLOBAL             22 (sys)
            416 CACHE
            418 LOAD_ATTR               12 (_file)
            438 CACHE

327     >>  440 LOAD_CONST              11 (True)
            442 LOAD_FAST                0 (self)
            444 STORE_ATTR               7 (_isstdin)
            454 EXTENDED_ARG             2
            456 JUMP_FORWARD           626 (to 1710)

329         458 LOAD_FAST                0 (self)
            460 LOAD_ATTR               13 (NULL|self + _file)
            480 CACHE
            482 CACHE
            484 CACHE
            486 LOAD_ATTR               15 (NULL|self + _isstdin)
            506 CACHE
            508 UNPACK_SEQUENCE          1
            512 CALL                     1
            520 CACHE
            522 LOAD_FAST                0 (self)
            524 LOAD_ATTR               16 (_backupfilename)
            544 STORE_ATTR               8 (_backupfilename)

332         554 NOP

333         556 LOAD_GLOBAL             29 (NULL + os)
            566 CACHE
            568 LOAD_ATTR               17 (NULL|self + _backupfilename)
            588 CACHE
            590 UNPACK_SEQUENCE          1
            594 CALL                     1
            602 CACHE
            604 POP_TOP
            606 JUMP_FORWARD            16 (to 640)
        >>  608 PUSH_EXC_INFO

334         610 LOAD_GLOBAL             36 (OSError)
            620 CACHE
            622 CHECK_EXC_MATCH
            624 POP_JUMP_IF_FALSE        3 (to 632)
            626 POP_TOP

335         628 POP_EXCEPT
            630 JUMP_FORWARD             4 (to 640)

334     >>  632 RERAISE                  0
        >>  634 COPY                     3
            636 POP_EXCEPT
            638 RERAISE                  1

337     >>  640 LOAD_GLOBAL             29 (NULL + os)
            650 CACHE
            652 LOAD_ATTR               19 (NULL|self + _encoding)
            672 CACHE
            674 LOAD_FAST                0 (self)
            676 LOAD_ATTR                8 (_startlineno)
            696 CACHE
            698 CACHE
            700 POP_TOP

338         702 LOAD_GLOBAL             41 (NULL + open)
            712 CACHE
            714 LOAD_FAST                0 (self)
            716 LOAD_ATTR                8 (_startlineno)
            736 CACHE

339         738 LOAD_FAST                1 (encoding)
            740 LOAD_FAST                0 (self)
            742 LOAD_ATTR               21 (NULL|self + getattr)
            762 CACHE
            764 CACHE
            766 CACHE
            768 LOAD_FAST                0 (self)
            770 STORE_ATTR               6 (_file)

340         780 NOP

341         782 LOAD_GLOBAL             29 (NULL + os)
            792 CACHE
            794 LOAD_ATTR               22 (sys)
            814 CACHE
            816 STORE_SUBSCR
            820 CACHE
            822 CACHE
            824 CACHE
            826 CACHE
            828 CACHE
            830 CACHE
            832 CACHE
            834 CACHE
            836 CACHE
            838 UNPACK_SEQUENCE          0
            842 CALL                     0
            850 CACHE
            852 UNPACK_SEQUENCE          1
            856 CALL                     1
            864 CACHE
            866 LOAD_ATTR               24 (stdin)
            886 CACHE
            888 CACHE
            890 LOAD_ATTR               25 (NULL|self + stdin)
            910 CACHE
            912 LOAD_ATTR               26 (_inplace)
            932 CACHE
            934 CACHE
            936 CACHE
            938 LOAD_ATTR               27 (NULL|self + _inplace)
            958 CACHE
            960 CACHE
            962 CACHE
            964 CACHE
            966 LOAD_GLOBAL             28 (os)
            976 CACHE
            978 LOAD_CONST              14 ('O_BINARY')
            980 UNPACK_SEQUENCE          2
            984 CALL                     2
            992 CACHE
            994 POP_JUMP_IF_FALSE       15 (to 1026)

348         996 LOAD_FAST                3 (mode)
            998 LOAD_GLOBAL             28 (os)
           1008 CACHE
           1010 LOAD_ATTR               29 (NULL|self + os)
           1030 CACHE
           1032 CACHE
           1034 CACHE
           1036 CACHE
           1038 LOAD_ATTR               20 (getattr)
           1058 CACHE
           1060 LOAD_FAST                3 (mode)
           1062 LOAD_FAST                2 (perm)
           1064 UNPACK_SEQUENCE          3
           1068 CALL                     3
           1076 CACHE
           1078 STORE_FAST               4 (fd)

351        1080 LOAD_GLOBAL             29 (NULL + os)
           1090 CACHE
           1092 LOAD_ATTR               30 (fspath)
           1112 CACHE
           1114 CACHE

352        1116 LOAD_FAST                1 (encoding)
           1118 LOAD_FAST                0 (self)
           1120 LOAD_ATTR               21 (NULL|self + getattr)
           1140 CACHE
           1142 CACHE
           1144 CACHE
           1146 LOAD_FAST                0 (self)
           1148 STORE_ATTR              32 (_output)

353        1158 NOP

354        1160 LOAD_GLOBAL             29 (NULL + os)
           1170 CACHE
           1172 LOAD_ATTR               33 (NULL|self + _backup)
           1192 CACHE
           1194 LOAD_FAST                2 (perm)
           1196 UNPACK_SEQUENCE          2
           1200 CALL                     2
           1208 CACHE
           1210 POP_TOP
           1212 JUMP_FORWARD            71 (to 1356)
        >> 1214 PUSH_EXC_INFO

355        1216 LOAD_GLOBAL             36 (OSError)
           1226 CACHE
           1228 CHECK_EXC_MATCH
           1230 POP_JUMP_IF_FALSE        3 (to 1238)
           1232 POP_TOP

356        1234 POP_EXCEPT
           1236 JUMP_FORWARD            59 (to 1356)

355     >> 1238 RERAISE                  0
        >> 1240 COPY                     3
           1242 POP_EXCEPT
           1244 RERAISE                  1
        >> 1246 PUSH_EXC_INFO

342        1248 LOAD_GLOBAL             36 (OSError)
           1258 CACHE
           1260 CHECK_EXC_MATCH
           1262 POP_JUMP_IF_FALSE       42 (to 1348)
           1264 POP_TOP

343        1266 LOAD_GLOBAL             41 (NULL + open)
           1276 CACHE
           1278 LOAD_FAST                0 (self)
           1280 LOAD_ATTR                2 (_mode)
           1300 CACHE

344        1302 LOAD_FAST                1 (encoding)
           1304 LOAD_FAST                0 (self)
           1306 LOAD_ATTR               21 (NULL|self + getattr)
           1326 CACHE
           1328 CACHE
           1330 CACHE
           1332 LOAD_FAST                0 (self)
           1334 STORE_ATTR              32 (_output)
           1344 POP_EXCEPT
           1346 JUMP_FORWARD             4 (to 1356)

342     >> 1348 RERAISE                  0
        >> 1350 COPY                     3
           1352 POP_EXCEPT
           1354 RERAISE                  1

357     >> 1356 LOAD_GLOBAL             22 (sys)
           1366 CACHE
           1368 LOAD_ATTR               34 (unlink)
           1388 CACHE

358        1390 LOAD_FAST                0 (self)
           1392 LOAD_ATTR               32 (_backup)
           1412 CACHE
           1414 STORE_ATTR              34 (stdout)
           1424 JUMP_FORWARD           142 (to 1710)

361        1426 LOAD_FAST                0 (self)
           1428 LOAD_ATTR               36 (OSError)
           1448 CACHE
           1450 CACHE
           1452 POP_JUMP_IF_NOT_NONE    38 (to 1530)

365        1454 LOAD_FAST                0 (self)
           1456 STORE_SUBSCR
           1460 CACHE
           1462 CACHE
           1464 CACHE
           1466 CACHE
           1468 CACHE
           1470 CACHE
           1472 CACHE
           1474 CACHE
           1476 CACHE
           1478 LOAD_FAST                0 (self)
           1480 LOAD_ATTR                2 (_mode)
           1500 CACHE
           1502 UNPACK_SEQUENCE          2
           1506 CALL                     2
           1514 CACHE
           1516 LOAD_FAST                0 (self)
           1518 STORE_ATTR               6 (_file)
           1528 JUMP_FORWARD            90 (to 1710)

367     >> 1530 LOAD_FAST                0 (self)
           1532 STORE_SUBSCR
           1536 CACHE
           1538 CACHE
           1540 CACHE
           1542 CACHE
           1544 CACHE
           1546 CACHE
           1548 CACHE
           1550 CACHE
           1552 CACHE

368        1554 LOAD_FAST                0 (self)
           1556 LOAD_ATTR                2 (_mode)
           1576 CACHE
           1578 LOAD_FAST                0 (self)
           1580 LOAD_ATTR                9 (NULL|self + _startlineno)
           1600 CACHE

367        1602 KW_NAMES                13 (('encoding', 'errors'))
           1604 UNPACK_SEQUENCE          4
           1608 CALL                     4
           1616 CACHE
           1618 LOAD_FAST                0 (self)
           1620 STORE_ATTR               6 (_file)
           1630 JUMP_FORWARD            39 (to 1710)

370        1632 LOAD_GLOBAL             41 (NULL + open)
           1642 CACHE
           1644 LOAD_FAST                0 (self)
           1646 LOAD_ATTR                2 (_mode)
           1666 CACHE
           1668 LOAD_FAST                1 (encoding)
           1670 LOAD_FAST                0 (self)
           1672 LOAD_ATTR               21 (NULL|self + getattr)
           1692 CACHE
           1694 CACHE
           1696 CACHE
           1698 LOAD_FAST                0 (self)
           1700 STORE_ATTR               6 (_file)

371     >> 1710 LOAD_FAST                0 (self)
           1712 LOAD_ATTR                6 (lineno)
           1732 LOAD_FAST                0 (self)
           1734 STORE_ATTR              38 (_readline)

372        1744 LOAD_FAST                0 (self)
           1746 STORE_SUBSCR
           1750 CACHE
           1752 CACHE
           1754 CACHE
           1756 CACHE
           1758 CACHE
           1760 CACHE
           1762 CACHE
           1764 CACHE
           1766 CACHE
           1768 UNPACK_SEQUENCE          0
           1772 CALL                     0
           1780 CACHE
           1782 RETURN_VALUE
ExceptionTable:
  556 to 604 -> 608 [0]
  608 to 626 -> 634 [1] lasti
  632 to 632 -> 634 [1] lasti
  782 to 876 -> 1246 [0]
  1160 to 1210 -> 1214 [0]
  1214 to 1232 -> 1240 [1] lasti
  1238 to 1238 -> 1240 [1] lasti
  1246 to 1342 -> 1350 [1] lasti
  1348 to 1348 -> 1350 [1] lasti

Disassembly of <code object filename at 0x000001B2A767BD20, file "fileinput.py", line 374>:
374           0 RESUME                   0

375           2 LOAD_FAST                0 (self)
              4 LOAD_ATTR                0 (_filename)

Disassembly of <code object lineno at 0x000001B2A768ACD0, file "fileinput.py", line 377>:
377           0 RESUME                   0

378           2 LOAD_FAST                0 (self)
              4 LOAD_ATTR                0 (_startlineno)
             24 CACHE
             26 BINARY_OP                0 (+)
             30 RETURN_VALUE

Disassembly of <code object filelineno at 0x000001B2A767BDF0, file "fileinput.py", line 380>:
380           0 RESUME                   0

381           2 LOAD_FAST                0 (self)
              4 LOAD_ATTR                0 (_filelineno)

Disassembly of <code object fileno at 0x000001B2A76B4C10, file "fileinput.py", line 383>:
383           0 RESUME                   0

384           2 LOAD_FAST                0 (self)
              4 LOAD_ATTR                0 (_file)
             24 CACHE
             26 CACHE
             28 CACHE
             30 STORE_SUBSCR
             34 CACHE
             36 CACHE
             38 CACHE
             40 CACHE
             42 CACHE
             44 CACHE
             46 CACHE
             48 CACHE
             50 CACHE
             52 UNPACK_SEQUENCE          0
             56 CALL                     0
             64 CACHE
             66 RETURN_VALUE
        >>   68 PUSH_EXC_INFO

387          70 LOAD_GLOBAL              4 (ValueError)
             80 CACHE
             82 CHECK_EXC_MATCH
             84 POP_JUMP_IF_FALSE        4 (to 94)
             86 POP_TOP

388          88 POP_EXCEPT
             90 LOAD_CONST               1 (-1)
             92 RETURN_VALUE

387     >>   94 RERAISE                  0
        >>   96 COPY                     3
             98 POP_EXCEPT
            100 RERAISE                  1

390         102 LOAD_CONST               1 (-1)
            104 RETURN_VALUE
ExceptionTable:
  18 to 64 -> 68 [0]
  68 to 86 -> 96 [1] lasti
  94 to 94 -> 96 [1] lasti

Disassembly of <code object isfirstline at 0x000001B2A768B830, file "fileinput.py", line 392>:
392           0 RESUME                   0

393           2 LOAD_FAST                0 (self)
              4 LOAD_ATTR                0 (_filelineno)

Disassembly of <code object isstdin at 0x000001B2A767A4C0, file "fileinput.py", line 395>:
395           0 RESUME                   0

396           2 LOAD_FAST                0 (self)
              4 LOAD_ATTR                0 (_isstdin)

Disassembly of <code object hook_compressed at 0x000001B2A71BB430, file "fileinput.py", line 401>:
401           0 RESUME                   0

402           2 LOAD_FAST                2 (encoding)
              4 POP_JUMP_IF_NOT_NONE     6 (to 18)
              6 LOAD_CONST               1 ('b')
              8 LOAD_FAST                1 (mode)
             10 CONTAINS_OP              1
             12 POP_JUMP_IF_FALSE        2 (to 18)

403          14 LOAD_CONST               2 ('locale')
             16 STORE_FAST               2 (encoding)

404     >>   18 LOAD_GLOBAL              0 (os)
             28 CACHE
             30 LOAD_ATTR                1 (NULL|self + os)
             50 CACHE
             52 CACHE
             54 CACHE
             56 CACHE
             58 CACHE
             60 CACHE
             62 LOAD_FAST                0 (filename)
             64 UNPACK_SEQUENCE          1
             68 CALL                     1
             76 CACHE
             78 LOAD_CONST               3 (1)
             80 BINARY_SUBSCR
             84 CACHE
             86 CACHE
             88 CACHE
             90 STORE_FAST               4 (ext)

405          92 LOAD_FAST                4 (ext)
             94 LOAD_CONST               4 ('.gz')
             96 COMPARE_OP               2 (<)
            100 CACHE
            102 POP_JUMP_IF_FALSE       27 (to 158)

406         104 LOAD_CONST               5 (0)
            106 LOAD_CONST               0 (None)
            108 IMPORT_NAME              3 (gzip)
            110 STORE_FAST               5 (gzip)

407         112 LOAD_FAST                5 (gzip)
            114 STORE_SUBSCR
            118 CACHE
            120 CACHE
            122 CACHE
            124 CACHE
            126 CACHE
            128 CACHE
            130 CACHE
            132 CACHE
            134 CACHE
            136 LOAD_FAST                0 (filename)
            138 LOAD_FAST                1 (mode)
            140 UNPACK_SEQUENCE          2
            144 CALL                     2
            152 CACHE
            154 STORE_FAST               6 (stream)
            156 JUMP_FORWARD            52 (to 262)

408     >>  158 LOAD_FAST                4 (ext)
            160 LOAD_CONST               6 ('.bz2')
            162 COMPARE_OP               2 (<)
            166 CACHE
            168 POP_JUMP_IF_FALSE       27 (to 224)

409         170 LOAD_CONST               5 (0)
            172 LOAD_CONST               0 (None)
            174 IMPORT_NAME              5 (bz2)
            176 STORE_FAST               7 (bz2)

410         178 LOAD_FAST                7 (bz2)
            180 STORE_SUBSCR
            184 CACHE
            186 CACHE
            188 CACHE
            190 CACHE
            192 CACHE
            194 CACHE
            196 CACHE
            198 CACHE
            200 CACHE
            202 LOAD_FAST                0 (filename)
            204 LOAD_FAST                1 (mode)
            206 UNPACK_SEQUENCE          2
            210 CALL                     2
            218 CACHE
            220 STORE_FAST               6 (stream)
            222 JUMP_FORWARD            19 (to 262)

412     >>  224 LOAD_GLOBAL              9 (NULL + open)
            234 CACHE
            236 LOAD_FAST                0 (filename)
            238 LOAD_FAST                1 (mode)
            240 LOAD_FAST                2 (encoding)
            242 LOAD_FAST                3 (errors)
            244 KW_NAMES                 7 (('encoding', 'errors'))
            246 UNPACK_SEQUENCE          4
            250 CALL                     4
            258 CACHE
            260 RETURN_VALUE

415     >>  262 LOAD_CONST               1 ('b')
            264 LOAD_FAST                1 (mode)
            266 CONTAINS_OP              1
            268 POP_JUMP_IF_FALSE       23 (to 316)

416         270 LOAD_GLOBAL             15 (NULL + io)
            280 CACHE
            282 LOAD_ATTR                8 (open)
            302 CACHE
            304 CALL                     3
            312 CACHE
            314 STORE_FAST               6 (stream)

417     >>  316 LOAD_FAST                6 (stream)
            318 RETURN_VALUE

Disassembly of <code object hook_encoded at 0x000001B2A768A6B0, file "fileinput.py", line 420>:
              0 MAKE_CELL                0 (encoding)
              2 MAKE_CELL                1 (errors)

420           4 RESUME                   0

421           6 LOAD_CLOSURE             0 (encoding)
              8 LOAD_CLOSURE             1 (errors)
             10 BUILD_TUPLE              2
             12 LOAD_CONST               1 (<code object openhook at 0x000001B2A767F960, file "fileinput.py", line 421>)
             14 MAKE_FUNCTION            8 (closure)
             16 STORE_FAST               2 (openhook)

423          18 LOAD_FAST                2 (openhook)
             20 RETURN_VALUE

Disassembly of <code object openhook at 0x000001B2A767F960, file "fileinput.py", line 421>:
              0 COPY_FREE_VARS           2

421           2 RESUME                   0

422           4 LOAD_GLOBAL              1 (NULL + open)
             14 CACHE
             16 LOAD_FAST                0 (filename)
             18 LOAD_FAST                1 (mode)
             20 LOAD_DEREF               2 (encoding)
             22 LOAD_DEREF               3 (errors)
             24 KW_NAMES                 1 (('encoding', 'errors'))
             26 UNPACK_SEQUENCE          4
             30 CALL                     4
             38 CACHE
             40 RETURN_VALUE

Disassembly of <code object _test at 0x000001B2A74FAC00, file "fileinput.py", line 426>:
426           0 RESUME                   0

427           2 LOAD_CONST               1 (0)
              4 LOAD_CONST               0 (None)
              6 IMPORT_NAME              0 (getopt)
              8 STORE_FAST               0 (getopt)

428          10 LOAD_CONST               2 (False)
             12 STORE_FAST               1 (inplace)

429          14 LOAD_CONST               2 (False)
             16 STORE_FAST               2 (backup)

430          18 LOAD_FAST                0 (getopt)
             20 STORE_SUBSCR
             24 CACHE
             26 CACHE
             28 CACHE
             30 CACHE
             32 CACHE
             34 CACHE
             36 CACHE
             38 CACHE
             40 CACHE
             42 LOAD_GLOBAL              2 (sys)
             52 CACHE
             54 LOAD_ATTR                2 (sys)
             74 CACHE
             76 CACHE
             78 CACHE
             80 LOAD_CONST               4 ('ib:')
             82 UNPACK_SEQUENCE          2
             86 CALL                     2
             94 CACHE
             96 UNPACK_SEQUENCE          2
            100 STORE_FAST               3 (opts)
            102 STORE_FAST               4 (args)

431         104 LOAD_FAST                3 (opts)
            106 GET_ITER
        >>  108 FOR_ITER                21 (to 154)
            112 CACHE
            114 STORE_FAST               5 (o)
            116 STORE_FAST               6 (a)

432         118 LOAD_FAST                5 (o)
            120 LOAD_CONST               5 ('-i')
            122 COMPARE_OP               2 (<)
            126 CACHE
            128 POP_JUMP_IF_FALSE        2 (to 134)
            130 LOAD_CONST               6 (True)
            132 STORE_FAST               1 (inplace)

433     >>  134 LOAD_FAST                5 (o)
            136 LOAD_CONST               7 ('-b')
            138 COMPARE_OP               2 (<)
            142 CACHE
            144 POP_JUMP_IF_FALSE        2 (to 150)
            146 LOAD_FAST                6 (a)
            148 STORE_FAST               2 (backup)
        >>  150 JUMP_BACKWARD           22 (to 108)

434         152 LOAD_GLOBAL              7 (NULL + input)
            162 CACHE
            164 LOAD_FAST                4 (args)
            166 LOAD_FAST                1 (inplace)
            168 LOAD_FAST                2 (backup)
            170 KW_NAMES                 8 (('inplace', 'backup'))
            172 UNPACK_SEQUENCE          3
            176 CALL                     3
            184 CACHE
            186 GET_ITER
        >>  188 FOR_ITER               125 (to 442)

435         192 LOAD_FAST                7 (line)
            194 LOAD_CONST               9 (-1)
            196 LOAD_CONST               0 (None)
            198 BUILD_SLICE              2
            200 BINARY_SUBSCR
            204 CACHE
            206 CACHE
            208 CACHE
            210 LOAD_CONST              10 ('\n')
            212 COMPARE_OP               2 (<)
            216 CACHE
            218 POP_JUMP_IF_FALSE       10 (to 240)
            220 LOAD_FAST                7 (line)
            222 LOAD_CONST               0 (None)
            224 LOAD_CONST               9 (-1)
            226 BUILD_SLICE              2
            228 BINARY_SUBSCR
            232 CACHE
            234 CACHE
            236 CACHE
            238 STORE_FAST               7 (line)

436     >>  240 LOAD_FAST                7 (line)
            242 LOAD_CONST               9 (-1)
            244 LOAD_CONST               0 (None)
            246 BUILD_SLICE              2
            248 BINARY_SUBSCR
            252 CACHE
            254 CACHE
            256 CACHE
            258 LOAD_CONST              11 ('\r')
            260 COMPARE_OP               2 (<)
            264 CACHE
            266 POP_JUMP_IF_FALSE       10 (to 288)
            268 LOAD_FAST                7 (line)
            270 LOAD_CONST               0 (None)
            272 LOAD_CONST               9 (-1)
            274 BUILD_SLICE              2
            276 BINARY_SUBSCR
            280 CACHE
            282 CACHE
            284 CACHE
            286 STORE_FAST               7 (line)

437     >>  288 LOAD_GLOBAL              9 (NULL + print)
            298 CACHE
            300 LOAD_CONST              12 ('%d: %s[%d]%s %s')
            302 LOAD_GLOBAL             11 (NULL + lineno)
            312 CACHE
            314 UNPACK_SEQUENCE          0
            318 CALL                     0
            326 CACHE
            328 LOAD_GLOBAL             13 (NULL + filename)
            338 CACHE
            340 UNPACK_SEQUENCE          0
            344 CALL                     0
            352 CACHE
            354 LOAD_GLOBAL             15 (NULL + filelineno)
            364 CACHE
            366 UNPACK_SEQUENCE          0
            370 CALL                     0
            378 CACHE

438         380 LOAD_GLOBAL             17 (NULL + isfirstline)
            390 CACHE
            392 UNPACK_SEQUENCE          0
            396 CALL                     0
            404 CACHE
            406 POP_JUMP_IF_FALSE        2 (to 412)
            408 LOAD_CONST              13 ('*')
            410 LOAD_GLOBAL              1 (NULL + getopt)
            420 CACHE
            422 UNPACK_SEQUENCE          1
            426 CALL                     1
            434 CACHE
            436 POP_TOP
            438 JUMP_BACKWARD          126 (to 188)

439         440 LOAD_GLOBAL              9 (NULL + print)
            450 CACHE
            452 LOAD_CONST              15 ('%d: %s[%d]')
            454 LOAD_GLOBAL             11 (NULL + lineno)
            464 CACHE
            466 UNPACK_SEQUENCE          0
            470 CALL                     0
            478 CACHE
            480 LOAD_GLOBAL             13 (NULL + filename)
            490 CACHE
            492 UNPACK_SEQUENCE          0
            496 CALL                     0
            504 CACHE
            506 LOAD_GLOBAL             15 (NULL + filelineno)
            516 CACHE
            518 UNPACK_SEQUENCE          0
            522 CALL                     0
            530 CACHE
            532 BUILD_TUPLE              3
            534 BINARY_OP                6 (%)
            538 UNPACK_SEQUENCE          1
            542 CALL                     1
            550 CACHE
            552 POP_TOP
            554 LOAD_CONST               0 (None)
            556 RETURN_VALUE


# Constants:
# 0: <string length 3244>
# 1: 0
# 2: None
# 3: tuple
# 4: tuple
# 5: False
# 6: ''
# 7: 'r'
# 8: tuple
# 9: <code object input>
# 10: <code object close>
# 11: <code object nextfile>
# 12: <code object filename>
# 13: <code object lineno>
# 14: <code object filelineno>
# 15: <code object fileno>
# 16: <code object isfirstline>
# 17: <code object isstdin>
# 18: <code object FileInput>
# 19: 'FileInput'
# 20: tuple
# 21: <code object hook_compressed>
# 22: <code object hook_encoded>
# 23: <code object _test>
# 24: '__main__'
# 25: tuple
# 26: tuple


# Names:
# 0: '__doc__'
# 1: 'io'
# 2: 'sys'
# 3: 'os'
# 4: 'types'
# 5: 'GenericAlias'
# 6: '__all__'
# 7: '_state'
# 8: 'input'
# 9: 'close'
# 10: 'nextfile'
# 11: 'filename'
# 12: 'lineno'
# 13: 'filelineno'
# 14: 'fileno'
# 15: 'isfirstline'
# 16: 'isstdin'
# 17: 'FileInput'
# 18: 'hook_compressed'
# 19: 'hook_encoded'
# 20: '_test'
# 21: '__name__'


# Variable names:
