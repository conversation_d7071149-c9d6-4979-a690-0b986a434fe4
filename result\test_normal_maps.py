#!/usr/bin/env python3
"""
Test script for normal map generation functions
"""

import cv2
import numpy as np
import os
import sys

# Add the current directory to path to import reconstructed_main
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from reconstructed_main import (
        create_object_normal_map, 
        create_tangent_normal_map,
        normalize_intensity,
        validate_normal_map
    )
except ImportError as e:
    print(f"Failed to import functions: {e}")
    sys.exit(1)


def create_test_images():
    """Create test images for normal map generation"""
    print("Creating test images...")
    height_map_path = []

    directional_paths = [
        r"Z:\MoCap-LA\TEST DATA\Gus_base\Photos\A\A1\A1_9.tif",
        r"Z:\MoCap-LA\TEST DATA\Gus_base\Photos\A\A1\A1_10.tif",
        r"Z:\MoCap-LA\TEST DATA\Gus_base\Photos\A\A1\A1_11.tif",
        r"Z:\MoCap-LA\TEST DATA\Gus_base\Photos\A\A1\A1_12.tif",
        r"Z:\MoCap-LA\TEST DATA\Gus_base\Photos\A\A1\A1_13.tif",
        r"Z:\MoCap-LA\TEST DATA\Gus_base\Photos\A\A1\A1_14.tif"
    ]
    
    # # Create test directory
    # test_dir = "test_images"
    # os.makedirs(test_dir, exist_ok=True)
    
    # # Create a simple height map (16-bit)
    # height, width = 256, 256
    
    # # Create a height map with some geometric features
    # height_map = np.zeros((height, width), dtype=np.uint16)
    
    # # Add a pyramid in the center
    # center_x, center_y = width // 2, height // 2
    # for y in range(height):
    #     for x in range(width):
    #         dist_from_center = np.sqrt((x - center_x)**2 + (y - center_y)**2)
    #         pyramid_height = max(0, 1.0 - dist_from_center / 80.0)
    #         height_map[y, x] = int(pyramid_height * 65535)
    
    # # Add some noise for texture
    # noise = np.random.normal(0, 0.02, (height, width))
    # height_map = np.clip(height_map.astype(np.float32) + noise * 65535, 0, 65535).astype(np.uint16)
    
    # # Save height map
    # height_map_path = os.path.join(test_dir, "test_height_map.tif")
    # cv2.imwrite(height_map_path, height_map)
    # print(f"Created height map: {height_map_path}")
    
    # # Create 6 directional lighting images for object normal map test
    # directional_paths = []
    
    # # Light directions (same as in the main function)
    # light_directions = [
    #     (-1.0, 0.0, 1.0),   # Left
    #     (1.0, 0.0, 1.0),    # Right
    #     (0.0, -1.0, 1.0),   # Top
    #     (0.0, 1.0, 1.0),    # Bottom
    #     (0.0, 0.0, 1.0),    # Front
    #     (0.0, 0.0, -1.0)    # Back
    # ]
    
    # # Create a simple 3D sphere for testing
    # sphere_radius = 80
    # for i, light_dir in enumerate(light_directions):
    #     # Normalize light direction
    #     light_dir = np.array(light_dir)
    #     light_dir = light_dir / np.linalg.norm(light_dir)
        
    #     # Create lit sphere image
    #     lit_image = np.zeros((height, width), dtype=np.uint8)
        
    #     for y in range(height):
    #         for x in range(width):
    #             # Calculate if point is on sphere
    #             dx = x - center_x
    #             dy = y - center_y
    #             dist_sq = dx*dx + dy*dy
                
    #             if dist_sq <= sphere_radius*sphere_radius:
    #                 # Calculate surface normal for sphere
    #                 dz = np.sqrt(sphere_radius*sphere_radius - dist_sq)
    #                 surface_normal = np.array([dx, dy, dz])
    #                 surface_normal = surface_normal / np.linalg.norm(surface_normal)
                    
    #                 # Calculate lighting (Lambertian)
    #                 intensity = max(0, np.dot(surface_normal, light_dir))
    #                 lit_image[y, x] = int(intensity * 255)
        
    #     # Save directional image
    #     dir_path = os.path.join(test_dir, f"directional_{i+1}.png")
    #     cv2.imwrite(dir_path, lit_image)
    #     directional_paths.append(dir_path)
    #     print(f"Created directional image {i+1}: {dir_path}")
    
    return height_map_path, directional_paths


def test_tangent_normal_map():
    """Test tangent normal map generation"""
    print("\n" + "="*50)
    print("Testing Tangent Normal Map Generation")
    print("="*50)
    
    height_map_path, _ = create_test_images()
    output_path = "test_images/tangent_normal_output.png"
    
    # Test with different parameters
    test_cases = [
        {"blur_size": 3, "kernel_size": 3, "sigma": 1.0, "remove_blue": False},
        {"blur_size": 5, "kernel_size": 3, "sigma": 1.5, "remove_blue": False},
        {"blur_size": 5, "kernel_size": 3, "sigma": 1.0, "remove_blue": True},
    ]
    
    for i, params in enumerate(test_cases):
        print(f"\nTest case {i+1}: {params}")
        output_file = f"test_images/tangent_normal_{i+1}.png"
        
        success = create_tangent_normal_map(
            height_map_path, 
            output_file,
            **params
        )
        
        if success:
            print(f"✓ Tangent normal map created: {output_file}")
            
            # Validate the output
            result_image = cv2.imread(output_file, cv2.IMREAD_UNCHANGED)
            if result_image is not None:
                print(f"  Output shape: {result_image.shape}, dtype: {result_image.dtype}")
                print(f"  Value range: [{np.min(result_image)}, {np.max(result_image)}]")
            else:
                print("  ✗ Failed to load output image")
        else:
            print(f"✗ Failed to create tangent normal map")


def test_object_normal_map():
    """Test object normal map generation"""
    print("\n" + "="*50)
    print("Testing Object Normal Map Generation")
    print("="*50)
    
    _, directional_paths = create_test_images()
    output_path = r"Z:\MoCap-LA\TEST DATA\Gus_base\Photos\Maps\SpecularNormalMap\object_normal_output.png"
    
    print(f"Using {len(directional_paths)} directional images")
    
    success = create_object_normal_map(directional_paths, output_path)
    
    if success:
        print(f"✓ Object normal map created: {output_path}")
        
        # Validate the output
        result_image = cv2.imread(output_path, cv2.IMREAD_UNCHANGED)
        if result_image is not None:
            print(f"  Output shape: {result_image.shape}, dtype: {result_image.dtype}")
            print(f"  Value range: [{np.min(result_image)}, {np.max(result_image)}]")
            
            # Check if it looks like a valid normal map
            # Normal maps should have a bluish tint (Z component > 0.5)
            blue_channel = result_image[:, :, 0]  # BGR format
            avg_blue = np.mean(blue_channel)
            print(f"  Average blue channel value: {avg_blue:.1f} (should be > 127 for valid normal map)")
        else:
            print("  ✗ Failed to load output image")
    else:
        print(f"✗ Failed to create object normal map")


def test_utility_functions():
    """Test utility functions"""
    print("\n" + "="*50)
    print("Testing Utility Functions")
    print("="*50)
    
    # Test normalize_intensity
    print("\nTesting normalize_intensity:")
    
    # 8-bit test
    test_8bit = np.array([[0, 127, 255]], dtype=np.uint8)
    normalized_8bit = normalize_intensity(test_8bit)
    if normalized_8bit is not None:
        print(f"✓ 8-bit normalization: {test_8bit} -> {normalized_8bit}")
    
    # 16-bit test
    test_16bit = np.array([[0, 32767, 65535]], dtype=np.uint16)
    normalized_16bit = normalize_intensity(test_16bit)
    if normalized_16bit is not None:
        print(f"✓ 16-bit normalization: {test_16bit} -> {normalized_16bit}")
    
    # Invalid type test
    test_invalid = np.array([[0, 127, 255]], dtype=np.float64)
    normalized_invalid = normalize_intensity(test_invalid)
    if normalized_invalid is None:
        print("✓ Invalid type correctly rejected")
    
    # Test validate_normal_map
    print("\nTesting validate_normal_map:")
    
    # Valid normal map
    valid_normal = np.random.uniform(-1, 1, (64, 64, 3)).astype(np.float32)
    if validate_normal_map(valid_normal):
        print("✓ Valid normal map accepted")
    
    # Invalid normal map (wrong shape)
    invalid_normal = np.random.uniform(-1, 1, (64, 64, 4)).astype(np.float32)
    if not validate_normal_map(invalid_normal):
        print("✓ Invalid normal map correctly rejected")


def main():
    """Main test function"""
    print("ImgProc Normal Map Functions Test Suite")
    print("="*60)
    
    try:
        # Test utility functions first
        # test_utility_functions()
        
        # Test tangent normal map generation
        # test_tangent_normal_map()
        
        # Test object normal map generation
        test_object_normal_map()
        
        print("\n" + "="*60)
        print("All tests completed!")
        print("Check the 'test_images' directory for output files.")
        
    except Exception as e:
        print(f"\nTest failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    return True


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
