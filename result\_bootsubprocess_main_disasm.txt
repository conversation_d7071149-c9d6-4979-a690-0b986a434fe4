# MAIN APPLICATION CODE OBJECT
# Position: 7254216
# Filename: _bootsubprocess.py
# Function: <module>
# Args: 0
# Locals: 0
# Stack size: 4
# Flags: 0

# BYTECODE DISASSEMBLY:
==================================================
  0           0 RESUME                   0

  1           2 LOAD_CONST               0 ('\nBasic subprocess implementation for POSIX which only uses os functions. Only\nimplement features required by setup.py to build C extension modules when\nsubprocess is unavailable. setup.py is not used on Windows.\n')
              4 STORE_NAME               0 (__doc__)

  6           6 LOAD_CONST               1 (0)
              8 LOAD_CONST               2 (None)
             10 IMPORT_NAME              1 (os)
             12 STORE_NAME               1 (os)

 11          14 PUSH_NULL
             16 LOAD_BUILD_CLASS
             18 LOAD_CONST               3 (<code object Popen at 0x000001E77EBD8570, file "_bootsubprocess.py", line 11>)
             20 MAKE_FUNCTION            0
             22 LOAD_CONST               4 ('Popen')
             24 UNPACK_SEQUENCE          2
             28 CALL                     2
             36 CACHE
             38 STORE_NAME               2 (Popen)

 36          40 LOAD_CONST               5 (<code object _check_cmd at 0x000001E77C46A710, file "_bootsubprocess.py", line 36>)
             42 MAKE_FUNCTION            0
             44 STORE_NAME               3 (_check_cmd)

 66          46 LOAD_CONST               6 (<code object check_output at 0x000001E77C40AC80, file "_bootsubprocess.py", line 66>)
             48 MAKE_FUNCTION            0
             50 STORE_NAME               4 (check_output)
             52 LOAD_CONST               2 (None)
             54 RETURN_VALUE

Disassembly of <code object Popen at 0x000001E77EBD8570, file "_bootsubprocess.py", line 11>:
 11           0 RESUME                   0
              2 LOAD_NAME                0 (__name__)
              4 STORE_NAME               1 (__module__)
              6 LOAD_CONST               0 ('Popen')
              8 STORE_NAME               2 (__qualname__)

 12          10 LOAD_CONST               4 ((None,))
             12 LOAD_CONST               2 (<code object __init__ at 0x000001E77EBCC5D0, file "_bootsubprocess.py", line 12>)
             14 MAKE_FUNCTION            1 (defaults)
             16 STORE_NAME               3 (__init__)

 17          18 LOAD_CONST               3 (<code object wait at 0x000001E77E8FC040, file "_bootsubprocess.py", line 17>)
             20 MAKE_FUNCTION            0
             22 STORE_NAME               4 (wait)
             24 LOAD_CONST               1 (None)
             26 RETURN_VALUE

Disassembly of <code object __init__ at 0x000001E77EBCC5D0, file "_bootsubprocess.py", line 12>:
 12           0 RESUME                   0

 13           2 LOAD_FAST                1 (cmd)
              4 LOAD_FAST                0 (self)
              6 STORE_ATTR               0 (_cmd)

 14          16 LOAD_FAST                2 (env)
             18 LOAD_FAST                0 (self)
             20 STORE_ATTR               1 (_env)

 15          30 LOAD_CONST               0 (None)
             32 LOAD_FAST                0 (self)
             34 STORE_ATTR               2 (returncode)
             44 LOAD_CONST               0 (None)
             46 RETURN_VALUE

Disassembly of <code object wait at 0x000001E77E8FC040, file "_bootsubprocess.py", line 17>:
 17           0 RESUME                   0

 18           2 LOAD_GLOBAL              1 (NULL + os)
             12 CACHE
             14 LOAD_ATTR                1 (NULL|self + os)
             34 CACHE
             36 CACHE
             38 STORE_FAST               1 (pid)

 19          40 LOAD_FAST                1 (pid)
             42 LOAD_CONST               1 (0)
             44 COMPARE_OP               2 (<)
             48 CACHE
             50 POP_JUMP_IF_FALSE      135 (to 322)

 21          52 NOP

 22          54 LOAD_FAST                0 (self)
             56 LOAD_ATTR                2 (fork)
             76 CACHE
             78 CACHE
             80 LOAD_ATTR                3 (NULL|self + fork)
            100 CACHE
            102 LOAD_CONST               1 (0)
            104 BINARY_SUBSCR
            108 CACHE
            110 CACHE
            112 CACHE
            114 LOAD_FAST                0 (self)
            116 LOAD_ATTR                4 (_env)
            136 CACHE
            138 UNPACK_SEQUENCE          3
            142 CALL                     3
            150 CACHE
            152 POP_TOP
            154 JUMP_FORWARD            37 (to 230)

 25         156 LOAD_GLOBAL              1 (NULL + os)
            166 CACHE
            168 LOAD_ATTR                5 (NULL|self + _env)
            188 CACHE
            190 LOAD_CONST               1 (0)
            192 BINARY_SUBSCR
            196 CACHE
            198 CACHE
            200 CACHE
            202 LOAD_FAST                0 (self)
            204 LOAD_ATTR                4 (_env)
            224 CACHE
            226 CACHE
            228 POP_TOP

 27     >>  230 LOAD_GLOBAL              1 (NULL + os)
            240 CACHE
            242 LOAD_ATTR                6 (execve)
            262 CACHE
            264 CACHE
            266 CACHE
            268 POP_TOP
            270 JUMP_FORWARD            74 (to 420)
        >>  272 PUSH_EXC_INFO
            274 LOAD_GLOBAL              1 (NULL + os)
            284 CACHE
            286 LOAD_ATTR                6 (execve)
            306 CACHE
            308 CACHE
            310 CACHE
            312 POP_TOP
            314 RERAISE                  0
        >>  316 COPY                     3
            318 POP_EXCEPT
            320 RERAISE                  1

 30     >>  322 LOAD_GLOBAL              1 (NULL + os)
            332 CACHE
            334 LOAD_ATTR                7 (NULL|self + execve)
            354 CACHE
            356 CACHE
            358 CACHE
            360 CACHE
            362 UNPACK_SEQUENCE          2
            366 STORE_FAST               2 (_)
            368 STORE_FAST               3 (status)

 31         370 LOAD_GLOBAL              1 (NULL + os)
            380 CACHE
            382 LOAD_ATTR                8 (_cmd)
            402 CACHE
            404 CACHE
            406 CACHE
            408 LOAD_FAST                0 (self)
            410 STORE_ATTR               9 (returncode)

 33     >>  420 LOAD_FAST                0 (self)
            422 LOAD_ATTR                9 (NULL|self + _cmd)
ExceptionTable:
  54 to 228 -> 272 [0]
  272 to 314 -> 316 [1] lasti

Disassembly of <code object _check_cmd at 0x000001E77C46A710, file "_bootsubprocess.py", line 36>:
 36           0 RESUME                   0

 38           2 BUILD_LIST               0
              4 STORE_FAST               1 (safe_chars)

 39           6 LOAD_CONST               1 ((('a', 'z'), ('A', 'Z'), ('0', '9')))
              8 GET_ITER
        >>   10 FOR_ITER                87 (to 188)
             14 CACHE
             16 STORE_FAST               2 (first)
             18 STORE_FAST               3 (last)

 40          20 LOAD_GLOBAL              1 (NULL + range)
             30 CACHE
             32 LOAD_GLOBAL              3 (NULL + ord)
             42 CACHE
             44 LOAD_FAST                2 (first)
             46 UNPACK_SEQUENCE          1
             50 CALL                     1
             58 CACHE
             60 LOAD_GLOBAL              3 (NULL + ord)
             70 CACHE
             72 LOAD_FAST                3 (last)
             74 UNPACK_SEQUENCE          1
             78 CALL                     1
             86 CACHE
             88 LOAD_CONST               2 (1)
             90 BINARY_OP                0 (+)
             94 UNPACK_SEQUENCE          2
             98 CALL                     2
            106 CACHE
            108 GET_ITER
        >>  110 FOR_ITER                36 (to 186)

 41         114 LOAD_FAST                1 (safe_chars)
            116 STORE_SUBSCR
            120 CACHE
            122 CACHE
            124 CACHE
            126 CACHE
            128 CACHE
            130 CACHE
            132 CACHE
            134 CACHE
            136 CACHE
            138 LOAD_GLOBAL              7 (NULL + chr)
            148 CACHE
            150 LOAD_FAST                4 (ch)
            152 UNPACK_SEQUENCE          1
            156 CALL                     1
            164 CACHE
            166 UNPACK_SEQUENCE          1
            170 CALL                     1
            178 CACHE
            180 POP_TOP
            182 JUMP_BACKWARD           37 (to 110)

 40         184 JUMP_BACKWARD           88 (to 10)

 42     >>  186 LOAD_FAST                1 (safe_chars)
        >>  188 STORE_SUBSCR
            192 CACHE
            194 CACHE
            196 CACHE
            198 CACHE
            200 CACHE
            202 CACHE
            204 CACHE
            206 CACHE
            208 CACHE
            210 LOAD_CONST               3 ('./-')
            212 UNPACK_SEQUENCE          1
            216 CALL                     1
            224 CACHE
            226 POP_TOP

 43         228 LOAD_CONST               4 ('')
            230 STORE_SUBSCR
            234 CACHE
            236 CACHE
            238 CACHE
            240 CACHE
            242 CACHE
            244 CACHE
            246 CACHE
            248 CACHE
            250 CACHE
            252 LOAD_FAST                1 (safe_chars)
            254 UNPACK_SEQUENCE          1
            258 CALL                     1
            266 CACHE
            268 STORE_FAST               1 (safe_chars)

 45         270 LOAD_GLOBAL             11 (NULL + isinstance)
            280 CACHE
            282 LOAD_FAST                0 (cmd)
            284 LOAD_GLOBAL             12 (tuple)
            294 CACHE
            296 LOAD_GLOBAL             14 (list)
            306 CACHE
            308 BUILD_TUPLE              2
            310 UNPACK_SEQUENCE          2
            314 CALL                     2
            322 CACHE
            324 POP_JUMP_IF_FALSE        3 (to 332)

 46         326 LOAD_FAST                0 (cmd)
            328 STORE_FAST               5 (check_strs)
            330 JUMP_FORWARD            27 (to 386)

 47     >>  332 LOAD_GLOBAL             11 (NULL + isinstance)
            342 CACHE
            344 LOAD_FAST                0 (cmd)
            346 LOAD_GLOBAL             16 (str)
            356 CACHE
            358 UNPACK_SEQUENCE          2
            362 CALL                     2
            370 CACHE
            372 POP_JUMP_IF_FALSE        4 (to 382)

 48         374 LOAD_FAST                0 (cmd)
            376 BUILD_LIST               1
            378 STORE_FAST               5 (check_strs)
            380 JUMP_FORWARD             2 (to 386)

 50     >>  382 LOAD_CONST               5 (False)
            384 RETURN_VALUE

 52     >>  386 LOAD_FAST                5 (check_strs)
            388 GET_ITER
        >>  390 FOR_ITER                44 (to 482)

 53         394 LOAD_GLOBAL             11 (NULL + isinstance)
            404 CACHE
            406 LOAD_FAST                6 (arg)
            408 LOAD_GLOBAL             16 (str)
            418 CACHE
            420 UNPACK_SEQUENCE          2
            424 CALL                     2
            432 CACHE
            434 POP_JUMP_IF_TRUE         3 (to 442)

 54         436 POP_TOP
            438 LOAD_CONST               5 (False)
            440 RETURN_VALUE

 55     >>  442 LOAD_FAST                6 (arg)
            444 POP_JUMP_IF_TRUE         3 (to 452)

 57         446 POP_TOP
            448 LOAD_CONST               5 (False)
            450 RETURN_VALUE

 58     >>  452 LOAD_FAST                6 (arg)
            454 GET_ITER
        >>  456 FOR_ITER                10 (to 480)

 59         460 LOAD_FAST                4 (ch)
            462 LOAD_FAST                1 (safe_chars)
            464 CONTAINS_OP              1
            466 POP_JUMP_IF_FALSE        4 (to 476)

 60         468 POP_TOP
            470 POP_TOP
            472 LOAD_CONST               5 (False)
            474 RETURN_VALUE

 59     >>  476 JUMP_BACKWARD           11 (to 456)

 58         478 JUMP_BACKWARD           45 (to 390)

 62     >>  480 LOAD_CONST               6 (True)
        >>  482 RETURN_VALUE

Disassembly of <code object check_output at 0x000001E77C40AC80, file "_bootsubprocess.py", line 66>:
 66           0 RESUME                   0

 67           2 LOAD_FAST                1 (kwargs)
              4 POP_JUMP_IF_FALSE       28 (to 62)

 68           6 LOAD_GLOBAL              1 (NULL + NotImplementedError)
             16 CACHE
             18 LOAD_GLOBAL              3 (NULL + repr)
             28 CACHE
             30 LOAD_FAST                1 (kwargs)
             32 UNPACK_SEQUENCE          1
             36 CALL                     1
             44 CACHE
             46 UNPACK_SEQUENCE          1
             50 CALL                     1
             58 CACHE
             60 RAISE_VARARGS            1

 70     >>   62 LOAD_GLOBAL              5 (NULL + _check_cmd)
             72 CACHE
             74 LOAD_FAST                0 (cmd)
             76 UNPACK_SEQUENCE          1
             80 CALL                     1
             88 CACHE
             90 POP_JUMP_IF_TRUE        18 (to 128)

 71          92 LOAD_GLOBAL              7 (NULL + ValueError)
            102 CACHE
            104 LOAD_CONST               1 ('unsupported command: ')
            106 LOAD_FAST                0 (cmd)
            108 FORMAT_VALUE             2 (repr)
            110 BUILD_STRING             2
            112 UNPACK_SEQUENCE          1
            116 CALL                     1
            124 CACHE
            126 RAISE_VARARGS            1

 73     >>  128 LOAD_CONST               2 ('check_output.tmp')
            130 STORE_FAST               2 (tmp_filename)

 74         132 LOAD_GLOBAL              9 (NULL + isinstance)
            142 CACHE
            144 LOAD_FAST                0 (cmd)
            146 LOAD_GLOBAL             10 (str)
            156 CACHE
            158 UNPACK_SEQUENCE          2
            162 CALL                     2
            170 CACHE
            172 POP_JUMP_IF_TRUE        21 (to 216)

 75         174 LOAD_CONST               3 (' ')
            176 STORE_SUBSCR
            180 CACHE
            182 CACHE
            184 CACHE
            186 CACHE
            188 CACHE
            190 CACHE
            192 CACHE
            194 CACHE
            196 CACHE
            198 LOAD_FAST                0 (cmd)
            200 UNPACK_SEQUENCE          1
            204 CALL                     1
            212 CACHE
            214 STORE_FAST               0 (cmd)

 76     >>  216 LOAD_FAST                0 (cmd)
            218 FORMAT_VALUE             0
            220 LOAD_CONST               4 (' >')
            222 LOAD_FAST                2 (tmp_filename)
            224 FORMAT_VALUE             0
            226 BUILD_STRING             3
            228 STORE_FAST               0 (cmd)

 78         230 NOP

 80         232 LOAD_GLOBAL             15 (NULL + os)
            242 CACHE
            244 LOAD_ATTR                8 (isinstance)
            264 CACHE
            266 CACHE
            268 CACHE
            270 STORE_FAST               3 (status)

 81         272 LOAD_GLOBAL             15 (NULL + os)
            282 CACHE
            284 LOAD_ATTR                9 (NULL|self + isinstance)
            304 CACHE
            306 CACHE
            308 CACHE
            310 STORE_FAST               4 (exitcode)

 82         312 LOAD_FAST                4 (exitcode)
            314 POP_JUMP_IF_FALSE       21 (to 358)

 83         316 LOAD_GLOBAL              7 (NULL + ValueError)
            326 CACHE
            328 LOAD_CONST               5 ('Command ')
            330 LOAD_FAST                0 (cmd)
            332 FORMAT_VALUE             2 (repr)
            334 LOAD_CONST               6 (' returned non-zero exit status ')

 84         336 LOAD_FAST                4 (exitcode)

 83         338 FORMAT_VALUE             2 (repr)
            340 BUILD_STRING             4
            342 UNPACK_SEQUENCE          1
            346 CALL                     1
            354 CACHE
            356 RAISE_VARARGS            1

 86     >>  358 NOP

 87         360 LOAD_GLOBAL             21 (NULL + open)
            370 CACHE
            372 LOAD_FAST                2 (tmp_filename)
            374 LOAD_CONST               7 ('rb')
            376 UNPACK_SEQUENCE          2
            380 CALL                     2
            388 CACHE
            390 BEFORE_WITH
            392 STORE_FAST               5 (fp)

 88         394 LOAD_FAST                5 (fp)
            396 STORE_SUBSCR
            400 CACHE
            402 CACHE
            404 CACHE
            406 CACHE
            408 CACHE
            410 CACHE
            412 CACHE
            414 CACHE
            416 CACHE
            418 UNPACK_SEQUENCE          0
            422 CALL                     0
            430 CACHE
            432 STORE_FAST               6 (stdout)

 87         434 LOAD_CONST               0 (None)
            436 LOAD_CONST               0 (None)
            438 LOAD_CONST               0 (None)
            440 UNPACK_SEQUENCE          2
            444 CALL                     2
            452 CACHE
            454 POP_TOP
            456 JUMP_FORWARD            11 (to 480)
        >>  458 PUSH_EXC_INFO
            460 WITH_EXCEPT_START
            462 POP_JUMP_IF_TRUE         4 (to 472)
            464 RERAISE                  2
        >>  466 COPY                     3
            468 POP_EXCEPT
            470 RERAISE                  1
        >>  472 POP_TOP
            474 POP_EXCEPT
            476 POP_TOP
            478 POP_TOP
        >>  480 JUMP_FORWARD            18 (to 518)
        >>  482 PUSH_EXC_INFO

 89         484 LOAD_GLOBAL             24 (FileNotFoundError)
            494 CACHE
            496 CHECK_EXC_MATCH
            498 POP_JUMP_IF_FALSE        5 (to 510)
            500 POP_TOP

 90         502 LOAD_CONST               8 (b'')
            504 STORE_FAST               6 (stdout)
            506 POP_EXCEPT
            508 JUMP_FORWARD             4 (to 518)

 89     >>  510 RERAISE                  0
        >>  512 COPY                     3
            514 POP_EXCEPT
            516 RERAISE                  1

 92     >>  518 NOP

 93         520 LOAD_GLOBAL             15 (NULL + os)
            530 CACHE
            532 LOAD_ATTR               13 (NULL|self + join)
            552 CACHE
            554 CACHE
            556 CACHE
            558 POP_TOP
            560 JUMP_FORWARD            58 (to 678)
        >>  562 PUSH_EXC_INFO

 94         564 LOAD_GLOBAL             28 (OSError)
            574 CACHE
            576 CHECK_EXC_MATCH
            578 POP_JUMP_IF_FALSE        3 (to 586)
            580 POP_TOP

 95         582 POP_EXCEPT
            584 JUMP_FORWARD            46 (to 678)

 94     >>  586 RERAISE                  0
        >>  588 COPY                     3
            590 POP_EXCEPT
            592 RERAISE                  1
        >>  594 PUSH_EXC_INFO

 92         596 NOP

 93         598 LOAD_GLOBAL             15 (NULL + os)
            608 CACHE
            610 LOAD_ATTR               13 (NULL|self + join)
            630 CACHE
            632 CACHE
            634 CACHE
            636 POP_TOP
            638 RERAISE                  0
        >>  640 PUSH_EXC_INFO

 94         642 LOAD_GLOBAL             28 (OSError)
            652 CACHE
            654 CHECK_EXC_MATCH
            656 POP_JUMP_IF_FALSE        3 (to 664)
            658 POP_TOP

 95         660 POP_EXCEPT
            662 RERAISE                  0

 94     >>  664 RERAISE                  0
        >>  666 COPY                     3
            668 POP_EXCEPT
            670 RERAISE                  1
        >>  672 COPY                     3
            674 POP_EXCEPT
            676 RERAISE                  1

 97     >>  678 LOAD_FAST                6 (stdout)
            680 RETURN_VALUE
ExceptionTable:
  232 to 356 -> 594 [0]
  360 to 390 -> 482 [0]
  392 to 432 -> 458 [1] lasti
  434 to 456 -> 482 [0]
  458 to 464 -> 466 [3] lasti
  466 to 470 -> 482 [0]
  472 to 472 -> 466 [3] lasti
  474 to 478 -> 482 [0]
  480 to 480 -> 594 [0]
  482 to 504 -> 512 [1] lasti
  506 to 508 -> 594 [0]
  510 to 510 -> 512 [1] lasti
  512 to 516 -> 594 [0]
  520 to 558 -> 562 [0]
  562 to 580 -> 588 [1] lasti
  586 to 586 -> 588 [1] lasti
  594 to 594 -> 672 [1] lasti
  598 to 636 -> 640 [2]
  638 to 638 -> 672 [1] lasti
  640 to 658 -> 666 [3] lasti
  660 to 662 -> 672 [1] lasti
  664 to 664 -> 666 [3] lasti
  666 to 670 -> 672 [1] lasti


# CONSTANTS:
==================================================
# 0: <string length 212> '
Basic subprocess implementation for POSIX which only uses os functions. Only
implement features req...'
# 1: 0
# 2: None
# 3: <code object Popen from _bootsubprocess.py>
# 4: 'Popen'
# 5: <code object _check_cmd from _bootsubprocess.py>
# 6: <code object check_output from _bootsubprocess.py>


# NAMES (global/attribute references):
==================================================
# 0: '__doc__'
# 1: 'os'
# 2: 'Popen'
# 3: '_check_cmd'
# 4: 'check_output'


# VARIABLE NAMES (local variables):
==================================================


# FREE VARIABLES:
==================================================
