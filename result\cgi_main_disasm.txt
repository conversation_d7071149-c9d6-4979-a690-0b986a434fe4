# MAIN APPLICATION CODE OBJECT
# Position: 8112481
# Filename: cgi.py
# Function: <module>
# Args: 0
# Locals: 0
# Stack size: 5
# Flags: 0

# BYTECODE DISASSEMBLY:
==================================================
   0           0 RESUME                   0

  12           2 LOAD_CONST               0 ('Support module for CGI (Common Gateway Interface) scripts.\n\nThis module defines a number of utilities for use by CGI scripts\nwritten in Python.\n\nThe global variable maxlen can be set to an integer indicating the maximum size\nof a POST request. POST requests larger than this size will result in a\nValueError being raised during parsing. The default value of this variable is 0,\nmeaning the request size is unlimited.\n')
               4 STORE_NAME               0 (__doc__)

  33           6 LOAD_CONST               1 ('2.6')
               8 STORE_NAME               1 (__version__)

  39          10 LOAD_CONST               2 (0)
              12 LOAD_CONST               3 (('StringIO', 'BytesIO', 'TextIOWrapper'))
              14 IMPORT_NAME              2 (io)
              16 IMPORT_FROM              3 (StringIO)
              18 STORE_NAME               3 (StringIO)
              20 IMPORT_FROM              4 (BytesIO)
              22 STORE_NAME               4 (BytesIO)
              24 IMPORT_FROM              5 (TextIOWrapper)
              26 STORE_NAME               5 (TextIOWrapper)
              28 POP_TOP

  40          30 LOAD_CONST               2 (0)
              32 LOAD_CONST               4 (('Mapping',))
              34 IMPORT_NAME              6 (collections.abc)
              36 IMPORT_FROM              7 (Mapping)
              38 STORE_NAME               7 (Mapping)
              40 POP_TOP

  41          42 LOAD_CONST               2 (0)
              44 LOAD_CONST               5 (None)
              46 IMPORT_NAME              8 (sys)
              48 STORE_NAME               8 (sys)

  42          50 LOAD_CONST               2 (0)
              52 LOAD_CONST               5 (None)
              54 IMPORT_NAME              9 (os)
              56 STORE_NAME               9 (os)

  43          58 LOAD_CONST               2 (0)
              60 LOAD_CONST               5 (None)
              62 IMPORT_NAME             10 (urllib.parse)
              64 STORE_NAME              11 (urllib)

  44          66 LOAD_CONST               2 (0)
              68 LOAD_CONST               6 (('FeedParser',))
              70 IMPORT_NAME             12 (email.parser)
              72 IMPORT_FROM             13 (FeedParser)
              74 STORE_NAME              13 (FeedParser)
              76 POP_TOP

  45          78 LOAD_CONST               2 (0)
              80 LOAD_CONST               7 (('Message',))
              82 IMPORT_NAME             14 (email.message)
              84 IMPORT_FROM             15 (Message)
              86 STORE_NAME              15 (Message)
              88 POP_TOP

  46          90 LOAD_CONST               2 (0)
              92 LOAD_CONST               5 (None)
              94 IMPORT_NAME             16 (html)
              96 STORE_NAME              16 (html)

  47          98 LOAD_CONST               2 (0)
             100 LOAD_CONST               5 (None)
             102 IMPORT_NAME             17 (locale)
             104 STORE_NAME              17 (locale)

  48         106 LOAD_CONST               2 (0)
             108 LOAD_CONST               5 (None)
             110 IMPORT_NAME             18 (tempfile)
             112 STORE_NAME              18 (tempfile)

  49         114 LOAD_CONST               2 (0)
             116 LOAD_CONST               5 (None)
             118 IMPORT_NAME             19 (warnings)
             120 STORE_NAME              19 (warnings)

  51         122 BUILD_LIST               0
             124 LOAD_CONST               8 (('MiniFieldStorage', 'FieldStorage', 'parse', 'parse_multipart', 'parse_header', 'test', 'print_exception', 'print_environ', 'print_form', 'print_directory', 'print_arguments', 'print_environ_usage'))
             126 LIST_EXTEND              1
             128 STORE_NAME              20 (__all__)

  57         130 PUSH_NULL
             132 LOAD_NAME               19 (warnings)
             134 LOAD_ATTR               21 (NULL|self + urllib.parse)
             154 CALL                     2
             162 CACHE
             164 POP_TOP

  62         166 LOAD_CONST              11 ('')
             168 STORE_GLOBAL            23 (logfile)

  63         170 LOAD_CONST               5 (None)
             172 STORE_GLOBAL            24 (logfp)

  65         174 LOAD_CONST              12 (<code object initlog at 0x000001E77ECFC6F0, file "cgi.py", line 65>)
             176 MAKE_FUNCTION            0
             178 STORE_NAME              25 (initlog)

 102         180 LOAD_CONST              13 (<code object dolog at 0x000001E77ECF1AC0, file "cgi.py", line 102>)
             182 MAKE_FUNCTION            0
             184 STORE_NAME              26 (dolog)

 106         186 LOAD_CONST              14 (<code object nolog at 0x000001E77EC23770, file "cgi.py", line 106>)
             188 MAKE_FUNCTION            0
             190 STORE_NAME              27 (nolog)

 110         192 LOAD_CONST              15 (<code object closelog at 0x000001E77ECCD6B0, file "cgi.py", line 110>)
             194 MAKE_FUNCTION            0
             196 STORE_NAME              28 (closelog)

 119         198 LOAD_NAME               25 (initlog)
             200 STORE_GLOBAL            29 (log)

 127         202 LOAD_CONST               2 (0)
             204 STORE_GLOBAL            30 (maxlen)

 129         206 LOAD_CONST               5 (None)
             208 LOAD_NAME                9 (os)
             210 LOAD_ATTR               31 (NULL|self + Message)
             230 MAKE_FUNCTION            1 (defaults)
             232 STORE_NAME              32 (parse)

 199         234 LOAD_CONST              36 (('utf-8', 'replace', '&'))
             236 LOAD_CONST              20 (<code object parse_multipart at 0x000001E77E762310, file "cgi.py", line 199>)
             238 MAKE_FUNCTION            1 (defaults)
             240 STORE_NAME              33 (parse_multipart)

 226         242 LOAD_CONST              21 (<code object _parseparam at 0x000001E77E9E9740, file "cgi.py", line 226>)
             244 MAKE_FUNCTION            0
             246 STORE_NAME              34 (_parseparam)

 238         248 LOAD_CONST              22 (<code object parse_header at 0x000001E77E9E7990, file "cgi.py", line 238>)
             250 MAKE_FUNCTION            0
             252 STORE_NAME              35 (parse_header)

 262         254 PUSH_NULL
             256 LOAD_BUILD_CLASS
             258 LOAD_CONST              23 (<code object MiniFieldStorage at 0x000001E77ECD9630, file "cgi.py", line 262>)
             260 MAKE_FUNCTION            0
             262 LOAD_CONST              24 ('MiniFieldStorage')
             264 UNPACK_SEQUENCE          2
             268 CALL                     2
             276 CACHE
             278 STORE_NAME              36 (MiniFieldStorage)

 287         280 PUSH_NULL
             282 LOAD_BUILD_CLASS
             284 LOAD_CONST              25 (<code object FieldStorage at 0x000001E77ECACED0, file "cgi.py", line 287>)
             286 MAKE_FUNCTION            0
             288 LOAD_CONST              26 ('FieldStorage')
             290 UNPACK_SEQUENCE          2
             294 CALL                     2
             302 CACHE
             304 STORE_NAME              37 (FieldStorage)

 853         306 LOAD_NAME                9 (os)
             308 LOAD_ATTR               31 (NULL|self + Message)
             328 LOAD_CONST              28 (<code object print_exception at 0x000001E77E9EB510, file "cgi.py", line 892>)
             330 MAKE_FUNCTION            1 (defaults)
             332 STORE_NAME              39 (print_exception)

 906         334 LOAD_NAME                9 (os)
             336 LOAD_ATTR               31 (NULL|self + Message)
             356 MAKE_FUNCTION            0
             358 STORE_NAME              41 (print_form)

 933         360 LOAD_CONST              31 (<code object print_directory at 0x000001E77EF6F560, file "cgi.py", line 933>)
             362 MAKE_FUNCTION            0
             364 STORE_NAME              42 (print_directory)

 945         366 LOAD_CONST              32 (<code object print_arguments at 0x000001E77ECB6700, file "cgi.py", line 945>)
             368 MAKE_FUNCTION            0
             370 STORE_NAME              43 (print_arguments)

 952         372 LOAD_CONST              33 (<code object print_environ_usage at 0x000001E77ECF5B60, file "cgi.py", line 952>)
             374 MAKE_FUNCTION            0
             376 STORE_NAME              44 (print_environ_usage)

 999         378 LOAD_CONST              34 (<code object valid_boundary at 0x000001E77ECBE3D0, file "cgi.py", line 999>)
             380 MAKE_FUNCTION            0
             382 STORE_NAME              45 (valid_boundary)

1011         384 LOAD_NAME               22 (__name__)
             386 LOAD_CONST              35 ('__main__')
             388 COMPARE_OP               2 (<)
             392 CACHE
             394 POP_JUMP_IF_FALSE       12 (to 420)

1012         396 PUSH_NULL
             398 LOAD_NAME               38 (test)
             400 UNPACK_SEQUENCE          0
             404 CALL                     0
             412 CACHE
             414 POP_TOP
             416 LOAD_CONST               5 (None)
             418 RETURN_VALUE

1011     >>  420 LOAD_CONST               5 (None)
             422 RETURN_VALUE

Disassembly of <code object initlog at 0x000001E77ECFC6F0, file "cgi.py", line 65>:
 65           0 RESUME                   0

 89           2 LOAD_GLOBAL              1 (NULL + warnings)
             12 CACHE
             14 LOAD_ATTR                1 (NULL|self + warnings)
             34 CACHE
             36 CACHE
             38 LOAD_CONST               2 (2)

 89          40 KW_NAMES                 3 (('stacklevel',))
             42 UNPACK_SEQUENCE          3
             46 CALL                     3
             54 CACHE
             56 POP_TOP

 91          58 LOAD_GLOBAL              6 (logfile)
             68 CACHE
             70 POP_JUMP_IF_FALSE       48 (to 168)
             72 LOAD_GLOBAL              8 (logfp)
             82 CACHE
             84 POP_JUMP_IF_TRUE        41 (to 168)

 92          86 NOP

 93          88 LOAD_GLOBAL             11 (NULL + open)
             98 CACHE
            100 LOAD_GLOBAL              6 (logfile)
            110 CACHE
            112 LOAD_CONST               4 ('a')
            114 LOAD_CONST               5 ('locale')
            116 KW_NAMES                 6 (('encoding',))
            118 UNPACK_SEQUENCE          3
            122 CALL                     3
            130 CACHE
            132 STORE_GLOBAL             4 (logfp)
            134 JUMP_FORWARD            16 (to 168)
        >>  136 PUSH_EXC_INFO

 94         138 LOAD_GLOBAL             12 (OSError)
            148 CACHE
            150 CHECK_EXC_MATCH
            152 POP_JUMP_IF_FALSE        3 (to 160)
            154 POP_TOP

 95         156 POP_EXCEPT
            158 JUMP_FORWARD             4 (to 168)

 94     >>  160 RERAISE                  0
        >>  162 COPY                     3
            164 POP_EXCEPT
            166 RERAISE                  1

 96     >>  168 LOAD_GLOBAL              8 (logfp)
            178 CACHE
            180 POP_JUMP_IF_TRUE         8 (to 198)

 97         182 LOAD_GLOBAL             14 (nolog)
            192 CACHE
            194 STORE_GLOBAL             8 (log)
            196 JUMP_FORWARD             7 (to 212)

 99     >>  198 LOAD_GLOBAL             18 (dolog)
            208 CACHE
            210 STORE_GLOBAL             8 (log)

100     >>  212 LOAD_GLOBAL             17 (NULL + log)
            222 CACHE
            224 LOAD_FAST                0 (allargs)
            226 CALL_FUNCTION_EX         0
            228 POP_TOP
            230 LOAD_CONST               7 (None)
            232 RETURN_VALUE
ExceptionTable:
  88 to 132 -> 136 [0]
  136 to 154 -> 162 [1] lasti
  160 to 160 -> 162 [1] lasti

Disassembly of <code object dolog at 0x000001E77ECF1AC0, file "cgi.py", line 102>:
102           0 RESUME                   0

104           2 LOAD_GLOBAL              0 (logfp)
             12 CACHE
             14 STORE_SUBSCR
             18 CACHE
             20 CACHE
             22 CACHE
             24 CACHE
             26 CACHE
             28 CACHE
             30 CACHE
             32 CACHE
             34 CACHE
             36 LOAD_FAST                0 (fmt)
             38 LOAD_FAST                1 (args)
             40 BINARY_OP                6 (%)
             44 LOAD_CONST               1 ('\n')
             46 BINARY_OP                0 (+)
             50 UNPACK_SEQUENCE          1
             54 CALL                     1
             62 CACHE
             64 POP_TOP
             66 LOAD_CONST               2 (None)
             68 RETURN_VALUE

Disassembly of <code object nolog at 0x000001E77EC23770, file "cgi.py", line 106>:
106           0 RESUME                   0

108           2 LOAD_CONST               1 (None)
              4 RETURN_VALUE

Disassembly of <code object closelog at 0x000001E77ECCD6B0, file "cgi.py", line 110>:
110           0 RESUME                   0

113           2 LOAD_CONST               1 ('')
              4 STORE_GLOBAL             0 (logfile)

114           6 LOAD_GLOBAL              2 (logfp)
             16 CACHE
             18 POP_JUMP_IF_FALSE       27 (to 74)

115          20 LOAD_GLOBAL              2 (logfp)
             30 CACHE
             32 STORE_SUBSCR
             36 CACHE
             38 CACHE
             40 CACHE
             42 CACHE
             44 CACHE
             46 CACHE
             48 CACHE
             50 CACHE
             52 CACHE
             54 UNPACK_SEQUENCE          0
             58 CALL                     0
             66 CACHE
             68 POP_TOP

116          70 LOAD_CONST               2 (None)
             72 STORE_GLOBAL             1 (logfp)

117     >>   74 LOAD_GLOBAL              6 (initlog)
             84 CACHE
             86 STORE_GLOBAL             4 (log)
             88 LOAD_CONST               2 (None)
             90 RETURN_VALUE

Disassembly of <code object parse at 0x000001E77E973440, file "cgi.py", line 129>:
129           0 RESUME                   0

153           2 LOAD_FAST                0 (fp)
              4 POP_JUMP_IF_NOT_NONE    12 (to 30)

154           6 LOAD_GLOBAL              0 (sys)
             16 CACHE
             18 LOAD_ATTR                1 (NULL|self + sys)
             38 CACHE
             40 CACHE
             42 LOAD_FAST                0 (fp)
             44 LOAD_CONST               2 ('encoding')
             46 UNPACK_SEQUENCE          2
             50 CALL                     2
             58 CACHE
             60 POP_JUMP_IF_FALSE        8 (to 78)

159          62 LOAD_FAST                0 (fp)
             64 LOAD_ATTR                3 (NULL|self + stdin)
             84 CACHE
             86 CACHE
             88 CACHE
             90 CACHE
             92 CACHE
             94 LOAD_FAST                0 (fp)
             96 LOAD_GLOBAL             10 (TextIOWrapper)
            106 CACHE
            108 UNPACK_SEQUENCE          2
            112 CALL                     2
            120 CACHE
            122 POP_JUMP_IF_FALSE        7 (to 138)

165         124 LOAD_FAST                0 (fp)
            126 LOAD_ATTR                6 (encoding)

168         146 LOAD_CONST               5 ('GET')
            148 LOAD_FAST                1 (environ)
            150 LOAD_CONST               4 ('REQUEST_METHOD')
            152 STORE_SUBSCR

169         156 LOAD_FAST                1 (environ)
            158 LOAD_CONST               4 ('REQUEST_METHOD')
            160 BINARY_SUBSCR
            164 CACHE
            166 CACHE
            168 CACHE
            170 LOAD_CONST               6 ('POST')
            172 COMPARE_OP               2 (<)
            176 CACHE
            178 POP_JUMP_IF_FALSE      228 (to 636)

170         180 LOAD_GLOBAL             15 (NULL + parse_header)
            190 CACHE
            192 LOAD_FAST                1 (environ)
            194 LOAD_CONST               7 ('CONTENT_TYPE')
            196 BINARY_SUBSCR
            200 CACHE
            202 CACHE
            204 CACHE
            206 UNPACK_SEQUENCE          1
            210 CALL                     1
            218 CACHE
            220 UNPACK_SEQUENCE          2
            224 STORE_FAST               6 (ctype)
            226 STORE_FAST               7 (pdict)

171         228 LOAD_FAST                6 (ctype)
            230 LOAD_CONST               8 ('multipart/form-data')
            232 COMPARE_OP               2 (<)
            236 CACHE
            238 POP_JUMP_IF_FALSE       18 (to 276)

172         240 LOAD_GLOBAL             17 (NULL + parse_multipart)
            250 CACHE
            252 LOAD_FAST                0 (fp)
            254 LOAD_FAST                7 (pdict)
            256 LOAD_FAST                4 (separator)
            258 KW_NAMES                 9 (('separator',))
            260 UNPACK_SEQUENCE          3
            264 CALL                     3
            272 CACHE
            274 RETURN_VALUE

173     >>  276 LOAD_FAST                6 (ctype)
            278 LOAD_CONST              10 ('application/x-www-form-urlencoded')
            280 COMPARE_OP               2 (<)
            284 CACHE
            286 POP_JUMP_IF_FALSE       95 (to 478)

174         288 LOAD_GLOBAL             19 (NULL + int)
            298 CACHE
            300 LOAD_FAST                1 (environ)
            302 LOAD_CONST              11 ('CONTENT_LENGTH')
            304 BINARY_SUBSCR
            308 CACHE
            310 CACHE
            312 CACHE
            314 UNPACK_SEQUENCE          1
            318 CALL                     1
            326 CACHE
            328 STORE_FAST               8 (clength)

175         330 LOAD_GLOBAL             20 (maxlen)
            340 CACHE
            342 POP_JUMP_IF_FALSE       26 (to 396)
            344 LOAD_FAST                8 (clength)
            346 LOAD_GLOBAL             20 (maxlen)
            356 CACHE
            358 COMPARE_OP               4 (<)
            362 CACHE
            364 POP_JUMP_IF_FALSE       15 (to 396)

176         366 LOAD_GLOBAL             23 (NULL + ValueError)
            376 CACHE
            378 LOAD_CONST              12 ('Maximum content length exceeded')
            380 UNPACK_SEQUENCE          1
            384 CALL                     1
            392 CACHE
            394 RAISE_VARARGS            1

177     >>  396 LOAD_FAST                0 (fp)
            398 STORE_SUBSCR
            402 CACHE
            404 CACHE
            406 CACHE
            408 CACHE
            410 CACHE
            412 CACHE
            414 CACHE
            416 CACHE
            418 CACHE
            420 LOAD_FAST                8 (clength)
            422 UNPACK_SEQUENCE          1
            426 CALL                     1
            434 CACHE
            436 STORE_SUBSCR
            440 CACHE
            442 CACHE
            444 CACHE
            446 CACHE
            448 CACHE
            450 CACHE
            452 CACHE
            454 CACHE
            456 CACHE
            458 LOAD_FAST                5 (encoding)
            460 UNPACK_SEQUENCE          1
            464 CALL                     1
            472 CACHE
            474 STORE_FAST               9 (qs)
            476 JUMP_FORWARD             2 (to 482)

179     >>  478 LOAD_CONST              13 ('')
            480 STORE_FAST               9 (qs)

180     >>  482 LOAD_CONST              14 ('QUERY_STRING')
            484 LOAD_FAST                1 (environ)
            486 CONTAINS_OP              0
            488 POP_JUMP_IF_FALSE       19 (to 528)

181         490 LOAD_FAST                9 (qs)
            492 POP_JUMP_IF_FALSE        5 (to 504)
            494 LOAD_FAST                9 (qs)
            496 LOAD_CONST              15 ('&')
            498 BINARY_OP                0 (+)
            502 STORE_FAST               9 (qs)

182     >>  504 LOAD_FAST                9 (qs)
            506 LOAD_FAST                1 (environ)
            508 LOAD_CONST              14 ('QUERY_STRING')
            510 BINARY_SUBSCR
            514 CACHE
            516 CACHE
            518 CACHE
            520 BINARY_OP                0 (+)
            524 STORE_FAST               9 (qs)
            526 JUMP_FORWARD            48 (to 624)

183     >>  528 LOAD_GLOBAL              0 (sys)
            538 CACHE
            540 LOAD_ATTR               14 (parse_header)
            560 CACHE
            562 CACHE
            564 CACHE
            566 POP_JUMP_IF_FALSE       28 (to 624)

184         568 LOAD_FAST                9 (qs)
            570 POP_JUMP_IF_FALSE        5 (to 582)
            572 LOAD_FAST                9 (qs)
            574 LOAD_CONST              15 ('&')
            576 BINARY_OP                0 (+)
            580 STORE_FAST               9 (qs)

185     >>  582 LOAD_FAST                9 (qs)
            584 LOAD_GLOBAL              0 (sys)
            594 CACHE
            596 LOAD_ATTR               14 (parse_header)
            616 CACHE
            618 BINARY_OP                0 (+)
            622 STORE_FAST               9 (qs)

186     >>  624 LOAD_FAST                9 (qs)
            626 LOAD_FAST                1 (environ)
            628 LOAD_CONST              14 ('QUERY_STRING')
            630 STORE_SUBSCR
            634 JUMP_FORWARD            59 (to 754)

187     >>  636 LOAD_CONST              14 ('QUERY_STRING')
            638 LOAD_FAST                1 (environ)
            640 CONTAINS_OP              0
            642 POP_JUMP_IF_FALSE        9 (to 662)

188         644 LOAD_FAST                1 (environ)
            646 LOAD_CONST              14 ('QUERY_STRING')
            648 BINARY_SUBSCR
            652 CACHE
            654 CACHE
            656 CACHE
            658 STORE_FAST               9 (qs)
            660 JUMP_FORWARD            46 (to 754)

190     >>  662 LOAD_GLOBAL              0 (sys)
            672 CACHE
            674 LOAD_ATTR               14 (parse_header)
            694 CACHE
            696 CACHE
            698 CACHE
            700 POP_JUMP_IF_FALSE       19 (to 740)

191         702 LOAD_GLOBAL              0 (sys)
            712 CACHE
            714 LOAD_ATTR               14 (parse_header)
            734 CACHE
            736 STORE_FAST               9 (qs)
            738 JUMP_FORWARD             2 (to 744)

193     >>  740 LOAD_CONST              13 ('')
            742 STORE_FAST               9 (qs)

194     >>  744 LOAD_FAST                9 (qs)
            746 LOAD_FAST                1 (environ)
            748 LOAD_CONST              14 ('QUERY_STRING')
            750 STORE_SUBSCR

195     >>  754 LOAD_GLOBAL             30 (urllib)
            764 CACHE
            766 LOAD_ATTR               16 (parse_multipart)
            786 CACHE
            788 CACHE
            790 CACHE
            792 CACHE
            794 CACHE
            796 CACHE
            798 LOAD_FAST                9 (qs)
            800 LOAD_FAST                2 (keep_blank_values)
            802 LOAD_FAST                3 (strict_parsing)

196         804 LOAD_FAST                5 (encoding)
            806 LOAD_FAST                4 (separator)

195         808 KW_NAMES                17 (('encoding', 'separator'))
            810 UNPACK_SEQUENCE          5
            814 CALL                     5
            822 CACHE
            824 RETURN_VALUE

Disassembly of <code object parse_multipart at 0x000001E77E762310, file "cgi.py", line 199>:
              0 MAKE_CELL                8 (fs)

199           2 RESUME                   0

214           4 LOAD_FAST                1 (pdict)
              6 LOAD_CONST               1 ('boundary')
              8 BINARY_SUBSCR
             12 CACHE
             14 CACHE
             16 CACHE
             18 STORE_SUBSCR
             22 CACHE
             24 CACHE
             26 CACHE
             28 CACHE
             30 CACHE
             32 CACHE
             34 CACHE
             36 CACHE
             38 CACHE
             40 LOAD_CONST               2 ('ascii')
             42 UNPACK_SEQUENCE          1
             46 CALL                     1
             54 CACHE
             56 STORE_FAST               5 (boundary)

215          58 LOAD_CONST               3 ('multipart/form-data; boundary={}')
             60 STORE_SUBSCR
             64 CACHE
             66 CACHE
             68 CACHE
             70 CACHE
             72 CACHE
             74 CACHE
             76 CACHE
             78 CACHE
             80 CACHE
             82 LOAD_FAST                5 (boundary)
             84 UNPACK_SEQUENCE          1
             88 CALL                     1
             96 CACHE
             98 STORE_FAST               6 (ctype)

216         100 LOAD_GLOBAL              5 (NULL + Message)
            110 CACHE
            112 UNPACK_SEQUENCE          0
            116 CALL                     0
            124 CACHE
            126 STORE_FAST               7 (headers)

217         128 LOAD_FAST                7 (headers)
            130 STORE_SUBSCR
            134 CACHE
            136 CACHE
            138 CACHE
            140 CACHE
            142 CACHE
            144 CACHE
            146 CACHE
            148 CACHE
            150 CACHE
            152 LOAD_FAST                6 (ctype)
            154 UNPACK_SEQUENCE          1
            158 CALL                     1
            166 CACHE
            168 POP_TOP

218         170 NOP

219         172 LOAD_FAST                1 (pdict)
            174 LOAD_CONST               4 ('CONTENT-LENGTH')
            176 BINARY_SUBSCR
            180 CACHE
            182 CACHE
            184 CACHE
            186 LOAD_FAST                7 (headers)
            188 LOAD_CONST               5 ('Content-Length')
            190 STORE_SUBSCR
            194 JUMP_FORWARD            16 (to 228)
        >>  196 PUSH_EXC_INFO

220         198 LOAD_GLOBAL              8 (KeyError)
            208 CACHE
            210 CHECK_EXC_MATCH
            212 POP_JUMP_IF_FALSE        3 (to 220)
            214 POP_TOP

221         216 POP_EXCEPT
            218 JUMP_FORWARD             4 (to 228)

220     >>  220 RERAISE                  0
        >>  222 COPY                     3
            224 POP_EXCEPT
            226 RERAISE                  1

222     >>  228 LOAD_GLOBAL             11 (NULL + FieldStorage)
            238 CACHE
            240 LOAD_FAST                0 (fp)
            242 LOAD_FAST                7 (headers)
            244 LOAD_FAST                2 (encoding)
            246 LOAD_FAST                3 (errors)

223         248 LOAD_CONST               6 ('REQUEST_METHOD')
            250 LOAD_CONST               7 ('POST')
            252 BUILD_MAP                1
            254 LOAD_FAST                4 (separator)

222         256 KW_NAMES                 8 (('headers', 'encoding', 'errors', 'environ', 'separator'))
            258 UNPACK_SEQUENCE          6
            262 CALL                     6
            270 CACHE
            272 STORE_DEREF              8 (fs)

224         274 LOAD_CLOSURE             8 (fs)
            276 BUILD_TUPLE              1
            278 LOAD_CONST               9 (<code object <dictcomp> at 0x000001E77ECD9330, file "cgi.py", line 224>)
            280 MAKE_FUNCTION            8 (closure)
            282 LOAD_DEREF               8 (fs)
            284 GET_ITER
            286 UNPACK_SEQUENCE          0
            290 CALL                     0
            298 CACHE
            300 RETURN_VALUE
ExceptionTable:
  172 to 192 -> 196 [0]
  196 to 214 -> 222 [1] lasti
  220 to 220 -> 222 [1] lasti

Disassembly of <code object <dictcomp> at 0x000001E77ECD9330, file "cgi.py", line 224>:
              0 COPY_FREE_VARS           1

224           2 RESUME                   0
              4 BUILD_MAP                0
              6 LOAD_FAST                0 (.0)
        >>    8 FOR_ITER                24 (to 60)
             12 LOAD_FAST                1 (k)
             14 LOAD_DEREF               2 (fs)
             16 STORE_SUBSCR
             20 CACHE
             22 CACHE
             24 CACHE
             26 CACHE
             28 CACHE
             30 CACHE
             32 CACHE
             34 CACHE
             36 CACHE
             38 LOAD_FAST                1 (k)
             40 UNPACK_SEQUENCE          1
             44 CALL                     1
             52 CACHE
             54 MAP_ADD                  2
             56 JUMP_BACKWARD           25 (to 8)
             58 RETURN_VALUE

Disassembly of <code object _parseparam at 0x000001E77E9E9740, file "cgi.py", line 226>:
226           0 RETURN_GENERATOR
              2 POP_TOP
              4 RESUME                   0

227           6 LOAD_FAST                0 (s)
              8 LOAD_CONST               0 (None)
             10 LOAD_CONST               1 (1)
             12 BUILD_SLICE              2
             14 BINARY_SUBSCR
             18 CACHE
             20 CACHE
             22 CACHE
             24 LOAD_CONST               2 (';')
             26 COMPARE_OP               2 (<)
             30 CACHE
             32 POP_JUMP_IF_FALSE      247 (to 528)

228          34 LOAD_FAST                0 (s)
             36 LOAD_CONST               1 (1)
             38 LOAD_CONST               0 (None)
             40 BUILD_SLICE              2
             42 BINARY_SUBSCR
             46 CACHE
             48 CACHE
             50 CACHE
             52 STORE_FAST               0 (s)

229          54 LOAD_FAST                0 (s)
             56 STORE_SUBSCR
             60 CACHE
             62 CACHE
             64 CACHE
             66 CACHE
             68 CACHE
             70 CACHE
             72 CACHE
             74 CACHE
             76 CACHE
             78 LOAD_CONST               2 (';')
             80 UNPACK_SEQUENCE          1
             84 CALL                     1
             92 CACHE
             94 STORE_FAST               1 (end)

230          96 LOAD_FAST                1 (end)
             98 LOAD_CONST               3 (0)
            100 COMPARE_OP               4 (<)
            104 CACHE
            106 POP_JUMP_IF_FALSE      131 (to 370)
            108 LOAD_FAST                0 (s)
            110 STORE_SUBSCR
            114 CACHE
            116 CACHE
            118 CACHE
            120 CACHE
            122 CACHE
            124 CACHE
            126 CACHE
            128 CACHE
            130 CACHE
            132 LOAD_CONST               4 ('"')
            134 LOAD_CONST               3 (0)
            136 LOAD_FAST                1 (end)
            138 UNPACK_SEQUENCE          3
            142 CALL                     3
            150 CACHE
            152 LOAD_FAST                0 (s)
            154 STORE_SUBSCR
            158 CACHE
            160 CACHE
            162 CACHE
            164 CACHE
            166 CACHE
            168 CACHE
            170 CACHE
            172 CACHE
            174 CACHE
            176 LOAD_CONST               5 ('\\"')
            178 LOAD_CONST               3 (0)
            180 LOAD_FAST                1 (end)
            182 UNPACK_SEQUENCE          3
            186 CALL                     3
            194 CACHE
            196 BINARY_OP               10 (-)
            200 LOAD_CONST               6 (2)
            202 BINARY_OP                6 (%)
            206 POP_JUMP_IF_FALSE       81 (to 370)

231         208 LOAD_FAST                0 (s)
            210 STORE_SUBSCR
            214 CACHE
            216 CACHE
            218 CACHE
            220 CACHE
            222 CACHE
            224 CACHE
            226 CACHE
            228 CACHE
            230 CACHE
            232 LOAD_CONST               2 (';')
            234 LOAD_FAST                1 (end)
            236 LOAD_CONST               1 (1)
            238 BINARY_OP                0 (+)
            242 UNPACK_SEQUENCE          2
            246 CALL                     2
            254 CACHE
            256 STORE_FAST               1 (end)

230         258 LOAD_FAST                1 (end)
            260 LOAD_CONST               3 (0)
            262 COMPARE_OP               4 (<)
            266 CACHE
            268 POP_JUMP_IF_FALSE       50 (to 370)
            270 LOAD_FAST                0 (s)
            272 STORE_SUBSCR
            276 CACHE
            278 CACHE
            280 CACHE
            282 CACHE
            284 CACHE
            286 CACHE
            288 CACHE
            290 CACHE
            292 CACHE
            294 LOAD_CONST               4 ('"')
            296 LOAD_CONST               3 (0)
            298 LOAD_FAST                1 (end)
            300 UNPACK_SEQUENCE          3
            304 CALL                     3
            312 CACHE
            314 LOAD_FAST                0 (s)
            316 STORE_SUBSCR
            320 CACHE
            322 CACHE
            324 CACHE
            326 CACHE
            328 CACHE
            330 CACHE
            332 CACHE
            334 CACHE
            336 CACHE
            338 LOAD_CONST               5 ('\\"')
            340 LOAD_CONST               3 (0)
            342 LOAD_FAST                1 (end)
            344 UNPACK_SEQUENCE          3
            348 CALL                     3
            356 CACHE
            358 BINARY_OP               10 (-)
            362 LOAD_CONST               6 (2)
            364 BINARY_OP                6 (%)
