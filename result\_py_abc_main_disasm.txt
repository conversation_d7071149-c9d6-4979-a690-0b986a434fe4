# MAIN APPLICATION CODE OBJECT
# Position: 7359262
# Filename: _py_abc.py
# Function: <module>
# Args: 0
# Locals: 0
# Stack size: 5
# Flags: 0

# BYTECODE DISASSEMBLY:
==================================================
  0           0 RESUME                   0

  1           2 LOAD_CONST               0 (0)
              4 LOAD_CONST               1 (('WeakSet',))
              6 IMPORT_NAME              0 (_weakrefset)
              8 IMPORT_FROM              1 (WeakSet)
             10 STORE_NAME               1 (WeakSet)
             12 POP_TOP

  4          14 LOAD_CONST               2 (<code object get_cache_token at 0x000001E77EBD8810, file "_py_abc.py", line 4>)
             16 MAKE_FUNCTION            0
             18 STORE_NAME               2 (get_cache_token)

 14          20 PUSH_NULL
             22 LOAD_BUILD_CLASS
             24 LOAD_CONST               3 (<code object ABCMeta at 0x000001E77EBF9460, file "_py_abc.py", line 14>)
             26 MAKE_FUNCTION            0
             28 LOAD_CONST               4 ('ABCMeta')
             30 LOAD_NAME                3 (type)
             32 UNPACK_SEQUENCE          3
             36 CALL                     3
             44 CACHE
             46 STORE_NAME               4 (ABCMeta)
             48 LOAD_CONST               5 (None)
             50 RETURN_VALUE

Disassembly of <code object get_cache_token at 0x000001E77EBD8810, file "_py_abc.py", line 4>:
  4           0 RESUME                   0

 11           2 LOAD_GLOBAL              0 (ABCMeta)
             12 CACHE
             14 LOAD_ATTR                1 (NULL|self + ABCMeta)

Disassembly of <code object ABCMeta at 0x000001E77EBF9460, file "_py_abc.py", line 14>:
              0 MAKE_CELL                0 (__class__)

 14           2 RESUME                   0
              4 LOAD_NAME                0 (__name__)
              6 STORE_NAME               1 (__module__)
              8 LOAD_CONST               0 ('ABCMeta')
             10 STORE_NAME               2 (__qualname__)

 15          12 LOAD_CONST               1 ("Metaclass for defining Abstract Base Classes (ABCs).\n\n    Use this metaclass to create an ABC.  An ABC can be subclassed\n    directly, and then acts as a mix-in class.  You can also register\n    unrelated concrete classes (even built-in classes) and unrelated\n    ABCs as 'virtual subclasses' -- these and their descendants will\n    be considered subclasses of the registering ABC by the built-in\n    issubclass() function, but the registering ABC won't show up in\n    their MRO (Method Resolution Order) nor will method\n    implementations defined by the registering ABC be callable (not\n    even via super()).\n    ")
             14 STORE_NAME               3 (__doc__)

 33          16 LOAD_CONST               2 (0)
             18 STORE_NAME               4 (_abc_invalidation_counter)

 35          20 LOAD_CLOSURE             0 (__class__)
             22 BUILD_TUPLE              1
             24 LOAD_CONST               3 (<code object __new__ at 0x000001E77C487E00, file "_py_abc.py", line 35>)
             26 MAKE_FUNCTION            8 (closure)
             28 STORE_NAME               5 (__new__)

 54          30 LOAD_CONST               4 (<code object register at 0x000001E77E6BE0D0, file "_py_abc.py", line 54>)
             32 MAKE_FUNCTION            0
             34 STORE_NAME               6 (register)

 72          36 LOAD_CONST              11 ((None,))
             38 LOAD_CONST               6 (<code object _dump_registry at 0x000001E77C422460, file "_py_abc.py", line 72>)
             40 MAKE_FUNCTION            1 (defaults)
             42 STORE_NAME               7 (_dump_registry)

 83          44 LOAD_CONST               7 (<code object _abc_registry_clear at 0x000001E77EBC3830, file "_py_abc.py", line 83>)
             46 MAKE_FUNCTION            0
             48 STORE_NAME               8 (_abc_registry_clear)

 87          50 LOAD_CONST               8 (<code object _abc_caches_clear at 0x000001E77E76EE80, file "_py_abc.py", line 87>)
             52 MAKE_FUNCTION            0
             54 STORE_NAME               9 (_abc_caches_clear)

 92          56 LOAD_CONST               9 (<code object __instancecheck__ at 0x000001E77E73DDF0, file "_py_abc.py", line 92>)
             58 MAKE_FUNCTION            0
             60 STORE_NAME              10 (__instancecheck__)

108          62 LOAD_CONST              10 (<code object __subclasscheck__ at 0x000001E77E9B8140, file "_py_abc.py", line 108>)
             64 MAKE_FUNCTION            0
             66 STORE_NAME              11 (__subclasscheck__)
             68 LOAD_CLOSURE             0 (__class__)
             70 COPY                     1
             72 STORE_NAME              12 (__classcell__)
             74 RETURN_VALUE

Disassembly of <code object __new__ at 0x000001E77C487E00, file "_py_abc.py", line 35>:
              0 COPY_FREE_VARS           1

 35           2 RESUME                   0

 36           4 PUSH_NULL
              6 LOAD_GLOBAL              1 (NULL + super)
             16 CACHE
             18 UNPACK_SEQUENCE          0
             22 CALL                     0
             30 CACHE
             32 LOAD_ATTR                1 (NULL|self + super)
             52 BUILD_MAP                0
             54 LOAD_FAST                4 (kwargs)
             56 DICT_MERGE               1
             58 CALL_FUNCTION_EX         1
             60 STORE_FAST               5 (cls)

 38          62 LOAD_CONST               1 (<code object <setcomp> at 0x000001E77EBC3730, file "_py_abc.py", line 38>)
             64 MAKE_FUNCTION            0

 39          66 LOAD_FAST                3 (namespace)
             68 STORE_SUBSCR
             72 CACHE
             74 CACHE
             76 CACHE
             78 CACHE
             80 CACHE
             82 CACHE
             84 CACHE
             86 CACHE
             88 CACHE
             90 UNPACK_SEQUENCE          0
             94 CALL                     0
            102 CACHE

 38         104 GET_ITER
            106 UNPACK_SEQUENCE          0
            110 CALL                     0
            118 CACHE
            120 STORE_FAST               6 (abstracts)

 41         122 LOAD_FAST                2 (bases)
            124 GET_ITER
        >>  126 FOR_ITER                89 (to 308)

 42         130 LOAD_GLOBAL              7 (NULL + getattr)
            140 CACHE
            142 LOAD_FAST                7 (base)
            144 LOAD_CONST               2 ('__abstractmethods__')
            146 LOAD_GLOBAL              9 (NULL + set)
            156 CACHE
            158 UNPACK_SEQUENCE          0
            162 CALL                     0
            170 CACHE
            172 UNPACK_SEQUENCE          3
            176 CALL                     3
            184 CACHE
            186 GET_ITER
        >>  188 FOR_ITER                57 (to 306)

 43         192 LOAD_GLOBAL              7 (NULL + getattr)
            202 CACHE
            204 LOAD_FAST                5 (cls)
            206 LOAD_FAST                1 (name)
            208 LOAD_CONST               0 (None)
            210 UNPACK_SEQUENCE          3
            214 CALL                     3
            222 CACHE
            224 STORE_FAST               8 (value)

 44         226 LOAD_GLOBAL              7 (NULL + getattr)
            236 CACHE
            238 LOAD_FAST                8 (value)
            240 LOAD_CONST               3 ('__isabstractmethod__')
            242 LOAD_CONST               4 (False)
            244 UNPACK_SEQUENCE          3
            248 CALL                     3
            256 CACHE
            258 POP_JUMP_IF_FALSE       21 (to 302)

 45         260 LOAD_FAST                6 (abstracts)
            262 STORE_SUBSCR
            266 CACHE
            268 CACHE
            270 CACHE
            272 CACHE
            274 CACHE
            276 CACHE
            278 CACHE
            280 CACHE
            282 CACHE
            284 LOAD_FAST                1 (name)
            286 UNPACK_SEQUENCE          1
            290 CALL                     1
            298 CACHE
            300 POP_TOP
        >>  302 JUMP_BACKWARD           58 (to 188)

 42         304 JUMP_BACKWARD           90 (to 126)

 46     >>  306 LOAD_GLOBAL             13 (NULL + frozenset)
            316 CACHE
            318 LOAD_FAST                6 (abstracts)
            320 UNPACK_SEQUENCE          1
            324 CALL                     1
            332 CACHE
            334 LOAD_FAST                5 (cls)
            336 STORE_ATTR               7 (__abstractmethods__)

 48         346 LOAD_GLOBAL             17 (NULL + WeakSet)
            356 CACHE
            358 UNPACK_SEQUENCE          0
            362 CALL                     0
            370 CACHE
            372 LOAD_FAST                5 (cls)
            374 STORE_ATTR               9 (_abc_registry)

 49         384 LOAD_GLOBAL             17 (NULL + WeakSet)
            394 CACHE
            396 UNPACK_SEQUENCE          0
            400 CALL                     0
            408 CACHE
            410 LOAD_FAST                5 (cls)
            412 STORE_ATTR              10 (_abc_cache)

 50         422 LOAD_GLOBAL             17 (NULL + WeakSet)
            432 CACHE
            434 UNPACK_SEQUENCE          0
            438 CALL                     0
            446 CACHE
            448 LOAD_FAST                5 (cls)
            450 STORE_ATTR              11 (_abc_negative_cache)

 51         460 LOAD_GLOBAL             24 (ABCMeta)
            470 CACHE
            472 LOAD_ATTR               13 (NULL|self + frozenset)
            492 CACHE

 52         494 LOAD_FAST                5 (cls)
            496 RETURN_VALUE

Disassembly of <code object <setcomp> at 0x000001E77EBC3730, file "_py_abc.py", line 38>:
 38           0 RESUME                   0
              2 BUILD_SET                0
              4 LOAD_FAST                0 (.0)
        >>    6 FOR_ITER                24 (to 58)
             10 CACHE
             12 STORE_FAST               1 (name)
             14 STORE_FAST               2 (value)

 40          16 LOAD_GLOBAL              1 (NULL + getattr)
             26 CACHE
             28 LOAD_FAST                2 (value)
             30 LOAD_CONST               0 ('__isabstractmethod__')
             32 LOAD_CONST               1 (False)
             34 UNPACK_SEQUENCE          3
             38 CALL                     3
             46 CACHE
