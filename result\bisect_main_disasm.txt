# MAIN APPLICATION CODE OBJECT
# Position: 8047978
# Filename: bisect.py
# Function: <module>
# Args: 0
# Locals: 0
# Stack size: 4
# Flags: 0

# BYTECODE DISASSEMBLY:
==================================================
  0           0 RESUME                   0

  1           2 LOAD_CONST               0 ('Bisection algorithms.')
              4 STORE_NAME               0 (__doc__)

  4           6 LOAD_CONST               9 ((0, None))
              8 LOAD_CONST               2 (None)
             10 LOAD_CONST               3 (('key',))
             12 BUILD_CONST_KEY_MAP      1
             14 LOAD_CONST               4 (<code object insort_right at 0x000001E77EC850B0, file "bisect.py", line 4>)
             16 MAKE_FUNCTION            3 (defaults, kwdefaults)
             18 STORE_NAME               1 (insort_right)

 19          20 LOAD_CONST               9 ((0, None))
             22 LOAD_CONST               2 (None)
             24 LOAD_CONST               3 (('key',))
             26 BUILD_CONST_KEY_MAP      1
             28 LOAD_CONST               5 (<code object bisect_right at 0x000001E77ECB05A0, file "bisect.py", line 19>)
             30 MAKE_FUNCTION            3 (defaults, kwdefaults)
             32 STORE_NAME               2 (bisect_right)

 53          34 LOAD_CONST               9 ((0, None))
             36 LOAD_CONST               2 (None)
             38 LOAD_CONST               3 (('key',))
             40 BUILD_CONST_KEY_MAP      1
             42 LOAD_CONST               6 (<code object insort_left at 0x000001E77EC85210, file "bisect.py", line 53>)
             44 MAKE_FUNCTION            3 (defaults, kwdefaults)
             46 STORE_NAME               3 (insort_left)

 68          48 LOAD_CONST               9 ((0, None))
             50 LOAD_CONST               2 (None)
             52 LOAD_CONST               3 (('key',))
             54 BUILD_CONST_KEY_MAP      1
             56 LOAD_CONST               7 (<code object bisect_left at 0x000001E77ECB0770, file "bisect.py", line 68>)
             58 MAKE_FUNCTION            3 (defaults, kwdefaults)
             60 STORE_NAME               4 (bisect_left)

103          62 NOP

104          64 LOAD_CONST               1 (0)
             66 LOAD_CONST               8 (('*',))
             68 IMPORT_NAME              5 (_bisect)
             70 LOAD_CONST               0 ('Bisection algorithms.')
             72 JUMP_FORWARD            11 (to 96)
        >>   74 PUSH_EXC_INFO

105          76 LOAD_NAME                6 (ImportError)
             78 CHECK_EXC_MATCH
             80 POP_JUMP_IF_FALSE        3 (to 88)
             82 POP_TOP

106          84 POP_EXCEPT
             86 JUMP_FORWARD             4 (to 96)

105     >>   88 RERAISE                  0
        >>   90 COPY                     3
             92 POP_EXCEPT
             94 RERAISE                  1

109     >>   96 LOAD_NAME                2 (bisect_right)
             98 STORE_NAME               7 (bisect)

110         100 LOAD_NAME                1 (insort_right)
            102 STORE_NAME               8 (insort)
            104 LOAD_CONST               2 (None)
            106 RETURN_VALUE
ExceptionTable:
  64 to 70 -> 74 [0]
  74 to 82 -> 90 [1] lasti
  88 to 88 -> 90 [1] lasti

Disassembly of <code object insort_right at 0x000001E77EC850B0, file "bisect.py", line 4>:
  4           0 RESUME                   0

 12           2 LOAD_FAST                4 (key)
              4 POP_JUMP_IF_NOT_NONE    19 (to 44)

 13           6 LOAD_GLOBAL              1 (NULL + bisect_right)
             16 CACHE
             18 LOAD_FAST                0 (a)
             20 LOAD_FAST                1 (x)
             22 LOAD_FAST                2 (lo)
             24 LOAD_FAST                3 (hi)
             26 UNPACK_SEQUENCE          4
             30 CALL                     4
             38 CACHE
             40 STORE_FAST               2 (lo)
             42 JUMP_FORWARD            29 (to 102)

 15     >>   44 LOAD_GLOBAL              1 (NULL + bisect_right)
             54 CACHE
             56 LOAD_FAST                0 (a)
             58 PUSH_NULL
             60 LOAD_FAST                4 (key)
             62 LOAD_FAST                1 (x)
             64 UNPACK_SEQUENCE          1
             68 CALL                     1
             76 CACHE
             78 LOAD_FAST                2 (lo)
             80 LOAD_FAST                3 (hi)
             82 LOAD_FAST                4 (key)
             84 KW_NAMES                 2 (('key',))
             86 UNPACK_SEQUENCE          5
             90 CALL                     5
             98 CACHE
            100 STORE_FAST               2 (lo)

 16     >>  102 LOAD_FAST                0 (a)
            104 STORE_SUBSCR
            108 CACHE
            110 CACHE
            112 CACHE
            114 CACHE
            116 CACHE
            118 CACHE
            120 CACHE
            122 CACHE
            124 CACHE
            126 LOAD_FAST                2 (lo)
            128 LOAD_FAST                1 (x)
            130 UNPACK_SEQUENCE          2
            134 CALL                     2
            142 CACHE
            144 POP_TOP
            146 LOAD_CONST               1 (None)
            148 RETURN_VALUE

Disassembly of <code object bisect_right at 0x000001E77ECB05A0, file "bisect.py", line 19>:
 19           0 RESUME                   0

 30           2 LOAD_FAST                2 (lo)
              4 LOAD_CONST               1 (0)
              6 COMPARE_OP               0 (<)
             10 CACHE
             12 POP_JUMP_IF_FALSE       15 (to 44)

 31          14 LOAD_GLOBAL              1 (NULL + ValueError)
             24 CACHE
             26 LOAD_CONST               2 ('lo must be non-negative')
             28 UNPACK_SEQUENCE          1
             32 CALL                     1
             40 CACHE
             42 RAISE_VARARGS            1

 32     >>   44 LOAD_FAST                3 (hi)
             46 POP_JUMP_IF_NOT_NONE    15 (to 78)

 33          48 LOAD_GLOBAL              3 (NULL + len)
             58 CACHE
             60 LOAD_FAST                0 (a)
             62 UNPACK_SEQUENCE          1
             66 CALL                     1
             74 CACHE
             76 STORE_FAST               3 (hi)

 36     >>   78 LOAD_FAST                4 (key)
             80 POP_JUMP_IF_NOT_NONE    41 (to 164)

 37          82 LOAD_FAST                2 (lo)
             84 LOAD_FAST                3 (hi)
             86 COMPARE_OP               0 (<)
             90 CACHE
             92 POP_JUMP_IF_FALSE       34 (to 162)

 38          94 LOAD_FAST                2 (lo)
             96 LOAD_FAST                3 (hi)
             98 BINARY_OP                0 (+)
            102 LOAD_CONST               4 (2)
            104 BINARY_OP                2 (//)
            108 STORE_FAST               5 (mid)

 39         110 LOAD_FAST                1 (x)
            112 LOAD_FAST                0 (a)
            114 LOAD_FAST                5 (mid)
            116 BINARY_SUBSCR
            120 CACHE
            122 CACHE
            124 CACHE
            126 COMPARE_OP               0 (<)
            130 CACHE
            132 POP_JUMP_IF_FALSE        3 (to 140)

 40         134 LOAD_FAST                5 (mid)
            136 STORE_FAST               3 (hi)
            138 JUMP_FORWARD             5 (to 150)

 42     >>  140 LOAD_FAST                5 (mid)
            142 LOAD_CONST               5 (1)
            144 BINARY_OP                0 (+)
            148 STORE_FAST               2 (lo)

 37     >>  150 LOAD_FAST                2 (lo)
            152 LOAD_FAST                3 (hi)
            154 COMPARE_OP               0 (<)
            158 CACHE
