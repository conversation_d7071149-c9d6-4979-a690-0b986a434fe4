Application Data Strings
==================================================

  1. 'uExample: ./imgproc --function create_object_normal_map --directional_images path/to/image1.tif path/to/image2.tif path/to/image3.tif path/to/image4.tif path/to/image5.tif path/to/image6.tif --output path/to/output.tif'
  2. 'uExample: ./imgproc --function create_specular_map --image1 path/to/image1.tif --image2 path/to/image2.tif --output path/to/output.tif'
  3. 'uExample: ./imgproc --function align_image --image1 path/to/image1.tif --image2 path/to/image2.tif --output path/to/output.tif'
  4. 'uExample: ./imgproc --function align_image_set --image_dir path/to/images --image_prefix TX-01 --output path/to/output'
  5. 'uExample: ./imgproc --function create_tangent_normal_map --image1 path/to/image1.tif --output path/to/output.tif'
  6. '  base_image: The reference image to which the image_to_align will be aligned.'
  7. 'u--image_dir and --image_prefix are required for align_image_set function'
  8. 'uBoth --image1 and --image2 are required for create_specular_map function'
  9. 'uImages must be of the same dimensions to calculate the specular map.'
 10. 'uPath to save the output (director or file depending on the function)'
 11. '  image_to_align: The image that will be aligned to the base_image.'
 12. 'uSix image paths are required for create_object_normal_map function'
 13. 'u--image1 --image2 --output are required for align_image function'
 14. 'uPrefix for images to be aligned (required for align_image_set)'
 15. 'u--image1 is required for create_tangent_normal_map function'
 16. 'uPaths to six images (required for create_object_normal_map)'
 17. 'uDirectory containing images (required for align_image_set)'
 18. 'uIndex of the base image (required for align_image_set)'
 19. "u%s got multiple values for keyword argument '%s'"
 20. 'uPrint the elapsed time of the decorated function'
 21. 'ualign_image_set_threaded.<locals>.align_and_save'
 22. 'uimgproc: Complete - elapsed time: %d:%02d:%0.2f'
 23. 'u%s argument after * must be an iterable, not %s'
 24. 'u%s argument after ** must be a mapping, not %s'
 25. 'uBase Image Face detected creating face_mask:'
 26. 'uRemove blue channel from tangent normal map'
 27. 'uKernel size for tangent normal filtering'
 28. 'uSigma value for tangent normal filtering'
 29. 'uAuto detect and crop face for alignment'
 30. 'uPath to mask image for face detection'
 31. 'uimage_suffix_sort.<locals>.<lambda>'
 32. 'uInput image must be a 16-bit image.'
 33. 'ualign_image_akaze.<locals>.<lambda>'
 34. 'uimage_prefix_sort.<locals>.<lambda>'
 35. 'ablurred_image_to_align_hist_match'
 36. 'ualign_image_ecc.<locals>.<lambda>'
 37. 'uBlur size for tangent normal map'
 38. 'uimgproc: Authorization Success.'
 39. 'aimage_to_align_hist_match_norm'
 40. 'uimgproc: Authorization Failed.'
 41. 'uImage aligned and saved to {}'
 42. 'uCrop fraction for alignment'
 43. '  Returns the aligned image.'
 44. 'uImage is not 8bit or 16bit'
 45. 'acreate_tangent_normal_map'
 46. 'aimage_to_align_hist_match'
 47. 'Faload_directional_images'
 48. 'acreate_object_normal_map'
 49. 'w_w.uImage for direction '
 50. 'uPath to the second image'
 51. 'aalign_image_set_threaded'
 52. 'uPath to the first image'
 53. 'uCopied base image to {}'
 54. 'ablurred_image_to_align'
 55. 'aload_directional_image'
 56. 'uimage min: %d, max: %d'
 57. 'aedge_preprocess_image'
 58. 'aget_image_prefix_list'
 59. 'w3w4w5w6w7w8aimage_dir'
 60. 'abase_image_face_rect'
 61. 'u--directional_images'
 62. 'nnnuImage not found: '
 63. 'anormalize_intensity'
 64. '?Faalign_image_akaze'
 65. '?tacreate_height_map'
 66. 'uobject_normal_map: '
 67. 'uFunction to execute'
 68. 'uimgproc: Started - '
 69. 'acreate_specular_map'
 70. 'u--base_image_index'
 71. 'atangent_normal_map'
 72. 'n:nnnaaligned_image'
 73. 'aoptical_flow_align'
 74. 'ablurred_base_image'
 75. 'adirectional_images'
 76. 'uAligned and saved '
 77. 'aimage_prefix_sort'
 78. 'adescriptors_align'
 79. 'aimage_prefix_list'
 80. 'aimage_suffix_sort'
 81. 'abase_image_index'
 82. 'uImgProc CLI Tool'
 83. 'aWARP_INVERSE_MAP'
 84. 'akeypoints_align'
 85. 'aalign_image_set'
 86. 'abase_image_norm'
 87. 'aalign_image_ecc'
 88. 'a__match_args__'
 89. 'aArgumentParser'
 90. 'u--image_prefix'
 91. 'aimage_to_align'
 92. 'aalign_and_save'
 93. 'acropped_align'
 94. 'aoutput_images'
 95. 'astar_arg_dict'
 96. 'astar_arg_list'
 97. 'aaligned_image'
 98. 'aimage_prefix'
 99. 'T.abase_image'
100. 'apoints_align'
101. 'aadd_argument'
102. 'aedges_align'
103. 'aalign_image'
104. 'aaligned_img'
105. 'u--image_dir'
106. 'agray_align'
107. 'u--function'
108. 'abase_image'
109. 'aimage_path'
110. 'aparse_args'
111. 'uimgproc.py'
112. 'anormalize'
113. 'aimage_dir'
114. 'afunction'
115. 'u--image2'
116. 'aargparse'
117. 'u--image1'
118. 'aaligned'
119. 'aimage1'
120. 'aimages'
121. 'aimage2'
122. 'aimage'
123. 'aremap'
124. 'anargs'
125. 'ahelp'
126. 'aargs'
